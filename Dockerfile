FROM openjdk:21-slim

# Cài freetype và các thư viện liên quan đến font
RUN apt-get update && apt-get install -y \
    libfreetype6 \
    fontconfig \
    fonts-dejavu-core \
 && rm -rf /var/lib/apt/lists/*

ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} app.jar
COPY ./overlays /overlays
EXPOSE 8090
#ENTRYPOINT ["/usr/local/openjdk-21/bin/java"]
ENTRYPOINT ["java"]
CMD ["-Djava.awt.headless=true","-jar", "app.jar"]