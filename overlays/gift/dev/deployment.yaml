apiVersion: apps/v1
kind: Deployment
metadata:
  name: vtd-be-java-gift
  namespace: default
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: vtd-be-java-gift
  template:
    metadata:
      labels:
        app: vtd-be-java-gift
    spec:
      tolerations:
        - key: "name"
          value: "api-services"
          operator: "Equal"
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: name
                    operator: In
                    values:
                      - api-services
      containers:
        - name: vtd-be-java-gift
          resources:
            requests:
              memory: 256Mi
              cpu: 200m
            limits:
              memory: 512Mi
              cpu: 500m
          image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-dev/vtd-be-java-gift:latest
          ports:
            - containerPort: 8090
              name: app-port
          startupProbe:
            httpGet:
              path: /v4/gs/no-auth-token/health/startup-probe
              port: app-port
            failureThreshold: 20
            periodSeconds: 30
            timeoutSeconds: 25
          livenessProbe:
            httpGet:
              path: /v4/gs/no-auth-token/health/liveness-probe
              port: app-port
            failureThreshold: 5
            periodSeconds: 30
            timeoutSeconds: 25
          volumeMounts:
            - name: vtd-be-java-gcs-credential
              mountPath: /secrets
              readOnly: true
      volumes:
        - name: vtd-be-java-gcs-credential
          secret:
            secretName: vtd-be-java-gcs-credential

