apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-gift-update-status-usergift
  namespace: default
spec:
  schedule: "50 0 * * *"
  timeZone: "Asia/Ho_Chi_Minh"
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 0
  jobTemplate:
    spec:
      backoffLimit: 0
      ttlSecondsAfterFinished: 60
      # activeDeadlineSeconds: 600
      template:
        spec:
          tolerations:
            - key: "name"
              value: "cron-services"
              operator: "Equal"
              effect: "NoSchedule"
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: name
                        operator: In
                        values:
                          - cron-services
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-be-java-gift:latest
              name: cronjob-vtd-be-java-gift-update-status-usergift
              resources:
                requests:
                  memory: 256Mi
                  cpu: 200m
                limits:
                  memory: 512Mi
                  cpu: 500m
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_gsUserGiftSchedule"
          restartPolicy: Never
