apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-gift-update-status-usergift
  namespace: default
spec:
  schedule: "0 1 * * *"
  timeZone: "Asia/Ho_Chi_Minh"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 0
      activeDeadlineSeconds: 800
      template:
        spec:
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-gift:latest
              name: cronjob-vtd-be-java-gift-update-status-usergift
              resources:
                requests:
                  memory: 256Mi
                  cpu: 200m
                limits:
                  memory: 512Mi
                  cpu: 500m
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_gsUserGiftSchedule"
          restartPolicy: Never
