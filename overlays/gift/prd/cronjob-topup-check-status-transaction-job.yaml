apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-topup-check-status-transaction
  namespace: default
spec:
  schedule: "0 * * * *"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  concurrencyPolicy: 'Forbid'
  jobTemplate:
    spec:
      backoffLimit: 0
      #activeDeadlineSeconds: 600
      template:
        spec:
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-gift:latest
              name: cronjob-vtd-be-java-topup-check-status-transaction
              resources:
                requests:
                  memory: 256Mi
                  cpu: 200m
                limits:
                  memory: 512Mi
                  cpu: 500m
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_topupCheckTransactionSchedulerService"
          restartPolicy: Never
