apiVersion: apps/v1
kind: Deployment
metadata:
  name: vtd-be-java-warehouse
  namespace: default
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
  selector:
    matchLabels:
      app: vtd-be-java-warehouse
  template:
    metadata:
      labels:
        app: vtd-be-java-warehouse
    spec:
      tolerations:
        - key: "name"
          value: "api-services"
          operator: "Equal"
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: name
                    operator: In
                    values:
                      - api-services
      containers:
        - name: vtd-be-java-warehouse
          resources:
            requests:
              memory: 256Mi
              cpu: 200m
            limits:
              memory: 512Mi
              cpu: 500m
          image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-dev/vtd-be-java-warehouse:latest
          ports:
            - containerPort: 8090
          readinessProbe:
            httpGet:
              path: /v4/ws/health/check
              port: 8090
            initialDelaySeconds: 20
            periodSeconds: 10
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /v4/ws/health/check
              port: 8090
            initialDelaySeconds: 60
            periodSeconds: 20
            failureThreshold: 6
