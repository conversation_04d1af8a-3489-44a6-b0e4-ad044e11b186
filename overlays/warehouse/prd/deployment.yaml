apiVersion: apps/v1
kind: Deployment
metadata:
  name: vtd-be-java-warehouse
  namespace: default
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
  selector:
    matchLabels:
      app: vtd-be-java-warehouse
  template:
    metadata:
      labels:
        app: vtd-be-java-warehouse
    spec:
      containers:
        - name: vtd-be-java-warehouse
          resources:
            requests:
              memory: 256Mi
              cpu: 500m
            limits:
              memory: 1024Mi
              cpu: 1000m
          image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-warehouse:latest
          ports:
            - containerPort: 8090
          readinessProbe:
            httpGet:
              path: /v4/ws/health/check
              port: 8090
            initialDelaySeconds: 20
            periodSeconds: 10
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /v4/ws/health/check
              port: 8090
            initialDelaySeconds: 60
            periodSeconds: 20
            failureThreshold: 6
