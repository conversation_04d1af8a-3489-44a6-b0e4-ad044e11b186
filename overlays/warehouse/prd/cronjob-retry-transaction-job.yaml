apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-warehouse-retry-transaction-job
  namespace: default
spec:
  schedule: '* * * * *'
  concurrencyPolicy: "Forbid"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-warehouse:latest
              name: cronjob-vtd-be-java-warehouse-retry-transaction-job
              resources:
                requests:
                  memory: 256Mi
                limits:
                  memory: 512Mi
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_retryTransactionJob"
          restartPolicy: Never
