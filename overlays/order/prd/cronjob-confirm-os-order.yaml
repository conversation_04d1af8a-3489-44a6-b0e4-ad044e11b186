apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-order-confirm-os-order
  namespace: default
spec:
  schedule: "0 0 * * *"
  timeZone: "Asia/Ho_Chi_Minh"
  concurrencyPolicy: "Forbid"
  jobTemplate:
    spec:
      backoffLimit: 0
      ttlSecondsAfterFinished: 60
      template:
        spec:
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-order:latest
              name: cronjob-vtd-be-java-order-confirm-os-order
              resources:
                requests:
                  memory: 256Mi
                  cpu: 200m
                limits:
                  memory: 512Mi
                  cpu: 500m
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_confirmOrderJob"
          restartPolicy: Never
