#apiVersion: batch/v1
#kind: CronJob
#metadata:
#  name: cronjob-vtd-be-java-order-migrate-os-order
#  namespace: default
#spec:
#  schedule: "4 16 17 9 *"
#  successfulJobsHistoryLimit: 1
#  failedJobsHistoryLimit: 1
#  jobTemplate:
#    spec:
#      backoffLimit: 0
#      template:
#        spec:
#          containers:
#            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-prod/vtd-be-java-order:v0.1.13
#              name: cronjob-vtd-be-java-order-migrate-os-order
#              resources:
#                requests:
#                  memory: 256Mi
#                  cpu: 200m
#                limits:
#                  memory: 512Mi
#                  cpu: 500m
#              env:
#                - name: MIGRATE_FROM
#                  value: "1704042000000"
#              command:
#                - "java"
#              args:
#                - "-jar"
#                - "app.jar"
#                - "--cronJob_migrate<PERSON>rderJob"
#          restartPolicy: Never
