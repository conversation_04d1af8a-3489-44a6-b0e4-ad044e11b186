apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-vtd-be-java-order-confirm-os-order
  namespace: default
spec:
  schedule: "0 0 * * *"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  timeZone: "Asia/Ho_Chi_Minh"
  concurrencyPolicy: "Forbid"
  jobTemplate:
    spec:
      backoffLimit: 0
      ttlSecondsAfterFinished: 60
      template:
        spec:
          tolerations:
            - key: "name"
              value: "cron-services"
              operator: "Equal"
              effect: "NoSchedule"
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: name
                        operator: In
                        values:
                          - cron-services
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-dev/vtd-be-java-order:latest
              name: cronjob-vtd-be-java-order-confirm-os-order
              resources:
                requests:
                  memory: 256Mi
                  cpu: 200m
                limits:
                  memory: 512Mi
                  cpu: 500m
              command:
                - "java"
              args:
                - "-jar"
                - "app.jar"
                - "--cronJob_confirmOrderJob"
          restartPolicy: Never
