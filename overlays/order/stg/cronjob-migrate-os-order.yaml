#apiVersion: batch/v1
#kind: CronJob
#metadata:
#  name: cronjob-vtd-be-java-order-migrate-os-order
#  namespace: default
#spec:
#  schedule: "4 16 17 9 *"
#  successfulJobsHistoryLimit: 1
#  failedJobsHistoryLimit: 1
#  jobTemplate:
#    spec:
#      backoffLimit: 0
#      activeDeadlineSeconds: 600
#      template:
#        spec:
#          tolerations:
#            - key: "name"
#              value: "cron-services"
#              operator: "Equal"
#              effect: "NoSchedule"
#          affinity:
#            nodeAffinity:
#              requiredDuringSchedulingIgnoredDuringExecution:
#                nodeSelectorTerms:
#                  - matchExpressions:
#                      - key: name
#                        operator: In
#                        values:
#                          - cron-services
#          containers:
#            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-be-java-order:latest
#              name: cronjob-vtd-be-java-order-migrate-os-order
#              resources:
#                requests:
#                  memory: 256Mi
#                  cpu: 200m
#                limits:
#                  memory: 512Mi
#                  cpu: 500m
#              command:
#                - "java"
#              args:
#                - "-jar"
#                - "app.jar"
#                - "--cronJob_migrateOrderJob"
#          restartPolicy: Never
