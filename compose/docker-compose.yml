version: "3.7"
services:
  zookeeper:
    container_name: zookeeper
    restart: always
    image: docker.io/bitnami/zookeeper:3.8
    ports:
      - "2181:2181"
    volumes:
      - "zookeeper-volume:/bitnami"
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
  kafka:
    container_name: kafka
    restart: always
    image: docker.io/bitnami/kafka:3.3
    ports:
      - "9093:9093"
    volumes:
      - "kafka-volume:/bitnami"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CLIENT:PLAINTEXT,EXTERNAL:PLAINTEXT,INTERNAL:PLAINTEXT
      - KAFKA_CFG_LISTENERS=CLIENT://:9092,EXTERNAL://:9093,INTERNAL://:9094
      - <PERSON><PERSON><PERSON>_CFG_ADVERTISED_LISTENERS=CLIENT://kafka:9092,EXTERNAL://localhost:9093,INTERNAL://kafka:9094
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=CLIENT
    depends_on:
      - zookeeper
#  kafka-ui:
#    restart: always
#    container_name: kafka-ui
#    image: provectuslabs/kafka-ui:latest
#    ports:
#      - 8081:8080
#    volumes:
#      - ./kafka-ui/config.yml:/etc/kafkaui/dynamic_config.yaml
#    environment:
#      DYNAMIC_CONFIG_ENABLED: "true"
#    depends_on:
#      - kafka
#      - zookeeper

#  postgres:
#    image: postgres:15
#    environment:
#      - POSTGRES_USER=postgres
#      - POSTGRES_PASSWORD=password
#    volumes:
#      - postgres-db-volume:/var/lib/postgresql/data
#    ports:
#      - 5432:5432

volumes:
  kafka-volume:
    driver: local
  zookeeper-volume:
    driver: local
  kafka-ui-volume:
    driver: local
#  postgres-db-volume:
#    driver: local