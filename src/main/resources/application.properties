server.port=8090
spring.application.name=main
spring.profiles.active=local
server.servlet.context-path=/
micro.service.context-path=/api
internal.servlet.context-path=/int

springdoc.api-docs.path=${micro.service.context-path}/api-docs
springdoc.swagger-ui.path=${micro.service.context-path}/swagger-ui.html
springdoc.swagger-ui.url=${micro.service.context-path}/api-docs

# JPA
# spring.jpa.generate-ddl=false
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.batch_size=500
spring.jpa.properties.hibernate.order_inserts = true
spring.flyway.enabled=false

spring.security.auth.enabled=true
#logging.level.org.springframework.web=TRACE

evoucher.service=got-it
got-it.content-type=application/json
got-it.accept-language=vi
got-it.connection-timeout-seconds=10
got-it.read-timeout-seconds=20
got-it.auth.header=X-GI-Authorization
got-it.auth.expiry-time-seconds=15768000
got-it.api.category=/api/category/getAll
got-it.api.brand=/api/brand/getAll
got-it.api.list-product=/api/product/list
got-it.api.product-detail=/api/product/detail
got-it.api.transaction=/api/transaction
got-it.api.topup=/api/topup
got-it.api.get-evoucher=/api/transaction
got-it.api.check-status-evoucher=/api/transaction/check
got-it.api.check-topup=/api/topup/check
got-it.api-v1.4.category=/api/v1.4/category/getAll
got-it.api-v1.4.brand=/api/v1.4/brand/getAll
got-it.api-v1.4.brand-detail=/api/v1.4/brand/detail/
got-it.api-v1.4.list-product=/api/v1.4/product/list
got-it.api-v1.4.product-detail=/api/v1.4/product/detail
got-it.api-v1.4.transaction=/api/v1.4/transaction
got-it.api-v1.4.topup=/api/v1.4/topup
got-it.api-v1.4.get-transaction=/api/v1.4/unilever/check
got-it.api-v1.4.get-evoucher=/api/v1.4/transaction
got-it.api-v1.4.check-status-evoucher=/api/v1.4/transaction/check
got-it.api-v1.4.check-topup=/api/v1.4/topup/check

bkids.auth.header=Authorization
bkids.api-path.request-coupon=/unlock_keys/request
bkids.api-path.detail-coupon=/unlock_keys