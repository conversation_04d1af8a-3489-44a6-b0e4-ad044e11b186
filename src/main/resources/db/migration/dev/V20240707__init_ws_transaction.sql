-- create or replace function generate_transaction_id()
--    returns bigint
--    language plpgsql
--   as
-- $$
-- declare
--     current_millis bigint = extract(epoch from date_trunc('milliseconds', current_timestamp) at time zone 'UTC') * 1000;
--     rand_num_3_digits integer = floor(100 + random() * 899);
-- begin
--     return
--         CAST(
--             (cast(current_millis as text) || cast(rand_num_3_digits as text))
--         as bigint);
-- end;
-- $$;

-- Ensure that the enable_partition_pruning configuration parameter is not disabled in postgresql.conf.
-- If it is, queries will not be optimized as desired

-- table store transaction sent from RA
CREATE TABLE IF NOT EXISTS ws_transaction (
    id SERIAL,
    code VARCHAR,
    payload JSONB NOT NULL,
    destination VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

CREATE INDEX ON ws_transaction (created_at);

CREATE TABLE ws_transaction_y2024m07 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01');

CREATE TABLE ws_transaction_y2024m08 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');

CREATE TABLE ws_transaction_y2024m09 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

CREATE TABLE ws_transaction_y2024m10 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

CREATE TABLE ws_transaction_y2024m11 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');

CREATE TABLE ws_transaction_y2024m12 PARTITION OF ws_transaction
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- table store sf response
CREATE TABLE ws_sf_response (
    id SERIAL,
    transaction_id SERIAL,
    http_code VARCHAR,
    response VARCHAR,
    sent_at TIMESTAMP,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, received_at)
) PARTITION BY RANGE (received_at);

CREATE INDEX ON ws_sf_response (received_at);

CREATE TABLE ws_sf_response_y2024m07 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01');

CREATE TABLE ws_sf_response_y2024m08 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');

CREATE TABLE ws_sf_response_y2024m09 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

CREATE TABLE ws_sf_response_y2024m10 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

CREATE TABLE ws_sf_response_y2024m11 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');

CREATE TABLE ws_sf_response_y2024m12 PARTITION OF ws_sf_response
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE ws_job_checkpoint (
    job varchar(50),
    previous_checkpoint TIMESTAMP,
    current_checkpoint TIMESTAMP,
    PRIMARY KEY (job)
);

CREATE TABLE ws_sf_api_config (
    transaction_code varchar,
    path varchar,
    http_method varchar(5)
)