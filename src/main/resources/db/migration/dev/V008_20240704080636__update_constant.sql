DROP TABLE IF EXISTS gs_constants;
CREATE TABLE gs_constants
(
    id           SERIAL PRIMARY KEY,
    key_alpha    VARCHAR   NOT NULL,
    key_beta     VARCHAR   NOT NULL,
    key_gamma    VARCHAR,
    key_delta    VARCHAR,
    "value"      VARCHAR   NOT NULL,
    priority INTEGER, -- Equivalent to Integer.MAX_VALUE in Java
    dynamic_data JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);