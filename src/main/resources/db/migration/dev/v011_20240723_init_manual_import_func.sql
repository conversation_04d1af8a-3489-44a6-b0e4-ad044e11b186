CREATE TABLE os_manual_import_order_temp
(
    line                bigint,
    session             varchar NOT NULL,
    order_code          varchar,
    transaction_code    varchar,
    delivery_code       varchar,
    delivery_provider   varchar,
    delivery_created_at varchar DEFAULT CURRENT_TIMESTAMP,
    ship_cost           varchar,
    status              varchar DEFAULT 'imported'
);

CREATE OR REPLACE FUNCTION is_valid_timestamp_string(time_string_input varchar, format_input varchar) RETURNS boolean
    LANGUAGE plpgsql
AS
$$
DECLARE
    item record;
BEGIN
    SELECT TO_TIMESTAMP(time_string_input, format_input)::timestamp WITHOUT TIME ZONE AT TIME ZONE 'UTC' INTO item;
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

CREATE OR REPLACE FUNCTION random_alpha_numeric(prefix varchar, suffixLength integer) RETURNS varchar
    LANGUAGE plpgsql
AS
$$
BEGIN
    RETURN FORMAT('%s%s%s', prefix, TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD'),
                  UPPER(LEFT(MD5(RANDOM()::text), suffixLength)));
END;
$$;

CREATE OR REPLACE FUNCTION validate_manual_import_os_order_temp_fn(session_input varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num      int := 500;
    msg            varchar;
    msg_array      varchar[];
    msg_array_item varchar[];
    item           record;
    maxLoop        INT := 100;
    countLoop      int := 0;
BEGIN
    WHILE countLoop < maxLoop
        LOOP
            countLoop := countLoop + 1;
            FOR item IN
                SELECT DISTINCT order_code
                FROM os_manual_import_order_temp
                WHERE session = session_input
                  AND status = 'imported'
                LIMIT batch_num
                LOOP
                    msg_array_item := validate_item_manual_import_os_order_temp_fn(session_input, item.order_code);
                    msg_array := ARRAY_CAT(msg_array, msg_array_item);

                    msg := 'checked';
                    IF ARRAY_LENGTH(msg_array_item, 1) > 0 THEN
                        msg := 'failure';
                    END IF;

                    UPDATE os_manual_import_order_temp
                    SET status = msg
                    WHERE session = session_input
                      AND order_code = item.order_code;
                END LOOP;
        END LOOP;
    RETURN msg_array;
END;
$$;


CREATE OR REPLACE FUNCTION validate_item_manual_import_os_order_temp_fn(session_input varchar, order_code_input varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    item                         record;
    item_order                   record;
    line_array                   varchar   := '';
    msg                          text      := '';
    count_number                 int       := 0;
    msg_return                   varchar[] := ARRAY []:: varchar[];
    delivery_code_array          varchar[] := ARRAY []:: varchar[];
    transaction_code_array_tmp   varchar[] := ARRAY []:: varchar[];
    transaction_code_array_order varchar[] := ARRAY []:: varchar[];
BEGIN
    FOR item IN
        SELECT *
        FROM os_manual_import_order_temp
        WHERE session = session_input
          AND order_code = order_code_input
          AND status = 'imported'
        LOOP
            -- start: Check null or empty
            line_array := CONCAT(line_array, ' #', item.line);
            transaction_code_array_tmp := ARRAY_APPEND(transaction_code_array_tmp, item.transaction_code);

            IF COALESCE(TRIM(item.order_code), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Mã Đơn Hàng (Order Code).', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.transaction_code), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Mã Giao Dịch (Transaction).', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.delivery_code), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Mã Vận Đơn (MVĐ).', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.delivery_provider), '') = '' THEN
                msg := msg , FORMAT('Dòng #%s: Không để trống Đơn Vị Vận Chuyển (ĐVVC).', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.delivery_created_at), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Ngày Tạo Mã Vận Đơn (Ngày Tạo MVD).', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            ELSE
                IF NOT is_valid_timestamp_string(item.delivery_created_at, 'DD/MM/YYYY') THEN
                    msg := FORMAT('Dòng #%s: Ngày Tạo Mã Vận Đơn (Ngày Tạo MVD) sai format (DD/MM/YYYY).', item.line);
                    msg_return := ARRAY_APPEND(msg_return, msg);
                END IF;
            END IF;

            IF COALESCE(TRIM(item.ship_cost), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Phí Ship.', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            -- end: Check null or empty
        END LOOP;

    -- start: Check Order
    IF ARRAY_LENGTH(msg_return, 1) > 0 THEN
        RETURN msg_return;
    END IF;

    SELECT *
    INTO item_order
    FROM os_order
    WHERE order_code = order_code_input
    LIMIT 1;

    IF item_order.id IS NULL THEN
        msg := FORMAT('Dòng%s: Không tìm thấy Mã Đơn Hàng (Order Code) [%s] trong hệ thống.', line_array,
                      order_code_input);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;


    SELECT ARRAY_AGG(spec."transactionCode")
    INTO transaction_code_array_order
    FROM os_order,
         JSONB_TO_RECORDSET(os_order.user_gift_snapshot) AS spec("transactionCode" varchar)
    WHERE order_code = order_code_input;

    IF transaction_code_array_order IS NULL
        OR transaction_code_array_tmp IS NULL THEN
        msg := FORMAT('Dòng%s: Transacion [%s] phải nằm trong đơn hàng với Mã Đơn Hàng (Order Code) [%s].', line_array,
                      ARRAY_TO_STRING(transaction_code_array_tmp, ',', '*'),
                      order_code_input);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF transaction_code_array_order IS NOT NULL
        AND transaction_code_array_tmp IS NOT NULL
        AND NOT ARRAY_LENGTH(transaction_code_array_order, 1) = ARRAY_LENGTH(transaction_code_array_tmp, 1) THEN
        msg := FORMAT(
                'Dòng%s: Tổng số Transaction phải bằng số lượng trong đơn hàng với Mã Đơn Hàng (Order Code) [%s].',
                line_array, order_code_input);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF transaction_code_array_order IS NOT NULL
        AND transaction_code_array_tmp IS NOT NULL
        AND ARRAY_LENGTH(transaction_code_array_order, 1) = ARRAY_LENGTH(transaction_code_array_tmp, 1)
        AND NOT (transaction_code_array_order @> transaction_code_array_tmp AND
                 transaction_code_array_tmp @> transaction_code_array_order) THEN
        msg := FORMAT('Dòng%s: Transacion [%s] phải nằm trong đơn hàng với Mã Đơn Hàng (Order Code) [%s].', line_array,
                      ARRAY_TO_STRING(transaction_code_array_tmp, ',', '*'),
                      order_code_input);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT ARRAY_AGG(delivery_code)
    INTO delivery_code_array
    FROM os_manual_import_order_temp
    WHERE order_code = order_code_input
      AND session = session_input;

    SELECT COUNT(*)
    INTO count_number
    FROM os_order,
         JSONB_TO_RECORD(os_order.extra_data) AS extra_data(delivery_code varchar)
    WHERE extra_data.delivery_code = ANY (delivery_code_array);

    IF count_number > 0 THEN
        msg := FORMAT('Dòng%s: Mã vận đơn [] không được trùng trong hệ thống.', line_array,
                      ARRAY_TO_STRING(delivery_code_array, ',', '*'));
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;
    -- end: Check Order
    RETURN msg_return;
END;
$$;

CREATE OR REPLACE FUNCTION manual_import_os_order_temp_fn(session_input varchar)
    RETURNS TABLE
            (
                messages varchar
            )
    LANGUAGE plpgsql
AS
$$
DECLARE
    msg        varchar := '';
    msg_array  varchar[];
    is_success bool;
BEGIN
    DROP TABLE IF EXISTS message_temp;
    CREATE TEMPORARY TABLE message_temp
    (
        messages varchar
    );
    msg_array := validate_manual_import_os_order_temp_fn(session_input);
    IF ARRAY_LENGTH(msg_array, 1) > 0 THEN
        FOREACH msg IN ARRAY msg_array
            LOOP
                INSERT INTO message_temp
                VALUES (msg);
            END LOOP;
    ELSE
        INSERT INTO message_temp VALUES ('ok');
        is_success := run_manual_import_os_order_temp_fn(session_input);
    END IF;
    RETURN QUERY SELECT * FROM message_temp;
    DROP TABLE IF EXISTS message_temp;
END;
$$;

CREATE OR REPLACE FUNCTION run_manual_import_os_order_temp_fn(session_input varchar) RETURNS boolean
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num       int := 1;
    is_success      bool;
    item            record;
    maxLoop         INT := 100;
    countLoop       int := 0;
    order_code_item record;
BEGIN
    WHILE countLoop < maxLoop
        LOOP
            countLoop := countLoop + 1;
            FOR order_code_item IN
                SELECT DISTINCT order_code
                FROM os_manual_import_order_temp
                WHERE session = session_input
                  AND status = 'checked'
                LIMIT batch_num
                LOOP
                    SELECT * INTO item FROM os_order WHERE order_code = order_code_item.order_code;
                    is_success := run_item_manual_import_os_order_temp_fn(session_input, item);
                END LOOP;
        END LOOP;
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

CREATE OR REPLACE FUNCTION run_item_manual_import_os_order_temp_fn(session_input varchar, order_input record) RETURNS boolean
    LANGUAGE plpgsql
AS

$$
DECLARE
    order_code_generate     varchar;
    delivery_code_item      varchar;
    gift_item               jsonb;
    user_gift_snapshot_item jsonb;
    extra_data_item         jsonb;
    import_order_item       record;
    transaction_code_array  varchar[];
BEGIN
    FOR delivery_code_item IN
        SELECT DISTINCT delivery_code
        FROM os_manual_import_order_temp
        WHERE session = session_input
          AND status = 'checked'
          AND order_code = order_input.order_code
        LOOP
            user_gift_snapshot_item := '[]';
            SELECT ARRAY_AGG(transaction_code)
            INTO transaction_code_array
            FROM os_manual_import_order_temp
            WHERE delivery_code = delivery_code_item
              AND session = session_input;

            FOR gift_item IN
                SELECT user_gift_snapshot
                FROM JSONB_ARRAY_ELEMENTS(order_input.user_gift_snapshot) AS user_gift_snapshot
                WHERE user_gift_snapshot ->> 'transactionCode' = ANY (transaction_code_array)
                LOOP
                    user_gift_snapshot_item := jsonb_insert(user_gift_snapshot_item, '{0}', gift_item);
                END LOOP;


            SELECT *
            INTO import_order_item
            FROM os_manual_import_order_temp
            WHERE delivery_code = delivery_code_item
            LIMIT 1;

            order_code_generate := random_alpha_numeric('OD', 6);

            extra_data_item := '{}';
            IF order_input.dynamic_data != 'null' THEN
                extra_data_item := order_input.dynamic_data;
            END IF;

            extra_data_item := JSONB_SET(extra_data_item, '{delivery_code}', TO_JSONB(import_order_item.delivery_code));
            extra_data_item := JSONB_SET(extra_data_item, '{delivery_provider}', TO_JSONB(import_order_item.delivery_provider));
            extra_data_item := JSONB_SET(extra_data_item, '{delivery_created_at}', TO_JSONB(import_order_item.delivery_created_at));
            extra_data_item := JSONB_SET(extra_data_item, '{ship_cost}', TO_JSONB(import_order_item.ship_cost));

            INSERT INTO os_order (order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id,
                                  extra_data, dynamic_data)
            VALUES (order_code_generate, 'IN_PROCESS', 'MANUAL', user_gift_snapshot_item,
                    order_input.recipient_snapshot, order_input.user_id, extra_data_item, order_input.dynamic_data);

        END LOOP;

    UPDATE os_order
    SET type_code = 'SEPARATED'
    WHERE order_code = order_input.order_code;

    UPDATE os_manual_import_order_temp
    SET status = 'done'
    WHERE order_code = order_input.order_code
      AND session = session_input;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'EXCEPTION: % %', SQLERRM, SQLSTATE;
        RETURN FALSE;
END;
$$;



