CREATE TABLE gs_user_gifts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    gift_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'PRE_ORDER',
    holding_date TIMESTAMP,
    quantity INTEGER NOT NULL,
    used_quantity INTEGER,
    reservation_point INTEGER NOT NULL,
    point INTEGER DEFAULT 0,
    dynamic_data JSONB,
    CONSTRAINT fk_gift FOREIGN KEY (gift_id) REFERENCES gs_gifts(id)
);

CREATE INDEX idx_user_gifts_user_id ON gs_user_gifts(user_id);