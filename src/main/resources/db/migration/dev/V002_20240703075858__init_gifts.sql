CREATE TABLE gs_gifts (
    id SERIAL PRIMARY KEY,
    images TEXT[] NOT NULL,
    transport_type_code VA<PERSON>HAR(255) NOT NULL,
    status VARCHAR(255) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    badge_codes TEXT[],
    category_code VARCHAR(255) NOT NULL,
    tier_codes TEXT[] NOT NULL,
    type VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    point INTEGER NOT NULL,
    price NUMERIC(19, 2),
    hidden_tags TEXT[],
    sf_number BIGINT,
    start_date DATE,
    end_date DATE,
    expire_date DATE,
    expire_hour INTEGER,
    sct_number VARCHAR(255),
    quantity BIGINT,
    inventory INTEGER,
    quantity_limit_for_booking INTEGER,
    purchase_option VARCHAR(255),
    quantity_reward INTEGER DEFAULT 0,
    quantity_reservation INTEGER DEFAULT 0,
    is_allow_reservation BOOLEAN,
    priority INTEGER,
    dynamic_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);