create table os_order (
      id serial primary key,
      order_code varchar(255) not null,
      previous_order_code varchar(255),
      status_code varchar(255) not null,
      type_code varchar(255) not null,
      user_gift_snapshot jsonb,
      recipient_snapshot jsonb,
      user_id int not null,
      extra_data jsonb,
      dynamic_data jsonb,
      created_at timestamp default current_timestamp,
      updated_at timestamp default current_timestamp
);

insert into public.os_order ( order_code, previous_order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id, extra_data, dynamic_data, created_at, updated_at)
values  ( 'OD20240709ABCDEFGH', null, 'IN_PROCESS', 'NORMAL', null, '{"name": "<PERSON>", "phone": "84961636918"}', 1, '{"shipping_fee": 20000, "delivery_number": "D123", "delivery_provider": "DP1", "delivery_create_time": "2024-07-06", "delivery_provider_status": "DELIVERY_IN_PROGRESS"}', '2024-07-09 14:37:26.088382', '2024-07-09 14:37:26.088382');


CREATE TABLE os_constants
(
    id           SERIAL PRIMARY KEY,
    key_alpha    VARCHAR   NOT NULL,
    key_beta     varchar   NOT NULL,
    "value"      varchar   NOT NULL,
    priority     INT,
    dynamic_data JSONB,
    is_active    BOOLEAN            DEFAULT true,
    created_at   timestamp NOT NULL DEFAULT NOW(),
    updated_at   timestamp NOT NULL DEFAULT NOW(),
    UNIQUE (key_alpha, key_beta, value)
);
