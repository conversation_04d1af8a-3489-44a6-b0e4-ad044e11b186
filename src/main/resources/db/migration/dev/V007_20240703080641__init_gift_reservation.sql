CREATE TABLE gs_gift_reservations (
    id SERIAL PRIMARY KEY,  
    gift_id INTEGER NOT NULL,
    reservation_point INTEGER,
    reservation_percent INTEGER,
    limit_reservation_time INTEGER,
    discount_reservation_point DECIMAL(10, 2),
    discount_reservation_percent INTEGER,
    reservation_expired_days INTEGER,
    min_coins_for_reservation INTEGER,
    maximum_reservation_quantity INTEGER,
    FOREIGN KEY (gift_id) REFERENCES gs_gifts(id) 
);