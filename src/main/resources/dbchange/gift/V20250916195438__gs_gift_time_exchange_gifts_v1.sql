-- Migration: gs_gift_time_exchange_gifts and gs_gift_time_exchange_gift_details

-- 1. Table: gs_gift_time_exchange_gifts
CREATE TABLE IF NOT EXISTS gs_gift_time_exchange_gifts (
    id BIGSERIAL PRIMARY KEY,
    gift_id BIGINT NOT NULL,
    period_type VARCHAR(10) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    limit_exchange_gift INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT fk_gift FOREIGN KEY (gift_id) REFERENCES gs_gifts(id) ON DELETE RESTRICT
);

CREATE INDEX IF NOT EXISTS idx_gift_time_exchange_gifts_period_dates
ON gs_gift_time_exchange_gifts (gift_id, period_type, start_date, end_date);

-- 2. Table: gs_gift_time_exchange_gift_details
CREATE TABLE IF NOT EXISTS gs_gift_time_exchange_gift_details (
    id BIGSERIAL PRIMARY KEY,
    gift_time_exchange_gift_id BIGINT NOT NULL,
    period_type VARCHAR(10) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    limit_exchange_gift INTEGER NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT fk_gift_time_exchange_gift FOREIGN KEY (gift_time_exchange_gift_id) REFERENCES gs_gift_time_exchange_gifts(id) ON DELETE RESTRICT
);

CREATE INDEX IF NOT EXISTS idx_gift_time_exchange_gift_details_period_dates
ON gs_gift_time_exchange_gift_details (gift_time_exchange_gift_id, period_type, start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_gift_time_exchange_gift_details_gift_start_date
ON gs_gift_time_exchange_gift_details (gift_time_exchange_gift_id, start_date);

CREATE INDEX IF NOT EXISTS idx_gift_time_exchange_gift_details_gift_end_date
ON gs_gift_time_exchange_gift_details (gift_time_exchange_gift_id, end_date);

-- 3. Add new column to gs_gifts
ALTER TABLE IF EXISTS gs_gifts
ADD COLUMN IF NOT EXISTS enable_time_exchange_gift BOOLEAN NOT NULL DEFAULT FALSE;
