DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'gs_user_gifts'
              AND column_name = 'notified_at'
        ) THEN
            ALTER TABLE gs_user_gifts
                ADD COLUMN notified_at TIMESTAMP;
        END IF;
    END $$;

CREATE OR REPLACE FUNCTION extract_expiry_date(jsonb JSONB) RETURNS timestamptz AS $$
BEGIN
    IF jsonb ->> 'expiry_date' != 'null' THEN
        RETURN (jsonb ->> 'expiry_date')::timestamptz;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE INDEX idx_gs_user_gifts_expiry_date_status_notified
    ON gs_user_gifts (
                      extract_expiry_date(dynamic_data),
                      status,
                      notified_at
        );