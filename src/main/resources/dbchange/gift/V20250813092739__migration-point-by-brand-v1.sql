-- Create table
CREATE TABLE IF NOT EXISTS map_gift_to_brand_points(
    id          BIGSERIAL PRIMARY KEY,
    gift_id     INTEGER NOT NULL,
    brand_id    INTEGER NOT NULL,
    brand_point float8 NOT NULL,
    created_at  TIMESTAMPTZ NOT NULL DEFAULT now(),
    FOREIGN KEY (gift_id) REFERENCES gs_gifts(id) ON DELETE RESTRICT
);

-- Create index
CREATE UNIQUE INDEX IF NOT EXISTS uidx_map_gift_to_brand_points_gift_id ON map_gift_to_brand_points (gift_id);
