CREATE TABLE gs_map_old_gift_to_new_gift (
                                             id SERIAL PRIMARY KEY,
                                             old_gift_id INTEGER NOT NULL,
                                             new_gift_id INTEGER NOT NULL,
                                             CONSTRAINT fk_new_gift FOREIGN KEY (new_gift_id) REFERENCES gs_gifts(id)
);

CREATE INDEX idx_map_old_gift_to_new_gift_old_gift_id ON gs_map_old_gift_to_new_gift(old_gift_id);