create table if not exists gs_export_history (
    id SERIAL PRIMARY KEY,
    export_type varchar(255) not null,
    filter jsonb not null,
    status varchar(10) not null,
    result_file_path varchar(255) null,
    error_message text null,
    started_at timestamp null,
    created_at timestamp not null default now(),
    updated_at timestamp not null default now()
);

CREATE INDEX idx_export_history_status_export_type_created_at ON gs_export_history (status, export_type, created_at);