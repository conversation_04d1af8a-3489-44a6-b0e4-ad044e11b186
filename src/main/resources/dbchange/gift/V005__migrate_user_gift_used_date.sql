CREATE OR REPLACE FUNCTION migrate_used_date_user_gifts() RETURNS VOID AS
$$
DECLARE
    item              RECORD;
    used_date_str     TEXT;
    correct_timestamp BIGINT;
    v_dynamic_data    JSONB;
BEGIN
    FOR item IN SELECT *
                from gs_user_gifts
                where dynamic_data ->> 'used_date' != 'null' AND created_at >= '2024-09-17 00:00:00'
        LOOP
            correct_timestamp = 0;
            v_dynamic_data := item.dynamic_data;
            used_date_str := v_dynamic_data ->> 'used_date';
            IF used_date_str = '0.0' THEN
                v_dynamic_data := jsonb_set(v_dynamic_data, '{used_date}', 'null'::jsonb);
            ELSIF POSITION('-' IN used_date_str) = 0 THEN
                correct_timestamp := CAST(LEFT(REPLACE(used_date_str, '.', ''), 10) AS BIGINT);
                v_dynamic_data := jsonb_set(v_dynamic_data, '{used_date}',
                                            to_jsonb(TO_CHAR(TO_TIMESTAMP(correct_timestamp),
                                                             'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')));
            END IF;
            UPDATE gs_user_gifts SET dynamic_data = v_dynamic_data WHERE id = item.id;
        END LOOP;
END;
$$ LANGUAGE plpgsql;

select migrate_used_date_user_gifts();