-- Migration: add index, constraint into table gs_gift_time_exchange_gifts, gs_gift_time_exchange_gift_details

-- Index: gs_gift_time_exchange_gift_details.gift_id, start_date, end_date
CREATE INDEX IF NOT EXISTS idx_gift_time_exchange_gift_details_gift_dates
ON gs_gift_time_exchange_gift_details (gift_id, start_date, end_date);

-- Add column: gs_gift_time_exchange_gift_details.gift_id
ALTER TABLE IF EXISTS gs_gift_time_exchange_gift_details
ADD COLUMN IF NOT EXISTS gift_id BIGINT NOT NULL;

-- Foreign key: gs_gift_time_exchange_gift_details.gift_id -> gs_gifts.id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints tc
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = 'gs_gift_time_exchange_gift_details'
          AND tc.constraint_name = 'fk_gs_gift_time_exchange_gift_details_gift'
    ) THEN
        ALTER TABLE gs_gift_time_exchange_gift_details
        ADD CONSTRAINT fk_gs_gift_time_exchange_gift_details_gift
        FOREIGN KEY (gift_id)
        REFERENCES gs_gifts(id) ON DELETE RESTRICT;
    END IF;
END$$;

-- Unique constraint: gs_gift_time_exchange_gifts(id, gift_id)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints tc
        WHERE tc.constraint_type = 'UNIQUE'
          AND tc.table_name = 'gs_gift_time_exchange_gifts'
          AND tc.constraint_name = 'uq_gs_gift_time_exchange_gifts_id_gift'
    ) THEN
        ALTER TABLE gs_gift_time_exchange_gifts
        ADD CONSTRAINT uq_gs_gift_time_exchange_gifts_id_gift
        UNIQUE (id, gift_id);
    END IF;
END$$;

-- Foreign key: gs_gift_time_exchange_gift_details(gift_time_exchange_gift_id, gift_id)
--               -> gs_gift_time_exchange_gifts(id, gift_id)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints tc
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = 'gs_gift_time_exchange_gift_details'
          AND tc.constraint_name = 'fk_gs_gift_time_exchange_gift_details_gift_time_exchange'
    ) THEN
        ALTER TABLE gs_gift_time_exchange_gift_details
        ADD CONSTRAINT fk_gs_gift_time_exchange_gift_details_gift_time_exchange
        FOREIGN KEY (gift_time_exchange_gift_id, gift_id)
        REFERENCES gs_gift_time_exchange_gifts(id, gift_id)
        ON DELETE RESTRICT;
    END IF;
END$$;
