DROP FUNCTION IF EXISTS manual_import_os_order_temp_fn;
DROP FUNCTION IF EXISTS validate_manual_import_os_order_temp_fn;
DROP FUNCTION IF EXISTS validate_item_manual_import_os_order_temp_fn;
DROP FUNCTION IF EXISTS run_manual_import_os_order_temp_fn;
DROP FUNCTION IF EXISTS run_item_manual_import_os_order_temp_fn;

DO
$$
    BEGIN
        -- Create index on os_order for transaction_delivery_code
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_order'
                         AND indexname = 'idx_os_order_transaction_delivery_code') THEN
            CREATE INDEX idx_os_order_transaction_delivery_code ON os_order USING gin ((extra_data -> 'transaction_delivery_code') jsonb_path_ops);
        END IF;

        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_order'
                         AND indexname = 'idx_os_order_delivery_code') THEN
            CREATE INDEX idx_os_order_delivery_code ON os_order USING gin ((extra_data -> 'delivery_code') jsonb_path_ops);
        END IF;

        -- Create index on os_manual_import_order_temp for session
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_manual_import_order_temp'
                         AND indexname = 'idx_os_manual_import_order_temp_session') THEN
            CREATE INDEX idx_os_manual_import_order_temp_session ON os_manual_import_order_temp (session);
        END IF;

        -- Create index on os_manual_import_order_temp for status
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_manual_import_order_temp'
                         AND indexname = 'idx_os_manual_import_order_temp_status') THEN
            CREATE INDEX idx_os_manual_import_order_temp_status ON os_manual_import_order_temp (status);
        END IF;

        -- Create index on os_manual_import_order_temp for delivery_code
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_manual_import_order_temp'
                         AND indexname = 'idx_os_manual_import_order_temp_delivery_code') THEN
            CREATE INDEX idx_os_manual_import_order_temp_delivery_code ON os_manual_import_order_temp (delivery_code);
        END IF;

        -- Create index on os_manual_import_order_temp for td_id
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_manual_import_order_temp'
                         AND indexname = 'idx_os_manual_import_order_temp_td_id') THEN
            CREATE INDEX idx_os_manual_import_order_temp_td_id ON os_manual_import_order_temp (td_id);
        END IF;

        -- Create index on os_order for extra_data
        IF NOT EXISTS (SELECT 1
                       FROM pg_indexes
                       WHERE schemaname = 'public'
                         AND tablename = 'os_order'
                         AND indexname = 'idx_os_order_extra_data') THEN
            CREATE INDEX idx_os_order_extra_data ON os_order USING gin (extra_data);
        END IF;
    END
$$;

CREATE OR REPLACE FUNCTION manual_import_os_order_temp_fn(session_input varchar)
    RETURNS TABLE
            (
                messages varchar
            )
    LANGUAGE plpgsql
AS
$$
DECLARE
    msg_array  varchar[];
    is_success bool;
BEGIN
    msg_array := validate_manual_import_os_order_temp_fn(session_input);
    IF ARRAY_LENGTH(msg_array, 1) IS NULL THEN
        is_success := run_manual_import_os_order_temp_fn(session_input);
    ELSE
        UPDATE os_manual_import_order_temp set status = 'failure' where session = session_input;
    END IF;
    RETURN QUERY SELECT * FROM UNNEST(msg_array);
END;
$$;

CREATE OR REPLACE FUNCTION validate_manual_import_os_order_temp_fn(session_input varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num      int    := 2000;
    msg            varchar;
    msg_array      varchar[];
    msg_array_item varchar[];
    item           record;
    maxItems       bigint;
    processedItems bigint := 0;
BEGIN
    SELECT COUNT(*)
    INTO maxItems
    FROM os_manual_import_order_temp
    WHERE session = session_input
      AND status = 'imported';
    WHILE processedItems < maxItems
        LOOP
            processedItems := processedItems + batch_num;
            FOR item IN
                SELECT *
                FROM os_manual_import_order_temp
                WHERE session = session_input
                  AND status = 'imported'
                ORDER BY line
                LIMIT batch_num
                LOOP
                    msg_array_item := validate_item_manual_import_os_order_temp_fn(session_input, item);
                    msg_array := ARRAY_CAT(msg_array, msg_array_item);

                    msg := 'checked';
                    IF ARRAY_LENGTH(msg_array_item, 1) > 0 THEN
                        msg := 'failure';
                    END IF;

                    UPDATE os_manual_import_order_temp
                    SET status = msg
                    WHERE session = session_input
                      AND line = item.line;
                END LOOP;
        END LOOP;
    RETURN msg_array;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'EXCEPTION: % %', SQLERRM, SQLSTATE;
        RETURN FALSE;
END;
$$;

CREATE OR REPLACE FUNCTION validate_item_manual_import_os_order_temp_fn(session_input varchar, item RECORD)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    item_order                    record;
    line_array                    varchar   := '';
    msg                           varchar   := '';
    count_number                  int       := 0;
    msg_return                    varchar[] := ARRAY []:: varchar[];
    delivery_code_array           varchar[] := ARRAY []:: varchar[];
    transaction_code_array_tmp    varchar[] := ARRAY []:: varchar[];
    transaction_code_array_order  varchar[] := ARRAY []:: varchar[];
    duplicate_delivery_code_input BOOLEAN;
BEGIN
    line_array := CONCAT(line_array, ' #', item.line);
    transaction_code_array_tmp := ARRAY_APPEND(transaction_code_array_tmp, item.transaction_code);

    IF COALESCE(TRIM(item.td_id), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống TD ID.', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF COALESCE(TRIM(item.transaction_code), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống Mã Giao Dịch (Transaction).', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF COALESCE(TRIM(item.delivery_code), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống Mã Vận Đơn (MVĐ).', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF COALESCE(TRIM(item.delivery_provider), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống Đơn Vị Vận Chuyển (ĐVVC).', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF COALESCE(TRIM(item.delivery_provider), '') != 'VTP'
        AND COALESCE(TRIM(item.delivery_provider), '') != 'GHTK' THEN
        msg := FORMAT('Dòng #%s: Sai Đơn Vị Vận Chuyển (ĐVVC) VPT hoặc GHTK.', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF COALESCE(TRIM(item.delivery_created_at), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống Ngày Tạo Mã Vận Đơn (Ngày Tạo MVD).', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    ELSE
        IF NOT is_valid_timestamp_string(item.delivery_created_at, 'DD/MM/YYYY') THEN
            msg := FORMAT('Dòng #%s: Ngày Tạo Mã Vận Đơn (Ngày Tạo MVD) sai format (DD/MM/YYYY).', item.line);
            msg_return := ARRAY_APPEND(msg_return, msg);
        END IF;
    END IF;

    IF COALESCE(TRIM(item.ship_cost), '') = '' THEN
        msg := FORMAT('Dòng #%s: Không để trống Phí Ship.', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF NOT COALESCE(TRIM(item.ship_cost), '') ~ '^[0-9]+$' THEN
        msg := FORMAT('Dòng #%s: phí ship sai định dạng number.', item.line);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    -- start: Check Order
    IF ARRAY_LENGTH(msg_return, 1) > 0 THEN
        RETURN msg_return;
    END IF;

    SELECT *
    INTO item_order
    FROM os_order
    WHERE extra_data ->> 'transaction_delivery_code' = item.td_id
    LIMIT 1;

    IF item_order.id IS NULL THEN
        msg := FORMAT('Dòng%s: Không tìm thấy TD ID [%s] trong hệ thống.', line_array,
                      item.td_id);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF item_order.status_code != 'IN_PROCESS' THEN
        msg := FORMAT('Dòng%s: Mã Đơn Hàng (Order Code) [%s] có trạng thái KHÁC "Chờ xử lý".', line_array,
                      item_order.order_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF item_order.type_code = 'SEPARATED' THEN
        msg := FORMAT('Dòng%s: Mã Đơn Hàng (Order Code) [%s] Đã thực hiện tách đơn.', line_array,
                      item_order.order_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF item_order.extra_data -> 'delivery_code' IS NOT NULL THEN
        msg := FORMAT('Dòng%s: Mã Đơn Hàng (Order Code) [%s] đã có MVĐ [%s].', line_array,
                      item_order.order_code, item_order.extra_data -> 'delivery_code');
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT ARRAY_AGG(spec."transactionCode")
    INTO transaction_code_array_order
    FROM os_order
    JOIN LATERAL JSONB_TO_RECORDSET(os_order.user_gift_snapshot) AS spec("transactionCode" varchar) ON TRUE
    WHERE os_order.extra_data ->> 'transaction_delivery_code' = item.td_id;

    SELECT ARRAY_AGG(transaction_code)
    INTO transaction_code_array_tmp
    FROM os_manual_import_order_temp
    WHERE td_id = item.td_id
      AND session = session_input;

    IF transaction_code_array_order IS NULL
        OR transaction_code_array_tmp IS NULL THEN
        msg := FORMAT('Dòng%s: Transacion [%s] phải nằm trong đơn hàng với Mã TD ID [%s].', line_array,
                      ARRAY_TO_STRING(transaction_code_array_tmp, ',', '*'),
                      item.td_id);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF transaction_code_array_order IS NOT NULL
        AND transaction_code_array_tmp IS NOT NULL
        AND NOT ARRAY_LENGTH(transaction_code_array_order, 1) = ARRAY_LENGTH(transaction_code_array_tmp, 1) THEN
        msg := FORMAT(
                'Dòng%s: Tổng số Transaction phải bằng số lượng trong đơn hàng với Mã TD ID [%s].',
                line_array, item.td_id);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    IF transaction_code_array_order IS NOT NULL
        AND transaction_code_array_tmp IS NOT NULL
        AND ARRAY_LENGTH(transaction_code_array_order, 1) = ARRAY_LENGTH(transaction_code_array_tmp, 1)
        AND NOT (transaction_code_array_order @> transaction_code_array_tmp AND
                 transaction_code_array_tmp @> transaction_code_array_order) THEN
        msg := FORMAT('Dòng%s: Transacion [%s] phải nằm trong đơn hàng với Mã TD ID [%s].', line_array,
                      ARRAY_TO_STRING(transaction_code_array_tmp, ',', '*'),
                      item.td_id);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT EXISTS (SELECT 1
                   FROM os_manual_import_order_temp
                   WHERE session = session_input
                     AND delivery_code = item.delivery_code
                   GROUP BY delivery_code
                   HAVING COUNT(*) > 1)
    INTO duplicate_delivery_code_input;

    IF duplicate_delivery_code_input THEN
        SELECT EXISTS (SELECT 1
                       FROM os_manual_import_order_temp
                       WHERE session = session_input
                         AND delivery_code = item.delivery_code
                         AND td_id = item.td_id
                       GROUP BY td_id
                       HAVING COUNT(*) > 1)
        INTO duplicate_delivery_code_input;

        IF NOT duplicate_delivery_code_input THEN
            msg := FORMAT('Dòng%s: Mã Vận Đơn (MVĐ) [%s] bị trùng.', line_array, item.delivery_code);
            msg_return := ARRAY_APPEND(msg_return, msg);
        END IF;
    END IF;

    SELECT COUNT(*)
    INTO count_number
    FROM os_order
    WHERE extra_data ->> 'delivery_code' = item.delivery_code;

    IF count_number > 0 THEN
        msg := FORMAT('Dòng%s: Mã vận đơn [%s] không được trùng trong hệ thống.', line_array,
                      ARRAY_TO_STRING(delivery_code_array, ',', '*'));
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT COUNT(*)
    INTO count_number
    FROM os_manual_import_order_temp
    WHERE td_id = item.td_id
      AND transaction_code = item.transaction_code
      AND session != session_input
      AND status IN ('checked', 'imported');

    IF count_number > 1 THEN
        msg := FORMAT('Dòng%s: Transacion [%s] đang được xử lý.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    -- end: Check Order
    RETURN msg_return;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'EXCEPTION: % %', SQLERRM, SQLSTATE;
        RETURN FALSE;
END ;
$$;

CREATE OR REPLACE FUNCTION run_manual_import_os_order_temp_fn(session_input varchar) RETURNS boolean
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num      int    := 2000;
    is_success     bool;
    order_item     record;
    maxItems       bigint;
    processedItems bigint := 0;
    import_item    record;
BEGIN
    SELECT COUNT(*)
    INTO maxItems
    FROM os_manual_import_order_temp
    WHERE session = session_input
      AND status = 'checked';
    WHILE processedItems < maxItems
        LOOP
            processedItems := processedItems + batch_num;
            FOR import_item IN
                SELECT *
                FROM os_manual_import_order_temp
                WHERE session = session_input
                  AND status = 'checked'
                ORDER BY line
                LIMIT batch_num
                LOOP
                    SELECT *
                    INTO order_item
                    FROM os_order
                    WHERE extra_data ->> 'transaction_delivery_code' = import_item.td_id;
                    is_success := run_item_manual_import_os_order_temp_fn(session_input, order_item, import_item);
                END LOOP;
        END LOOP;
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

CREATE OR REPLACE FUNCTION run_item_manual_import_os_order_temp_fn(session_input varchar, order_item record, import_item record) RETURNS boolean
    LANGUAGE plpgsql
AS

$$
DECLARE
    order_code_generate     varchar;
    gift_item               jsonb;
    user_gift_snapshot_item jsonb;
    extra_data_item         jsonb;
    count_item              int;
BEGIN
    SELECT jsonb_array_length(order_item.user_gift_snapshot)
    INTO count_item;

    IF count_item = 1 THEN
        extra_data_item := '{}';
        IF order_item.extra_data != 'null' THEN
            extra_data_item := order_item.extra_data;
        END IF;

        extra_data_item := JSONB_SET(extra_data_item, '{delivery_code}', TO_JSONB(import_item.delivery_code));
        extra_data_item := JSONB_SET(extra_data_item, '{delivery_provider}', TO_JSONB(import_item.delivery_provider));
        extra_data_item :=
                JSONB_SET(extra_data_item, '{delivery_created_time}', TO_JSONB(import_item.delivery_created_at));
        extra_data_item := JSONB_SET(extra_data_item, '{shipping_fee}', TO_JSONB(import_item.ship_cost::DECIMAL));

        UPDATE os_order
        SET extra_data = extra_data_item
        WHERE order_code = order_item.order_code;

        UPDATE os_manual_import_order_temp
        SET status = 'done'
        WHERE line = import_item.line
          AND session = session_input;
    ELSE
        user_gift_snapshot_item := '[]';
        FOR gift_item IN
            SELECT user_gift_snapshot
            FROM JSONB_ARRAY_ELEMENTS(order_item.user_gift_snapshot) AS user_gift_snapshot
            WHERE user_gift_snapshot ->> 'transactionCode' = import_item.transaction_code
            LOOP
                user_gift_snapshot_item := jsonb_insert(user_gift_snapshot_item, '{0}', gift_item);
            END LOOP;
        order_code_generate := random_alpha_numeric('OD', 6);

        extra_data_item := '{}';
        IF order_item.extra_data != 'null' THEN
            extra_data_item := order_item.extra_data;
        END IF;

        extra_data_item := JSONB_SET(extra_data_item, '{delivery_code}', TO_JSONB(import_item.delivery_code));
        extra_data_item :=
                JSONB_SET(extra_data_item, '{delivery_provider}', TO_JSONB(import_item.delivery_provider));
        extra_data_item := JSONB_SET(extra_data_item, '{delivery_created_time}',
                                     TO_JSONB(import_item.delivery_created_at));
        extra_data_item := JSONB_SET(extra_data_item, '{shipping_fee}', TO_JSONB(import_item.ship_cost));

        INSERT INTO os_order (order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id,
                              extra_data, created_at, updated_at)
        VALUES (order_code_generate, 'IN_PROCESS', 'MANUAL', user_gift_snapshot_item,
                order_item.recipient_snapshot, order_item.user_id, extra_data_item,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC');

        UPDATE os_order
        SET type_code = 'SEPARATED'
        WHERE order_code = order_item.order_code
          AND type_code = 'NORMAL';
    END IF;

    UPDATE os_manual_import_order_temp
    SET status = 'done'
    WHERE line = import_item.line
      AND session = session_input;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'EXCEPTION: % %', SQLERRM, SQLSTATE;
        RETURN FALSE;
END;
$$;