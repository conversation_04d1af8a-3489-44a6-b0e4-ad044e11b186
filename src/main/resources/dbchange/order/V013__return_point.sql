ALTER TABLE os_import_return_point_temp
    DROP COLUMN order_id;
ALTER TABLE os_import_return_point_temp
    ADD COLUMN IF NOT EXISTS order_code VARCHAR;

DROP FUNCTION get_import_return_point_fn;
DROP FUNCTION validate_import_return_point_fn;

CREATE OR REPLACE FUNCTION get_import_return_point_fn(session_input varchar)
    RETURNS TABLE
            (
                line         bigint,
                session      varchar,
                user_id      bigint,
                point        numeric(10, 2),
                user_gift_id bigint,
                order_code     varchar,
                reason       varchar
            )
    LANGUAGE plpgsql
AS
$$
BEGIN
    RETURN QUERY SELECT os.line, os.session, os.user_id, os.point, os.user_gift_id, os.order_code, os.reason
                 FROM os_import_return_point_temp os
                 WHERE os.session = session_input
                   AND os.status = 'checked'
                 order by os.line;
END;
$$;

CREATE OR REPLACE FUNCTION validate_import_return_point_fn(session_input varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num      int    := 500;
    msg            varchar;
    msg_array      varchar[];
    msg_array_item varchar[];
    item           record;
    maxItems       bigint;
    processedItems bigint := 0;
    update_record  record;
    db_details     RECORD;
    conn_str       text ;
    gs_query       VARCHAR;
BEGIN
    SELECT * INTO db_details FROM get_gift_db_details();
    conn_str :=
            'dbname=' || db_details.db_name || ' host=' || db_details.db_address ||
            ' user=' || db_details.db_user || ' password=' || db_details.db_password;

    SELECT COUNT(*)
    INTO maxItems
    FROM os_import_return_point_temp
    WHERE session = session_input
      AND status = 'imported';
    WHILE processedItems < maxItems
        LOOP
            processedItems := processedItems + batch_num;
            FOR item IN
                SELECT DISTINCT transaction_code
                FROM os_import_return_point_temp
                WHERE session = session_input
                  AND status = 'imported'
                LIMIT batch_num
                LOOP
                    msg_array_item := validate_item_import_return_point_fn(session_input, item.transaction_code);
                    msg_array := ARRAY_CAT(msg_array, msg_array_item);

                    msg := 'checked';
                    IF ARRAY_LENGTH(msg_array_item, 1) > 0 THEN
                        msg := 'failure';
                    END IF;

                    SELECT os_order.user_id::bigint,
                           os_order.order_code::varchar          as order_code,
                           (gift ->> 'id')::bigint      as userGiftId,
                           (gift ->> 'transactionCode') as transactionCode,
                           (gift ->> 'point')::bigint   as point
                    INTO update_record
                    FROM os_order,
                         jsonb_array_elements(os_order.user_gift_snapshot) AS gift
                    WHERE (gift ->> 'transactionCode') = item.transaction_code;

                    IF update_record IS NULL OR update_record.userGiftId IS NULL THEN
                        gs_query :=
                                'SELECT id::bigint as userGiftId, user_id::bigint, point as count, NULL::varchar as order_code FROM gs_user_gifts WHERE transaction_code = ''' ||
                                item.transaction_code || '''';

                        EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
                                quote_literal(gs_query) || ') AS t(
                                    userGiftId             bigint, user_id bigint, point numeric(10,2), order_code varchar
                                )' INTO update_record;
                    END IF;

                    IF update_record.order_code IS NULL THEN
                        UPDATE os_import_return_point_temp
                        SET status       = msg,
                            user_id      = update_record.user_id,
                            point        = update_record.point,
                            user_gift_id = update_record.userGiftId
                        WHERE session = session_input
                          AND transaction_code = item.transaction_code;
                    ELSE
                        UPDATE os_import_return_point_temp
                        SET status       = msg,
                            user_id      = update_record.user_id,
                            point        = update_record.point,
                            user_gift_id = update_record.userGiftId,
                            order_code     = update_record.order_code
                        WHERE session = session_input
                          AND transaction_code = item.transaction_code;
                    END IF;

                END LOOP;
        END LOOP;
    RETURN msg_array;
END;
$$;