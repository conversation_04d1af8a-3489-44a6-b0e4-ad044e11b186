ALTER TABLE os_history_return_point
    ADD COLUMN IF NOT EXISTS order_id BIGINT;
ALTER TABLE os_import_return_point_temp
    ADD COLUMN IF NOT EXISTS order_id BIGINT;

DROP FUNCTION do_import_history_return_point_fn;
DROP FUNCTION get_import_return_point_fn;
DROP FUNCTION validate_import_return_point_fn;
DROP FUNCTION validate_item_import_return_point_fn;

CREATE OR REPLACE FUNCTION do_import_history_return_point_fn(session_input varchar)
    RETURNS VOID
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num        int    := 5000;
    item             record;
    transaction_item record;
    v_user_gift      jsonb;
    maxItems         bigint;
    processedItems   bigint := 0;
    gs_query         VARCHAR;
    gs_result        RECORD;
    user_result        RECORD;
    db_details       RECORD;
    conn_str         text ;
BEGIN
    SELECT COUNT(*)
    INTO maxItems
    FROM os_import_return_point_temp
    WHERE session = session_input
      AND status = 'checked';
    WHILE processedItems < maxItems
        LOOP
            processedItems := processedItems + batch_num;
            FOR item IN
                SELECT *
                FROM os_import_return_point_temp
                WHERE session = session_input
                  AND status = 'checked'
                ORDER BY line
                LIMIT batch_num
                LOOP
                    SELECT jsonb_element               as gift_data,
                           os_order.extra_data         as delivery_data,
                           os_order.user_gift_snapshot as user_gift_snapshot,
                           os_order.id                 as order_id,
                           os_order.recipient_snapshot as recipient_snapshot,
                           os_order.order_code         as order_code
                    INTO transaction_item
                    FROM os_order,
                         jsonb_array_elements(os_order.user_gift_snapshot) AS jsonb_element
                    WHERE (jsonb_element ->> 'transactionCode') = item.transaction_code
                    ORDER BY id
                    LIMIT 1;

                    IF transaction_item IS NOT NULL AND transaction_item.order_code IS NOT NULL THEN
                        INSERT INTO os_history_return_point (session, line, order_code, order_id, transaction_code,
                                                             delivery_code,
                                                             gift_snapshot, user_snapshot,
                                                             user_id, point, total_gift, reason, created_at)
                        VALUES (session_input, item.line, transaction_item.order_code, transaction_item.order_id,
                                item.transaction_code,
                                transaction_item.delivery_data ->> 'delivery_code', transaction_item.gift_data,
                                transaction_item.recipient_snapshot,
                                item.user_id, item.point, (transaction_item.gift_data ->> 'quantity')::INTEGER,
                                item.reason, CURRENT_TIMESTAMP AT TIME ZONE 'UTC');

                        v_user_gift := transaction_item.user_gift_snapshot;

                        FOR idx IN 0..JSONB_ARRAY_LENGTH(v_user_gift) - 1
                            LOOP
                                IF (v_user_gift -> idx ->> 'transactionCode') = item.transaction_code THEN
                                    v_user_gift := jsonb_set(v_user_gift, ARRAY [idx::TEXT, 'returned_point'],
                                                             to_jsonb(item.point));
                                    v_user_gift :=
                                            jsonb_set(v_user_gift, ARRAY [idx::TEXT, 'status'],
                                                      to_jsonb('FAILED'::TEXT));
                                END IF;
                            END LOOP;

                        UPDATE os_order
                        set user_gift_snapshot = v_user_gift
                        WHERE id = transaction_item.order_id;

                    ELSE
                        SELECT * INTO db_details FROM get_gift_db_details();
                        conn_str :=
                                'dbname=' || db_details.db_name || ' host=' || db_details.db_address ||
                                ' user=' || db_details.db_user || ' password=' || db_details.db_password;

                        gs_query :=
                                'SELECT user_id, transaction_code, point, quantity, recipient_snapshot, jsonb_build_object(
                                    ''id'', g.id,
                                    ''userId'', g.user_id,
                                    ''status'', g.status,
                                    ''quantity'', g.quantity,
                                    ''reservationPoint'', g.reservation_point,
                                    ''point'', g.point,
                                    ''dynamicData'', g.dynamic_data,
                                    ''createdAt'', g.created_at,
                                    ''updatedAt'', g.updated_at,
                                    ''gift'', g.gift_snapshot,
                                    ''recipientSnapshot'', g.recipient_snapshot,
                                    ''transaction_code'', g.transaction_code
                                ) AS gift_snapshot
                                FROM gs_user_gifts g
                                WHERE transaction_code = ''' || item.transaction_code || '''';

                        EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
                                quote_literal(gs_query) || ') AS t(
            user_id             integer,
            transaction_code    varchar,
            point               numeric(10,2),
            quantity            integer,
            recipient_snapshot  jsonb,
            gift_snapshot       jsonb
        )' INTO gs_result;

                        SELECT * INTO db_details FROM get_zoo_db_details();
                        conn_str :=
                                'dbname=' || db_details.db_name || ' host=' || db_details.db_address || ' port=' || db_details.db_port ||
                                ' user=' || db_details.db_user || ' password=' || db_details.db_password;

                        gs_query :=
                                'select jsonb_build_object(''recipientName'', first_name || '' '' || last_name, ''recipientPhone'', phone_number) as user_snapshot from users where id =' || item.user_id;

                        EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
                                quote_literal(gs_query) || ') AS t(
            user_snapshot  jsonb
        )' INTO user_result;

                        INSERT INTO os_history_return_point (session, line, order_code, transaction_code, delivery_code,
                                                             gift_snapshot, user_snapshot,
                                                             user_id, point, total_gift, reason, created_at)
                        VALUES (session_input, item.line, null, item.transaction_code,
                                null, gs_result.gift_snapshot,
                                user_result.user_snapshot,
                                item.user_id, item.point, gs_result.quantity,
                                item.reason, CURRENT_TIMESTAMP AT TIME ZONE 'UTC');

                    END IF;

                    UPDATE os_import_return_point_temp
                    SET status = 'done'
                    WHERE session = session_input
                      AND transaction_code = item.transaction_code;
                END LOOP;
        END LOOP;
END ;
$$;


CREATE OR REPLACE FUNCTION get_import_return_point_fn(session_input varchar)
    RETURNS TABLE
            (
                line         bigint,
                session      varchar,
                user_id      bigint,
                point        numeric(10, 2),
                user_gift_id bigint,
                order_id     bigint,
                reason       varchar
            )
    LANGUAGE plpgsql
AS
$$
BEGIN
    RETURN QUERY SELECT os.line, os.session, os.user_id, os.point, os.user_gift_id, os.order_id, os.reason
                 FROM os_import_return_point_temp os
                 WHERE os.session = session_input
                   AND os.status = 'checked'
                 order by os.line;
END;
$$;

CREATE OR REPLACE FUNCTION validate_import_return_point_fn(session_input varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    batch_num      int    := 500;
    msg            varchar;
    msg_array      varchar[];
    msg_array_item varchar[];
    item           record;
    maxItems       bigint;
    processedItems bigint := 0;
    update_record  record;
    db_details     RECORD;
    conn_str       text ;
    gs_query       VARCHAR;
BEGIN
    SELECT * INTO db_details FROM get_gift_db_details();
    conn_str :=
            'dbname=' || db_details.db_name || ' host=' || db_details.db_address ||
            ' user=' || db_details.db_user || ' password=' || db_details.db_password;

    SELECT COUNT(*)
    INTO maxItems
    FROM os_import_return_point_temp
    WHERE session = session_input
      AND status = 'imported';
    WHILE processedItems < maxItems
        LOOP
            processedItems := processedItems + batch_num;
            FOR item IN
                SELECT DISTINCT transaction_code
                FROM os_import_return_point_temp
                WHERE session = session_input
                  AND status = 'imported'
                LIMIT batch_num
                LOOP
                    msg_array_item := validate_item_import_return_point_fn(session_input, item.transaction_code);
                    msg_array := ARRAY_CAT(msg_array, msg_array_item);

                    msg := 'checked';
                    IF ARRAY_LENGTH(msg_array_item, 1) > 0 THEN
                        msg := 'failure';
                    END IF;

                    SELECT os_order.user_id::bigint,
                           os_order.id::bigint          as order_id,
                           (gift ->> 'id')::bigint      as userGiftId,
                           (gift ->> 'transactionCode') as transactionCode,
                           (gift ->> 'point')::bigint   as point
                    INTO update_record
                    FROM os_order,
                         jsonb_array_elements(os_order.user_gift_snapshot) AS gift
                    WHERE (gift ->> 'transactionCode') = item.transaction_code;

                    IF update_record IS NULL OR update_record.userGiftId IS NULL THEN
                        gs_query :=
                                'SELECT id::bigint as userGiftId, user_id::bigint, point as count, NULL::bigint as order_id FROM gs_user_gifts WHERE transaction_code = ''' ||
                                item.transaction_code || '''';

                        EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
                                quote_literal(gs_query) || ') AS t(
                                    userGiftId             bigint, user_id bigint, point numeric(10,2), order_id bigint
                                )' INTO update_record;
                    END IF;

                    IF update_record.order_id IS NULL THEN
                        UPDATE os_import_return_point_temp
                        SET status       = msg,
                            user_id      = update_record.user_id,
                            point        = update_record.point,
                            user_gift_id = update_record.userGiftId
                        WHERE session = session_input
                          AND transaction_code = item.transaction_code;
                    ELSE
                        UPDATE os_import_return_point_temp
                        SET status       = msg,
                            user_id      = update_record.user_id,
                            point        = update_record.point,
                            user_gift_id = update_record.userGiftId,
                            order_id     = update_record.order_id
                        WHERE session = session_input
                          AND transaction_code = item.transaction_code;
                    END IF;

                END LOOP;
        END LOOP;
    RETURN msg_array;
END;
$$;


CREATE OR REPLACE FUNCTION validate_item_import_return_point_fn(session_input varchar, tx_code varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    item             record;
    transaction_item JSONB;
    line_array       varchar   := '';
    msg              varchar   := '';
    msg_return       varchar[] := ARRAY []:: varchar[];
    gs_query         VARCHAR;
    gs_result        RECORD;
    db_details       RECORD;
    conn_str         text ;
    count            integer;
BEGIN
    SELECT * INTO db_details FROM get_gift_db_details();
    conn_str :=
            'dbname=' || db_details.db_name || ' host=' || db_details.db_address ||
            ' user=' || db_details.db_user || ' password=' || db_details.db_password;

    FOR item IN
        SELECT *
        FROM os_import_return_point_temp osi
        WHERE osi.session = session_input
          AND osi.transaction_code = tx_code
          AND osi.status = 'imported'
        LOOP
            -- start: Check null or empty
            line_array := CONCAT(line_array, ' #', item.line);

            IF COALESCE(TRIM(item.transaction_code), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Mã giao dịch (Transaction ID)', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.reason), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống lý do', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF NOT COALESCE(TRIM(item.reason), '') ~ '^[0-9]+$' THEN
                msg := FORMAT('Dòng #%s: lý do sai định dạng number.', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            -- end: Check null or empty
        END LOOP;

    -- start: Check Order
    IF ARRAY_LENGTH(msg_return, 1) > 0 THEN
        RETURN msg_return;
    END IF;

    SELECT COUNT(*) INTO count
    FROM os_return_point_reason_mapping
    WHERE code = CAST(item.reason AS INTEGER);

    IF count = 0 THEN
        msg := FORMAT('Dòng%s: Lý do [%s] không tồn tại trong hệ thống.', line_array, item.reason);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT jsonb_element
    INTO transaction_item
    FROM os_order,
         jsonb_array_elements(os_order.user_gift_snapshot) AS jsonb_element
    WHERE (jsonb_element ->> 'transactionCode') = item.transaction_code;

    IF transaction_item IS NOT NULL AND (transaction_item ->> 'returned_point') IS NOT NULL THEN
        msg := FORMAT('Dòng%s: Transacion [%s] đã được trả điểm.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    gs_query :=
            'SELECT COUNT(*) as count FROM gs_user_gifts WHERE transaction_code = ''' || item.transaction_code || '''';

    EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
            quote_literal(gs_query) || ') AS t(
                    count             integer
                )' INTO gs_result;

    IF gs_result.count = 0 THEN
        msg := FORMAT('Dòng%s: Transacion [%s] không tồn tại trong hệ thống.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT COUNT(*) INTO count
    FROM os_import_return_point_temp osi
    WHERE osi.transaction_code = tx_code
      AND osi.status = 'done';

    IF count > 0 THEN
        msg := FORMAT('Dòng%s: Transacion [%s] đã được xử lý.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;
    -- end: Check Order
    RETURN msg_return;
END ;
$$;