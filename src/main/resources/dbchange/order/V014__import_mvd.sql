DELETE FROM os_return_point_reason_mapping WHERE code = 7;
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (7,'<PERSON>h<PERSON><PERSON> hàng không lên TD','<PERSON><PERSON><PERSON><PERSON> hàng không xác nhận địa chỉ giao hàng','<PERSON><PERSON>n xu vì khách hàng không xác nhận địa chỉ giao hàng và quà bị hủy trên hệ thống do quá hạn');

DROP FUNCTION run_item_manual_import_os_order_temp_fn;
CREATE OR REPLACE FUNCTION run_item_manual_import_os_order_temp_fn(session_input varchar, order_item record, import_item record) RETURNS boolean
    LANGUAGE plpgsql
AS

$$
DECLARE
    order_code_generate     varchar;
    gift_item               jsonb;
    user_gift_snapshot_item jsonb;
    extra_data_item         jsonb;
    count_item              int;
    count_delivery_item              int;
BEGIN
    SELECT jsonb_array_length(order_item.user_gift_snapshot)
    INTO count_item;

    SELECT COUNT(DISTINCT delivery_code) INTO count_delivery_item
    FROM os_manual_import_order_temp
    WHERE td_id = import_item.td_id
      AND delivery_code = import_item.delivery_code;

    IF count_item = 1 OR count_delivery_item = 1 THEN
        extra_data_item := '{}';
        IF order_item.extra_data != 'null' THEN
            extra_data_item := order_item.extra_data;
        END IF;

        extra_data_item := JSONB_SET(extra_data_item, '{delivery_code}', TO_JSONB(import_item.delivery_code));
        extra_data_item := JSONB_SET(extra_data_item, '{delivery_provider}', TO_JSONB(import_item.delivery_provider));
        extra_data_item :=
                JSONB_SET(extra_data_item, '{delivery_created_time}', TO_JSONB(import_item.delivery_created_at));
        extra_data_item := JSONB_SET(extra_data_item, '{shipping_fee}', TO_JSONB(import_item.ship_cost::DECIMAL));

        UPDATE os_order
        SET extra_data = extra_data_item
        WHERE order_code = order_item.order_code;

        UPDATE os_manual_import_order_temp
        SET status = 'done'
        WHERE line = import_item.line
          AND session = session_input;
    ELSE
        user_gift_snapshot_item := '[]';
        FOR gift_item IN
            SELECT user_gift_snapshot
            FROM JSONB_ARRAY_ELEMENTS(order_item.user_gift_snapshot) AS user_gift_snapshot
            WHERE user_gift_snapshot ->> 'transactionCode' = import_item.transaction_code
            LOOP
                user_gift_snapshot_item := jsonb_insert(user_gift_snapshot_item, '{0}', gift_item);
            END LOOP;
        order_code_generate := random_alpha_numeric('OD', 6);

        extra_data_item := '{}';
        IF order_item.extra_data != 'null' THEN
            extra_data_item := order_item.extra_data;
        END IF;

        extra_data_item := JSONB_SET(extra_data_item, '{delivery_code}', TO_JSONB(import_item.delivery_code));
        extra_data_item :=
                JSONB_SET(extra_data_item, '{delivery_provider}', TO_JSONB(import_item.delivery_provider));
        extra_data_item := JSONB_SET(extra_data_item, '{delivery_created_time}',
                                     TO_JSONB(import_item.delivery_created_at));
        extra_data_item := JSONB_SET(extra_data_item, '{shipping_fee}', TO_JSONB(import_item.ship_cost));

        INSERT INTO os_order (order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id,
                              extra_data, created_at, updated_at)
        VALUES (order_code_generate, 'IN_PROCESS', 'MANUAL', user_gift_snapshot_item,
                order_item.recipient_snapshot, order_item.user_id, extra_data_item,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC',
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC');

        UPDATE os_order
        SET type_code = 'SEPARATED'
        WHERE order_code = order_item.order_code
          AND type_code = 'NORMAL';
    END IF;

    UPDATE os_manual_import_order_temp
    SET status = 'done'
    WHERE line = import_item.line
      AND session = session_input;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'EXCEPTION: % %', SQLERRM, SQLSTATE;
        RETURN FALSE;
END;
$$;