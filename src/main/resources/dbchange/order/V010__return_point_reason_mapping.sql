DROP TABLE IF EXISTS os_return_point_reason_mapping CASCADE;

CREATE TABLE os_return_point_reason_mapping
(
    code                    integer,
    description             varchar,
    reason_for_notification varchar,
    reason_for_sf           varchar,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE os_return_point_reason_mapping
    ADD CONSTRAINT os_return_point_reason_mapping_pkey
        PRIMARY KEY (code);

INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (1,'Hết hàng','Hết hàng','Hoàn xu vì kho đã hết quà giao cho khách hàng');
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (2,'Hàng không giống mô tả, sai k<PERSON>ch thước, sai mẫu, quà lỗi, hư hỏng,…','<PERSON>u<PERSON> tặng khác mô tả','<PERSON>àn xu vì khách hàng khiếu nại quà không giống mô tả');
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (3,'KH chọn nhầm quà/EV và chưa lên TD/ sử dụng EV KH muốn cập nhật lại thông tin nhân hàng,...','Khách hàng muốn thay đổi quà tặng','Hoàn xu vì khách hàng muốn thay đổi quà tặng');
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (4,'Lỗi RA: không hiển thị quà, hiển thị sai xu của quà, không đẩy TD lên SF,…','Sự cố hệ thống','Hoàn xu vì khách hàng đổi quà không thành công do sự cố hệ thống');
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (5,'Kết thúc chương trình, quá thời gian nhận quà,…','Hết thời hạn trao giải thưởng của chương trình','Hoàn xu vì hết thời hạn trao giải thưởng của chương trình');
INSERT INTO os_return_point_reason_mapping (code, description, reason_for_notification, reason_for_sf) VALUES (6,'Đơn vị vận chuyển làm hư hỏng hàng hóa','Sự cố quá trình vận chuyển','Hoàn xu vì sự cố quá trình vận chuyển');

DROP FUNCTION validate_item_import_return_point_fn;
CREATE OR REPLACE FUNCTION validate_item_import_return_point_fn(session_input varchar, tx_code varchar)
    RETURNS varchar[]
    LANGUAGE plpgsql
AS
$$
DECLARE
    item             record;
    transaction_item JSONB;
    line_array       varchar   := '';
    msg              varchar   := '';
    msg_return       varchar[] := ARRAY []:: varchar[];
    gs_query         VARCHAR;
    gs_result        RECORD;
    db_details       RECORD;
    reason_mapping   INTEGER;
    conn_str         text ;
BEGIN
    SELECT * INTO db_details FROM get_gift_db_details();
    conn_str :=
            'dbname=' || db_details.db_name || ' host=' || db_details.db_address ||
            ' user=' || db_details.db_user || ' password=' || db_details.db_password;

    FOR item IN
        SELECT *
        FROM os_import_return_point_temp osi
        WHERE osi.session = session_input
          AND osi.transaction_code = tx_code
          AND osi.status = 'imported'
        LOOP
            -- start: Check null or empty
            line_array := CONCAT(line_array, ' #', item.line);

            IF COALESCE(TRIM(item.transaction_code), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống Mã giao dịch (Transaction ID)', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF COALESCE(TRIM(item.reason), '') = '' THEN
                msg := FORMAT('Dòng #%s: Không để trống lý do', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            IF NOT COALESCE(TRIM(item.reason), '') ~ '^[0-9]+$' THEN
                msg := FORMAT('Dòng #%s: lý do sai định dạng number.', item.line);
                msg_return := ARRAY_APPEND(msg_return, msg);
            END IF;

            -- end: Check null or empty
        END LOOP;

    -- start: Check Order
    IF ARRAY_LENGTH(msg_return, 1) > 0 THEN
        RETURN msg_return;
    END IF;

    SELECT COUNT(*) INTO reason_mapping
    FROM os_return_point_reason_mapping
    WHERE code = CAST(item.reason AS INTEGER);

    IF reason_mapping = 0 THEN
        msg := FORMAT('Dòng%s: Lý do [%s] không tồn tại trong hệ thống.', line_array, item.reason);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    SELECT jsonb_element
    INTO transaction_item
    FROM os_order,
         jsonb_array_elements(os_order.user_gift_snapshot) AS jsonb_element
    WHERE (jsonb_element ->> 'transactionCode') = item.transaction_code;

    IF transaction_item IS NOT NULL AND (transaction_item ->> 'returned_point') IS NOT NULL THEN
        msg := FORMAT('Dòng%s: Transacion [%s] đã được trả điểm.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;

    gs_query :=
            'SELECT COUNT(*) as count FROM gs_user_gifts WHERE transaction_code = ''' || item.transaction_code || '''';

    EXECUTE 'SELECT * FROM dblink(' || quote_literal(conn_str) || ', ' ||
            quote_literal(gs_query) || ') AS t(
                    count             integer
                )' INTO gs_result;

    IF gs_result.count = 0 THEN
        msg := FORMAT('Dòng%s: Transacion [%s] không tồn tại trong hệ thống.', line_array, item.transaction_code);
        msg_return := ARRAY_APPEND(msg_return, msg);
    END IF;
    -- end: Check Order
    RETURN msg_return;
END ;
$$;