CREATE TABLE ws_history_lucky_transaction
(
    id               serial,
    user_id          bigint,
    phone_number     varchar,
    user_name        varchar,
    sku              varchar,
    gift_name        varchar,
    brand            varchar,
    transaction_code varchar,
    gift_received_date    timestamp,
    user_registered_date  timestamp,
    qr_code          varchar,
    spoon_code       varchar,
    province         varchar,
    status           varchar,
    created_at       timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
    PARTITION BY RANGE (created_at);

ALTER TABLE ws_history_lucky_transaction
    ADD CONSTRAINT ws_history_lucky_transaction_pkey
        PRIMARY KEY (id, created_at);

CREATE INDEX IF NOT EXISTS ws_history_lucky_transaction_created_at_idx ON ONLY public.ws_history_lucky_transaction USING btree (created_at);

GRANT SELECT ON ws_history_lucky_transaction TO vtd_db_reader;

CREATE TABLE ws_history_lucky_transaction_2024
    PARTITION OF ws_history_lucky_transaction
        FOR VALUES FROM ('2024-01-01 00:00:00') TO ('2024-12-31 23:59:59');

CREATE TABLE ws_history_lucky_transaction_2025
    PARTITION OF ws_history_lucky_transaction
        FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-12-31 23:59:59');

CREATE TABLE ws_history_lucky_transaction_2026
    PARTITION OF ws_history_lucky_transaction
        FOR VALUES FROM ('2026-01-01 00:00:00') TO ('2026-12-31 23:59:59');

CREATE TABLE ws_history_lucky_transaction_2027
    PARTITION OF ws_history_lucky_transaction
        FOR VALUES FROM ('2027-01-01 00:00:00') TO ('2027-12-31 23:59:59');

CREATE TABLE ws_history_lucky_transaction_from_2028
    PARTITION OF ws_history_lucky_transaction
        FOR VALUES FROM ('2028-01-01 00:00:00') TO (MAXVALUE);