CREATE TABLE ws_third_party_transaction
(
    id               serial,
    transaction_code varchar,
    request          jsonb,
    response         jsonb,
    status           varchar,
    notes            varchar,
    created_at       timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
)
    PARTITION BY RANGE (created_at);

-- Column id is associated with sequence public.ws_transaction_id_seq

ALTER TABLE ws_third_party_transaction
    ADD CONSTRAINT ws_third_party_transaction_pkey
        PRIMARY KEY (id, created_at);

CREATE INDEX IF NOT EXISTS ws_third_party_transaction_created_at_idx ON ONLY public.ws_third_party_transaction USING btree (created_at);

GRANT SELECT ON ws_third_party_transaction TO vtd_db_reader;

CREATE TABLE ws_third_party_transaction_2024
    PARTITION OF ws_third_party_transaction
        FOR VALUES FROM ('2024-01-01 00:00:00') TO ('2024-12-31 23:59:59');

CREATE TABLE ws_third_party_transaction_2025
    PARTITION OF ws_third_party_transaction
        FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-12-31 23:59:59');

CREATE TABLE ws_third_party_transaction_2026
    PARTITION OF ws_third_party_transaction
        FOR VALUES FROM ('2026-01-01 00:00:00') TO ('2026-12-31 23:59:59');

CREATE TABLE ws_third_party_transaction_2027
    PARTITION OF ws_third_party_transaction
        FOR VALUES FROM ('2027-01-01 00:00:00') TO ('2027-12-31 23:59:59');

CREATE TABLE ws_third_party_transaction_from_2028
    PARTITION OF ws_third_party_transaction
        FOR VALUES FROM ('2028-01-01 00:00:00') TO (MAXVALUE);