INSERT INTO ws_sf_api_config (transaction_code, path, http_method) VALUES 
('EXCHANGE_VOUCHER_V2', '/services/apexrest/api/v2/transaction/sendVourcher/', 'POST'),
('EXCHANGE_GIFT_V2', '/services/apexrest/api/v2/transaction/sendGift/', 'POST'),
('DELIVERY_TRANSACTION_V2', '/services/apexrest/api/v2/delivery_transaction', 'POST'),
('RETURN_POINT_TLIST_V2', '/services/apexrest/api/v2/transaction/list/returnPoint/', 'POST'),
('ADD_POINT_V2', '/services/apexrest/api/v2/Insert_Transaction', 'POST');


CREATE TABLE ws_sf_user_init_data (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    trigger_start_time TIMESTAMP NULL,
    trigger_end_time TIMESTAMP NULL
);
CREATE UNIQUE INDEX idx_unique_ws_sf_user_init_data_user_id ON ws_sf_user_init_data (user_id);

ALTER TABLE ws_transaction add column user_id int NULL;
CREATE INDEX idx_ws_transaction_user_id ON ws_transaction (user_id);