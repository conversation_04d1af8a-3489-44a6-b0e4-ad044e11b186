-- table store sf response
CREATE TABLE ws_sap_response (
    id SERIAL,
    transaction_id SERIAL,
    http_code VARCHAR,
    response VARCHAR,
    sent_at TIMESTAMP,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, received_at)
) PARTITION BY RANGE (received_at);

CREATE INDEX ON ws_sap_response (received_at);

CREATE TABLE ws_sap_response_y2024m07 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-07-01') TO ('2024-08-01');

CREATE TABLE ws_sap_response_y2024m08 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-08-01') TO ('2024-09-01');

CREATE TABLE ws_sap_response_y2024m09 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-09-01') TO ('2024-10-01');

CREATE TABLE ws_sap_response_y2024m10 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-10-01') TO ('2024-11-01');

CREATE TABLE ws_sap_response_y2024m11 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-11-01') TO ('2024-12-01');

CREATE TABLE ws_sap_response_y2024m12 PARTITION OF ws_sap_response
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');