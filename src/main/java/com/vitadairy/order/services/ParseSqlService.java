package com.vitadairy.order.services;

import com.vitadairy.main.dto.ParseQueryResponse;
import com.vitadairy.order.dtos.request.ListOrderRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ParseSqlService {

    @Qualifier("dbDateFormat")
    private final SimpleDateFormat sdf;

    public ParseQueryResponse parse(ListOrderRequest request) {
        ParseQueryResponse response = new ParseQueryResponse();
        response.setFailed();

        String jpaQuery = " FROM os_order o WHERE 1 = 1 ";
        Map<String, Object> parameters = new HashMap<>();
        if (Objects.nonNull(request.getStatusCode())) {
            jpaQuery += " AND o.status_code = :statusCode ";
            parameters.put("statusCode", request.getStatusCode().toString());
        }
        if (!StringUtils.isEmpty(request.getDeliveryProviderId())) {
            jpaQuery += " AND jsonb_extract_path_text(o.extra_data, 'delivery_provider') = :deliveryProviderId";
            parameters.put("deliveryProviderId", request.getDeliveryProviderId());
        }
        if (Objects.nonNull(request.getTypeCode())) {
            jpaQuery += " AND o.type_code = :typeCode";
            parameters.put("typeCode", request.getTypeCode().toString());
        }
        if (Objects.nonNull(request.getCreatedDate())) {
            jpaQuery += " AND date(o.created_at) = date(:createdDate)";
            parameters.put("createdDate", sdf.format(Date.from(request.getCreatedDate().toInstant())));
        }
        if (Objects.nonNull(request.getDateFrom()) && Objects.nonNull(request.getDateTo())) {
            jpaQuery += " AND o.created_at BETWEEN :dateFrom AND :dateTo";
            parameters.put("dateFrom", request.getDateFrom().toInstant());
            parameters.put("dateTo", request.getDateTo().toInstant());
        }
        else if (Objects.nonNull(request.getDateFrom())){
            jpaQuery += " AND o.created_at >= :dateFrom";
            parameters.put("dateFrom", request.getDateFrom().toInstant());
        }
        else if (Objects.nonNull(request.getDateTo())){
            jpaQuery += " AND o.created_at <= :dateTo";
            parameters.put("dateTo", request.getDateTo().toInstant());
        }

        if (!StringUtils.isEmpty(request.getKeyword())) {
            jpaQuery += " AND (o.order_code LIKE :keyword " +
                    "OR jsonb_extract_path_text(o.recipient_snapshot, 'recipientName') LIKE :keyword " +
                    "OR jsonb_extract_path_text(o.recipient_snapshot, 'recipientPhone') LIKE :keyword " +
                    "OR jsonb_extract_path_text(o.extra_data, 'delivery_code') LIKE :keyword " +
                    "OR EXISTS " +
                    "(SELECT 1 FROM jsonb_array_elements(o.user_gift_snapshot) AS jsonb_element " +
                    "WHERE jsonb_extract_path_text(jsonb_element, 'transactionCode') LIKE :keyword) " +
                    ")";
            parameters.put("keyword", "%" + request.getKeyword() + "%");
        }
        if (!CollectionUtils.isEmpty(request.getIds())) {
            jpaQuery += " AND id in :ids";
            parameters.put("ids", request.getIds());
        }

        response.setSuccess();
        response.setSelectQuery("SELECT o.* " + jpaQuery + " ORDER BY created_at DESC");
        response.setCountQuery("SELECT COUNT(*) " + jpaQuery);
        response.setParameters(parameters);
        return response;
    }
}
