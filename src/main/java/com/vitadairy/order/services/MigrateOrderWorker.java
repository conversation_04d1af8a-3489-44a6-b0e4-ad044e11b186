package com.vitadairy.order.services;

import com.vitadairy.order.common.OsOrderFactory;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.zoo.entities.DeliveryOrder;
import com.vitadairy.zoo.repositories.LegacyDeliveryOrderRepository;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public class MigrateOrderWorker implements Runnable {
    private final AtomicBoolean isRunning = new AtomicBoolean(true);
    private final AtomicInteger page;
    private final int batchSize;
    private final Long fromMilisTimestamp;
    private final Long toMilisTimestamp;
    private final AtomicInteger count;
    private final boolean isInterval;

    private final LegacyDeliveryOrderRepository legacyOrderRepository;
    private final OsOrderFactory osOrderFactory;
    private final AsyncOrderService asyncOrderService;

    @Setter
    private String workerName;

    public MigrateOrderWorker(AtomicInteger page, int batchSize, Long fromMilisTimestamp, Long toMilisTimestamp, AtomicInteger count,
                              boolean isInterval, ApplicationContext applicationContext) {
        this.page = page;
        this.batchSize = batchSize;
        this.fromMilisTimestamp = fromMilisTimestamp;
        this.toMilisTimestamp = toMilisTimestamp;
        this.count = count;
        this.isInterval = isInterval;

        this.legacyOrderRepository = applicationContext.getBean(LegacyDeliveryOrderRepository.class);
        this.osOrderFactory = applicationContext.getBean(OsOrderFactory.class);
        this.asyncOrderService = applicationContext.getBean(AsyncOrderService.class);
    }

    @Override
    public void run() {
        validate();

        if (!isRunning.get()) {
            return;
        }

        Pageable pageable = Pageable.ofSize(batchSize).withPage(page.getAndIncrement());
        Page<DeliveryOrder> deliveryOrders = findOrder(pageable, fromMilisTimestamp, toMilisTimestamp);
        while (isRunning.get() && deliveryOrders.hasContent()) {
            log.info("Current page is {}", pageable.getPageNumber());
            deliveryOrders.forEach(deliveryOrder -> {
                OsOrder osOrder = osOrderFactory.fromLegacyDeliverOrder(deliveryOrder);
                if (Objects.isNull(osOrder)) {
                    return;
                }

                log.info("Migrate order code: {}, id: {} from legacy {}", osOrder.getOrderCode(), osOrder.getId(), deliveryOrder.getId());
                asyncOrderService.addOrderToQueue(osOrder);
                count.incrementAndGet();
            });

            if (isInterval) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error("Error while sleeping", e);
                }
            }
            pageable = Pageable.ofSize(batchSize).withPage(page.getAndIncrement());
            deliveryOrders = findOrder(pageable, fromMilisTimestamp, toMilisTimestamp);
        }
        isRunning.set(false);
        log.info("MigrateOrderWorker {} is stopped", Thread.currentThread().getName());
    }

    public void validate() {
        if (!StringUtils.isEmpty(workerName)) {
            Thread.currentThread().setName(workerName);
        }

        if (page.get() < 0) {
            isRunning.set(false);
        }
        if (Objects.isNull(legacyOrderRepository) || Objects.isNull(osOrderFactory) || Objects.isNull(asyncOrderService)) {
            isRunning.set(false);
        }
        log.info("MigrateOrderWorker {} is running", Thread.currentThread().getName());
    }

    private Page<DeliveryOrder> findOrder(Pageable pageable, Long millisecondTimestamp, Long toMilisTimestamp) {
        if (Objects.isNull(millisecondTimestamp)) {
            return legacyOrderRepository.findAll(pageable);
        }
        if (Objects.nonNull(toMilisTimestamp)) {
            Timestamp from = Timestamp.from(Instant.ofEpochMilli(millisecondTimestamp));
            Timestamp to = Timestamp.from(Instant.ofEpochMilli(toMilisTimestamp));
//            log.info("From {} to {}", from, to);

            return legacyOrderRepository.findAllByAcceptedTimeGreaterThanEqualAndAcceptedTimeLessThanEqual(from, to, pageable);
        }
        Timestamp timestamp = Timestamp.from(Instant.ofEpochMilli(millisecondTimestamp));
//        log.info("{} timestamp vs {} from timeStamp", new Timestamp(millisecondTimestamp), timestamp);
        return legacyOrderRepository.findAllByAcceptedTimeGreaterThanEqual(timestamp, pageable);
    }

}
