package com.vitadairy.order.services;

import com.vitadairy.order.common.OsOrderFactory;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.order.repositories.OsOrderRepository;
import com.vitadairy.zoo.entities.DeliveryOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Service
@Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
@Slf4j
public class AsyncOrderService {
    private static final int BATCH_SIZE = 200;

    private final LinkedBlockingQueue<OsOrder> orderInQueues = new LinkedBlockingQueue<>();
    private final AtomicBoolean isRunning = new AtomicBoolean(true);

    private final OsOrderRepository osOrderRepository;
    private final Object migrateOrderSynchronizeObject;
    private final OsOrderFactory osOrderFactory;
    private final OsOrderCustomRepository osOrderCustomRepository;
    private final ExecutorService executor;

    private final AtomicInteger count = new AtomicInteger(0);
    private final AtomicInteger failed = new AtomicInteger(0);

    public AsyncOrderService(OsOrderRepository osOrderRepository,
                             @Qualifier("migrateOrderSynchronizeObject") Object migrateOrderSynchronizeObject,
                             OsOrderFactory osOrderFactory, OsOrderCustomRepository osOrderCustomRepository,
                             @Qualifier("migrateOrderExecutor") ExecutorService executor) {
        this.osOrderRepository = osOrderRepository;
        this.migrateOrderSynchronizeObject = migrateOrderSynchronizeObject;
        this.osOrderFactory = osOrderFactory;
        this.osOrderCustomRepository = osOrderCustomRepository;
        this.executor = executor;
    }

    public void addOrderToQueue(OsOrder order) {
        if (this.orderInQueues.size() >= BATCH_SIZE * 5) {
            synchronized (migrateOrderSynchronizeObject) {
                try {
                    log.warn("Queue is more than 5 times of batch size, waiting for processing");
                    migrateOrderSynchronizeObject.wait();
                } catch (Exception e) {
                    log.error("Error while waiting", e);
                }
            }
        }
        try {
            orderInQueues.put(order);
        } catch (Exception ex) {
            log.error("Error while adding order to queue", ex);
        }
    }

    public void stop(boolean isGraceFullyShutdown) {
        log.info("Stop async batch save order, isGracefullyShutdown: {}", isGraceFullyShutdown);
        if (!isGraceFullyShutdown) {
            isRunning.set(false);
        } else {
            executor.shutdown();
            log.info("Waiting for queue to be empty, shutdown executor");

            while (!orderInQueues.isEmpty()) {
                try {
                    TimeUnit.SECONDS.sleep(30);
                } catch (Exception e) {
                    log.error("Error while waiting for queue to be empty", e);
                }
            }
            isRunning.set(false);
        }
    }

    @Async
    public void process() {
        log.info("Start processing async batch save order with batchSize {}", BATCH_SIZE);
        while (isRunning.get()) {
            try {
                OsOrder order = orderInQueues.take();

                List<OsOrder> orders = new ArrayList<>();
                orders.add(order);

                while (orders.size() < BATCH_SIZE) {
                    OsOrder order2 = orderInQueues.poll();
                    if (order2 == null) {
                        break;
                    } else {
                        orders.add(order2);
                    }
                }

                log.info("Statistics: queue size: {}, total order saved: {}", orderInQueues.size(), count.get());
                log.info("Saving order: {}, size {}", orders.stream().map(OsOrder::getId).toList(), orders.size());
//                osOrderRepository.saveAll(orders);
                List<OsOrder> resOrders = osOrderCustomRepository.manualSaveAllOnDuplicateDoNothing(orders);
                log.info("Saved order: {}, size {}", resOrders.stream().map(OsOrder::getId).toList(), resOrders.size());
                count.addAndGet(resOrders.size());
                if (resOrders.size() != orders.size()) {
                    failed.addAndGet(orders.size() - resOrders.size());
                }
            } catch (Exception ex) {
                log.error("Error while processing order", ex);
            } finally {
                synchronized (migrateOrderSynchronizeObject) {
                    migrateOrderSynchronizeObject.notifyAll();
                }
            }
        }

        log.info("Finish processing async batch save order, total order saved: {}", count.get());
        log.info("Finish processing async batch save order, total order failed: {}", failed.get());
    }

    @Async("migrateOrderExecutor")
    public void processOrder(DeliveryOrder legacyOrder) {
        OsOrder osOrder = osOrderFactory.fromLegacyDeliverOrder(legacyOrder);
        addOrderToQueue(osOrder);
        log.info("Migrating order {} with code {} from legacy {}", osOrder.getId(), osOrder.getOrderCode(), legacyOrder.getId());
//        OsOrder res = osOrderRepository.save(osOrder);
    }

}
