package com.vitadairy.order.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.main.dto.GiftReturnPointRequestDto;
import com.vitadairy.main.enums.ReturnPointStatusEnum;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.services.ProcessDataAbstractService;
import com.vitadairy.order.dtos.ReturnPointMappingDto;
import com.vitadairy.order.entities.OsImportReturnPointTemp;
import com.vitadairy.order.repositories.OsImportOrderReturnPointTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.order.services.RestApiGiftService;
import com.vitadairy.order.services.ReturnPointReasonMappingService;
import com.vitadairy.zoo.repositories.ZooCustomRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessDataOsImportReturnPointService extends ProcessDataAbstractService<OsImportReturnPointTemp> implements ProcessDataService<OsImportReturnPointTemp> {

    private final OsImportOrderReturnPointTempRepository repository;
    private final ReturnPointReasonMappingService returnPointReasonMappingService;
    private final OsOrderCustomRepository osOrderCustomRepository;
    private final RestApiGiftService restApiGiftService;
    private final ZooCustomRepository zooCustomRepository;
    private final ObjectMapper objectMapper;

    @Override
    protected OsImportReturnPointTemp save(OsImportReturnPointTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsImportReturnPointTemp> saveAll(List<OsImportReturnPointTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsImportReturnPointTemp entity) {
        String session = entity.getId().getSession();
        List<Object[]> res = osOrderCustomRepository.callFunctionValidateReturnPoint(session);
        try {
            var zoors = processZooFunction(session);
            if(!CollectionUtils.isEmpty(zoors)){
                res.addAll(zoors);
            }
        } catch (Exception ex) {
            log.error("Error on process zoo function", ex);
            throw new ApplicationException("Error on update user point", HttpStatus.BAD_REQUEST);
        }
        return res;
    }

    public List<Object[]> processZooFunction(String session) {
        List<Object[]> result = new ArrayList<>();
        List<OsImportReturnPointTemp> list = osOrderCustomRepository.callFunctionGetImportReturnPoint(session);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        osOrderCustomRepository.callFunctionDoImportHistoryReturnPoint(session);
        var returnPointResult = restApiGiftService.returnPoint("", list.stream().map(el -> {
            ReturnPointMappingDto reasonMapping = returnPointReasonMappingService.findByCode(Integer.valueOf(el.getReason()));

            return GiftReturnPointRequestDto.builder()
                    .userId(Long.valueOf(el.getUserId()))
                    .session(el.getId().getSession())
                    .line(el.getId().getLine().longValue())
                    .userGiftId(Long.valueOf(el.getUserGiftId()))
                    .orderCode(el.getOrderCode())
                    .point(el.getPoint())
                    .reasonForNotification(reasonMapping.getReasonForNotification())
                    .reasonForSf(reasonMapping.getReasonForSf())
                    .build();
        }).collect(Collectors.toList()));

        if(!CollectionUtils.isEmpty(returnPointResult)) {
            returnPointResult.forEach(el -> {
                if(el.getStatus() == ReturnPointStatusEnum.FAILED){
                    osOrderCustomRepository.updateFailedImportReturnPoint(session, el.getLine());
                    osOrderCustomRepository.deleteHistoryReturnPoint(session, el.getLine());
                    var msg = String.format("Dòng #%s: Hoàn xu không thành công (%s)", el.getLine(), el.getMessage());
                    result.add(new Object[]{msg});
                }
            });
        }
        return result;
    }
}
