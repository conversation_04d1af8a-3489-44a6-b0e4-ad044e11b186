package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.main.common.ImportStatus;
import com.vitadairy.order.entities.OsImportFullRedeliveryOrderTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsImportFullRedeliveryOrderTempService implements ParseDataService<OsImportFullRedeliveryOrderTemp> {

    private final String session;

    @Override
    public OsImportFullRedeliveryOrderTemp parseEntity(RowData rowData) throws ParseDataException {
        OsImportFullRedeliveryOrderTemp res = new OsImportFullRedeliveryOrderTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "Mã vận đơn":
                    res.setDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "Mã vận đơn mới":
                    res.setNewDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "Ngày tạo MVĐ":
                    res.setDeliveryCreatedAt(cellData.getCellValue() + "");
                    break;
                case "Đơn vị vận chuyển":
                    res.setDeliveryProvider(cellData.getCellValue() + "");
                    break;
                case "Hiện phí ship":
                    res.setShowShippingFee((cellData.getCellValue() + "").equals("TRUE"));
                    break;
                case "Phí ship":
                    res.setShippingFee((Double) cellData.getCellValue());
                    break;
                default:
                    break;
            }
        }
        OsOrderImportTempKey key = new OsOrderImportTempKey();
        key.setLine(rowData.getLineNumber());
        key.setSession(session);
        res.setId(key);
        res.setStatus(ImportStatus.IMPORTED.getValue());
        return res;
    }
}
