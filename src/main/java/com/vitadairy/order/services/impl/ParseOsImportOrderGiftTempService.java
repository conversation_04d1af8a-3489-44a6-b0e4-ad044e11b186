package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.main.common.ImportStatus;
import com.vitadairy.order.entities.OsImportOrderGiftTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsImportOrderGiftTempService implements ParseDataService<OsImportOrderGiftTemp> {

    private final String session;

    @Override
    public OsImportOrderGiftTemp parseEntity(RowData rowData) throws ParseDataException {
        OsImportOrderGiftTemp res = new OsImportOrderGiftTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "Số lượng":
                    res.setTotalGift(Integer.parseInt(cellData.getCellValue() + ""));
                    break;
                case "Mã giao dịch":
                    res.setTransactionCode(cellData.getCellValue() + "");
                    break;
                case "Mã vận đơn":
                    res.setDeliveryCode(cellData.getCellValue() + "");
                    break;
                default:
                    break;
            }
        }
        OsOrderImportTempKey key = new OsOrderImportTempKey();
        key.setLine(rowData.getLineNumber());
        key.setSession(session);
        res.setId(key);
        res.setStatus(ImportStatus.IMPORTED.getValue());
        return res;
    }
}
