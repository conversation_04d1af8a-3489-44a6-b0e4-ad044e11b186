package com.vitadairy.order.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.common.RecipientFieldDefs;
import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.dto.TransactionDto;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.order.dtos.DeliverySFOrderGiftPayloadDto;
import com.vitadairy.order.dtos.DeliverySFPayloadDto;
import com.vitadairy.order.dtos.OrderDto;
import com.vitadairy.order.dtos.OrderGiftDto;
import com.vitadairy.order.iservice.SaleForceService;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryServiceSaleForceServiceImpl implements SaleForceService<OrderDto> {

    private static final String SF_ORDER_STATUS = "CREATE";

    private final ObjectMapper objectMapper;
    private final WarehouseTransactionService warehouseTransactionService;
    private final UserRepository userRepository;

    private static final String EVENT_ADD_CAN_LOYALTY_PROGRAM_SF_ID = "event_add_can_loyalty_program_sf_id";
    private static final String MAP_USER_TO_BRAND_POINT_ID_SEND_SF = "map_user_to_brand_point_id_send_sf";
    private static final String MAP_USER_TO_BRAND_POINT_ID = "map_user_to_brand_point_id";

    @Async
    @Override
    public void log(List<OrderDto> orders, Object... params) {
        try {
            List<TransactionDto> histories = orders.stream()
                    .map(order -> {
                        TransactionDto history = new TransactionDto();
                        history.setCode("DELIVERY_TRANSACTION");
                        history.setPayload(buildPayLoad(order, params));
                        return history;
                    }).toList();
            warehouseTransactionService.send(TransactionBatchDto.builder()
                    .transactions(histories)
                    .build());
            log.info("Logged history for orders {}", orders.stream().map(OrderDto::getOrderCode));
        } catch (Exception ex) {
            log.error("Cannot log history for orders {}", orders.stream().map(OrderDto::getOrderCode), ex);
        }
    }

    @Override
    public Map<String, Object> buildPayLoad(OrderDto object, Object... params) {
        User user = userRepository.getUserById(object.getUserId().longValue()).orElse(null);
        List<OrderGiftDto> userGiftSnapshot = object.getUserGiftSnapshot();
        DeliverySFPayloadDto payload = DeliverySFPayloadDto.builder()
                .refId(object.getTdId())
                .orderId(object.getOrderCode())
                .reOrderBy(object.getPreviousOrderCode())
                .orderType(object.getTransportTypeCode())
                .transportService(object.getDeliveryProviderId())
                .orderStatus(SF_ORDER_STATUS)
                .phoneNumberUser(Objects.nonNull(user) ? user.getPhoneNumber() : null)
                .phoneNumberRecipient(object.getCustomerInfo().get(RecipientFieldDefs.RECIPIENT_PHONE))
                .nameRecipient(object.getCustomerInfo().get(RecipientFieldDefs.RECIPIENT_NAME))
                .userId(object.getUserId() + "")
                .dateOfPurchase(Date.from(object.getCreatedAt()))
                .orderBy("User")
                .billingCountry("1")
//                .billingProvince(object.getCustomerInfo().get(RecipientFieldDefs.PROVINCE_CODE))
//                .billingProvinceName(object.getCustomerInfo().get(RecipientFieldDefs.PROVINCE_NAME))
                .billingNewProvince(object.getCustomerInfo().get(RecipientFieldDefs.NEW_PROVINCE_CODE))
                .billingNewProvinceName(object.getCustomerInfo().get(RecipientFieldDefs.NEW_PROVINCE_NAME))
//                .billingDistrict(object.getCustomerInfo().get(RecipientFieldDefs.DISTRICT_CODE))
//                .billingDistrictName(object.getCustomerInfo().get(RecipientFieldDefs.DISTRICT_NAME))
//                .billingWard(object.getCustomerInfo().get(RecipientFieldDefs.WARD_CODE))
//                .billingWardName(object.getCustomerInfo().get(RecipientFieldDefs.WARD_NAME))
                .billingNewWard(object.getCustomerInfo().get(RecipientFieldDefs.NEW_WARD_CODE))
                .billingNewWardName(object.getCustomerInfo().get(RecipientFieldDefs.NEW_WARD_NAME))
                .billingStreet(object.getCustomerInfo().get(RecipientFieldDefs.STREET_NAME))
                .exportDate(null)
                .deliveryDate(object.getDeliveryCreatedTime())
//                .deliveryId(object.getDeliveryNumber())
//                .deliveryCost(object.getShippingFee())
                .lossReason("")
                .height(0)
                .width(0)
                .length(0)
                .totalQuantity(object.getTotalGifts())
                .acceptedDate(null)
                .isFreeShip(true)
                .gifts(buildOrderGiftsPayload(object, object.getCustomerInfo()))
                .redeemPoint(userGiftSnapshot.stream().map(OrderGiftDto::getPoint).reduce(0, Integer::sum))
                .build();

        userGiftSnapshot.stream().findFirst().ifPresent(userGift -> {
            var programName = userGift.getDynamicData().getOrDefault(EVENT_ADD_CAN_LOYALTY_PROGRAM_SF_ID, "").toString();
            var idSendSf = userGift.getDynamicData().getOrDefault(MAP_USER_TO_BRAND_POINT_ID_SEND_SF, "").toString();
            var id = userGift.getDynamicData().getOrDefault(MAP_USER_TO_BRAND_POINT_ID, "").toString();

            if (!programName.isEmpty() || !idSendSf.isEmpty() || !id.isEmpty()) {
                payload.setProgramName(programName);
                payload.setLoyaltyMember(String.format("%s-%s", idSendSf, id));
            }
        });

//        payload.setBillingAddress(payload.getBillingStreet() + ", " +
//                payload.getBillingWardName() + ", " +
//                payload.getBillingDistrictName() + ", " +
//                payload.getBillingProvinceName());
        payload.setBillingAddress(payload.getBillingStreet() + ", " +
                payload.getBillingNewWardName() + ", " +
                payload.getBillingNewProvinceName());
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(payload), Map.class);
        } catch (Exception ex) {
            log.error("Error when build payload for delivery service", ex);
            return new HashMap<>();
        }
    }

    private List<DeliverySFOrderGiftPayloadDto> buildOrderGiftsPayload(OrderDto orderDto, Map<String, String> customerInfo) {
        return orderDto.getUserGiftSnapshot().stream()
                .map(val -> {
//                    List<HistoryPoint> oHistoryPoint = historyPointRepository.findByGiftId((long) val.getId());
                    return DeliverySFOrderGiftPayloadDto.builder()
                            .refId(val.getTransactionCode())
                            .orderId(orderDto.getOrderCode())
                            .statusUserGift(val.getStatus())
                            .recipientName(customerInfo.get(RecipientFieldDefs.RECIPIENT_NAME))
                            .recipientPhone(customerInfo.get(RecipientFieldDefs.RECIPIENT_PHONE))
                            .recipientAddress(String.join(", ", Arrays.asList(
                                            customerInfo.get(RecipientFieldDefs.STREET_NAME),
//                                            customerInfo.get(RecipientFieldDefs.WARD_NAME),
//                                            customerInfo.get(RecipientFieldDefs.DISTRICT_NAME),
//                                            customerInfo.get(RecipientFieldDefs.PROVINCE_NAME)
                                            customerInfo.get(RecipientFieldDefs.NEW_WARD_NAME),
                                            customerInfo.get(RecipientFieldDefs.NEW_PROVINCE_NAME)
                                    ))
                            )
//                            .billingDistrict(customerInfo.get(RecipientFieldDefs.DISTRICT_NAME))
//                            .billingProvince(customerInfo.get(RecipientFieldDefs.PROVINCE_NAME))
                            .billingNewProvince(customerInfo.get(RecipientFieldDefs.NEW_PROVINCE_NAME))
                            .billingStreet(customerInfo.get(RecipientFieldDefs.STREET_NAME))
//                            .billingWard(customerInfo.get(RecipientFieldDefs.WARD_NAME))
                            .billingNewWard(customerInfo.get(RecipientFieldDefs.NEW_WARD_NAME))
                            .acceptedDate(null)
//                            .deliveryId(orderDto.getTdId())
                            .transportService(orderDto.getDeliveryProviderId())
                            .orderStatus(null)
                            .deliveryDate(orderDto.getDeliveryCreatedTime())
                            .build();
                })
                .toList();
    }
}
