package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.order.entities.OsImportPartRedeliveryOrderTemp;
import com.vitadairy.order.repositories.OsImportPartRedeliveryOrderTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.main.services.ProcessDataAbstractService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessDataOsImportPartReDeliveryOrderTempService extends ProcessDataAbstractService<OsImportPartRedeliveryOrderTemp>
        implements ProcessDataService<OsImportPartRedeliveryOrderTemp> {

    private final OsImportPartRedeliveryOrderTempRepository repository;
    private final OsOrderCustomRepository osOrderCustomRepository;

    @Override
    protected OsImportPartRedeliveryOrderTemp save(OsImportPartRedeliveryOrderTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsImportPartRedeliveryOrderTemp> saveAll(List<OsImportPartRedeliveryOrderTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsImportPartRedeliveryOrderTemp entity) {
        return osOrderCustomRepository.callFunctionImportOrderPartRedelivery(entity.getId().getSession());
    }
}
