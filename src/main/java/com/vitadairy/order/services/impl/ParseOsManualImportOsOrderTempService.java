package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.main.common.ImportStatus;
import com.vitadairy.order.entities.OsManualImportOrderTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsManualImportOsOrderTempService implements ParseDataService<OsManualImportOrderTemp> {

    private final String session;

    @Override
    public OsManualImportOrderTemp parseEntity(RowData rowData) throws ParseDataException {
        OsManualImportOrderTemp osManualImportOrderTemp = new OsManualImportOrderTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "TD ID":
                    osManualImportOrderTemp.setTdId(cellData.getCellValue() + "");
                    break;
                case "Transaction: Transaction External ID":
                    osManualImportOrderTemp.setTransactionCode(cellData.getCellValue() + "");
                    break;
                case "Ma van don":
                    osManualImportOrderTemp.setDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "Ngay tao ma van don":
                    osManualImportOrderTemp.setCreatedAt(cellData.getCellValue() + "");
                    break;
                case "Don vi van chuyen":
                    osManualImportOrderTemp.setDeliveryProvider(cellData.getCellValue() + "");
                    break;
                case "Phi giao hang":
                    osManualImportOrderTemp.setShipCost(cellData.getCellValue() + "");
                    break;
                default:
                    break;
            }
        }
        OsOrderImportTempKey key = new OsOrderImportTempKey();
        key.setLine(rowData.getLineNumber());
        key.setSession(session);
        osManualImportOrderTemp.setId(key);
        osManualImportOrderTemp.setStatus(ImportStatus.IMPORTED.getValue());
        return osManualImportOrderTemp;
    }
}
