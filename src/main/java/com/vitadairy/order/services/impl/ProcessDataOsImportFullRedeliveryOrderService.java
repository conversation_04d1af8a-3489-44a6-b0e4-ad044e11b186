package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.order.entities.OsImportFullRedeliveryOrderTemp;
import com.vitadairy.order.repositories.OsImportFullRedeliveryOrderTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.main.services.ProcessDataAbstractService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessDataOsImportFullRedeliveryOrderService extends ProcessDataAbstractService<OsImportFullRedeliveryOrderTemp> implements ProcessDataService<OsImportFullRedeliveryOrderTemp> {

    private final OsImportFullRedeliveryOrderTempRepository repository;
    private final OsOrderCustomRepository osOrderCustomRepository;

    @Override
    protected OsImportFullRedeliveryOrderTemp save(OsImportFullRedeliveryOrderTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsImportFullRedeliveryOrderTemp> saveAll(List<OsImportFullRedeliveryOrderTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsImportFullRedeliveryOrderTemp entity) {
        return osOrderCustomRepository.callFunctionImportOrderFullRedelivery(entity.getId().getSession());
    }
}
