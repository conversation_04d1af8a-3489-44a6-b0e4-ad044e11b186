package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.order.entities.OsManualImportOrderTemp;
import com.vitadairy.order.repositories.OsManualImportOrderTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.main.services.ProcessDataAbstractService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessDataOsManualImportOsOrderService extends ProcessDataAbstractService<OsManualImportOrderTemp> implements ProcessDataService<OsManualImportOrderTemp> {

    private final OsManualImportOrderTempRepository repository;
    private final OsOrderCustomRepository osOrderCustomRepository;

    @Override
    protected OsManualImportOrderTemp save(OsManualImportOrderTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsManualImportOrderTemp> saveAll(List<OsManualImportOrderTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsManualImportOrderTemp entity) {
        return osOrderCustomRepository.callFunctionImportOrderManual(entity.getId().getSession());
    }
}
