package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.order.entities.OsImportOrderGiftTemp;
import com.vitadairy.order.repositories.OsImportOrderGiftTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.main.services.ProcessDataAbstractService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessDataOsImportOrderGiftTempService extends ProcessDataAbstractService<OsImportOrderGiftTemp> implements ProcessDataService<OsImportOrderGiftTemp> {

    private final OsImportOrderGiftTempRepository repository;
    private final OsOrderCustomRepository osOrderCustomRepository;

    @Override
    protected OsImportOrderGiftTemp save(OsImportOrderGiftTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsImportOrderGiftTemp> saveAll(List<OsImportOrderGiftTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsImportOrderGiftTemp entity) {
        return osOrderCustomRepository.callFunctionImportOrderGift(entity.getId().getSession());
    }
}
