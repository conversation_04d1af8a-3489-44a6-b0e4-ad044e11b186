package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.dto.Page;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.order.dtos.OrderDto;
import com.vitadairy.order.dtos.OrderDtoExportItem;
import com.vitadairy.order.dtos.request.ListOrderRequest;
import com.vitadairy.order.services.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FetchExportOsOrderService implements FetchDataService<OrderDtoExportItem, ListOrderRequest> {

    private final OrderService orderService;

    @Override
    public List<OrderDtoExportItem> fetch(FetchRequest<ListOrderRequest> fetchRequest) {

        Page page = fetchRequest.getPageable();
        if (Objects.nonNull(page)) {
            fetchRequest.getRequest().setPage(page.page());
            fetchRequest.getRequest().setSize(page.size());
        }

        List<OrderDto> orders = orderService.findListOrderDto(fetchRequest.getRequest());
        return orders.stream()
                .map(OrderDtoExportItem::fromOrder)
                .flatMap(List::stream)
                .toList();
    }
}
