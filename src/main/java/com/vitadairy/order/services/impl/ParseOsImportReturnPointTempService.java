package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.main.common.ImportStatus;
import com.vitadairy.order.entities.OsImportReturnPointTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsImportReturnPointTempService implements ParseDataService<OsImportReturnPointTemp> {

    private final String session;

    @Override
    public OsImportReturnPointTemp parseEntity(RowData rowData) throws ParseDataException {
        OsImportReturnPointTemp res = new OsImportReturnPointTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "Transaction external ID":
                    res.setTransactionCode(cellData.getCellValue() + "");
                    break;
                case "Lý do hoàn":
                    res.setReason(cellData.getCellValue() + "");
                    break;
                default:
                    break;
            }
        }
        if(StringUtils.isEmpty(res.getTransactionCode())){
            return null;
        }
        OsOrderImportTempKey key = new OsOrderImportTempKey();
        key.setLine(rowData.getLineNumber());
        key.setSession(session);
        res.setId(key);
        res.setStatus(ImportStatus.IMPORTED.getValue());
        return res;
    }
}
