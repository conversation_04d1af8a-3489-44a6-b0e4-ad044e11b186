package com.vitadairy.order.services.impl;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.main.common.ImportStatus;
import com.vitadairy.order.entities.OsImportPartRedeliveryOrderTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsImportPartRedeliveryOrderTempService implements ParseDataService<OsImportPartRedeliveryOrderTemp> {
    private final String session;

    @Override
    public OsImportPartRedeliveryOrderTemp parseEntity(RowData rowData) throws ParseDataException {
        OsImportPartRedeliveryOrderTemp res = new OsImportPartRedeliveryOrderTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "Mã giao dịch":
                    res.setTransactionCode(cellData.getCellValue() + "");
                    break;
                case "Mã vận đơn cũ":
                    res.setOldDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "Mã vận đơn mới":
                    res.setNewDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "Ngày tạo MVĐ":
                    res.setDeliveryCreatedAt(cellData.getCellValue() + "");
                    break;
                case "Đơn vị vận chuyển":
                    res.setDeliveryProvider(cellData.getCellValue() + "");
                    break;
                case "Phí giao hàng":
                    res.setShippingFee((Double) cellData.getCellValue());
                    break;
                case "Hiện phí ship":
                    res.setShowShippingFee((cellData.getCellValue() + "").equals("TRUE"));
                    break;
                case "Số lượng":
                    res.setQuantity((int) Double.parseDouble((cellData.getCellValue() + "")));
                    break;
                default:
                    break;
            }
        }
        OsOrderImportTempKey id = OsOrderImportTempKey.builder()
                .line(rowData.getLineNumber())
                .session(session)
                .build();
        res.setId(id);
        res.setStatus(ImportStatus.IMPORTED.getValue());
        return res;
    }
}
