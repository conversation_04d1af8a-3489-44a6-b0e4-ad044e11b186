package com.vitadairy.order.services;

import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import com.vitadairy.order.entities.OsImportDeliveryProviderTemp;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ParseOsImportDeliveryProviderTempService implements ParseDataService<OsImportDeliveryProviderTemp> {

    private final String session;

    @Override
    public OsImportDeliveryProviderTemp parseEntity(RowData rowData) throws ParseDataException {
        OsImportDeliveryProviderTemp res = new OsImportDeliveryProviderTemp();
        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName()) {
                case "Mã vận đơn":
                    res.setDeliveryCode(cellData.getCellValue() + "");
                    break;
                case "ĐVVC":
                    res.setDeliveryProvider(cellData.getCellValue() + "");
                    break;
                case "Trạng thái ĐVVC":
                    res.setDeliveryProviderStatus(cellData.getCellValue() + "");
                    break;
                default:
                    break;
            }
        }
        OsOrderImportTempKey key = new OsOrderImportTempKey();
        key.setLine(rowData.getLineNumber());
        key.setSession(session);
        res.setId(key);
        return res;
    }
}
