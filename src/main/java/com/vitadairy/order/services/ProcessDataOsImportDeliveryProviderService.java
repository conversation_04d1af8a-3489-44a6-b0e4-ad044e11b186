package com.vitadairy.order.services;

import com.vitadairy.libraries.importexport.service.ProcessDataService;
import com.vitadairy.main.services.ProcessDataAbstractService;
import com.vitadairy.order.entities.OsImportDeliveryProviderTemp;
import com.vitadairy.order.repositories.OsImportDeliveryProviderTempRepository;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ProcessDataOsImportDeliveryProviderService extends ProcessDataAbstractService<OsImportDeliveryProviderTemp> implements ProcessDataService<OsImportDeliveryProviderTemp> {

    private final OsImportDeliveryProviderTempRepository repository;
    private final OsOrderCustomRepository osOrderCustomRepository;

    @Override
    protected OsImportDeliveryProviderTemp save(OsImportDeliveryProviderTemp entity) {
        return repository.save(entity);
    }

    @Override
    protected List<OsImportDeliveryProviderTemp> saveAll(List<OsImportDeliveryProviderTemp> list) throws Exception {
        return repository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(OsImportDeliveryProviderTemp entity) {
        return osOrderCustomRepository.callFunctionImportDeliveryProvider(entity.getId().getSession());
    }
}
