package com.vitadairy.order.services;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.vitadairy.order.common.OrderObjectTypeDefs;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.dtos.ConstantDto;
import com.vitadairy.order.entities.OsConstant;
import com.vitadairy.order.dtos.ResourceDto;
import com.vitadairy.order.repositories.OsConstantRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResourceService {
    private final OsConstantRepository osConstantRepository;
    private final Gson gson;

    public ResourceService(OsConstantRepository osConstantRepository, Gson gson) {
        this.osConstantRepository = osConstantRepository;
        this.gson = gson;
    }

    public List<ConstantDto> getAllOrderStatus() {
        List<OsConstant> entities = osConstantRepository.findAll();
        return entities.stream().map(ConstantDto::fromEntity).toList();
    }

    public List<ResourceDto> getAllDeliveryProviderInfos() {
        List<OsConstant> entities = osConstantRepository.findByKeyAlphaAndKeyBeta(OrderObjectTypeDefs.OS_ORDER,
                OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER);
        if (entities.isEmpty()) {
            return List.of();
        }
        // return gson.fromJson(entities.getFirst().getValue(), JsonArray.class)
        return gson.fromJson(entities.iterator().next().getValue(), JsonArray.class)
                .asList().stream()
                .map(JsonElement::getAsJsonObject)
                .map(ResourceDto::fromDeliveryProviderInfo)
                .toList();
    }
}
