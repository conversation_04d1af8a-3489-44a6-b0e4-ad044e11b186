package com.vitadairy.order.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.common.RestApiQueryDefs;
import com.vitadairy.main.dto.GiftReturnPointRequestDto;
import com.vitadairy.main.dto.GiftReturnPointResponseDto;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.main.response.RestApiBaseResponse;
import com.vitadairy.order.dtos.GiftDto;
import com.vitadairy.order.dtos.response.GetGiftResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RestApiGiftService {

    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;

    public RestApiGiftService(
            @Qualifier("restTemplateOsGiftClient") RestTemplateClient restTemplateClient,
            ObjectMapper objectMapper) {
        this.restTemplateClient = restTemplateClient;
        this.objectMapper = objectMapper;
    }


    public GetGiftResponseDto getGifts(String token, Map<String, String> queries, Integer page, Integer pageSize) throws Exception {
        restTemplateClient.buildAuth(token);
        if (Objects.isNull(page) || page <= 0) {
            queries.put(RestApiQueryDefs.PAGE, "1");
        } else {
            queries.put(RestApiQueryDefs.PAGE, page.toString());
        }
        if (Objects.isNull(pageSize) || pageSize <= 0) {
            queries.put(RestApiQueryDefs.PAGE_SIZE, "10");
        } else {
            queries.put(RestApiQueryDefs.PAGE_SIZE, pageSize.toString());
        }
        LinkedMultiValueMap<String, String> queriesMap = new LinkedMultiValueMap<>();
        queriesMap.setAll(queries);
        Map<String, Object> apiResponse = restTemplateClient.get("/in/gs/gifts", queriesMap, new ParameterizedTypeReference<>() {
        });
        log.debug("apiResponse: {}", apiResponse);
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(apiResponse), GetGiftResponseDto.class);
        } catch (JsonProcessingException ex) {
            log.error("Error when parse response from api", ex);
            throw new ApplicationException(ex.getMessage());
        }
    }

    public RestApiBaseResponse updateGift(String token, Integer id, GiftDto giftDto) {
        restTemplateClient.buildAuth(token);
        try {
            Map<String, Object> request = objectMapper.readValue(objectMapper.writeValueAsString(giftDto), HashMap.class);
            return restTemplateClient.patch("/in/gs/gifts/" + id, request, new ParameterizedTypeReference<>() {
            });
        } catch (JsonProcessingException ex) {
            log.error("Error when parse request to api", ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    public List<GiftReturnPointResponseDto> returnPoint(String token, List<GiftReturnPointRequestDto> requestData) {
        restTemplateClient.buildAuth(token);
        try {
            List<Map<String, Object>> request = objectMapper.readValue(objectMapper.writeValueAsString(requestData), List.class);
            Map<String, Object> apiResponse = restTemplateClient.post("/in/gs/gifts/return-point", request, new ParameterizedTypeReference<>() {});
            if(MapUtils.isNotEmpty(apiResponse) && Objects.nonNull(apiResponse.get("response"))){
                return objectMapper.readValue(
                        objectMapper.writeValueAsString(((Map) apiResponse.get("response")).get("data")),
                        new TypeReference<List<GiftReturnPointResponseDto>>() {}
                );
            }
            return null;
        } catch (JsonProcessingException ex) {
            log.error("Error when parse request to api", ex);
            throw new RuntimeException(ex.getMessage());
        }
    }
}
