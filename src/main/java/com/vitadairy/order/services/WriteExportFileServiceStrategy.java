package com.vitadairy.order.services;

import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.order.common.ExportTypeDefs;
import com.vitadairy.order.dtos.OrderDtoExportItem;
import com.vitadairy.order.dtos.request.ListOrderRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WriteExportFileServiceStrategy {
    private final WriteExportFileService<OrderDtoExportItem, ListOrderRequest> writeExportFileOrderService;

    public WriteExportFileService<?, ?> getProcessExportFileService(String type) {
        if (Objects.isNull(type)) {
            return null;
        }
        switch (type) {
            case ExportTypeDefs.EXPORT_ORDER:
                return writeExportFileOrderService;
            default:
                return null;
        }
    }
}
