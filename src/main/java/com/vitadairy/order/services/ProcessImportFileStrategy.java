package com.vitadairy.order.services;

import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.order.common.ImportTypeDefs;
import com.vitadairy.order.entities.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessImportFileStrategy {
    private final ProcessImportFileService<OsManualImportOrderTemp> osManualImportOrderTempProcessImportFileService;
    private final ProcessImportFileService<OsImportFullRedeliveryOrderTemp> osImportFullRedeliveryOrderTempProcessImportFileService;
    private final ProcessImportFileService<OsImportDeliveryProviderTemp> osImportDeliveryProviderTempProcessImportFileService;
    private final ProcessImportFileService<OsImportOrderGiftTemp> osImportOrderGiftTempProcessImportFileService;
    private final ProcessImportFileService<OsImportPartRedeliveryOrderTemp> osImportPartRedeliveryOrderTempProcessImportFileService;
    private final ProcessImportFileService<OsImportReturnPointTemp> osImportReturnPointTempProcessImportFileService;

    public ProcessImportFileService<?> getProcessImportFileService(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        switch (type) {
            case ImportTypeDefs.IMPORT_DELIVERY_CODE:
                return osManualImportOrderTempProcessImportFileService;
            case ImportTypeDefs.IMPORT_FULL_REDELIVERY_CODE:
                return osImportFullRedeliveryOrderTempProcessImportFileService;
            case ImportTypeDefs.IMPORT_DELIVERY_PROVIDER_CODE:
                return osImportDeliveryProviderTempProcessImportFileService;
            case ImportTypeDefs.IMPORT_ORDER_GIFT:
                return osImportOrderGiftTempProcessImportFileService;
            case ImportTypeDefs.IMPORT_PART_REDELIVERY_CODE:
                return osImportPartRedeliveryOrderTempProcessImportFileService;
            case ImportTypeDefs.IMPORT_ORDER_RETURN_POINT:
                return osImportReturnPointTempProcessImportFileService;
            default:
                return null;
        }
    }
}
