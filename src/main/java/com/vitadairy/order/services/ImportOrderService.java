package com.vitadairy.order.services;

import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.response.ImportResponse;
import com.vitadairy.main.services.ImportResponseDataService;
import com.vitadairy.main.services.StorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImportOrderService {

    private final StorageService storageFileService;
    private final ImportResponseDataService importResponseDataService;
    private final ProcessImportFileStrategy processImportFileStrategy;

    public ImportResponse doImport(MultipartFile multipartFile, String type) throws Exception {
        validateFile(multipartFile);
        String savedFilePath = storageFileService.saveMultipartFile(multipartFile);
        return importOrder(savedFilePath, type);
    }

    public ImportResponse importOrder(String fullFilePath, String type) throws Exception {
        File file = new File(fullFilePath);
        if (!file.exists()) {
            throw new ApplicationException("File not found.");
        }
        ProcessImportFileService<?> processImportFileService = processImportFileStrategy.getProcessImportFileService(type);
        if (processImportFileService == null) {
            throw new ApplicationException("Not support type: " + type);
        }
        return importResponseDataService.fromRawImportResp(processImportFileService.process(fullFilePath));
    }

    public void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new ApplicationException("Failed to store empty file.");
        }
        if (StringUtils.isEmpty(file.getOriginalFilename())) {
            throw new ApplicationException("File name invalid, can not check file.");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            throw new UnsupportedOperationException("Not support other file type than xlsx.");
        }
    }

}
