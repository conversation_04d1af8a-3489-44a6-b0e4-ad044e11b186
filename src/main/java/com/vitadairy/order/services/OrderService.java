package com.vitadairy.order.services;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.auth.utils.SecurityUtil;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.dto.CustomNotificationRequestDto;
import com.vitadairy.main.dto.NotificationData;
import com.vitadairy.main.dto.ParseQueryResponse;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.services.NotificationService;
import com.vitadairy.order.common.NotificationDefs;
import com.vitadairy.order.common.OsOrderErrorCode;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.common.OsOrderFactory;
import com.vitadairy.order.dtos.GetOrderDto;
import com.vitadairy.order.dtos.GetOrderGiftsDetailDto;
import com.vitadairy.order.dtos.GiftDto;
import com.vitadairy.order.dtos.OrderCountDto;
import com.vitadairy.order.dtos.OrderDto;
import com.vitadairy.order.dtos.OrderGiftDto;
import com.vitadairy.order.dtos.UserGiftDto;
import com.vitadairy.order.dtos.UserInfoDto;
import com.vitadairy.order.dtos.request.CreateOrderRequest;
import com.vitadairy.order.dtos.request.GetOrderGiftsDetailRequest;
import com.vitadairy.order.dtos.request.ListOrderByUserRequest;
import com.vitadairy.order.dtos.request.ListOrderRequest;
import com.vitadairy.order.dtos.request.UpdateOrderRequest;
import com.vitadairy.order.dtos.response.CreateOrderResponse;
import com.vitadairy.order.dtos.response.GetGiftResponseDto;
import com.vitadairy.order.dtos.response.GetUserGiftResponseDto;
import com.vitadairy.order.dtos.response.OrderDetailsResponse;
import com.vitadairy.order.dtos.response.OrderDetailsResponseDto;
import com.vitadairy.order.entities.OrderCount;
import com.vitadairy.order.entities.OrderGift;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.DeliveryProviderEnum;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.iservice.SaleForceService;
import com.vitadairy.order.mapper.OsOrderMapper;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import com.vitadairy.order.repositories.OsOrderRepository;
import com.vitadairy.zoo.entities.AdminActionHistory;
import com.vitadairy.zoo.entities.DeliveryOrder;
import com.vitadairy.zoo.enums.EnumAdminActionHistoryActionName;
import com.vitadairy.zoo.enums.EnumNameAdminMenuModule;
import com.vitadairy.zoo.enums.FeatureNoti;
import com.vitadairy.zoo.repositories.AdminActionHistoryRepository;
import com.vitadairy.zoo.repositories.LegacyDeliveryOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderService {

    private final ResponseFactory responseFactory;
    private final OsOrderRepository osOrderRepository;
    private final OsOrderCustomRepository osOrderCustomRepository;
    private final ParseSqlService parseSqlService;
    private final OsOrderMapper osOrderMapper;
    private final RestApiGiftService restApiGiftService;
    private final RestApiUserGiftService restApiUserGiftService;
    private final OsOrderFactory osOrderFactory;
    private final SaleForceService<OrderDto> deliveryServiceSaleForceService;
    private final AsyncOrderService asyncOrderService;

    private final LegacyDeliveryOrderRepository legacyOrderRepository;
    private final ApplicationContext applicationContext;
    private final NotificationService notificationService;
    private final AdminActionHistoryRepository adminActionHistoryRepository;

    public ResponseEntity<GetOrderDto> listOrder(ListOrderRequest request) {

        ParseQueryResponse parseQueryResponse = parseSqlService.parse(request);
        if (parseQueryResponse.getRc() != 0) {
            throw new IllegalArgumentException("Invalid search request, cannot parse sql res " + parseQueryResponse);
        }

        long totalCount = osOrderCustomRepository.countByQuerySql(parseQueryResponse.getCountQuery(), parseQueryResponse.getParameters());
        if (totalCount == 0) {
            return responseFactory.pageableDtoEmpty(request.getPage(), request.getSize());
        }

        List<OsOrder> orders = osOrderCustomRepository.findByQuerySql(parseQueryResponse.getSelectQuery(), parseQueryResponse.getParameters(),
                request.getPageable());
        if (CollectionUtils.isEmpty(orders)) {
            return responseFactory.pageableDtoEmpty(request.getPage(), request.getSize());
        }

        List<OrderDto> orderDtos = orders.stream().map(OrderDto::fromEntity).toList();
        GetOrderDto responseDto = GetOrderDto.orderBuilder()
                .items(orderDtos)
                .total(totalCount)
                .page(request.getPage())
                .pageSize(request.getSize())
                .build();
        return responseFactory.successDto(responseDto);
    }

    public List<OrderCountDto> getOrderCountByStatus() {
        AuthorizationResponse authRes = AuthenticationHelper.getCurrentUser();
        Integer userId = Objects.nonNull(authRes) ? authRes.getUserId().intValue() : null;
        String[] status = {OrderStatusCodeEnum.IN_PROCESS.toString(), OrderStatusCodeEnum.REQUEST_RECEIVED.toString(), OrderStatusCodeEnum.DELIVERING.toString(), OrderStatusCodeEnum.SUCCESS.toString(), OrderStatusCodeEnum.FAILURE.toString()};
        List<OrderCount> orderCountDtos = osOrderRepository.countByStatus(status, userId);
        List<OrderCountDto> result = new ArrayList<>();
        OrderStatusCodeEnum.getAll().forEach(orderStatusCodeEnum -> {
            Integer count = orderCountDtos.stream().filter(orderCountDto -> orderCountDto.getStatusCode().equals(orderStatusCodeEnum.toString())).map(OrderCount::getCount).findFirst().orElse(0);
            result.add(OrderCountDto.builder()
                    .statusCode(orderStatusCodeEnum.toString())
                    .count(count)
                    .build());
        });

        return result;
    }

    public ResponseEntity<GetOrderGiftsDetailDto> getOrderGiftsDetail(GetOrderGiftsDetailRequest request) {
        if (Objects.nonNull(request.getOsOrderId())) {
            Optional<OsOrder> oOrder = osOrderRepository.findById(request.getOsOrderId());
            if (oOrder.isEmpty()) {
                return responseFactory.pageableDtoEmpty(request.getPage(), request.getSize());
            }
            return fromOsOrderAndPage(oOrder.get(), request.getPageable());
        }
        if (!StringUtils.isEmpty(request.getOrderId())) {
            Optional<OsOrder> oOrder = osOrderRepository.findByOrderCode(request.getOrderId());
            if (oOrder.isEmpty()) {
                return responseFactory.pageableDtoEmpty(request.getPage(), request.getSize());
            }
            return fromOsOrderAndPage(oOrder.get(), request.getPageable());
        }
        return responseFactory.pageableDtoEmpty(request.getPage(), request.getSize());
    }

    @Transactional(rollbackFor = Exception.class, value = "orderTransactionManager")
    public void updateOrderDeliveryStatus(String deliveryOrderCode, OrderDeliveryStatusCodeEnum status, Long totalAmount, Date updatedAt, Object originalStatus, DeliveryProviderEnum deliveryProvider) {
        List<OsOrder> orders = osOrderRepository.findAllByExtraData(OsOrderExtraDataFieldDefs.DELIVERY_CODE, deliveryOrderCode);
        if (orders.isEmpty()) {
            throw new ApplicationException("Order not found", HttpStatus.NOT_FOUND);
        }
        OrderStatusCodeEnum orderStatusCode = OrderDeliveryStatusCodeEnum.toOrderStatus(status);
        orders.forEach(order -> {
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS, status);
            if (Objects.nonNull(totalAmount)) {
                order.getExtraData().put(OsOrderExtraDataFieldDefs.SHIPPING_FEE, totalAmount);
            }
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_UPDATED_TIME, updatedAt.getTime());
            order.getExtraData().put(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS, originalStatus);
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER, deliveryProvider.getCode());
            if (status != OrderDeliveryStatusCodeEnum.SUCCESS) {
                order.setStatusCode(Objects.nonNull(orderStatusCode) ? orderStatusCode.toString() : null);
            } else {
                order.setStatusCode(OrderStatusCodeEnum.DELIVERING.toString());
                order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERED_TIME, updatedAt.getTime());
                String tdId = Objects.nonNull(order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_CODE)) ?
                        order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_CODE).toString() :
                        Objects.nonNull(order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE)) ? order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE).toString() : null;
                pushNotificationSuccessDeliveryOrder(tdId, order.getUserId().longValue());
            }
        });
        osOrderRepository.saveAll(orders);
    }
    
    @Transactional(rollbackFor = Exception.class, value = "orderTransactionManager")
    public void updateOrderDeliveryStatus(String transactionDeliveryCode, OrderDeliveryStatusCodeEnum status, Long totalAmount, Date updatedAt, Object originalStatus, DeliveryProviderEnum deliveryProvider, String reason) {
        List<OsOrder> orders = osOrderRepository.findAllByExtraData(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE, transactionDeliveryCode);
        if (orders.isEmpty()) {
            throw new ApplicationException("Order not found", HttpStatus.NOT_FOUND);
        }

        // TODO: Check logic update order delivery status below -> move to common method
        OrderStatusCodeEnum orderStatusCode = OrderDeliveryStatusCodeEnum.toOrderStatus(status);
        orders.forEach(order -> {
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS, status);
            if (Objects.nonNull(totalAmount)) {
                order.getExtraData().put(OsOrderExtraDataFieldDefs.SHIPPING_FEE, totalAmount);
            }
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_UPDATED_TIME, updatedAt.getTime());
            order.getExtraData().put(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS, originalStatus);
            order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER, deliveryProvider.getCode());
            if (Objects.nonNull(reason)) {
                order.getExtraData().put(OsOrderExtraDataFieldDefs.REASON, reason);
            }
            order.setStatusCode(Objects.nonNull(orderStatusCode) ? orderStatusCode.toString() : null);
            if (status == OrderDeliveryStatusCodeEnum.SUCCESS) {
                order.setStatusCode(OrderStatusCodeEnum.SUCCESS.toString());
                order.getExtraData().put(OsOrderExtraDataFieldDefs.DELIVERED_TIME, updatedAt.getTime());
                String tdId = Objects.nonNull(order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_CODE)) ?
                        order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_CODE).toString() :
                        Objects.nonNull(order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE)) ? order.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE).toString() : null;
                pushNotificationDoneDeliveryOrder(tdId, order.getUserId().longValue());
            }
        });
        osOrderRepository.saveAll(orders);
    }

    private void pushNotificationSuccessDeliveryOrder(String orderCode, Long userId) {
        CustomNotificationRequestDto customNotificationRequestDto = CustomNotificationRequestDto.builder()
                .userIds(new Long[]{userId})
                .title(NotificationDefs.DELIVERY_ORDER_SUCCESS_TITLE)
                .content(String.format(NotificationDefs.DELIVERY_ORDER_SUCCESS_CONTENT, orderCode))
                .description(String.format(NotificationDefs.DELIVERY_ORDER_SUCCESS_DESCRIPTION, orderCode, orderCode))
                .data(NotificationData.builder().deeplink("").description(String.format(NotificationDefs.DELIVERY_ORDER_SUCCESS_DESCRIPTION, orderCode, orderCode)).build())
                .featureNoti(FeatureNoti.NOTI_STATUS_ORDER)
                .build();
        notificationService.push(customNotificationRequestDto);
    }

    private void pushNotificationDoneDeliveryOrder(String tdId, Long userId) {
        CustomNotificationRequestDto customNotificationRequestDto = CustomNotificationRequestDto.builder()
                .userIds(new Long[]{userId})
                .title(NotificationDefs.DELIVERY_ORDER_DONE_TITLE)
                .content(String.format(NotificationDefs.DELIVERY_ORDER_DONE_CONTENT, tdId))
                .description(String.format(NotificationDefs.DELIVERY_ORDER_DONE_DESCRIPTION, tdId, tdId))
                .data(NotificationData.builder().deeplink("").description(String.format(NotificationDefs.DELIVERY_ORDER_DONE_DESCRIPTION, tdId, tdId)).build())
                .featureNoti(FeatureNoti.NOTI_STATUS_ORDER)
                .build();
        notificationService.push(customNotificationRequestDto);
    }

    @Transactional(rollbackFor = Exception.class, value = "orderTransactionManager")
    public ResponseEntity<?> confirmOrder(Integer orderId) {
        var principal = AuthenticationHelper.getCurrentUser();
        if (Objects.isNull(principal)) {
            throw new ApplicationException("Cannot get current user");
        }
        Optional<OsOrder> oOrder = osOrderRepository.findById(orderId);
        if (oOrder.isEmpty()) {
            throw new ApplicationException("Order not found");
        }
        if (!oOrder.get().getUserId().equals(principal.getUserId().intValue())) {
            throw new ApplicationException(String.format("Order %s not belong to user %s", orderId, principal.getUserId()));
        }
        OsOrder order = oOrder.get();
        if(Objects.isNull(order.getExtraData()) || Objects.isNull(order.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS))
            || !order.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS).equals(OrderDeliveryStatusCodeEnum.SUCCESS.toString())){
            throw new ApplicationException("Order not delivered yet");
        }

        if(Objects.equals(order.getStatusCode(), OrderStatusCodeEnum.SUCCESS.toString())){
            throw new ApplicationException("Order already confirmed");
        }

        if (!Objects.equals(order.getStatusCode(), OrderStatusCodeEnum.SUCCESS.toString())) {
            order.setStatusCode(OrderStatusCodeEnum.SUCCESS.toString());
            osOrderRepository.save(order);
        }
        OrderDetailsResponseDto responseDto = OrderDetailsResponseDto
                .orderDetailsBuilder()
                .data(OrderDto.fromEntity(order))
                .build();
        return responseFactory.successDto(responseDto);
    }

    @Transactional(rollbackFor = Exception.class, value = "orderTransactionManager")
    public ResponseEntity<OrderDetailsResponseDto> updateOrder(Integer id, UpdateOrderRequest request) throws Exception {
        Optional<OsOrder> oOrder = osOrderRepository.findById(id);
        if (oOrder.isEmpty()) {
            throw new ApplicationException("Order not found");
        }
        OsOrder order = oOrder.get();
        BeanUtils.copyProperties(request, order);
        osOrderRepository.save(order);
        OrderDetailsResponseDto responseDto = OrderDetailsResponseDto
                .orderDetailsBuilder()
                .data(OrderDto.fromEntity(order))
                .build();
        return responseFactory.successDto(responseDto);
    }

    @Transactional(rollbackFor = Exception.class, value = "orderTransactionManager")
    public ResponseEntity<CreateOrderResponse> createOrder(CreateOrderRequest request) throws Exception {
        validateSctNumber(request);
        GetGiftResponseDto giftResponseDto = validateAndSearchGift(request);
        GetUserGiftResponseDto userGiftResponseDto = validateAndSearchUserGift(request);

        Map<Long, UserGiftDto> userGiftMap = userGiftResponseDto.getData().stream()
            .collect(Collectors.toMap(UserGiftDto::getId, Function.identity()));
        
        // update dynamic data for order gift
        for (OrderGift orderGift : request.getUserGiftSnapshot()) {
            UserGiftDto userGiftDto = userGiftMap.get(orderGift.getId().longValue());
            if (Objects.isNull(userGiftDto)) {
                throw new ApplicationException("User gift not found");
            }
            orderGift.setDynamicData(userGiftDto.getDynamicData());
        }

        AuthorizationResponse authRes = AuthenticationHelper.getCurrentUser();
        if (Objects.nonNull(authRes) && Objects.nonNull(authRes.getUserId())) {
            UserInfoDto userInfoDto = new UserInfoDto(authRes.getUserId(), authRes.getName(), authRes.getPhoneNumber());
            request.setUserInfoDto(userInfoDto);
        }

        List<GiftDto> giftDtos = giftResponseDto.getData();
        request.setGiftSnapshot(giftDtos);
        List<OrderGift> userGiftDtos = request.getUserGiftSnapshot();
        validateExpiredUserGift(userGiftDtos);
        validateGiftQuantity(userGiftDtos, giftDtos);
        userGiftDtos.forEach(orderGift -> orderGift.setStatus("IN_PROCESS"));

        List<OsOrder> orders = osOrderFactory.listFromCreateOrderRequest(request);
        if (CollectionUtils.isEmpty(orders)) {
            throw new ApplicationException("Cannot create order");
        }
        osOrderRepository.saveAll(orders);

        List<OrderDto> responseDtos = orders.stream()
                .map(OrderDto::fromEntity)
                .toList();
        CreateOrderResponse response = new CreateOrderResponse(responseDtos);
//        updateGifts(giftDtos);
        updateUserGiftsStatusAfterCreated(userGiftDtos, request.getUserId());

        deliveryServiceSaleForceService.log(responseDtos, AuthenticationHelper.getCurrentUser());

        return responseFactory.successDto(response);
    }

    private void validateSctNumber(CreateOrderRequest request){
        if(request.getUserGiftSnapshot().stream().anyMatch(userGift -> Objects.isNull(userGift.getGift()) || StringUtils.isEmpty(userGift.getGift().getSctNumber()))){
            throw new ApplicationException("Quà tặng đang được cập nhật", HttpStatus.BAD_REQUEST);
        }
    }

    private void validateExpiredUserGift(List<OrderGift> gifts){
        gifts.forEach(gift -> {
            if(Objects.nonNull(gift.getDynamicData()) && Objects.nonNull(gift.getDynamicData().get("expiryDate"))){
                Instant expiredDate = Instant.parse(gift.getDynamicData().get("expiryDate").toString());
                if (Objects.nonNull(expiredDate) && expiredDate.isBefore(Instant.now())) {
                    restApiUserGiftService.updateUserGiftStatus("", gift.getId(), "EXPIRED");
                    throw new ApplicationException("Quà đã hết hạn lên đơn", HttpStatus.BAD_REQUEST);
                }
            }
        });
    }

    public ResponseEntity<OrderDetailsResponse> details(Integer id, String orderCode, String deliveryCode) {
        if (Objects.nonNull(id)) {
            Optional<OsOrder> oOrder = osOrderRepository.findById(id);
            if (oOrder.isEmpty()) {
                return responseFactory.errorDto("Order not found");
            }
            return responseFactory.successDto(
                    OrderDetailsResponse.orderBuilder()
                            .data(OrderDto.fromEntity(oOrder.get()))
                            .build()
            );
        }
        if (!StringUtils.isEmpty(orderCode)) {
            Optional<OsOrder> oOrder = osOrderRepository.findByOrderCode(orderCode);
            if (oOrder.isEmpty()) {
                return responseFactory.errorDto("Order not found");
            }
            return responseFactory.successDto(
                    OrderDetailsResponse.orderBuilder()
                            .data(OrderDto.fromEntity(oOrder.get()))
                            .build()
            );
        }
        if (!StringUtils.isEmpty(deliveryCode)) {
            List<OsOrder> orders = osOrderRepository.findAllByExtraData(OsOrderExtraDataFieldDefs.DELIVERY_CODE, deliveryCode);
            if (CollectionUtils.isEmpty(orders)) {
                return responseFactory.errorDto("Order not found");
            }
            return responseFactory.successDto(
                    OrderDetailsResponse.orderBuilder()
                            .data(OrderDto.fromEntity(orders.getFirst()))
                            .build()
            );
        }
        throw new ApplicationException("Not found any param to search order");
    }

    public ResponseEntity<GetOrderDto> listUserOrder(ListOrderByUserRequest request) {
        Page<OsOrder> orders = osOrderRepository.findOsOrderByUserIdAnAndStatusCode(request.getUserId(), request.getStatusCode(), request.getPageable(Sort.by("updatedAt")));
        if (CollectionUtils.isEmpty(orders.getContent())) {
            int page = request.getPage() != null ? request.getPage() : 0;
            int size = request.getSize() != null ? request.getSize() : -1;
            return responseFactory.pageableDtoEmpty(page, size);
        }

        List<OrderDto> orderDtos = orders.stream().map(OrderDto::fromEntity).toList();
        GetOrderDto responseDto = GetOrderDto.orderBuilder()
                .items(orderDtos)
                .total(orders.getTotalElements())
                .page(request.getPage())
                .pageSize(request.getSize() != null ? request.getSize() : -1)
                .build();
        return responseFactory.successDto(responseDto);
    }

    public ResponseEntity<GetOrderGiftsDetailDto> fromOsOrderAndPage(OsOrder osOrder, Pageable pageable) {
        if (osOrder.getUserGiftSnapshot() == null || osOrder.getUserGiftSnapshot().isEmpty()) {
            return responseFactory.pageableDtoEmpty(pageable.getPageNumber(), pageable.getPageSize());
        }
        List<OrderGiftDto> orderGiftDtoList = osOrder.getUserGiftSnapshot()
                .stream()
                .map(osOrderMapper::mapFromOrderGift)
//                .sorted(Comparator.comparing(OrderGiftDto::getGiftName))
                .toList();
        int start = pageable.getPageNumber() * pageable.getPageSize();
        List<OrderGiftDto> returnList = orderGiftDtoList.subList(start,
                Math.min(start + pageable.getPageSize(), orderGiftDtoList.size()));
        GetOrderGiftsDetailDto responseDto = GetOrderGiftsDetailDto.orderGiftsDetailBuilder()
                .orderCode(osOrder.getOrderCode())
                .items(returnList)
                .total((long) orderGiftDtoList.size())
                .page(pageable.getPageNumber())
                .pageSize(pageable.getPageSize())
                .build();
        return responseFactory.successDto(responseDto);
    }

    private GetGiftResponseDto validateAndSearchGift(CreateOrderRequest request) throws Exception {

        List<Integer> giftIds = request.getUserGiftSnapshot()
                .stream().map(val -> val.getGift().getId()).toList();
        Map<String, String> queries = new HashMap<>();
        queries.put("ids", giftIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        GetGiftResponseDto giftResponseDto = restApiGiftService.getGifts("", queries, 0, giftIds.size());
        if (Objects.isNull(giftResponseDto)) {
            throw new ApplicationException("Cannot get gift information");
        }
        return giftResponseDto;
    }

    private GetUserGiftResponseDto validateAndSearchUserGift(CreateOrderRequest request) throws Exception {
        List<Long> userGiftIds = request.getUserGiftSnapshot()
                .stream().map(val -> val.getId().longValue()).toList();
        GetUserGiftResponseDto userGiftResponseDto = restApiUserGiftService.getUserGifts("", userGiftIds);
        if (Objects.isNull(userGiftResponseDto)) {
            throw new ApplicationException("Cannot get user gift information");
        }
        return userGiftResponseDto;
    }

    @SuppressWarnings("deprecation")
    private void validateGiftQuantity(List<OrderGift> userGiftDtos, List<GiftDto> giftDtos) throws ApplicationException {
        for (OrderGift val : userGiftDtos) {
            Optional<GiftDto> oGift = giftDtos.stream()
                    .filter(giftDto -> giftDto.getId().equals(val.getGift().getId()))
                    .findFirst();
            if (oGift.isEmpty()) {
                throw new ApplicationException("Gift not found");
            }
            GiftDto gift = oGift.get();
            if (gift.getInventory() < val.getQuantity()) {
                throw new ApplicationException(OsOrderErrorCode.GIFTS_OUT_OF_STOCK, HttpStatus.METHOD_FAILURE);
            }
            gift.setInventory(gift.getInventory() - val.getQuantity());
        }
    }

    //    @Async
    public void deleteUserGifts(List<OrderGift> userGiftDtos) {
        userGiftDtos.forEach(giftDto -> restApiUserGiftService.deleteUserGift("", giftDto.getId()));
    }

    public void updateUserGiftsStatusAfterCreated(List<OrderGift> userGiftDtos, Integer userId) {
        List<Integer> userGiftIds = userGiftDtos.stream().map(OrderGift::getId).toList();
        try {
            this.updateUserGiftsStatus(
                    userGiftIds,
                    "IN_PROCESS",
                    Boolean.TRUE
            );

//            if (List.of(137137).contains(userId)) {
//                throw new ApplicationException("Test create order fail and lose data", HttpStatus.BAD_REQUEST);
//            }
        } catch (Exception e) {
            this.updateUserGiftsStatus(
                    userGiftIds,
                    "PENDING",
                    Boolean.FALSE
            );

            throw e;
        }
//        userGiftDtos.forEach(giftDto -> restApiUserGiftService.updateUserGiftStatus("", giftDto.getId(), "IN_PROCESS"));
    }

    private void updateUserGiftsStatus(
            List<Integer> userGiftIds,
            String status,
            Boolean throwException
    ) {
        try {
            userGiftIds.forEach(userGiftId -> restApiUserGiftService.updateUserGiftStatus("", userGiftId, status));
        } catch (Exception e) {
            if (throwException.equals(Boolean.TRUE)) {
                throw e;
            }
        }
    }

    //    @Async
    public void updateGifts(List<GiftDto> giftDtos) {
        giftDtos.forEach(giftDto -> {
            giftDto.setStartDate(null);
            giftDto.setEndDate(null);
            restApiGiftService.updateGift("", giftDto.getId(), giftDto);
        });
    }

    public boolean toggleShowShippingFee(Integer id, boolean isShowShippingFee) {
        Optional<OsOrder> oOrder = osOrderRepository.findById(id);
        if (oOrder.isEmpty()) {
            throw new ApplicationException("Order not found", HttpStatus.NOT_FOUND);
        }
        OsOrder order = oOrder.get();
        Map<String, Object> extraData = order.getExtraData();
        if (CollectionUtils.isEmpty(extraData)) {
            extraData = new HashMap<>();
        }
        boolean currentShowShippingFee = (boolean) extraData.getOrDefault(OsOrderExtraDataFieldDefs.IS_SHOW_SHIPPING_FEE, false);
        if (currentShowShippingFee == isShowShippingFee) {
            return false;
        }
        extraData.put(OsOrderExtraDataFieldDefs.IS_SHOW_SHIPPING_FEE, isShowShippingFee);
        order.setExtraData(extraData);
        osOrderRepository.save(order);

        try {
            AdminActionHistory adminActionHistory = new AdminActionHistory();
            adminActionHistory.setAdminName(SecurityUtil.getCurrentAdminEmail());
            adminActionHistory.setAdminMenu(EnumNameAdminMenuModule.QUAN_LY_DON_HANG.getLabel());
            adminActionHistory.setAdminAction(EnumAdminActionHistoryActionName.UPDATE.getLabel());
            adminActionHistory.setRecordIdentity(order.getOrderCode());
            this.adminActionHistoryRepository.save(adminActionHistory);
        } catch (Exception e) {
            log.error("Failed to log admin action: {}", e.getMessage(), e);
        }

        return true;
    }

    public List<OrderDto> findListOrderDto(ListOrderRequest request) {

        ParseQueryResponse parseQueryResponse = parseSqlService.parse(request);
        if (parseQueryResponse.getRc() != 0) {
            throw new IllegalArgumentException("Invalid search request, cannot parse sql res " + parseQueryResponse);
        }

        long totalCount = osOrderCustomRepository.countByQuerySql(parseQueryResponse.getCountQuery(), parseQueryResponse.getParameters());
        if (totalCount == 0) {
            return List.of();
        }

        List<OsOrder> orders = osOrderCustomRepository.findByQuerySql(parseQueryResponse.getSelectQuery(), parseQueryResponse.getParameters(),
                request.getPageable());
        if (CollectionUtils.isEmpty(orders)) {
            return List.of();
        }

        return orders.stream().map(OrderDto::fromEntity).toList();
    }

    public void confirmOrderByJob() {
        Long checkDate = Date.from(Instant.now().minus(7, ChronoUnit.DAYS)).getTime();
        List<OsOrder> orders = osOrderRepository.getOrderDelivered(checkDate);
        if (!CollectionUtils.isEmpty(orders)) {
            orders.forEach(order -> {
                order.setStatusCode(OrderStatusCodeEnum.SUCCESS.toString());
            });
            osOrderRepository.saveAll(orders);
            
        }
    }

    public void migrateOrder() {
        int pageNumber = 0;
        int pageSize = 100;
        //milliseconds
        String migrateFromTimestamp = System.getenv("MIGRATE_FROM");
        String migrateToTimestamp = System.getenv("MIGRATE_TO");
        Long fromTimestamp = null;
        Long toTimestamp = null;
        if (!StringUtils.isEmpty(migrateFromTimestamp)) {
            fromTimestamp = Long.parseLong(migrateFromTimestamp);
            log.info("Found settings to migrate from timestamp {} - date {}", fromTimestamp, Timestamp.from(Instant.ofEpochMilli(fromTimestamp)));
        }
        if (!StringUtils.isEmpty(migrateToTimestamp)) {
            toTimestamp = Long.parseLong(migrateToTimestamp);
            log.info("Found settings to migrate to timestamp {} - date {}", toTimestamp, Timestamp.from(Instant.ofEpochMilli(toTimestamp)));
        }

        // hardcode timestamp
        // TODO: migrate using ENV if have time
        fromTimestamp = 0L;
        toTimestamp = 1725123600000L;
        log.info("According to mr Nick, we will migrate from {} - to {}", Timestamp.from(Instant.ofEpochMilli(fromTimestamp)), Timestamp.from(Instant.ofEpochMilli(toTimestamp)));

        asyncOrderService.process();

        long total = legacyOrderRepository.countByAcceptedTimeGreaterThanEqualAndAcceptedTimeLessThanEqual(Timestamp.from(Instant.ofEpochMilli(fromTimestamp)),
                Timestamp.from(Instant.ofEpochMilli(toTimestamp)));
        log.info("Total orders to migrate: {}", total);
        AtomicInteger count = new AtomicInteger(0);

        MigrateOrderWorker runnableWorker = new MigrateOrderWorker(new AtomicInteger(pageNumber), pageSize,
                fromTimestamp, toTimestamp, count, true, applicationContext);
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        List<Future<?>> futures = new ArrayList<>();
        for (int index = -1; ++index < 5; ) {
            runnableWorker.setWorkerName("MigrateOrderWorker-" + index);
            Future<?> submitRes = executorService.submit(runnableWorker);
            futures.add(submitRes);
        }

        futures.forEach(future -> {
            try {
                future.get();
            } catch (Exception ex) {
                log.error("Error while waiting for future", ex);
            }
        });

        log.info("Finish migrating order, total order migrated: {}", count.get());
        asyncOrderService.stop(true);
        executorService.close();
    }

    private Page<DeliveryOrder> findOrder(Pageable pageable, Long millisecondTimestamp) {
        if (Objects.isNull(millisecondTimestamp)) {
            return legacyOrderRepository.findAll(pageable);
        }
        Timestamp timestamp = Timestamp.from(Instant.ofEpochMilli(millisecondTimestamp));
        log.info("{} timestamp vs {} from timeStamp", new Timestamp(millisecondTimestamp), timestamp);
        return legacyOrderRepository.findAllByAcceptedTimeGreaterThanEqual(timestamp, pageable);
    }

}
