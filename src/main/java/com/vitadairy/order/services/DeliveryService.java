package com.vitadairy.order.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.dto.ThirdPartyTransactionDto;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.dtos.DeliveryDetailDto;
import com.vitadairy.order.dtos.request.GhtkDeliveryUpdateRequest;
import com.vitadairy.order.dtos.request.SlgDeliveryUpdateRequest;
import com.vitadairy.order.dtos.request.VtpDeliveryUpdateRequest;
import com.vitadairy.order.dtos.response.DeliveryDetailsResponseDto;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.DeliveryProviderEnum;
import com.vitadairy.order.repositories.OsOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class DeliveryService {

    @Value("${shippingunit.hashed.key.ghtk}")
    private String hashKeyGhtk;
    @Value("${shippingunit.hashed.key.vtp}")
    private String hashKeyVtp;
    @Value("${shippingunit.hashed.key.slg}")
    private String hashKeySlg;

    private final OrderService orderService;
    private final OsOrderRepository osOrderRepository;
    private final WarehouseTransactionService warehouseTransactionService;
    private final ObjectMapper objectMapper;
    private final ResponseFactory responseFactory;

    public Object validateWebhookRequest(String hashKey, Object request) throws Exception {
        if (Objects.nonNull(hashKey)) {
            if (hashKey.equals(hashKeyGhtk)) {
                Gson gson = new GsonBuilder()
                        .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                        .create();
                GhtkDeliveryUpdateRequest ghtkDeliveryUpdateRequest = gson.fromJson(gson.toJson(request), GhtkDeliveryUpdateRequest.class);
                ghtkDeliveryUpdateRequest.doValidate();
                return ghtkDeliveryUpdateRequest;
            } else if (hashKey.equals(hashKeySlg)) {
                Gson gson = new GsonBuilder()
                        .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                        .create();
                SlgDeliveryUpdateRequest slgDeliveryUpdateRequest = gson.fromJson(gson.toJson(request), SlgDeliveryUpdateRequest.class);
                slgDeliveryUpdateRequest.doValidate();
                return slgDeliveryUpdateRequest;
            }
        } else {
            try {
                Gson gson = new GsonBuilder()
                        .setDateFormat("dd/MM/yyyy HH:mm:ss")
                        .create();
                VtpDeliveryUpdateRequest vtpDeliveryUpdateRequest = gson.fromJson(gson.toJson(request), VtpDeliveryUpdateRequest.class);
                if (Objects.nonNull(vtpDeliveryUpdateRequest) && vtpDeliveryUpdateRequest.getToken().equals(hashKeyVtp)) {
                    vtpDeliveryUpdateRequest.doValidate();
                    return vtpDeliveryUpdateRequest;
                }
            } catch (Exception e) {
                log.error("cannot parse to vtp request", e.getMessage());
            }
        }
        log.error("Invalid request with body: {}", request);
        throw new ApplicationException("Invalid request", HttpStatus.BAD_REQUEST);
    }

    private void logHistory(String transactionCode, Map request, Map response, TransactionStatus status, String note) {
        try {
            ThirdPartyTransactionDto history = new ThirdPartyTransactionDto();
            history.setTransactionCode(transactionCode);
            history.setRequest(request);
            history.setResponse(response);
            history.setStatus(status);
            history.setNotes(note);
            warehouseTransactionService.send3rdPartyTransaction(history);
        } catch (Exception e) {
            log.error("Cannot log history for transaction {}", transactionCode, e);
        }
    }

    public void updateDeliveryStatus(String hashKey, Object requestData) {
        String transactionCode = null;
        Map requestJson = objectMapper.convertValue(requestData, Map.class);
        Map responseJson = null;
        TransactionStatus status = null;
        Object request = null;
        
        try {
            request = this.validateWebhookRequest(hashKey, requestData);
        } catch (Exception e) {
            throw new ApplicationException("Invalid request", HttpStatus.BAD_REQUEST);
        }

        try{
            if (request instanceof GhtkDeliveryUpdateRequest ghtkDeliveryUpdateRequest) {
                transactionCode = ghtkDeliveryUpdateRequest.getOrderNumber();
                orderService.updateOrderDeliveryStatus(ghtkDeliveryUpdateRequest.getOrderNumber(),
                        ghtkDeliveryUpdateRequest.getStatus(), null, ghtkDeliveryUpdateRequest.getUpdateAt()
                        , ghtkDeliveryUpdateRequest.getStatusId(), DeliveryProviderEnum.GHTK);
                responseJson = objectMapper.convertValue(new ResponseEntity<>(HttpStatus.OK), Map.class);
                status = TransactionStatus.SUCCESS;
            } else if (request instanceof VtpDeliveryUpdateRequest vtpDeliveryUpdateRequest) {
                transactionCode = vtpDeliveryUpdateRequest.getData().getOrderNumber();
                orderService.updateOrderDeliveryStatus(vtpDeliveryUpdateRequest.getData().getOrderNumber(),
                        vtpDeliveryUpdateRequest.getData().getStatus(), vtpDeliveryUpdateRequest.getData().getTotalAmount()
                        , vtpDeliveryUpdateRequest.getData().getOrderStatusDate(), vtpDeliveryUpdateRequest.getData().getOrderStatus(), DeliveryProviderEnum.VTP);
                responseJson = objectMapper.convertValue(new ResponseEntity<>(HttpStatus.OK), Map.class);
                status = TransactionStatus.SUCCESS;
            } else if (request instanceof SlgDeliveryUpdateRequest slgDeliveryUpdateRequest) {
                transactionCode = slgDeliveryUpdateRequest.getAttributes().getTrackingCodeSap();
                Date currentDate = new Date();
                orderService.updateOrderDeliveryStatus(transactionCode,
                        slgDeliveryUpdateRequest.getMappedStatus(), slgDeliveryUpdateRequest.getShippingFee(), currentDate, 
                        slgDeliveryUpdateRequest.getStatus(), DeliveryProviderEnum.SLG, slgDeliveryUpdateRequest.getReason());
                responseJson = objectMapper.convertValue(new ResponseEntity<>(HttpStatus.OK), Map.class);
                status = TransactionStatus.SUCCESS;
            }
        }
        catch (Exception ex){
            status = TransactionStatus.FAILED;
            responseJson = objectMapper.convertValue(responseFactory.errorDto(ex.getMessage()), Map.class);
        }
        finally {
            logHistory(transactionCode, requestJson, responseJson, status, null);
        }
    }

    public ResponseEntity<DeliveryDetailsResponseDto> getDeliveryInfo(String trackingCode) {
        if (Objects.nonNull(trackingCode)) {
            List<OsOrder> orders = osOrderRepository.findAllByExtraData(OsOrderExtraDataFieldDefs.DELIVERY_CODE, trackingCode);
            if (!CollectionUtils.isEmpty(orders) && orders.size() == 1) {
                OsOrder order = orders.get(0);
                return ResponseEntity.ok(DeliveryDetailsResponseDto.deliveryDetailsBuilder()
                        .data(DeliveryDetailDto.fromOrder(order))
                        .build());
            }
        }
        throw new ApplicationException("Invalid tracking code", HttpStatus.BAD_REQUEST);
    }
}
