package com.vitadairy.order.services;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.dtos.GetHistoryReturnPointDto;
import com.vitadairy.order.dtos.HistoryReturnPointDto;
import com.vitadairy.order.dtos.HistoryReturnPointExportDto;
import com.vitadairy.order.dtos.OrderGiftDto;
import com.vitadairy.order.dtos.ReturnPointMappingDto;
import com.vitadairy.order.dtos.request.HistoryReturnPointRequest;
import com.vitadairy.order.entities.OsHistoryReturnPoint;
import com.vitadairy.order.enums.HistoryReturnPointStatusEnum;
import com.vitadairy.order.repositories.OsHistoryReturnPointRepository;
import com.opencsv.CSVWriter;
import lombok.RequiredArgsConstructor;

import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PointService {

    private final OsHistoryReturnPointRepository osHistoryReturnPointRepository;
    private final ReturnPointReasonMappingService returnPointReasonMappingService;
    private final ResponseFactory responseFactory;

    public ResponseEntity<GetHistoryReturnPointDto> listHistoryReturnPoint(HistoryReturnPointRequest request) {
        request.cleanEmpty();
        Page<OsHistoryReturnPoint> historyReturnPoints = osHistoryReturnPointRepository.findHistoryReturnPoint(request, request.getPageable());
        GetHistoryReturnPointDto responseDto = GetHistoryReturnPointDto.historyReturnPointBuilder()
                .items(historyReturnPoints.getContent().stream().map(this::fromEntity).toList())
                .total(historyReturnPoints.getTotalElements())
                .page(request.getPage())
                .pageSize(request.getSize())
                .build();

        return responseFactory.successDto(responseDto);
    }
    
    public ResponseEntity<InputStreamResource> exportHistoryReturnPoint(HistoryReturnPointRequest request) throws IOException {
        request.cleanEmpty();
        
        Page<OsHistoryReturnPoint> historyReturnPoints = osHistoryReturnPointRepository.findHistoryReturnPoint(request, Pageable.unpaged());
        Set<Integer> reasons = historyReturnPoints.getContent().stream()
                .map(point -> {
                    try {
                        return Integer.parseInt(point.getReason());
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Integer, String> reasonMapping = returnPointReasonMappingService.getReasonForNotificationMap(reasons);

        List<HistoryReturnPointExportDto> exportData = historyReturnPoints.getContent().stream()
                .map(entity -> fromEntityToExportDto(entity, reasonMapping))
                .toList();
        
        ByteArrayInputStream csvData = generateCsvData(exportData);
        
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=history_return_point.xlsx");
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(csvData));
    }
    
    private ByteArrayInputStream generateCsvData(List<HistoryReturnPointExportDto> exportData) throws IOException {
        final String[] CSV_HEADERS = {
                "ID", "MVĐ", "Transaction ID", "Order ID", "Gift Code", "Tên quà", 
                "User ID", "Khách hàng", "SL quà", "Số xu hoàn", "Trạng thái", "Ngày hoàn xu", "Lý do hoàn"
        };
        
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        CSVWriter csvWriter = new CSVWriter(new OutputStreamWriter(out));
        
        // Write header
        csvWriter.writeNext(CSV_HEADERS);
        
        // Write data rows
        for (HistoryReturnPointExportDto data : exportData) {
            String[] rowData = new String[CSV_HEADERS.length];
            rowData[0] = data.getId() != null ? data.getId().toString() : "";
            rowData[1] = data.getDeliveryCode() != null ? data.getDeliveryCode() : "";
            rowData[2] = data.getTransactionCode() != null ? data.getTransactionCode() : "";
            rowData[3] = data.getOrderCode() != null ? data.getOrderCode() : "";
            rowData[4] = data.getGiftId() != null ? data.getGiftId().toString() : "";
            rowData[5] = data.getGiftName() != null ? data.getGiftName() : "";
            rowData[6] = data.getUserId() != null ? data.getUserId().toString() : "";
            rowData[7] = data.getUserInfo() != null ? data.getUserInfo() : "";
            rowData[8] = data.getTotalGifts() != null ? data.getTotalGifts().toString() : "";
            rowData[9] = data.getTotalPoint() != null ? data.getTotalPoint().toString() : "";
            rowData[10] = data.getStatusDescription() != null ? data.getStatusDescription() : "";
            rowData[11] = data.getReturnDate() != null ? data.getReturnDate().toString() : "";
            rowData[12] = data.getReason() != null ? data.getReason() : "";
            
            csvWriter.writeNext(rowData);
        }
        
        csvWriter.close();
        return new ByteArrayInputStream(out.toByteArray());
    }

    private HistoryReturnPointDto fromEntity(OsHistoryReturnPoint entity){
        String reason = null;
        if(isInteger(entity.getReason())){
            ReturnPointMappingDto reasonMapping = returnPointReasonMappingService.findByCode(Integer.valueOf(entity.getReason()));
            if(Objects.nonNull(reasonMapping)){
                reason = reasonMapping.getReasonForNotification();
            }
        } else {
            reason = entity.getReason();
        }

        HistoryReturnPointStatusEnum status = HistoryReturnPointStatusEnum.fromCode(entity.getStatus());
        return HistoryReturnPointDto.builder()
                .id(entity.getId())
                .returnDate(entity.getCreatedAt())
                .orderCode(entity.getOrderCode())
                .transactionCode(entity.getTransactionCode())
                .userGiftSnapshot(OrderGiftDto.fromEntity(entity.getGiftSnapshot()))
                .deliveryCode(entity.getDeliveryCode())
                .totalGifts(entity.getTotalGift())
                .totalPoint(entity.getTotalPoint())
                .status(status)
                .statusDescription(Objects.nonNull(status) ? status.getName() : null)
                .userId(entity.getUserId())
                .userInfo(entity.getUserSnapshot())
                .reason(reason)
                .build();
    }
    
    private HistoryReturnPointExportDto fromEntityToExportDto(OsHistoryReturnPoint entity, Map<Integer, String> reasonMapping) {
        String reason = null;
        if(isInteger(entity.getReason())){
            reason = reasonMapping.get(Integer.valueOf(entity.getReason()));
        } else {
            reason = entity.getReason();
        }
        
        HistoryReturnPointExportDto dto = HistoryReturnPointExportDto.fromEntity(entity);
        dto.setReason(reason);
        
        // Set gift details from gift snapshot if available
        if (entity.getGiftSnapshot() != null && entity.getGiftSnapshot().getGift() != null) {
            var gift = entity.getGiftSnapshot().getGift();
            dto.setGiftId(gift.getId());
            dto.setGiftName(gift.getName());
        }
        
        return dto;
    }

    private boolean isInteger(String value) {
        if (value == null) {
            return false;
        }
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
