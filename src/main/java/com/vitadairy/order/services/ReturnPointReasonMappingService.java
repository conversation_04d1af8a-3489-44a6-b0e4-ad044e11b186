package com.vitadairy.order.services;

import com.vitadairy.order.dtos.ReturnPointMappingDto;
import com.vitadairy.order.entities.OsReturnPointReasonMapping;
import com.vitadairy.order.repositories.OsReturnPointReasonMappingRepository;
import lombok.RequiredArgsConstructor;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReturnPointReasonMappingService {

    private final OsReturnPointReasonMappingRepository osHistoryReturnPointRepository;

    public ReturnPointMappingDto findByCode(Integer code) {
        OsReturnPointReasonMapping osReturnPointReasonMapping = osHistoryReturnPointRepository.findById(code).orElse(null);
        if (osReturnPointReasonMapping == null) {
            return null;
        }
        return fromEntity(osReturnPointReasonMapping);
    }

    public Map<Integer, String> getReasonForNotificationMap(Set<Integer> codes) {
        return osHistoryReturnPointRepository.findByCodeIn(codes).stream()
                .collect(Collectors.toMap(OsReturnPointReasonMapping::getCode, OsReturnPointReasonMapping::getReasonForNotification));
    }

    private ReturnPointMappingDto fromEntity(OsReturnPointReasonMapping entity) {
        return ReturnPointMappingDto.builder()
                .code(entity.getCode())
                .description(entity.getDescription())
                .reasonForNotification(entity.getReasonForNotification())
                .build();
    }
}
