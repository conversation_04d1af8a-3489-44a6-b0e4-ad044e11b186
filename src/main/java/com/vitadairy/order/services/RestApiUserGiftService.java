package com.vitadairy.order.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.main.response.RestApiBaseResponse;
import com.vitadairy.order.dtos.response.GetUserGiftResponseDto;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RestApiUserGiftService {

    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;

    public RestApiUserGiftService(@Qualifier("restTemplateOsGiftClient") RestTemplateClient restTemplateClient, 
                                    ObjectMapper objectMapper){
        this.restTemplateClient = restTemplateClient;
        this.objectMapper = objectMapper;
    }

    public RestApiBaseResponse deleteUserGift(String token, Integer id) {
        restTemplateClient.buildAuth(token);
        return restTemplateClient.delete("/in/gs/user-gifts/" + id, null, new ParameterizedTypeReference<>() {});
    }

    public GetUserGiftResponseDto getUserGifts(String token, List<Long> ids) {
        restTemplateClient.buildAuth(token);
        var queries = new LinkedMultiValueMap<String, String>();
        queries.add("ids", ids.stream().map(String::valueOf).collect(Collectors.joining(",")));
        Map<String, Object> apiResponse = restTemplateClient.get("/in/gs/user-gifts", queries, new ParameterizedTypeReference<>() {});
        log.debug("apiResponse: {}", apiResponse);
        try {
            return objectMapper.readValue(objectMapper.writeValueAsString(apiResponse), GetUserGiftResponseDto.class);
        } catch (JsonProcessingException ex) {
            log.error("Error when parse response from api", ex);
            throw new ApplicationException(ex.getMessage());
        }
    }

    public RestApiBaseResponse updateUserGiftStatus(String token, Integer id, String status) {
        restTemplateClient.buildAuth(token);
        Map<String, Object> request = new HashMap<>();
        request.put("status", status);
        return restTemplateClient.put("/in/gs/user-gifts/" + id + "/status", status, new ParameterizedTypeReference<>() {});
    }
}
