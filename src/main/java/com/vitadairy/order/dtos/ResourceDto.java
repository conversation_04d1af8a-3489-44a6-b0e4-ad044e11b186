package com.vitadairy.order.dtos;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class ResourceDto {
    private String code;
    private String name;
    private String description;

    public static ResourceDto fromOrderStatusCodeEnum(OrderStatusCodeEnum item) {
        return ResourceDto.builder()
                .code(item.toString())
                .name(item.name)
                .description(item.description)
                .build();
    }

    public static ResourceDto fromOrderTypeCodeEnum(OrderTypeCodeEnum item) {
        return ResourceDto.builder()
                .code(item.toString())
                .name(item.name)
                .description(item.description)
                .build();
    }

    public static ResourceDto fromOrderDeliveryStatusCodeEnum(OrderDeliveryStatusCodeEnum item) {
        return ResourceDto.builder()
                .code(item.toString())
                .name(item.getName())
                .description(item.getDescription())
                .build();
    }

    public static ResourceDto fromDeliveryProviderInfo(JsonElement json) {
        JsonObject jsonObject = json.getAsJsonObject();
        String code = jsonObject.has(ConstantFieldDefs.ID)
                ? jsonObject.get(ConstantFieldDefs.ID).getAsString() : null;
        String name = jsonObject.has(ConstantFieldDefs.NAME)
                ? jsonObject.get(ConstantFieldDefs.NAME).getAsString() : null;
        String description = jsonObject.has(ConstantFieldDefs.DESCRIPTION)
                ? jsonObject.get(ConstantFieldDefs.DESCRIPTION).getAsString() : null;
        return ResourceDto.builder()
                .code(code)
                .name(name)
                .description(description)
                .build();
    }

}
