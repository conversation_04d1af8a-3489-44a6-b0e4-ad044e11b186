package com.vitadairy.order.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vitadairy.main.serdes.GMT7DateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliverySFOrderGiftPayloadDto {
    @JsonProperty(value = "ref_id")
    private String refId;
    @JsonProperty(value = "order_id")
    private String orderId;
    @JsonProperty(value = "status_user_gift")
    private String statusUserGift;
    @JsonProperty(value = "recipientName")
    private String recipientName;
    @JsonProperty(value = "recipientPhone")
    private String recipientPhone;
    @JsonProperty(value = "recipientAddress")
    private String recipientAddress;
    @JsonProperty(value = "Billing_District__c")
    private String billingDistrict;
    @JsonProperty(value = "Billing_Province__c")
    private String billingProvince;
    @JsonProperty(value = "Billing_Province_New__c")
    private String billingNewProvince;
    @JsonProperty(value = "Billing_Street__c")
    private String billingStreet;
    @JsonProperty(value = "Billing_Ward__c")
    private String billingWard;
    @JsonProperty(value = "Billing_Ward_New__c")
    private String billingNewWard;
    @JsonProperty(value = "accepted_date")
    @JsonSerialize(using = GMT7DateSerializer.class)
    private Date acceptedDate;
    @JsonProperty(value = "Delivery_Id__c")
    private String deliveryId;
    @JsonProperty(value = "transport_service")
    private String transportService;
    @JsonProperty(value = "order_status")
    private String orderStatus;
    @JsonProperty(value = "Delivery_Date__c")
    private String deliveryDate;
}
