package com.vitadairy.order.dtos;

import com.vitadairy.main.dto.BaseDto;
import com.vitadairy.order.entities.OsConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
@Builder
public class ConstantDto extends BaseDto {
    private Integer id;
    private String keyAlpha;
    private String keyBeta;
    private String value;
    private Integer priority;
    private Map<String, String> dynamicData;
    private Boolean isActive;

    public static ConstantDto fromEntity(OsConstant entity) {
        return ConstantDto.builder()
                .id(entity.getId())
                .keyAlpha(entity.getKeyAlpha())
                .keyBeta(entity.getKeyBeta())
                .value(entity.getValue())
                .priority(entity.getPriority())
                .dynamicData(entity.getDynamicData())
                .isActive(entity.getIsActive())
                .build();
    }
}
