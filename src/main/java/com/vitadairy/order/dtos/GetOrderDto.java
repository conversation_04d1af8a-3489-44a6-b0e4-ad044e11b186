package com.vitadairy.order.dtos;

import com.vitadairy.main.response.PageableResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatusCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class GetOrderDto extends PageableResponse<OrderDto> {

    @Builder(builderMethodName = "orderBuilder")
    public GetOrderDto(List<OrderDto> items, Long total, Integer page, Integer pageSize,
                       HttpStatusCode statusCode, String msg) {
        super(items, total, 0L, page, pageSize, statusCode, msg);
    }

}
