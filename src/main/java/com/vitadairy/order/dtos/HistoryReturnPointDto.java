package com.vitadairy.order.dtos;

import com.vitadairy.order.entities.OsHistoryReturnPoint;
import com.vitadairy.order.enums.HistoryReturnPointStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.Map;
import java.util.Objects;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistoryReturnPointDto {
    private Integer id;
    private Instant returnDate;
    private String orderCode;
    private String transactionCode;
    private OrderGiftDto userGiftSnapshot;
    private Map<String, String> userInfo;
    private String deliveryCode;
    private Integer totalGifts;
    private Integer totalPoint;
    private HistoryReturnPointStatusEnum status;
    private String statusDescription;
    private Integer userId;
    private String reason;

    public static HistoryReturnPointDto fromEntity(OsHistoryReturnPoint entity){
        HistoryReturnPointStatusEnum status = HistoryReturnPointStatusEnum.fromCode(entity.getStatus());
        return HistoryReturnPointDto.builder()
                .id(entity.getId())
                .returnDate(entity.getCreatedAt())
                .orderCode(entity.getOrderCode())
                .transactionCode(entity.getTransactionCode())
                .userGiftSnapshot(OrderGiftDto.fromEntity(entity.getGiftSnapshot()))
                .deliveryCode(entity.getDeliveryCode())
                .totalGifts(entity.getTotalGift())
                .totalPoint(entity.getTotalPoint())
                .status(status)
                .statusDescription(Objects.nonNull(status) ? status.getName() : null)
                .userId(entity.getUserId())
                .userInfo(entity.getUserSnapshot())
                .reason(entity.getReason())
                .build();
    }
}
