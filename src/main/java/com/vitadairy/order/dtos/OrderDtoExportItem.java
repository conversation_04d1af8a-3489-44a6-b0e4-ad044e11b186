package com.vitadairy.order.dtos;

import com.vitadairy.main.common.RecipientFieldDefs;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDtoExportItem {
    private Integer id;
    private Date createdAt;
    private String orderCode;
    private String transactionDeliveryId;
    private String recipientInfo;
    private String recipientPhone;
    private String recipientName;
    private Integer userId;
    private String creatorPhoneNumber;
    private String deliveryNumber;
    private String deliveryProviderId;
    private String deliveryCreatedTime;
    private String deliveryProviderStatusDescription;
    private String statusCode;
    private Boolean isShowShippingFee;
    private Double shippingFee;
    private String previousOrderCode;
    private String orderType;
    private Integer totalGifts;
    // userGift transactionCode
    private String transactionExternalId;
    private String giftName;
    private String giftCode;
    private Integer point;
    private Integer quantity;
    private Integer quantityDelivered;

    public static List<OrderDtoExportItem> fromOrder(OrderDto orderDto) {
        Date createdAt = Optional.ofNullable(orderDto.getCreatedAt())
                .map(val -> val.getEpochSecond() * 1000L)
                .map(Date::new)
                .orElse(null);
        String recipientPhone = orderDto.getCustomerInfo().get(RecipientFieldDefs.RECIPIENT_PHONE);
        String recipientName = orderDto.getCustomerInfo().get(RecipientFieldDefs.RECIPIENT_NAME);
        String customerInfo = recipientPhone + "\n" + recipientName;
        return orderDto.getUserGiftSnapshot().stream().map(userGift -> OrderDtoExportItem.builder()
                .id(orderDto.getId())
                .createdAt(createdAt)
                .orderCode(orderDto.getOrderCode())
                .transactionDeliveryId(orderDto.getTdId())
                .recipientName(recipientName)
                .recipientPhone(recipientPhone)
                .recipientInfo(customerInfo)
                .userId(orderDto.getUserId())
                .creatorPhoneNumber(orderDto.getCreatorPhoneNumber())
                .deliveryNumber(orderDto.getDeliveryNumber())
                .deliveryProviderId(orderDto.getDeliveryProviderId())
                .deliveryCreatedTime(orderDto.getDeliveryCreatedTime())
                .deliveryProviderStatusDescription(orderDto.getDeliveryProviderStatusDescription())
                .statusCode(orderDto.getStatusCode() != null ? orderDto.getStatusCode().getName() : null)
                .isShowShippingFee(orderDto.getIsShowShippingFee())
                .shippingFee(orderDto.getShippingFee())
                .previousOrderCode(orderDto.getPreviousOrderCode())
                .orderType(orderDto.getTypeCode() != null ? orderDto.getTypeCode().getName() : null)
                .totalGifts(orderDto.getTotalGifts())
                .transactionExternalId(userGift.getTransactionCode())
                .giftName(userGift.getGift().getName())
                .giftCode(userGift.getId() + "")
                .point(userGift.getPoint())
                .quantity(userGift.getQuantity())
                .quantityDelivered(userGift.getQuantityDelivered())
                .build()
        ).toList();
    }
}
