package com.vitadairy.order.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vitadairy.main.serdes.GMT7DateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliverySFPayloadDto {
    @JsonProperty(value = "ref_id")
    private String refId;
    @JsonProperty(value = "order_id")
    private String orderId;
    @JsonProperty(value = "re_order_by")
    private String reOrderBy;
    @JsonProperty(value = "order_type")
    private String orderType;
    @JsonProperty(value = "transport_service")
    private String transportService;
    @Builder.Default
    @JsonProperty(value = "order_status")
    private String orderStatus = "1";
    @JsonProperty(value = "phone_number_user")
    private String phoneNumberUser;
    @JsonProperty(value = "phone_number_recipient")
    private String phoneNumberRecipient;
    @JsonProperty(value = "name_recipient")
    private String nameRecipient;
    @JsonProperty(value = "user_id")
    private String userId;
    @JsonProperty(value = "Date_of_Purchase__c")
    @JsonSerialize(using = GMT7DateSerializer.class)
    private Date dateOfPurchase;
    @Builder.Default
    @JsonProperty(value = "Order_By__c")
    private String orderBy = "User";
    @JsonProperty(value = "Billing_Country__c")
    private String billingCountry;
    @Builder.Default
    @JsonProperty(value = "Billing_Country_Name__c")
    private String billingCountryName = "Việt Nam";
    @JsonProperty(value = "Billing_Province__c")
    private String billingProvince;
    @JsonProperty(value = "Billing_Province_Name__c")
    private String billingProvinceName;
    @JsonProperty(value = "Billing_Province_New__c")
    private String billingNewProvince;
    @JsonProperty(value = "Billing_Province_Name_New__c")
    private String billingNewProvinceName;
    @JsonProperty(value = "Billing_District__c")
    private String billingDistrict;
    @JsonProperty(value = "Billing_District_Name__c")
    private String billingDistrictName;
    @JsonProperty(value = "Billing_Ward__c")
    private String billingWard;
    @JsonProperty(value = "Billing_Ward_Name__c")
    private String billingWardName;
    @JsonProperty(value = "Billing_Ward_New__c")
    private String billingNewWard;
    @JsonProperty(value = "Billing_Ward_Name_New__c")
    private String billingNewWardName;
    @JsonProperty(value = "Billing_Street__c")
    private String billingStreet;
    @JsonProperty(value = "Billing_Address__c")
    private String billingAddress;
    @JsonProperty(value = "Shipping_Country__c")
    private String shippingCountry;
    @JsonProperty(value = "Shipping_Province__c")
    private String shippingProvince;
    @JsonProperty(value = "Shipping_Province_New__c")
    private String shippingNewProvince;
    @JsonProperty(value = "Shipping_District__c")
    private String shippingDistrict;
    @JsonProperty(value = "Shipping_Ward__c")
    private String shippingWard;
    @JsonProperty(value = "Shipping_Ward_New__c")
    private String shippingNewWard;
    @JsonProperty(value = "Shipping_Country_Name__c")
    private String shippingCountryName;
    @JsonProperty(value = "Shipping_Province_Name__c")
    private String shippingProvinceName;
    @JsonProperty(value = "Shipping_Province_Name_New__c")
    private String shippingNewProvinceName;
    @JsonProperty(value = "Shipping_District_Name__c")
    private String shippingDistrictName;
    @JsonProperty(value = "Shipping_Ward_Name__c")
    private String shippingWardName;
    @JsonProperty(value = "Shipping_Ward_Name_New__c")
    private String shippingNewWardName;
    @JsonProperty(value = "Shipping_Street__c")
    private String shippingStreet;
    @JsonProperty(value = "Shipping_Address__c")
    private String shippingAddress;
    @JsonProperty(value = "Export_Date__c")
    @JsonSerialize(using = GMT7DateSerializer.class)
    private Date exportDate;
    @JsonProperty(value = "Delivery_Date__c")
    private String deliveryDate;
    @JsonProperty(value = "Delivery_Id__c")
    private String deliveryId;
    @JsonProperty(value = "Delivery_Cost__c")
    private Double deliveryCost;
    @JsonProperty(value = "Loss_Reason__c")
    private String lossReason;
    @JsonProperty(value = "Height__c")
    private Integer height;
    @JsonProperty(value = "Length__c")
    private Integer length;
    @JsonProperty(value = "Width__c")
    private Integer width;
    @JsonProperty(value = "total_quantity__c")
    private Integer totalQuantity;
    @JsonProperty(value = "redeem_point")
    private Integer redeemPoint;
    @JsonProperty(value = "accepted_date")
    @JsonSerialize(using = GMT7DateSerializer.class)
    private Date acceptedDate;
    @JsonProperty(value = "is_freeship")
    private Boolean isFreeShip;
    @JsonProperty(value = "gifts")
    private List<DeliverySFOrderGiftPayloadDto> gifts;
    @JsonProperty(value = "Program_Name__c")
    private String programName;
    @JsonProperty(value = "Loyalty_Member__c")
    private String loyaltyMember;
}
