package com.vitadairy.order.dtos;

import com.vitadairy.main.dto.BaseDto;
import com.vitadairy.order.entities.OrderGift;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderGiftDto extends BaseDto {
    private Integer id;
    private Integer userId;
    private GiftDto gift;
    private String status;
    private Integer quantity;
    private Integer quantityDelivered;
    private Integer quantityReDelivery;
    private Integer reservationPoint;
    private Integer point;
    private Integer returnedPoint;
    private String transactionCode;
    private Map<String, Object> dynamicData;

    public static OrderGiftDto fromEntity(OrderGift entity) {
        OrderGiftDto res = OrderGiftDto.builder()
                .id(entity.getId())
                .userId(entity.getUserId())
                .gift(entity.getGift())
                .status(entity.getStatus())
                .quantity(entity.getQuantity())
                .quantityDelivered(entity.getQuantityDelivered())
                .quantityReDelivery(entity.getQuantityReDelivery())
                .reservationPoint(entity.getReservationPoint())
                .point(entity.getPoint())
                .returnedPoint(entity.getReturnedPoint())
                .transactionCode(entity.getTransactionCode())
                .dynamicData(entity.getDynamicData())
                .build();
        res.setCreatedAt(entity.getCreatedAt());
        res.setUpdatedAt(entity.getUpdatedAt());
        return res;
    }
}
