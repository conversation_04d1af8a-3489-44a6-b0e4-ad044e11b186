package com.vitadairy.order.dtos.request;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.google.gson.annotations.SerializedName;
import com.vitadairy.main.request.BaseRequest;
import com.vitadairy.order.common.OsOrderDeliverySlgStatusMapping;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;

import lombok.Data;

@Data
public class SlgDeliveryUpdateRequest extends BaseRequest {
    private String owner;

    @SerializedName("sales_channel")
    private String salesChannel;

    @SerializedName("external_order_key")
    private String externalOrderKey;

    @SerializedName("estimate_ship_time")
    private String estimateShipTime;

    @SerializedName("estimate_delivery_time")
    private String estimateDeliveryTime;

    private List<String> tags;

    @SerializedName("warehouse_code")
    private String warehouseCode;

    private Attributes attributes;

    @SerializedName("shipping_fee")
    private Long shippingFee;

    private String note;

    @SerializedName("payment_method")
    private String paymentMethod;

    @SerializedName("customer_name")
    private String customerName;

    @SerializedName("customer_code")
    private String customerCode;

    @SerializedName("tax_code")
    private String taxCode;

    @SerializedName("customer_address_code")
    private String customerAddressCode;

    @SerializedName("address_shipping")
    private AddressShipping addressShipping;

    @SerializedName("outbound_order_type")
    private String outboundOrderType;

    @SerializedName("tracking_code")
    private String trackingCode;

    @SerializedName("GHTKnote")
    private String ghtkNote;

    @SerializedName("actual_ship_date")
    private String actualShipDate;

    private String status;

    private String reason;

    private OrderDeliveryStatusCodeEnum mappedStatus;

    @Override
    public void doValidate() throws Exception {
        if (this.attributes.getTrackingCodeSap() == null) {
            throw new Exception("Tracking code is required");
        }

        this.mappedStatus = this.fromCode(this.status);
        if(Objects.isNull(this.mappedStatus)){
            throw new IllegalArgumentException("Invalid status");
        }
    }

    @Data
    public static class AddressShipping {
        @SerializedName("lv0_name")
        private String lv0Name;

        @SerializedName("lv1_name")
        private String lv1Name;

        @SerializedName("lv2_name")
        private String lv2Name;

        @SerializedName("lv3_name")
        private String lv3Name;

        private String address;

        private String phone;

        private String name;
    }

    @Data
    public static class Attributes {
        @SerializedName("tracking_code_sap")
        private String trackingCodeSap;

        @SerializedName("external_order_key")
        private String externalOrderKey;
        
        @SerializedName("so_reward_app")
        private String soRewardApp;

        @SerializedName("created_by")
        private String createdBy;

        @SerializedName("created_time")
        private String createdTime;

        @SerializedName("created_on")
        private Date createdOn;

        @SerializedName("shipping_point")
        private String shippingPoint;

        private String route;

        @SerializedName("shipping_phone")
        private String shippingPhone;

        @SerializedName("shipping_receiver")
        private String shippingReceiver;

        @SerializedName("shipping_deliveryAddress")
        private String shippingDeliveryAddress;

        @SerializedName("shipping_code")
        private String shippingCode;

        @SerializedName("3plUser")
        private String threePlUser;
    }

    private OrderDeliveryStatusCodeEnum fromCode(String status) {
        if (OsOrderDeliverySlgStatusMapping.ACCEPTED_STATUS.contains(status)) {
            return OrderDeliveryStatusCodeEnum.ACCEPTED;
        } else if (OsOrderDeliverySlgStatusMapping.DELIVERY_IN_PROGRESS_STATUS.contains(status)) {
            return OrderDeliveryStatusCodeEnum.DELIVERY_IN_PROGRESS;
        } else if (OsOrderDeliverySlgStatusMapping.SUCCESS_STATUS.contains(status)) {
            return OrderDeliveryStatusCodeEnum.SUCCESS;
        } else if (OsOrderDeliverySlgStatusMapping.FAILURE_STATUS.contains(status)) {
            return OrderDeliveryStatusCodeEnum.FAILURE;
        }
        return null;
    }
}
