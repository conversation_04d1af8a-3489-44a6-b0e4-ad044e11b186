package com.vitadairy.order.dtos.request;

import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.request.PageableRequest;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class GetOrderGiftsDetailRequest extends PageableRequest {
    private Integer osOrderId;
    private String orderId;

    @Override
    public void doValidate() throws Exception {
        if (StringUtils.isEmpty(orderId) && Objects.isNull(osOrderId)) {
            throw new ApplicationException("orderId or osOrderId is required");
        }
        super.doValidate();
    }
}
