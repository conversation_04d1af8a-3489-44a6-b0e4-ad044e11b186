package com.vitadairy.order.dtos.request;

import com.google.gson.annotations.SerializedName;
import com.vitadairy.main.request.BaseRequest;
import com.vitadairy.order.common.OsOrderDeliveryGhtkStatusMapping;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class GhtkDeliveryUpdateRequest extends BaseRequest {
    @SerializedName("partner_id")
    private String orderNumber;
    @SerializedName("status_id")
    private Integer statusId;
    @SerializedName("action_time")
    private Date updateAt;
    @SerializedName("fee")
    private Long totalAmount;

    private OrderDeliveryStatusCodeEnum status;

    @Override
    public void doValidate() throws Exception {
        if (orderNumber == null || orderNumber.isEmpty()) {
            throw new IllegalArgumentException("Order number is required");
        }
        if (statusId == null) {
            throw new IllegalArgumentException("Status id is required");
        }
        if (updateAt == null) {
            throw new IllegalArgumentException("Update at is required");
        }
        if (totalAmount == null) {
            throw new IllegalArgumentException("Total amount is required");
        }
        this.status = this.fromCode(statusId);
        if(Objects.isNull(this.status)){
            throw new IllegalArgumentException("Invalid status");
        }
    }

    private OrderDeliveryStatusCodeEnum fromCode(Integer statusId) {
        if(OsOrderDeliveryGhtkStatusMapping.ACCEPTED_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.ACCEPTED;
        } else if(OsOrderDeliveryGhtkStatusMapping.DELIVERY_IN_PROGRESS_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.DELIVERY_IN_PROGRESS;
        } else if(OsOrderDeliveryGhtkStatusMapping.SUCCESS_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.SUCCESS;
        } else if(OsOrderDeliveryGhtkStatusMapping.FAILURE_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.FAILURE;
        }
        return null;
    }
}
