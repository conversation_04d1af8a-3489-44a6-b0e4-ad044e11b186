package com.vitadairy.order.dtos.request;

import com.google.gson.annotations.SerializedName;
import com.vitadairy.main.request.BaseRequest;
import lombok.Data;

import java.util.Objects;

@Data
public class VtpDeliveryUpdateRequest extends BaseRequest {
    @SerializedName("DATA")
    private VtpDeliveryData data;
    @SerializedName("TOKEN")
    private String token;

    @Override
    public void doValidate() throws Exception {
        if(Objects.isNull(token)){
            throw new IllegalArgumentException("Token is required");
        }
        data.doValidate();
    }
}

