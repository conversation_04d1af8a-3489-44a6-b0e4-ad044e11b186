package com.vitadairy.order.dtos.request;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.dto.Page;
import com.vitadairy.main.request.ExportRequest;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ExportOrderRequest extends ExportRequest<ListOrderRequest, Integer> {
    @Override
    public void doValidate() throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            ids = null;
        }

        fetchRequest.doValidate();

        if (Objects.nonNull(page)) {
            fetchRequest.setPage(page.page());
            fetchRequest.setSize(page.size());
        } else if (Objects.isNull(isExportAll) || Boolean.FALSE.equals(isExportAll)) {
            page = new Page(fetchRequest.getPage(), fetchRequest.getSize());
        }

        super.doValidate();
    }

    @Override
    public FetchRequest<ListOrderRequest> toFetchRequest() {
        FetchRequest<ListOrderRequest> res = super.toFetchRequest();
        res.getRequest().setIds(this.ids);
        return res;
    }
}
