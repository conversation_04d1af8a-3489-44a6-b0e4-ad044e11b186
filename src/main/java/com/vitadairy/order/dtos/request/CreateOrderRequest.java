package com.vitadairy.order.dtos.request;

import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.request.BaseRequest;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.common.OsOrderErrorCode;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.dtos.GiftDto;
import com.vitadairy.order.dtos.UserInfoDto;
import com.vitadairy.order.entities.OrderGift;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CreateOrderRequest extends BaseRequest {
    @NotNull
    private Integer userId;
    private String tdId;
    @NotNull
    private List<OrderGift> userGiftSnapshot;
    private Map<String, String> recipientSnapshot;
    private List<GiftDto> giftSnapshot;
    private UserInfoDto userInfoDto;

    @Override
    public void doValidate() throws Exception {
        if (recipientSnapshot == null || recipientSnapshot.isEmpty()) {
            throw new ApplicationException(OsOrderErrorCode.RECIPIENT_ADDRESS_NOT_FOUND);
        }
    }

    public static OsOrder toOsOrderEntity(CreateOrderRequest request, OrderTypeCodeEnum orderType) {
        String orderCode = IdUtils.generateId(ConstantFieldDefs.ORDER_PREFIX, true, true, 6);
        String transactionDeliveryId = IdUtils.generateId(ConstantFieldDefs.TRANSACTION_DELIVERY_ID, true, true, 6);
        String transportTypeFromGift = !CollectionUtils.isEmpty(request.getGiftSnapshot()) ? request.getGiftSnapshot().getFirst().getTransportTypeCode() : "";
        String transportTypeFromUserGift = Objects.nonNull(request.getUserGiftSnapshot().getFirst().getGift()) ? request.getUserGiftSnapshot().getFirst().getGift().getTransportTypeCode() : "";
        String transportTypeCode = StringUtils.isEmpty(transportTypeFromGift) ? transportTypeFromUserGift : transportTypeFromGift;

        Map<String, Object> extraData = new HashMap<>();
        extraData.put(OsOrderExtraDataFieldDefs.IS_SHOW_SHIPPING_FEE, false);
        extraData.put(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE, transactionDeliveryId);
        extraData.put(OsOrderExtraDataFieldDefs.TRANSPORT_TYPE_CODE, transportTypeCode);

        UserInfoDto creatorInfo = request.getUserInfoDto();
        if (Objects.nonNull(creatorInfo)) {
            extraData.put(OsOrderExtraDataFieldDefs.CREATOR_PHONE_NUMBER, creatorInfo.phoneNumber());
            extraData.put(OsOrderExtraDataFieldDefs.CREATOR_NAME, creatorInfo.name());
        }

        List<OrderGift> orderGifts = request.getUserGiftSnapshot();

        return OsOrder.builder()
                .orderCode(orderCode)
                .userId(request.getUserId())
                .statusCode(OrderStatusCodeEnum.IN_PROCESS.toString())
                .typeCode(orderType.toString())
                .userGiftSnapshot(orderGifts)
                .recipientSnapshot(request.getRecipientSnapshot())
                .userId(request.getUserId())
                .extraData(extraData)
                .build();
    }
}
