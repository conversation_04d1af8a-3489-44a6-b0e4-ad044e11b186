package com.vitadairy.order.dtos.request;

import com.vitadairy.main.request.PageableRequest;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Data
public class ListOrderRequest extends PageableRequest {
    private String deliveryProviderId;
    private OrderStatusCodeEnum statusCode;
    private OrderTypeCodeEnum typeCode;
    private String keyword;
    @DateTimeFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime createdDate;
    @DateTimeFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime dateFrom;
    @DateTimeFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime dateTo;
    private List<Integer> ids;
}
