package com.vitadairy.order.dtos.request;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.request.PageableRequest;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ListOrderByUserRequest extends PageableRequest {
    private Integer userId;
    private String statusCode;

    @Override
    public void doValidate() throws Exception {
        AuthorizationResponse authRes = AuthenticationHelper.getCurrentUser();
        if (authRes == null) {
            throw new ApplicationException("User not found");
        }
        if (Objects.nonNull(authRes.getAccountId()) && authRes.getAccountId().intValue() != userId) {
            throw new ApplicationException("Not allow check other user order", HttpStatus.FORBIDDEN);
        }
        if (Objects.nonNull(authRes.getUserId()) && authRes.getUserId().intValue() != userId) {
            throw new ApplicationException("Not allow check other user order", HttpStatus.FORBIDDEN);
        }
        super.doValidate();
    }
}
