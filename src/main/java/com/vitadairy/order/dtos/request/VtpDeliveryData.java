package com.vitadairy.order.dtos.request;

import com.google.gson.annotations.SerializedName;
import com.vitadairy.main.request.BaseRequest;
import com.vitadairy.order.common.OsOrderDeliveryVtpStatusMapping;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class VtpDeliveryData extends BaseRequest {
    @SerializedName("ORDER_NUMBER")
    private String orderNumber;

    @SerializedName("ORDER_STATUSDATE")
    private Date orderStatusDate;

    @SerializedName("ORDER_STATUS")
    private Integer orderStatus;

    @SerializedName("STATUS_NAME")
    private String statusName;

    @SerializedName("MONEY_TOTAL")
    private Long totalAmount;

    private OrderDeliveryStatusCodeEnum status;

    @Override
    public void doValidate() throws Exception {
        if (Objects.isNull(orderStatus)) {
            throw new IllegalArgumentException("Invalid delivery provider status");
        }
        this.status = this.fromCode(orderStatus);
    }

    private OrderDeliveryStatusCodeEnum fromCode(Integer statusId) {
        if (OsOrderDeliveryVtpStatusMapping.ACCEPTED_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.ACCEPTED;
        } else if (OsOrderDeliveryVtpStatusMapping.DELIVERY_IN_PROGRESS_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.DELIVERY_IN_PROGRESS;
        } else if (OsOrderDeliveryVtpStatusMapping.SUCCESS_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.SUCCESS;
        } else if (OsOrderDeliveryVtpStatusMapping.FAILURE_STATUS.contains(statusId)) {
            return OrderDeliveryStatusCodeEnum.FAILURE;
        }
        return null;
    }
}
