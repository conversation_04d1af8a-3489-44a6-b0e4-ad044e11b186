package com.vitadairy.order.dtos.request;

import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.request.BaseRequest;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.entities.OrderGift;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class UpdateOrderRequest extends BaseRequest {
    @NotNull
    private String orderId;
    private String tdId;
    @NotNull
    private String statusCode;
    @NotNull
    private String type;
    private List<OrderGift> userGiftSnapshot;
    private Map<String, String> recipientSnapshot;
    @NotNull
    private Integer userId;
    private Map<String, Object> extraData;
    private Map<String, String> dynamicData;

    private String deliveryNumber;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryCreatedTime;
    private String deliveryProviderId;
    private String deliveryProviderStatusCode;

    @Override
    public void doValidate() throws Exception {
        if (!CollectionUtils.isEmpty(userGiftSnapshot)) {
            if (StringUtils.isEmpty(deliveryNumber) || StringUtils.isEmpty(deliveryProviderId)) {
                throw new ApplicationException("Delivery number, delivery provider and delivery provider status are required if order has gift");
            }
            if (Objects.isNull(OrderStatusCodeEnum.fromCode(statusCode))) {
                throw new IllegalArgumentException("Invalid delivery provider status");
            }
            if (Objects.isNull(OrderDeliveryStatusCodeEnum.fromCode(deliveryProviderStatusCode))) {
                throw new IllegalArgumentException("Invalid delivery provider status");
            }
            if (Objects.isNull(extraData)) {
                extraData = new HashMap<>();
            }
            if (Objects.isNull(dynamicData)) {
                dynamicData = new HashMap<>();
            }
            extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_CODE, deliveryNumber);
            extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_CREATED_TIME, deliveryCreatedTime != null ? Timestamp.from(deliveryCreatedTime.toInstant()) : null);
            extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER, deliveryProviderId);
            extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS, deliveryProviderStatusCode);
        }
    }
}
