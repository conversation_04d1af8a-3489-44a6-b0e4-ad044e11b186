package com.vitadairy.order.dtos.request;

import com.vitadairy.main.request.PageableRequest;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.ZonedDateTime;

@Setter
@Getter
@Data
public class HistoryReturnPointRequest extends PageableRequest {
    private String orderId;
    private String tdId;
    private String deliveryCode;
    private Long userId;
    @DateTimeFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime fromDate;
    @DateTimeFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime toDate;

    public void cleanEmpty(){
        if(StringUtils.isEmpty(orderId)){
            orderId = null;
        }
        if(StringUtils.isEmpty(tdId)){
            tdId = null;
        }
        if(StringUtils.isEmpty(deliveryCode)){
            deliveryCode = null;
        }
    }
}
