package com.vitadairy.order.dtos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GiftDynamicDataDto {
    private String description;
    private String link;
    private BigDecimal weight;
    private BigDecimal height;
    private BigDecimal width;
    private BigDecimal length;
    private String rewardAppGiftCode;
    private String smsGiftId;
    private Boolean activeSmsNotification;
    private String sourceGift;
    private String selectGift;
}