package com.vitadairy.order.dtos;

import com.vitadairy.main.common.RecipientFieldDefs;
import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.common.OsOrderGiftStatus;
import com.vitadairy.order.entities.OrderGift;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.DeliveryProviderEnum;
import com.vitadairy.order.enums.GhtkDeliveryStatusCodeEnum;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import com.vitadairy.order.enums.VtpDeliveryStatusCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDto {
    private Integer id;
    private Instant createdAt;
    private String orderCode;
    private String tdId;
    private List<OrderGiftDto> userGiftSnapshot;
    private Map<String, String> customerInfo;
    private String deliveryNumber;
    private String deliveryCreatedTime;
    private String deliveryProviderId;
    private String deliveryProviderStatusCode;
    private String deliveryProviderStatusDescription;
    private OrderStatusCodeEnum statusCode;
    private String statusDescription;
    private OrderTypeCodeEnum typeCode;
    private Double shippingFee;
    private String previousOrderCode;
    private Integer totalGifts;
    private Boolean isShowShippingFee;
    private Integer userId;
    private String transportTypeCode;
    private String creatorPhoneNumber;
    private Instant deliveredTime;

    public static OrderDto fromEntity(OsOrder entity) {
        List<OrderGift> orderGifts = entity.getUserGiftSnapshot();
        AtomicInteger totalGifts = new AtomicInteger();
        if (!CollectionUtils.isEmpty(orderGifts)) {
            orderGifts.forEach(val -> totalGifts.addAndGet(val.getQuantity()));
        }
        String deliveryNumber = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CODE) != null
                ? entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CODE).toString() : null;

        Object deliveryCreatedTimeObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CREATED_TIME);
        String deliveryCreatedTime = null;

        if (Objects.nonNull(deliveryCreatedTimeObject)) {
            if (deliveryCreatedTimeObject instanceof Long) {
                deliveryCreatedTime = Instant.ofEpochSecond((Long) deliveryCreatedTimeObject).toString();
            }
            if (deliveryCreatedTimeObject instanceof String) {
                deliveryCreatedTime = (String) deliveryCreatedTimeObject;
            }

        }

        DeliveryProviderEnum deliveryProviderEnum = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER) != null
                ? DeliveryProviderEnum.fromCode(entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER).toString()) : null;

        String deliveryStatusDescription = null;
        Object deliveryStatusCode = null;
        if (Objects.nonNull(entity.getExtraData()) && entity.getExtraData().get(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS) != null) {
            if (deliveryProviderEnum == DeliveryProviderEnum.VTP) {
                VtpDeliveryStatusCodeEnum statusEnum = VtpDeliveryStatusCodeEnum.fromCode((Integer) entity.getExtraData().get(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS));
                deliveryStatusDescription = statusEnum != null ? statusEnum.getDescription() : null;
                deliveryStatusCode = statusEnum != null ? statusEnum.getCode() : null;
            } else if (deliveryProviderEnum == DeliveryProviderEnum.GHTK) {
                GhtkDeliveryStatusCodeEnum statusEnum = GhtkDeliveryStatusCodeEnum.fromCode((Integer) entity.getExtraData().get(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS));
                deliveryStatusDescription = statusEnum != null ? statusEnum.getDescription() : null;
                deliveryStatusCode = statusEnum != null ? statusEnum.getCode() : null;
            }
        }

        Object shippingFreeObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.SHIPPING_FEE);
        Double shippingFee = Objects.nonNull(shippingFreeObject) ? Double.parseDouble(shippingFreeObject.toString()) : 0;

        Object isShowShippingFreeObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.IS_SHOW_SHIPPING_FEE);
        Boolean isShowShippingFee = !Objects.isNull(isShowShippingFreeObject) && Boolean.parseBoolean(isShowShippingFreeObject.toString());

        OrderStatusCodeEnum orderStatusCodeEnum = OrderStatusCodeEnum.fromCode(entity.getStatusCode());

        Object transactionDeliveryObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_DELIVERY_CODE);
        String transactionDeliveryCode = Objects.nonNull(transactionDeliveryObject) ? transactionDeliveryObject.toString() : null;
        if (StringUtils.isEmpty(transactionDeliveryCode)) {
            Object transactionObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSACTION_CODE);
            transactionDeliveryCode = Objects.nonNull(transactionObject) ? transactionObject.toString() : null;
        }

        Object transportTypeCodeObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.TRANSPORT_TYPE_CODE);
        String transportTypeCode = Objects.nonNull(transportTypeCodeObject) ? transportTypeCodeObject.toString() : null;

        Object creatorPhoneNumberObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.CREATOR_PHONE_NUMBER);
        String creatorPhoneNumber = Objects.nonNull(creatorPhoneNumberObject) ? creatorPhoneNumberObject.toString() : null;

        Object deliveredTimeObject = Objects.isNull(entity.getExtraData()) ? null : entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERED_TIME);
        Instant deliveredTime = Objects.nonNull(deliveredTimeObject) ? Instant.ofEpochMilli((Long) deliveredTimeObject) : null;

        Map<String, String> customerInfo = entity.getRecipientSnapshot();
        customerInfo.replaceAll((key, value) -> value == null ? "" : value);
        if(!customerInfo.containsKey(RecipientFieldDefs.NEW_WARD_CODE)){
            customerInfo.put(RecipientFieldDefs.NEW_WARD_CODE, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.NEW_WARD_NAME)){
            customerInfo.put(RecipientFieldDefs.NEW_WARD_NAME, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.NEW_PROVINCE_CODE)){
            customerInfo.put(RecipientFieldDefs.NEW_PROVINCE_CODE, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.NEW_PROVINCE_NAME)){
            customerInfo.put(RecipientFieldDefs.NEW_PROVINCE_NAME, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.WARD_NAME)){
            customerInfo.put(RecipientFieldDefs.WARD_NAME, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.DISTRICT_NAME)){
            customerInfo.put(RecipientFieldDefs.DISTRICT_NAME, "");
        }
        if(!customerInfo.containsKey(RecipientFieldDefs.PROVINCE_NAME)){
            customerInfo.put(RecipientFieldDefs.PROVINCE_NAME, "");
        }
        List<OrderGiftDto> userGiftDtos = entity.getUserGiftSnapshot().stream().filter(g -> !Objects.equals(g.getStatus(), OsOrderGiftStatus.HIDDEN))
                .map(OrderGiftDto::fromEntity)
                .map(ug -> {
                    if(Objects.nonNull(ug.getGift()) && Objects.nonNull(ug.getGift().getEventId()) && ug.getGift().getEventId() == 621){
                        ug.getGift().setPrice(null);
                    }
                    return ug;
                })
                .toList();

        return OrderDto.builder()
                .id(entity.getId())
                .createdAt(entity.getCreatedAt())
                .orderCode(entity.getOrderCode())
                .tdId(transactionDeliveryCode)
                .userGiftSnapshot(userGiftDtos)
                .customerInfo(customerInfo)
                .deliveryNumber(deliveryNumber)
                .deliveryCreatedTime(deliveryCreatedTime)
                .deliveryProviderId(deliveryProviderEnum != null ? deliveryProviderEnum.getCode() : null)
                .deliveryProviderStatusCode(deliveryStatusCode != null ? deliveryStatusCode.toString() : null)
                .deliveryProviderStatusDescription(deliveryStatusDescription)
                .statusCode(orderStatusCodeEnum)
                .statusDescription(Objects.nonNull(orderStatusCodeEnum) ? orderStatusCodeEnum.getName() : null)
                .typeCode(OrderTypeCodeEnum.fromCode(entity.getTypeCode()))
                .shippingFee(shippingFee)
                .previousOrderCode(entity.getPreviousOrderCode())
                .totalGifts(totalGifts.get())
                .isShowShippingFee(isShowShippingFee)
                .userId(entity.getUserId())
                .transportTypeCode(transportTypeCode)
                .creatorPhoneNumber(creatorPhoneNumber)
                .deliveredTime(deliveredTime)
                .build();
    }
}
