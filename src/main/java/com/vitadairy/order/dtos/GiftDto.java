package com.vitadairy.order.dtos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(of = {"id", "sctNumber"})
public class GiftDto {

    private Integer id;
    private List<String> images;
    private String transportTypeCode;
    @Enumerated(EnumType.STRING)
    private GiftStatusEnum status = GiftStatusEnum.ENABLED;
    private String name;
    private List<String> badgeCodes;
    private String categoryCode;
    private List<String> tierCodes;
    private GiftTypeEnum type;
    private Integer point;
    private BigInteger price;
    private List<String> hiddenTags;
    private Long sfNumber;
    private Timestamp startDate;
    private Timestamp endDate;
    private Timestamp expireDate;
    private Integer expireHour;
    private String sctNumber;
    private String tpmNumber;
    private Long eventId;
    private Long quantity;
    private Integer inventory;
    private Integer quantityLimitForBooking;
    private String purchaseOption;
    private Integer quantityReward;
    private Integer quantityReservation;
    private Boolean isAllowReservation;
    private Integer priority;
    private GiftDynamicDataDto dynamicData;

    public enum GiftStatusEnum {
        ENABLED,
        DISABLED,
    }

    public enum GiftTypeEnum {
        GIFT,
        E_VOUCHER,
        E_VOUCHER_SHOP,
        E_VOUCHER_SHOP_BKIDS,
        EV_VITA_CODE,
        COUPON
    }

}
