package com.vitadairy.order.dtos;

import com.vitadairy.main.response.PageableResponse;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class GetOrderGiftsDetailDto extends PageableResponse<OrderGiftDto> {
    private String orderCode;

    @Builder(builderMethodName = "orderGiftsDetailBuilder")
    public GetOrderGiftsDetailDto(String orderCode, List<OrderGiftDto> items, Long total, Integer page, Integer pageSize,
                                  HttpStatusCode statusCode, String msg) {
        super(items, total, 0L, page, pageSize, statusCode, msg);
        this.orderCode = orderCode;
    }
}
