package com.vitadairy.order.dtos;

import com.vitadairy.order.entities.OsHistoryReturnPoint;
import com.vitadairy.order.enums.HistoryReturnPointStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.Objects;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistoryReturnPointExportDto {
    private Integer id;
    private String deliveryCode;
    private String transactionCode;
    private String orderCode;
    private Integer giftId;
    private String giftName;
    private Integer userId;
    private String userInfo; // recipientPhone + recipientName
    private Integer totalGifts;
    private Integer totalPoint;
    private String statusDescription;
    private Instant returnDate;
    private String reason;

    public static HistoryReturnPointExportDto fromEntity(OsHistoryReturnPoint entity){
        HistoryReturnPointStatusEnum status = HistoryReturnPointStatusEnum.fromCode(entity.getStatus());
        var userSnapshot = entity.getUserSnapshot();
        var dto = HistoryReturnPointExportDto.builder()
                .id(entity.getId())
                .returnDate(entity.getCreatedAt())
                .orderCode(entity.getOrderCode())
                .transactionCode(entity.getTransactionCode())
                .deliveryCode(entity.getDeliveryCode())
                .totalGifts(entity.getTotalGift())
                .totalPoint(entity.getTotalPoint())
                .statusDescription(Objects.nonNull(status) ? status.getName() : null)
                .userId(entity.getUserId())
                .reason(entity.getReason())
                .build();

        if(Objects.nonNull(userSnapshot)){
            dto.setUserInfo(userSnapshot.get("recipientPhone") + " - " + userSnapshot.get("recipientName"));
        }
        return dto;
    }
}
