package com.vitadairy.order.dtos.response;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.OrderDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CreateOrderResponse extends EntityResponse<List<OrderDto>> {

    public CreateOrderResponse(List<OrderDto> orders) {
        super(orders, HttpStatusCode.valueOf(200), "ok");
    }
}
