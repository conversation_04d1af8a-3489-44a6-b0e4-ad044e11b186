package com.vitadairy.order.dtos.response;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.DeliveryDetailDto;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DeliveryDetailsResponseDto extends EntityResponse<DeliveryDetailDto> {

    @Builder(builderMethodName = "deliveryDetailsBuilder")
    public DeliveryDetailsResponseDto(DeliveryDetailDto data) {
        super.setData(data);
    }

}
