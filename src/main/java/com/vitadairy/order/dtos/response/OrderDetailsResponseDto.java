package com.vitadairy.order.dtos.response;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.OrderDto;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class OrderDetailsResponseDto extends EntityResponse<OrderDto> {

    @Builder(builderMethodName = "orderDetailsBuilder")
    public OrderDetailsResponseDto(OrderDto data) {
        super.setData(data);
    }

}
