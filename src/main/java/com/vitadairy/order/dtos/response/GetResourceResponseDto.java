package com.vitadairy.order.dtos.response;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.ResourceDto;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatusCode;

import java.util.List;

@Getter
@Setter
public class GetResourceResponseDto extends EntityResponse<List<ResourceDto>> {

    @Builder(builderMethodName = "resourceBuilder")
    public GetResourceResponseDto(List<ResourceDto> data, HttpStatusCode statusCode, String msg) {
        super(data, statusCode, msg);
    }
}

