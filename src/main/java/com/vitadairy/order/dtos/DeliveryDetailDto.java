package com.vitadairy.order.dtos;

import com.vitadairy.order.common.OsOrderExtraDataFieldDefs;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryDetailDto {
    private String deliveryNumber;
    private Timestamp deliveryCreatedTime;
    private Timestamp deliveryUpdatedTime;
    private OrderDeliveryStatusCodeEnum status;
    private Double shippingFee;

    public static DeliveryDetailDto fromOrder(OsOrder entity) {
        String deliveryNumber = entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CODE) != null
                ? entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CODE).toString() : null;
        Timestamp deliveryCreatedTime = entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CREATED_TIME) != null
                ? new Timestamp((Long)entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_CREATED_TIME)) : null;
        Timestamp deliveryUpdatedTime = entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_UPDATED_TIME) != null
                ? new Timestamp((Long)entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_UPDATED_TIME)) : null;
        OrderDeliveryStatusCodeEnum deliveryProviderStatusCode = entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS) != null
                ? OrderDeliveryStatusCodeEnum.fromCode(entity.getExtraData().get(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS).toString()) : null;
        Double shippingFee = entity.getExtraData().get(OsOrderExtraDataFieldDefs.SHIPPING_FEE) != null
                ? Double.parseDouble(entity.getExtraData().get(OsOrderExtraDataFieldDefs.SHIPPING_FEE).toString()) : 0;

        return DeliveryDetailDto.builder()
                .deliveryNumber(deliveryNumber)
                .deliveryCreatedTime(deliveryCreatedTime)
                .deliveryUpdatedTime(deliveryUpdatedTime)
                .status(deliveryProviderStatusCode)
                .shippingFee(shippingFee)
                .build();
    }
}
