package com.vitadairy.order.dtos;

import java.time.Instant;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserGiftDto {
    private Integer giftId;
    private Integer quantity;
    private Long id;
    private Long userId;
    private Integer usedQuantity;
    private Integer reservationPoint;
    private Float point;
    private Instant createdAt;
    private Instant updatedAt;
    private Map<String, Object> dynamicData;
    private GiftDto gift;
    private String status;
    private String transactionCode;
}
