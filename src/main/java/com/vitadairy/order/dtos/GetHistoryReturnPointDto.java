package com.vitadairy.order.dtos;

import com.vitadairy.main.response.PageableResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatusCode;

import java.util.List;

@Data
@AllArgsConstructor
public class GetHistoryReturnPointDto extends PageableResponse<HistoryReturnPointDto> {

    @Builder(builderMethodName = "historyReturnPointBuilder")
    public GetHistoryReturnPointDto(List<HistoryReturnPointDto> items, Long total, Integer page, Integer pageSize,
                                    HttpStatusCode statusCode, String msg) {
        super(items, total, 0L, page, pageSize, statusCode, msg);
    }

}
