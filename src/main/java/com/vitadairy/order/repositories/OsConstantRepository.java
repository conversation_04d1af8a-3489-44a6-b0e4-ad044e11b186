package com.vitadairy.order.repositories;

import com.vitadairy.order.entities.OsConstant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OsConstantRepository extends JpaRepository<OsConstant, Integer> {
    @Query("SELECT c FROM OsConstant c WHERE c.keyAlpha = :alpha AND c.keyBeta = :beta and c.isActive is true")
    List<OsConstant> findByKeyAlphaAndKeyBeta(@Param("alpha") String keyAlpha,@Param("beta") String keyBeta);
}
