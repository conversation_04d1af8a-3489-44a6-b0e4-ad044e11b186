package com.vitadairy.order.repositories;

import com.vitadairy.order.entities.OsReturnPointReasonMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface OsReturnPointReasonMappingRepository extends JpaRepository<OsReturnPointReasonMapping, Integer> {
    Optional<OsReturnPointReasonMapping> findByCode(Integer code);
    List<OsReturnPointReasonMapping> findByCodeIn(Set<Integer> codes);
}
