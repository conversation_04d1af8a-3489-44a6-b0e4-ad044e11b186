package com.vitadairy.order.repositories;

import com.vitadairy.order.entities.OrderCount;
import com.vitadairy.order.entities.OsOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface OsOrderRepository extends JpaRepository<OsOrder, Integer> {
    Optional<OsOrder> findByOrderCode(String orderCode);

    @Query(value = "select * from os_order where (extra_data ->> 'delivered_time')::BIGINT <= :date and status_code != 'SUCCESS'", nativeQuery = true)
    List<OsOrder> getOrderDelivered(Long date);

    @Query(value = "SELECT o FROM OsOrder o WHERE o.userId = :userId AND (o.statusCode = :statusCode OR COALESCE(:statusCode, '') = '') " +
            "and o.typeCode != 'SEPARATED' and o.statusCode != 'HIDDEN'")
    Page<OsOrder> findOsOrderByUserIdAnAndStatusCode(@Param("userId") Integer userId, @Param("statusCode") String statusCode, Pageable pageable);

    @Query(value = "SELECT * FROM os_order o WHERE o.extra_data ->> :key = :value", nativeQuery = true)
    List<OsOrder> findAllByExtraData(String key, String value);

    @Query(value = "select count(*) as count, status_code from os_order where status_code IN (:status) and (:userId IS NULL OR user_id = :userId) group by status_code", nativeQuery = true)
    List<OrderCount> countByStatus(String[] status, Integer userId);
}
