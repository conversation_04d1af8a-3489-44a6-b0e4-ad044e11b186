package com.vitadairy.order.repositories;

import com.vitadairy.order.entities.OsImportReturnPointTemp;
import com.vitadairy.order.entities.OsOrder;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OsOrderCustomRepository {
    List<OsOrder> findByQuerySql(String jpql, Map<String, Object> parameters, Pageable pageable);

    long countByQuerySql(String jpql, Map<String, Object> parameters);

    List<Object[]> runFunction(String functionName, Map<String, Object> parameters);

    List<Object[]> runFunctionAsQuery(String sql, Map<String, Object> parameters);

    List<Object[]> callFunctionImportOrderManual(String sessionInput);

    List<Object[]> callFunctionImportDeliveryProvider(String sessionInput);

    List<Object[]> callFunctionImportOrderFullRedelivery(String sessionInput);

    List<Object[]> callFunctionImportOrderGift(String sessionInput);

    List<Object[]> callFunctionImportOrderPartRedelivery(String sessionInput);

    List<Object[]> callFunctionValidateReturnPoint(String sessionInput);

    List<OsImportReturnPointTemp> callFunctionGetImportReturnPoint(String sessionInput);

    List<Object[]> callFunctionDoImportHistoryReturnPoint(String sessionInput);

    void deleteHistoryReturnPoint(String sessionInput, Long line);

    void updateFailedImportReturnPoint(String sessionInput, Long line);

    List<OsOrder> manualSaveAll(List<OsOrder> osOrders);

    List<OsOrder> manualSaveAllOnDuplicateDoNothing(List<OsOrder> osOrders);

    OsOrder manualSave(OsOrder osOrder);

    OsOrder manualSaveOnDuplicateDoNothing(OsOrder osOrder);
}
