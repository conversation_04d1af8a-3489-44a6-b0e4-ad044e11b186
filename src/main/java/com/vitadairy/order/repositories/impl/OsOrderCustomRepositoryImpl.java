package com.vitadairy.order.repositories.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.order.entities.OsImportReturnPointTemp;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.entities.OsOrderImportTempKey;
import com.vitadairy.order.repositories.OsOrderCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.StoredProcedureQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OsOrderCustomRepositoryImpl implements OsOrderCustomRepository {

    private static final String SESSION_INPUT = "sessionInput";

    @PersistenceContext(unitName = "order")
    private final EntityManager entityManager;

    private final ObjectMapper objectMapper;

    @Override
    @SuppressWarnings("unchecked")
    public List<OsOrder> findByQuerySql(String jpql, Map<String, Object> parameters, Pageable pageable) {
        try {
            Query query = entityManager.createNativeQuery(jpql, OsOrder.class);
            if (!parameters.isEmpty()) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    if (entry.getValue() instanceof List) {
                        // trying to be more clear on list parameter
                        query.setParameter(entry.getKey(), (List) entry.getValue());
                        continue;
                    }
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }
            int firstResult = pageable.getPageNumber() * pageable.getPageSize();
            return query
                    .setFirstResult(firstResult)
                    .setMaxResults(pageable.getPageSize())
                    .getResultList();
        } catch (Exception ex) {
            log.error("Error when execute query: {}", jpql, ex);
            return List.of();
        }
    }

    @Override
    public long countByQuerySql(String jpql, Map<String, Object> parameters) {
        try {
            Query query = entityManager.createNativeQuery(jpql);
            if (!parameters.isEmpty()) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }
            return (long) query.getSingleResult();
        } catch (Exception ex) {
            log.error("Error when execute query: {}", jpql, ex);
            return -1;
        }
    }

    @Override
    public List<Object[]> runFunction(String functionName, Map<String, Object> parameters) {
        try {
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery(functionName);
            if (!parameters.isEmpty()) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    query.registerStoredProcedureParameter(entry.getKey(), entry.getValue().getClass(), jakarta.persistence.ParameterMode.IN);
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }
            query.execute();
            return query.getResultList();
        } catch (Exception ex) {
            log.error("Error when execute function: {}", functionName, ex);
            return null;
        }
    }

    @Override
    public List<Object[]> runFunctionAsQuery(String sql, Map<String, Object> parameters) {
        try {
            Query query = entityManager.createNativeQuery(sql, Object.class);
            if (!parameters.isEmpty()) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }
            return query.getResultList();
        } catch (Exception ex) {
            log.error("Error when execute query: {}", sql, ex);
            return List.of();
        }
    }

    @Override
    public List<Object[]> callFunctionImportOrderManual(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM manual_import_os_order_temp_fn(:sessionInput)", parameters);
    }

    @Override
    public List<Object[]> callFunctionImportDeliveryProvider(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("sessionInput", sessionInput);
        return runFunctionAsQuery("SELECT * FROM import_delivery_provider_fn(:sessionInput)", parameters);
    }

    @Override
    public List<Object[]> callFunctionImportOrderFullRedelivery(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM import_full_redelivery_fn(:sessionInput)", parameters);
    }

    @Override
    public List<Object[]> callFunctionImportOrderGift(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM import_order_gift_fn(:sessionInput)", parameters);
    }

    @Override
    public List<Object[]> callFunctionImportOrderPartRedelivery(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM import_part_redelivery_fn(:sessionInput)", parameters);
    }

    @Override
    public List<Object[]> callFunctionValidateReturnPoint(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM validate_import_return_point_fn(:sessionInput)", parameters);
//        return runFunction("validate_import_return_point_fn", parameters);
    }

    @Override
    public List<OsImportReturnPointTemp> callFunctionGetImportReturnPoint(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        List<Object[]> resultSet = runFunctionAsQuery("SELECT * FROM get_import_return_point_fn(:sessionInput)", parameters);
        if (CollectionUtils.isEmpty(resultSet)) {
            return List.of();
        }
        return resultSet.stream()
                .map(row -> {
                    OsImportReturnPointTemp temp = new OsImportReturnPointTemp();
                    OsOrderImportTempKey id = OsOrderImportTempKey
                            .builder()
                            .line(((Long) row[0]).intValue())
                            .session((String) row[1])
                            .build();
                    temp.setId(id);
                    temp.setUserId(((Long) row[2]).intValue());
                    temp.setPoint(((BigDecimal) row[3]).doubleValue());
                    temp.setUserGiftId(((Long) row[4]).intValue());
                    if(row[5] != null){
                        temp.setOrderCode((String) row[5]);
                    }
                    temp.setReason((String) row[6]);
                    return temp;
                })
                .toList();
    }

    @Override
    public List<Object[]> callFunctionDoImportHistoryReturnPoint(String sessionInput) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SESSION_INPUT, sessionInput);
        return runFunctionAsQuery("SELECT * FROM do_import_history_return_point_fn(:sessionInput)", parameters);
    }

    @Override
    public void deleteHistoryReturnPoint(String sessionInput, Long line){
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("session", sessionInput);
        parameters.put("line", line);
        runFunctionAsQuery("DELETE FROM os_history_return_point WHERE session =:session AND line =:line", parameters);
    }

    @Override
    public void updateFailedImportReturnPoint(String sessionInput, Long line){
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("session", sessionInput);
        parameters.put("line", line);
        runFunctionAsQuery("UPDATE os_import_return_point_temp set status = 'failure' WHERE session =:session AND line =:line", parameters);
    }

    @Override
    @Transactional(value = "orderTransactionManager")
    public List<OsOrder> manualSaveAll(List<OsOrder> osOrders) {
        List<OsOrder> res = new ArrayList<>();
        for (OsOrder osOrder : osOrders) {
            res.add(manualSave(osOrder));
        }
        return res.stream()
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    @Transactional(value = "orderTransactionManager")
    public List<OsOrder> manualSaveAllOnDuplicateDoNothing(List<OsOrder> osOrders) {
        List<OsOrder> res = new ArrayList<>();
        for (OsOrder osOrder : osOrders) {
            res.add(manualSaveOnDuplicateDoNothing(osOrder));
        }
        return res.stream()
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    public OsOrder manualSave(OsOrder osOrder) {
        try {

            Query query = entityManager.createNativeQuery("""
                    insert into os_order 
                        (id, order_code, previous_order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id, extra_data) 
                        values (?, ?, ?, ?, ?, ?, ?::jsonb, ?, ?::jsonb) 
                        on conflict (id) 
                        do update set order_code = excluded.order_code, 
                            previous_order_code = excluded.previous_order_code, 
                            status_code = excluded.status_code,
                            type_code = excluded.type_code, 
                            user_gift_snapshot = excluded.user_gift_snapshot, 
                            recipient_snapshot = excluded.recipient_snapshot, 
                            user_id = excluded.user_id, 
                            extra_data = excluded.extra_data 
                    """
            );
            query.setParameter(1, osOrder.getId());
            query.setParameter(2, osOrder.getOrderCode());
            query.setParameter(3, osOrder.getPreviousOrderCode());
            query.setParameter(4, osOrder.getStatusCode());
            query.setParameter(5, osOrder.getTypeCode());
            query.setParameter(6, objectMapper.writeValueAsString(osOrder.getUserGiftSnapshot()));
            query.setParameter(7, objectMapper.writeValueAsString(osOrder.getRecipientSnapshot()));
            query.setParameter(8, osOrder.getUserId());
            query.setParameter(9, objectMapper.writeValueAsString(osOrder.getExtraData()));

            query.executeUpdate();
            return osOrder;
        } catch (Exception ex) {
            log.error("Error when save order: {}", osOrder.getId(), ex);
        }
        return null;
    }

    @Override
    public OsOrder manualSaveOnDuplicateDoNothing(OsOrder osOrder) {
        try {

            Query query = entityManager.createNativeQuery("""
                    insert into os_order 
                        (id, order_code, previous_order_code, status_code, type_code, user_gift_snapshot, recipient_snapshot, user_id, extra_data) 
                        values (?, ?, ?, ?, ?, ?, ?::jsonb, ?, ?::jsonb) 
                        on conflict (id) 
                        do nothing 
                    """
            );
            query.setParameter(1, osOrder.getId());
            query.setParameter(2, osOrder.getOrderCode());
            query.setParameter(3, osOrder.getPreviousOrderCode());
            query.setParameter(4, osOrder.getStatusCode());
            query.setParameter(5, osOrder.getTypeCode());
            query.setParameter(6, objectMapper.writeValueAsString(osOrder.getUserGiftSnapshot()));
            query.setParameter(7, objectMapper.writeValueAsString(osOrder.getRecipientSnapshot()));
            query.setParameter(8, osOrder.getUserId());
            query.setParameter(9, objectMapper.writeValueAsString(osOrder.getExtraData()));

            query.executeUpdate();
            return osOrder;
        } catch (Exception ex) {
            log.error("Error when save order: {}", osOrder.getId(), ex);
        }
        return null;
    }

}
