package com.vitadairy.order.repositories;

import com.vitadairy.order.dtos.request.HistoryReturnPointRequest;
import com.vitadairy.order.entities.OsHistoryReturnPoint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface OsHistoryReturnPointRepository extends JpaRepository<OsHistoryReturnPoint, Integer> {

    @Query(value = "SELECT h.* FROM os_history_return_point h LEFT JOIN os_order o ON h.delivery_code = o.extra_data ->> 'delivery_code' " +
            "WHERE (COALESCE(:#{#request.orderId}) IS NULL OR o.id = :#{#request.orderId}) " +
            "AND (COALESCE(:#{#request.tdId}) IS NULL OR h.transaction_code = :#{#request.tdId})" +
            "AND (COALESCE(:#{#request.deliveryCode}) IS NULL OR h.delivery_code = :#{#request.deliveryCode})" +
            "AND (COALESCE(:#{#request.userId}) IS NULL OR h.user_id = :#{#request.userId})" +
            "AND ((COALESCE(:#{#request.fromDate}) IS NULL AND COALESCE(:#{#request.toDate}) IS NULL)" +
                "OR (COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NOT NULL AND h.created_at between :#{#request.fromDate} AND :#{#request.toDate})" +
                "OR (COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NULL AND h.created_at >= :#{#request.fromDate})" +
                "OR (COALESCE(:#{#request.toDate}) IS NOT NULL AND COALESCE(:#{#request.fromDate}) IS NULL AND h.created_at <= :#{#request.toDate}))" +
            "ORDER BY h.created_at DESC",
            countQuery = "SELECT COUNT(h.*) FROM os_history_return_point h LEFT JOIN os_order o ON h.delivery_code = o.extra_data ->> 'delivery_code' " +
                    "WHERE (COALESCE(:#{#request.orderId}) IS NULL OR o.id = :#{#request.orderId}) " +
                    "AND (COALESCE(:#{#request.tdId}) IS NULL OR h.transaction_code = :#{#request.tdId})" +
                    "AND (COALESCE(:#{#request.deliveryCode}) IS NULL OR h.delivery_code = :#{#request.deliveryCode})" +
                    "AND (COALESCE(:#{#request.userId}) IS NULL OR h.user_id = :#{#request.userId})" +
                    "AND ((COALESCE(:#{#request.fromDate}) IS NULL AND COALESCE(:#{#request.toDate}) IS NULL)" +
                    "OR (COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NOT NULL AND h.created_at between :#{#request.fromDate} AND :#{#request.toDate})" +
                    "OR (COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NULL AND h.created_at >= :#{#request.fromDate})" +
                    "OR (COALESCE(:#{#request.toDate}) IS NOT NULL AND COALESCE(:#{#request.fromDate}) IS NULL AND h.created_at <= :#{#request.toDate}))", nativeQuery = true)
    Page<OsHistoryReturnPoint> findHistoryReturnPoint(HistoryReturnPointRequest request, Pageable pageable);
}
