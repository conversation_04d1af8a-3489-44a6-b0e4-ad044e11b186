package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.WriteRowProcessor;
import com.vitadairy.libraries.importexport.service.*;
import com.vitadairy.order.dtos.OrderDtoExportItem;
import com.vitadairy.order.dtos.request.ListOrderRequest;
import com.vitadairy.main.services.ImportLogger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class ExportOrderConfiguration {
    @Bean(name = "exportOrderMetaData")
    public List<CellMetaData> exportOrderMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("RA order ID").dataType(DataType.STRING).fieldName("id").build());
        metaDataList.add(CellMetaData.builder().name("Ngày tạo RA").dataType(DataType.DATE).fieldName("createdAt").build());
        metaDataList.add(CellMetaData.builder().name("Order ID").dataType(DataType.STRING).fieldName("orderCode").build());
        metaDataList.add(CellMetaData.builder().name("TD ID").dataType(DataType.STRING).fieldName("transactionDeliveryId").build());
        metaDataList.add(CellMetaData.builder().name("Tên khách hàng").dataType(DataType.STRING).fieldName("recipientName").build());
        metaDataList.add(CellMetaData.builder().name("SĐT").dataType(DataType.STRING).fieldName("recipientPhone").build());
        metaDataList.add(CellMetaData.builder().name("User id").dataType(DataType.NUMBER).fieldName("userId").build());
        metaDataList.add(CellMetaData.builder().name("SĐT người tạo đơn").dataType(DataType.STRING).fieldName("creatorPhoneNumber").build());
        metaDataList.add(CellMetaData.builder().name("Mã vận đơn").dataType(DataType.STRING).fieldName("deliveryNumber").build());
        metaDataList.add(CellMetaData.builder().name("Đơn vị vận chuyển").dataType(DataType.STRING).fieldName("deliveryProviderId").build());
        metaDataList.add(CellMetaData.builder().name("Ngày tạo ĐVVC").dataType(DataType.STRING).fieldName("deliveryCreatedTime").build());
        metaDataList.add(CellMetaData.builder().name("TT ĐVVC").dataType(DataType.STRING).fieldName("deliveryProviderStatusDescription").build());
        metaDataList.add(CellMetaData.builder().name("TT RA").dataType(DataType.STRING).fieldName("statusCode").build());
        metaDataList.add(CellMetaData.builder().name("Hiện phí ship").dataType(DataType.STRING).fieldName("isShowShippingFee").build());
        metaDataList.add(CellMetaData.builder().name("Phí ship").dataType(DataType.NUMBER).fieldName("shippingFee").build());
        metaDataList.add(CellMetaData.builder().name("Re Delivery ID by").dataType(DataType.STRING).fieldName("previousOrderCode").build());
        metaDataList.add(CellMetaData.builder().name("Loại đơn").dataType(DataType.STRING).fieldName("orderType").build());
        metaDataList.add(CellMetaData.builder().name("Số lượng đơn").dataType(DataType.NUMBER).fieldName("totalGifts").build());
        metaDataList.add(CellMetaData.builder().name("Transaction external ID").dataType(DataType.STRING).fieldName("transactionExternalId").build());
        metaDataList.add(CellMetaData.builder().name("Tên quà").dataType(DataType.STRING).fieldName("giftName").build());
        metaDataList.add(CellMetaData.builder().name("Gift code").dataType(DataType.STRING).fieldName("giftCode").build());
        metaDataList.add(CellMetaData.builder().name("Số xu").dataType(DataType.NUMBER).fieldName("point").build());
        metaDataList.add(CellMetaData.builder().name("Số lượng quà").dataType(DataType.NUMBER).fieldName("quantity").build());
        metaDataList.add(CellMetaData.builder().name("Số lượng thực nhận").dataType(DataType.NUMBER).fieldName("quantityDelivered").build());
        return metaDataList;
    }

    @Bean(name = "exportOrderLogger")
    public ILogger exportOrderLogger() {
        return new ImportLogger("ExportOrder");
    }

    @Bean(name = "exportOrderRowProcessor")
    public WriteRowProcessor exportOrderRowProcessor(
            @Qualifier("exportOrderMetaData") List<CellMetaData> metaDataList,
            @Qualifier("exportOrderLogger") ILogger logger) {
        return new WriteRowProcessor(
                new WriteDataServiceStrategy(
                        new WriteDataDefaultService(),
                        new WriteDataNumberService(),
                        new WriteDataDateService("dd/MM/yyyy hh:mm:ss", logger)),
                metaDataList,
                logger);
    }

    @Bean(name = "exportOrderWriter")
    public WriteExportFileService<OrderDtoExportItem, ListOrderRequest> exportOrderWriter(
            FetchDataService<OrderDtoExportItem, ListOrderRequest> fetchDataService,
            @Qualifier("exportOrderRowProcessor") WriteRowProcessor rowProcessor,
            @Qualifier("exportOrderLogger") ILogger logger) {
        return new WriteExportFileServiceImpl<>(fetchDataService, rowProcessor, logger);
    }
}
