package com.vitadairy.order.configs;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Configuration
public class MigrateOrderConfiguration {

    @Bean("migrateOrderExecutor")
    public ExecutorService executor() {
        return Executors.newFixedThreadPool(50);
    }


    @Bean(name = "migrateOrderSynchronizeObject")
    public Object migrateOrderSynchronizeObject() {
        return new Object();
    }
}
