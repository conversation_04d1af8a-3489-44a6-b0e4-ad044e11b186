package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.ParseCellDataStrategy;
import com.vitadairy.libraries.importexport.service.ParseCellDateService;
import com.vitadairy.libraries.importexport.service.ParseCellDefaultService;
import com.vitadairy.libraries.importexport.service.ParseCellNumberService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileServiceImpl;
import com.vitadairy.libraries.importexport.service.ReadImportFileService;
import com.vitadairy.libraries.importexport.service.ReadImportFileServiceImpl;
import com.vitadairy.order.entities.OsImportDeliveryProviderTemp;
import com.vitadairy.main.services.ImportLogger;
import com.vitadairy.order.services.ParseOsImportDeliveryProviderTempService;
import com.vitadairy.order.services.ProcessDataOsImportDeliveryProviderService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
public class ImportDeliveryProviderConfiguration {


    @Bean(name = "importDeliveryProviderMetaData")
    public List<CellMetaData> importDeliveryProviderMetaData() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("Mã vận đơn").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("ĐVVC").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Trạng thái ĐVVC").dataType(DataType.STRING).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importDeliveryProviderMetaDataMap() {
        return importDeliveryProviderMetaData().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importDeliveryProviderLogger")
    public ILogger importDeliveryProviderLogger() {
        return new ImportLogger("ImportDeliveryProvider");
    }

    @Bean(name = "importDeliveryProviderRowProcessor")
    public ReadRowProcessor importDeliveryProviderRowProcessor(@Qualifier("importDeliveryProviderLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("dd/MM/yyyy HH:mm:ss", logger),
                        new ParseCellNumberService()
                ),
                importDeliveryProviderMetaDataMap()
        );
    }

    @Bean(name = "importDeliveryProviderReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<OsImportDeliveryProviderTemp> osImportDeliveryProviderTempReadImportFileService(
            @Qualifier("importDeliveryProviderRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importDeliveryProviderLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseOsImportDeliveryProviderTempService(UUID.randomUUID().toString()),
                rowProcessor,
                logger
        );
    }

    @Bean(name = "importDeliveryProviderService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<OsImportDeliveryProviderTemp> osImportDeliveryProviderTempProcessImportFileService(
            @Qualifier("importDeliveryProviderReader") ReadImportFileService<OsImportDeliveryProviderTemp> reader,
            ProcessDataOsImportDeliveryProviderService processImportDataService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processImportDataService);
    }

}
