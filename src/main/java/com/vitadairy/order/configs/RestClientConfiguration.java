package com.vitadairy.order.configs;

import com.vitadairy.main.helper.RestTemplateClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */
@Configuration
public class RestClientConfiguration {

    @Value("${rest.url.gift-service}")
    private String giftServiceUrl;

    @Bean
    public RestTemplateClient restTemplateOsGiftClient(RestTemplate restTemplate) {
        return new RestTemplateClient(giftServiceUrl, restTemplate);
    }
}
