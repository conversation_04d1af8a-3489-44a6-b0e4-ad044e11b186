package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.ParseCellDataStrategy;
import com.vitadairy.libraries.importexport.service.ParseCellDateService;
import com.vitadairy.libraries.importexport.service.ParseCellDefaultService;
import com.vitadairy.libraries.importexport.service.ParseCellNumberService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileServiceImpl;
import com.vitadairy.libraries.importexport.service.ReadImportFileService;
import com.vitadairy.libraries.importexport.service.ReadImportFileServiceImpl;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.entities.OsManualImportOrderTemp;
import com.vitadairy.main.services.ImportLogger;
import com.vitadairy.order.services.impl.ParseOsManualImportOsOrderTempService;
import com.vitadairy.order.services.impl.ProcessDataOsManualImportOsOrderService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
public class ImportDeliveryCodeOrderConfiguration {


    @Bean(name = "importMvdOrderMetaData")
    public List<CellMetaData> importDeliveryCodeOrderMetaData() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("TD ID").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Transaction: Transaction External ID").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Ma van don").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Ngay tao ma van don").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Don vi van chuyen").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Phi giao hang").dataType(DataType.STRING).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importDeliveryCodeOrderMetaDataMap() {
        return importDeliveryCodeOrderMetaData().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importDeliveryCodeOrderLogger")
    public ILogger importDeliveryCodeOrderLogger() {
        return new ImportLogger("ImportDeliveryCodeOrder");
    }

    @Bean(name = "importDeliveryCodeOrderRowProcessor")
    public ReadRowProcessor importDeliveryCodeOrderRowProcessor(@Qualifier("importDeliveryCodeOrderLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("dd/MM/yyyy", logger),
                        new ParseCellNumberService()
                ),
                importDeliveryCodeOrderMetaDataMap()
        );
    }

    @Bean(name = "importDeliveryCodeOrderReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<OsManualImportOrderTemp> osManualImportOrderTempReadImportFileService(
            @Qualifier("importDeliveryCodeOrderRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importDeliveryCodeOrderLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseOsManualImportOsOrderTempService(IdUtils.generateId(ConstantFieldDefs.SESSION_PREFIX)),
                rowProcessor,
                logger
        );
    }

    @Bean(name = "importDeliveryCodeOrderService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<OsManualImportOrderTemp> osManualImportOrderTempProcessImportFileService(
            @Qualifier("importDeliveryCodeOrderReader") ReadImportFileService<OsManualImportOrderTemp> reader,
            ProcessDataOsManualImportOsOrderService processDataOsManualImportOsOrderService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processDataOsManualImportOsOrderService);
    }

}
