package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.ParseCellDataStrategy;
import com.vitadairy.libraries.importexport.service.ParseCellDateService;
import com.vitadairy.libraries.importexport.service.ParseCellDefaultService;
import com.vitadairy.libraries.importexport.service.ParseCellNumberService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileServiceImpl;
import com.vitadairy.libraries.importexport.service.ReadImportFileService;
import com.vitadairy.libraries.importexport.service.ReadImportFileServiceImpl;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.entities.OsImportPartRedeliveryOrderTemp;
import com.vitadairy.main.services.ImportLogger;
import com.vitadairy.order.services.impl.ParseOsImportPartRedeliveryOrderTempService;
import com.vitadairy.order.services.impl.ProcessDataOsImportPartReDeliveryOrderTempService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
public class ImportPartReDeliveryOrderConfiguration {


    @Bean(name = "importPartRedeliveryOrderMetaData")
    public List<CellMetaData> importPartRedeliveryOrderMetaData() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("Mã giao dịch").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Mã vận đơn cũ").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Mã vận đơn mới").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Ngày tạo MVĐ").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Đơn vị vận chuyển").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Phí giao hàng").dataType(DataType.NUMBER).build());
        metaDataList.add(CellMetaData.builder().name("Hiện phí ship").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Số lượng").dataType(DataType.NUMBER).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importPartRedeliveryOrderMetaDataMap() {
        return importPartRedeliveryOrderMetaData().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importPartRedeliveryOrderLogger")
    public ILogger importPartRedeliveryOrderLogger() {
        return new ImportLogger("ImportPartRedeliveryOrder");
    }

    @Bean(name = "importPartRedeliveryOrderRowProcessor")
    public ReadRowProcessor importPartRedeliveryOrderRowProcessor(@Qualifier("importPartRedeliveryOrderLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("dd/MM/yyyy HH:mm:ss", logger),
                        new ParseCellNumberService()
                ),
                importPartRedeliveryOrderMetaDataMap()
        );
    }

    @Bean(name = "importPartRedeliveryOrderReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<OsImportPartRedeliveryOrderTemp> osImportPartRedeliveryOrderTempReadImportFileService(
            @Qualifier("importPartRedeliveryOrderRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importPartRedeliveryOrderLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseOsImportPartRedeliveryOrderTempService(IdUtils.generateId(ConstantFieldDefs.SESSION_PREFIX)),
                rowProcessor,
                logger
        );
    }

    @Bean(name = "importPartRedeliveryOrderService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<OsImportPartRedeliveryOrderTemp> osImportPartRedeliveryOrderTempProcessImportFileService(
            @Qualifier("importPartRedeliveryOrderReader") ReadImportFileService<OsImportPartRedeliveryOrderTemp> reader,
            ProcessDataOsImportPartReDeliveryOrderTempService processImportDataService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processImportDataService);
    }

}
