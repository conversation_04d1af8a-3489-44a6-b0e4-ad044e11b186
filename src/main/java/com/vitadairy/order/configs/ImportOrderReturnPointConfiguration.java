package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.ParseCellDataStrategy;
import com.vitadairy.libraries.importexport.service.ParseCellDateService;
import com.vitadairy.libraries.importexport.service.ParseCellDefaultService;
import com.vitadairy.libraries.importexport.service.ParseCellNumberService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileServiceImpl;
import com.vitadairy.libraries.importexport.service.ReadImportFileService;
import com.vitadairy.libraries.importexport.service.ReadImportFileServiceImpl;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.entities.OsImportReturnPointTemp;
import com.vitadairy.main.services.ImportLogger;
import com.vitadairy.order.services.impl.ParseOsImportReturnPointTempService;
import com.vitadairy.order.services.impl.ProcessDataOsImportReturnPointService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
public class ImportOrderReturnPointConfiguration {


    @Bean(name = "importOrderReturnPointMetaData")
    public List<CellMetaData> importOrderReturnPointMetaData() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("Transaction external ID").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Lý do hoàn").dataType(DataType.STRING).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importOrderReturnPointMetaDataMap() {
        return importOrderReturnPointMetaData().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importOrderReturnPointLogger")
    public ILogger importOrderReturnPointLogger() {
        return new ImportLogger("ImportOrderReturnPoint");
    }

    @Bean(name = "importOrderReturnPointRowProcessor")
    public ReadRowProcessor importOrderReturnPointRowProcessor(@Qualifier("importOrderReturnPointLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("dd/MM/yyyy", logger),
                        new ParseCellNumberService()
                ),
                importOrderReturnPointMetaDataMap()
        );
    }

    @Bean(name = "importOrderReturnPointReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<OsImportReturnPointTemp> osImportReturnPointTempReadImportFileService(
            @Qualifier("importOrderReturnPointRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importOrderReturnPointLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseOsImportReturnPointTempService(IdUtils.generateId(ConstantFieldDefs.SESSION_PREFIX)),
                rowProcessor,
                logger
        );
    }

    @Bean(name = "importOrderReturnPointService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<OsImportReturnPointTemp> osImportReturnPointTempProcessImportFileService(
            @Qualifier("importOrderReturnPointReader") ReadImportFileService<OsImportReturnPointTemp> reader,
            ProcessDataOsImportReturnPointService processDataOsImportReturnPointService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processDataOsImportReturnPointService);
    }

}
