package com.vitadairy.order.configs;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = "com.vitadairy.order.repositories", entityManagerFactoryRef = "orderEntityManagerFactory", transactionManagerRef = "orderTransactionManager")
public class OrderDatasourceConfiguration {

    @Value("${spring.flyway.order.enabled}")
    private Boolean flywayEnabled;

    @Bean(name = "orderDataSource")
    @ConfigurationProperties("spring.datasource.order")
    public DataSource dataSource(
            @Value("${spring.datasource.order.hikari.pool-name:HikariOrderCP}") String poolName,
            @Value("${spring.datasource.order.hikari.maximum-pool-size:10}") Integer maximumPoolSize,
            @Value("${spring.datasource.order.hikari.minimum-idle:2}") Integer minimumIdle,
            @Value("${spring.datasource.order.hikari.idle-timeout:120000}") Integer idleTimeout,
            @Value("${spring.datasource.order.hikari.connection-timeout:30000}") Integer connectionTimeout,
            @Value("${spring.datasource.order.hikari.max-lifetime:1800000}") Integer maxLifeTime
    ) {
        /*return DataSourceBuilder.create()
                .build();*/
        HikariDataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        dataSource.setConnectionTimeout(connectionTimeout);
        dataSource.setMaximumPoolSize(maximumPoolSize);
        dataSource.setMinimumIdle(minimumIdle);
        dataSource.setIdleTimeout(idleTimeout);
        dataSource.setMaxLifetime(maxLifeTime);
        dataSource.setPoolName(poolName);

        return dataSource;
    }

    @Bean(name = "orderTransactionManager")
    @Autowired
    JpaTransactionManager dataSourceTransactionManager(
            @Qualifier("orderEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    @Bean(name = "orderEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("orderDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.vitadairy.order.entities")
                .persistenceUnit("order")
                .build();
    }

    @Bean
    public FlywayMigrationInitializer dbOrderFlywayInitializer(@Qualifier("orderDataSource") DataSource dataSource) {
        if(flywayEnabled){
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:dbchange/order")
                    .baselineOnMigrate(true)
                    .validateMigrationNaming(true)
                    .outOfOrder(true)
                    .load();
            return new FlywayMigrationInitializer(flyway);
        }
        else {
            return null;
        }
    }
}
