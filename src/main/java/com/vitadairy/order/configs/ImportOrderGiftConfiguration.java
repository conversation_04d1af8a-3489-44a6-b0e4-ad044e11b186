package com.vitadairy.order.configs;

import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.ParseCellDataStrategy;
import com.vitadairy.libraries.importexport.service.ParseCellDateService;
import com.vitadairy.libraries.importexport.service.ParseCellDefaultService;
import com.vitadairy.libraries.importexport.service.ParseCellNumberService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import com.vitadairy.libraries.importexport.service.ProcessImportFileServiceImpl;
import com.vitadairy.libraries.importexport.service.ReadImportFileService;
import com.vitadairy.libraries.importexport.service.ReadImportFileServiceImpl;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.entities.OsImportOrderGiftTemp;
import com.vitadairy.main.services.ImportLogger;
import com.vitadairy.order.services.impl.ParseOsImportOrderGiftTempService;
import com.vitadairy.order.services.impl.ProcessDataOsImportOrderGiftTempService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
public class ImportOrderGiftConfiguration {

    @Bean(name = "importOrderGiftMetaData")
    public List<CellMetaData> importOrGiftMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("Số lượng").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Mã giao dịch").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Mã vận đơn").dataType(DataType.STRING).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importOrGiftMetadataMap() {
        return importOrGiftMetadata().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importOrderGiftLogger")
    public ILogger importOrGiftLogger() {
        return new ImportLogger("ImportOrderGift");
    }

    @Bean(name = "importOrderGiftRowProcessor")
    public ReadRowProcessor importOrGiftRowProcessor(@Qualifier("importOrderGiftLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("dd/MM/yyyy HH:mm:ss", logger),
                        new ParseCellNumberService()
                ),
                importOrGiftMetadataMap()
        );
    }

    @Bean(name = "importOrderGiftReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<OsImportOrderGiftTemp> importOrderGiftReader(
            @Qualifier("importOrderGiftRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importOrderGiftLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseOsImportOrderGiftTempService(IdUtils.generateId(ConstantFieldDefs.SESSION_PREFIX)),
                rowProcessor,
                logger
        );
    }

    @Bean(name = "importOrderGiftService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<OsImportOrderGiftTemp> importOrderGiftService(
            @Qualifier("importOrderGiftReader") ReadImportFileService<OsImportOrderGiftTemp> reader,
            ProcessDataOsImportOrderGiftTempService processDataOsImportOrderGiftTempService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processDataOsImportOrderGiftTempService);
    }
}
