package com.vitadairy.order.common;

import com.vitadairy.main.common.PrefixDefs;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.order.dtos.GiftDto;
import com.vitadairy.order.dtos.request.CreateOrderRequest;
import com.vitadairy.order.entities.OrderGift;
import com.vitadairy.order.entities.OsOrder;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import com.vitadairy.zoo.entities.DeliveryOrder;
import com.vitadairy.zoo.entities.DeliveryOrderUserGift;
import com.vitadairy.zoo.entities.Gift;
import com.vitadairy.zoo.entities.UserGift;
import com.vitadairy.zoo.repositories.LegacyDeliveryOrderUserGiftRepository;
import com.vitadairy.zoo.repositories.LegacyGiftRepository;
import com.vitadairy.zoo.repositories.LegacyUserGiftRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OsOrderFactory {

    private final LegacyDeliveryOrderUserGiftRepository legacyOrderUserGiftRepository;
    private final LegacyUserGiftRepository legacyUsergiftRepository;
    private final LegacyGiftRepository legacyGiftRepository;

    public List<OsOrder> listFromCreateOrderRequest(CreateOrderRequest request) {
        if (CollectionUtils.isEmpty(request.getUserGiftSnapshot())) {
            return Collections.emptyList();
        }
        
        Map<String, List<OrderGift>> giftMapBySctTpmKey = new HashMap<>();

        request.getUserGiftSnapshot().forEach(orderGift -> {
            var gift = orderGift.getGift();
            var groupingKey = buildOrderGroupingKey(gift);
            var orderGifts = giftMapBySctTpmKey.computeIfAbsent(groupingKey, k -> new ArrayList<>());
            orderGifts.add(orderGift);
        });

        if (giftMapBySctTpmKey.isEmpty()) {
            return List.of(CreateOrderRequest.toOsOrderEntity(request, OrderTypeCodeEnum.NORMAL));
        }

        return giftMapBySctTpmKey.values().stream().map(orderGifts -> {
                CreateOrderRequest createRequest = CreateOrderRequest.builder()
                        .userId(request.getUserId())
                        .tdId(request.getTdId())
                        .userGiftSnapshot(orderGifts)
                        .recipientSnapshot(request.getRecipientSnapshot())
                        .build();
                return CreateOrderRequest.toOsOrderEntity(createRequest, OrderTypeCodeEnum.NORMAL);
            }).toList();
    }

    private String buildOrderGroupingKey(GiftDto giftDto) {
        String sourceGift = null;
        if (giftDto.getDynamicData() != null) {
            sourceGift =  giftDto.getDynamicData().getSourceGift();
        }
        return String.format("%s-%s-%s", giftDto.getSctNumber(), giftDto.getTpmNumber(), sourceGift);
    }

    public OsOrder fromLegacyDeliverOrder(DeliveryOrder deliveryOrder) {
        Map<String, String> recipientSnapshot = new HashMap<>();
        recipientSnapshot.put("wardCode", deliveryOrder.getRecipientWardCode());
        recipientSnapshot.put("streetName", deliveryOrder.getRecipientAddress());
        recipientSnapshot.put("districtCode", deliveryOrder.getRecipientDistrictCode());
        recipientSnapshot.put("provinceCode", deliveryOrder.getRecipientProvinceCode());
        recipientSnapshot.put("recipientName", deliveryOrder.getRecipientName());
        recipientSnapshot.put("recipientPhone", deliveryOrder.getPhoneNumberRecipient());

        Map<String, Object> extraData = new HashMap<>();
        extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_CODE, null);
        extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_CREATED_TIME, deliveryOrder.getAcceptedTime());
        extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER, deliveryOrder.getTransportService());
        extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_PROVIDER_STATUS, deliveryOrder.getSubOrderStatus());
        extraData.put(OsOrderExtraDataFieldDefs.DELIVERY_UPDATED_TIME, deliveryOrder.getTransportTime());
        extraData.put(OsOrderExtraDataFieldDefs.SHIPPING_FEE, deliveryOrder.getShipFee());
        extraData.put(OsOrderExtraDataFieldDefs.ORIGINAL_DELIVERY_PROVIDER_STATUS, deliveryOrder.getSubOrderStatus());
        extraData.put(OsOrderExtraDataFieldDefs.IS_SHOW_SHIPPING_FEE, false);
        extraData.put(OsOrderExtraDataFieldDefs.TRANSACTION_CODE, deliveryOrder.getTransactionOrderId());

        OrderStatusCodeEnum orderStatus = populateNewOrderStatus(deliveryOrder.getOrderStatus());

        return OsOrder.builder()
                .id(deliveryOrder.getId())
                .orderCode(StringUtils.isNotEmpty(deliveryOrder.getRefOrderCode()) ? deliveryOrder.getRefOrderCode() : IdUtils.generateId(PrefixDefs.OS_ORDER, deliveryOrder.getAcceptedTime()))
                .previousOrderCode(null)
                .statusCode(Objects.nonNull(orderStatus) ? orderStatus.toString() : OrderStatusCodeEnum.IN_PROCESS.toString())
                .typeCode(deliveryOrder.getOrderType())
                .userGiftSnapshot(buildFromLegacyDeliveryOrder(deliveryOrder.getId()))
                .recipientSnapshot(recipientSnapshot)
                .userId(deliveryOrder.getUserId())
                .extraData(extraData)
                .build();
    }

    private static OrderStatusCodeEnum populateNewOrderStatus(String legacyStatus) {
        // RETURNED, ACCEPT, BLOCK, CANCELED, CANCEL_WAIT, COMPLETE, COMPLETE_SYS, CREATE
        // CREATE_FAIL, CREATE_IMPORTED, CREATE_WAIT, FAIL_TRANSFER, RETURN_WAIT
        // SUCCESS, TRANSFERRING
        if (StringUtils.isEmpty(legacyStatus)) {
            return OrderStatusCodeEnum.IN_PROCESS;
        }
        OrderStatusCodeEnum orderStatus = OrderStatusCodeEnum.fromCode(legacyStatus);
        if (StringUtils.isEmpty(legacyStatus)) {
            return OrderStatusCodeEnum.IN_PROCESS;
        }
        if (legacyStatus.equals("TRANSFERRING")) {
            orderStatus = OrderStatusCodeEnum.DELIVERING;
        }
        if (List.of("COMPLETE", "COMPLETE_SYS").contains(legacyStatus)) {
            orderStatus = OrderStatusCodeEnum.SUCCESS;
        }
        return orderStatus;
    }

    public List<OrderGift> buildFromLegacyDeliveryOrder(Integer orderId) {
        try {
            List<DeliveryOrderUserGift> deliveryOrderUserGifts = legacyOrderUserGiftRepository.findByOrderId(orderId);
            if (CollectionUtils.isEmpty(deliveryOrderUserGifts)) {
                return Collections.emptyList();
            }
            List<UserGift> userGifts = legacyUsergiftRepository.findAllById(deliveryOrderUserGifts.stream()
                    .map(DeliveryOrderUserGift::getUserGiftId)
                    .toList());
            if (CollectionUtils.isEmpty(userGifts)) {
                return Collections.emptyList();
            }
            List<Gift> gifts = legacyGiftRepository.findAllById(userGifts.stream().map(UserGift::getGiftId).toList());

            return userGifts.stream()
                    .map(deliveryOrderUserGift -> buildFromLegacyInfo(deliveryOrderUserGift, gifts))
                    .toList();
        } catch (Exception ex) {
            log.error("Error while building order gift from legacy delivery order", ex);
            return Collections.emptyList();
        }

    }

    private OrderGift buildFromLegacyInfo(UserGift legacyUserGift, List<Gift> legacyGifts) {
        Gift legacyGift = legacyGifts.stream()
                .filter(gift -> gift.getId().equals(legacyUserGift.getGiftId()))
                .findFirst()
                .orElse(null);
        GiftDto giftDto = null;
        if (Objects.nonNull(legacyGift)) {
            giftDto = fromLegacyGift(legacyGift);
        }
        return OrderGift.builder()
                .id(legacyUserGift.getId())
                .userId(legacyUserGift.getUserId())
                .gift(giftDto)
                .status(legacyUserGift.getStatus())
                .quantity(legacyUserGift.getQuantity())
                .reservationPoint(null)
                .point(Objects.nonNull(legacyUserGift.getPoint()) ? legacyUserGift.getPoint().intValue() : null)
                .returnedPoint(null)
                .createdAt(Objects.nonNull(legacyUserGift.getCreatedDate()) ? legacyUserGift.getCreatedDate().toInstant() : null)
                .updatedAt(Objects.nonNull(legacyUserGift.getUpdatedDate()) ? legacyUserGift.getUpdatedDate().toInstant() : null)
                .transactionCode(null)
                .dynamicData(null)
                .build();
    }

    private GiftDto fromLegacyGift(Gift legacyGift) {
        GiftDto.GiftTypeEnum giftTypeEnum = null;
        try {
            giftTypeEnum = StringUtils.isEmpty(legacyGift.getType()) ? null : GiftDto.GiftTypeEnum.valueOf(legacyGift.getType());
        } catch (Exception e) {
            log.error("Gift type not found: {}", legacyGift.getType());
        }
        return GiftDto.builder()
                .id(legacyGift.getId())
                .images(Stream.of(legacyGift.getImage(), legacyGift.getCoverImage()).filter(StringUtils::isNotEmpty).toList())
                .transportTypeCode(null)
                .status(legacyGift.getActive() ? GiftDto.GiftStatusEnum.ENABLED : GiftDto.GiftStatusEnum.DISABLED)
                .name(legacyGift.getName())
                .badgeCodes(null)
                .categoryCode(null)
                .tierCodes(null)
                .type(giftTypeEnum)
                .point(Objects.nonNull(legacyGift.getPoint()) ? legacyGift.getPoint().intValue() : null)
                .price(Objects.nonNull(legacyGift.getMoney()) ? BigInteger.valueOf(legacyGift.getMoney()) : null)
                .hiddenTags(null)
                .sfNumber(null)
                .startDate(legacyGift.getStartDate())
                .endDate(legacyGift.getEndDate())
                .expireDate(null)
                .expireHour(null)
                .sctNumber(null)
                .quantity(Objects.nonNull(legacyGift.getTotal()) ? legacyGift.getTotal().longValue() : null)
                .inventory(null)
                .quantityLimitForBooking(null)
                .purchaseOption(null)
                .quantityReward(null)
                .quantityReservation(null)
                .isAllowReservation(null)
                .priority(legacyGift.getPriority())
                .dynamicData(null)
                .build();
    }

}
