package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "os_import_return_point_temp")
public class OsImportReturnPointTemp implements Serializable {
    @EmbeddedId
    private OsOrderImportTempKey id;
    @Column(name = "transaction_code")
    private String transactionCode;
    @Column(name = "reason")
    private String reason;
    @Column(name = "user_id")
    private Integer userId;
    @Column(name = "point")
    private Double point;
    @Column(name = "status")
    private String status;
    @Column(name = "user_gift_id")
    private Integer userGiftId;
    @Column(name = "order_code")
    private String orderCode;
}
