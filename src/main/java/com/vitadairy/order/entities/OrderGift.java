package com.vitadairy.order.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.utils.JsonNodeUtils;
import com.vitadairy.order.dtos.GiftDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.time.Instant;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = OrderGiftDeserializer.class)
public class OrderGift {
    private Integer id;
    private Integer userId;
    private GiftDto gift;
    private String status;
    private Integer quantity;
    private Integer quantityDelivered;
    private Integer quantityReDelivery;
    private Integer reservationPoint;
    private Integer point;
    private Integer returnedPoint;
    private Instant createdAt;
    private Instant updatedAt;
    private String transactionCode;
    private Map<String, Object> dynamicData;
}

class OrderGiftDeserializer extends JsonDeserializer<OrderGift> {
    @Override
    public OrderGift deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException, JacksonException {
        return JsonNodeUtils.deserialize(jp, OrderGift.class);
    }
}