package com.vitadairy.order.entities;

import com.vitadairy.main.entities.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "os_return_point_reason_mapping")
public class OsReturnPointReasonMapping extends BaseEntity implements Serializable {
    @Id
    @Column(name = "code")
    private Integer code;
    @Column(name = "description")
    private String description;
    @Column(name = "reason_for_notification")
    private String reasonForNotification;
    @Column(name = "reason_for_sf")
    private String reasonForSf;
}
