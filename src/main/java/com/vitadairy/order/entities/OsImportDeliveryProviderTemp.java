package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "os_import_delivery_provider_temp")
public class OsImportDeliveryProviderTemp {
    @EmbeddedId
    private OsOrderImportTempKey id;
    @Column(name = "delivery_code")
    private String deliveryCode;
    @Column(name = "delivery_provider_status")
    private String deliveryProviderStatus;
    @Column(name = "delivery_provider")
    private String deliveryProvider;
    @Column(name = "status")
    private String status = "imported";
}
