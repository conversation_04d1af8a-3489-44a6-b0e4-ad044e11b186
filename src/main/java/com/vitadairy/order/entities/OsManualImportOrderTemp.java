package com.vitadairy.order.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "os_manual_import_order_temp")
public class OsManualImportOrderTemp implements Serializable {
    @EmbeddedId
    private OsOrderImportTempKey id;

    @Column(name = "td_id")
    private String tdId;

    @Column(name = "transaction_code")
    private String transactionCode;

    @Column(name = "delivery_code")
    private String deliveryCode;

    @Column(name = "delivery_provider")
    private String deliveryProvider;

    @Column(name = "delivery_created_at")
    private String createdAt;

    @Column(name = "ship_cost")
    private String shipCost;

    @Column(name = "status")
    private String status;
}
