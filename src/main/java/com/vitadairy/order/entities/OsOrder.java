package com.vitadairy.order.entities;

import com.vitadairy.main.entities.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "os_order")
public class OsOrder extends BaseEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;
    @Column(name = "order_code")
    private String orderCode;
    @Column(name = "previous_order_code")
    private String previousOrderCode;
    @Column(name = "status_code")
    private String statusCode;
    @Column(name = "type_code")
    private String typeCode;
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "user_gift_snapshot", columnDefinition = "JSONB")
    private List<OrderGift> userGiftSnapshot;
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "recipient_snapshot", columnDefinition = "JSONB")
    private Map<String, String> recipientSnapshot;
    @Column(name = "user_id")
    private Integer userId;
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "extra_data", columnDefinition = "JSONB")
    private Map<String, Object> extraData;
}
