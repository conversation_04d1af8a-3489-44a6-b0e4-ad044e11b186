package com.vitadairy.order.entities;

import com.vitadairy.main.entities.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "os_constants")
//@Table(name = "os_constants", uniqueConstraints = @UniqueConstraint(columnNames = {"key", "type"}))
// @TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class OsConstant extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "key_alpha", nullable = false, updatable = false)
    private String keyAlpha;

    @Column(name = "key_beta", nullable = false, updatable = false)
    private String keyBeta;

    @Column(name = "value", nullable = false)
    private String value;

    @Column(name = "priority")
    private Integer priority = Integer.MAX_VALUE;

    // @Convert(converter = JsonbConverter.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dynamic_data", columnDefinition = "JSONB")
    @SuppressWarnings("JpaAttributeTypeInspection")
    private Map<String, String> dynamicData;

    @Column(name = "is_active")
    private Boolean isActive = true;
}
