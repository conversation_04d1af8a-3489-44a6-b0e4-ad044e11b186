package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "os_import_order_gift_temp")
public class OsImportOrderGiftTemp implements Serializable {

    @EmbeddedId
    private OsOrderImportTempKey id;

    @Column(name = "total_gift")
    private Integer totalGift;

    @Column(name = "transaction_code")
    private String transactionCode;

    @Column(name = "delivery_code")
    private String deliveryCode;

    @Column(name = "status")
    private String status;
}
