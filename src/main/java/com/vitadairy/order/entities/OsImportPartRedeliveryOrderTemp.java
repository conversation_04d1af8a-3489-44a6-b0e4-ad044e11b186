package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@Entity
@Table(name = "os_import_part_redelivery_order_temp")
public class OsImportPartRedeliveryOrderTemp implements Serializable {
    @EmbeddedId
    private OsOrderImportTempKey id;
    @Column(name = "transaction_code")
    private String transactionCode;
    @Column(name = "old_delivery_code")
    private String oldDeliveryCode;
    @Column(name = "new_delivery_code")
    private String newDeliveryCode;
    @Column(name = "delivery_provider")
    private String deliveryProvider;
    @Column(name = "delivery_created_at")
    private String deliveryCreatedAt;
    @Column(name = "shipping_fee")
    private Double shippingFee;
    @Column(name = "show_shipping_fee", columnDefinition = "boolean default true")
    private Boolean showShippingFee;
    @Column(name = "status")
    private String status;
    @Column(name = "quantity")
    private Integer quantity;
}
