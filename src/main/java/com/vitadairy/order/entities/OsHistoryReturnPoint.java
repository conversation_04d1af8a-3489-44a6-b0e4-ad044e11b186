package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.time.Instant;
import java.util.Map;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "os_history_return_point")
public class OsHistoryReturnPoint implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "order_code")
    private String orderCode;

    @Column(name = "transaction_code")
    private String transactionCode;

    @Column(name = "delivery_code")
    private String deliveryCode;

    @Column(name = "status")
    private String status;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "gift_snapshot", columnDefinition = "JSONB")
    private OrderGift giftSnapshot;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "user_snapshot", columnDefinition = "JSONB")
    private Map<String, String> userSnapshot;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "reason")
    private String reason;

    @Column(name = "total_gift")
    private Integer totalGift;

    @Column(name = "point")
    private Integer totalPoint;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Instant createdAt;
}
