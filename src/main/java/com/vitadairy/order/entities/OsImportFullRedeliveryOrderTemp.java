package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "os_import_full_redelivery_order_temp")
public class OsImportFullRedeliveryOrderTemp {
    @EmbeddedId
    private OsOrderImportTempKey id;
    @Column(name = "delivery_code")
    private String deliveryCode;
    @Column(name = "new_delivery_code")
    private String newDeliveryCode;
    @Column(name = "delivery_provider")
    private String deliveryProvider;
    @Column(name = "delivery_created_at")
    private String deliveryCreatedAt;
    @Column(name = "show_shipping_fee", columnDefinition = "boolean")
    private Boolean showShippingFee;
    @Column(name = "status")
    private String status = "imported";
    @Column(name = "shipping_fee")
    private Double shippingFee;
}
