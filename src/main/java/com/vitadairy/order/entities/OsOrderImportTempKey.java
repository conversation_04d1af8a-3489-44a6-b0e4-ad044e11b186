package com.vitadairy.order.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@Embeddable
public class OsOrderImportTempKey implements Serializable {
    @Column(name = "line")
    private Integer line;
    @Column(name = "session")
    private String session;
}
