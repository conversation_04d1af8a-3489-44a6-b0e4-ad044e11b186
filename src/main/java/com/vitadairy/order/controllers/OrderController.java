package com.vitadairy.order.controllers;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.iresponse.BaseResponse;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.GetOrderDto;
import com.vitadairy.order.dtos.GetOrderGiftsDetailDto;
import com.vitadairy.order.dtos.OrderCountDto;
import com.vitadairy.order.dtos.request.*;
import com.vitadairy.order.dtos.response.CreateOrderResponse;
import com.vitadairy.order.dtos.response.OrderCountResponseDto;
import com.vitadairy.order.dtos.response.OrderDetailsResponse;
import com.vitadairy.order.dtos.response.OrderDetailsResponseDto;
import com.vitadairy.order.services.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping({"v4/os/orders", "${internal.servlet.context-path}/orders"})
@RequiredArgsConstructor
@Slf4j
public class OrderController {
    private final ResponseFactory responseFactory;
    private final OrderService orderService;

    /**
     * list order with paging option
     *
     * @param request
     * @return
     * <AUTHOR>
     * @since 11/7/24
     */
    @GetMapping
    @PreAuthorize("hasAnyAuthority({'ADMIN','INTERNAL'})")
    public ResponseEntity<GetOrderDto> findOrder(@ModelAttribute ListOrderRequest request) {
        try {
            request.doValidate();
            ResponseEntity<GetOrderDto> response = orderService.listOrder(request);
            return response;
        } catch (Exception ex) {
            log.error("Error while finding order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    /**
     * update order
     *
     * @param id
     * @param request
     * @return
     * <AUTHOR>
     * @since 14/7/24
     */
    @Operation(summary = "Update order")
    @PutMapping("/{id}")
    public ResponseEntity<OrderDetailsResponseDto> updateOrder(@PathVariable Integer id, @RequestBody @Valid UpdateOrderRequest request) {
        try {
            request.doValidate();
            return orderService.updateOrder(id, request);
        } catch (Exception ex) {
            log.error("Error while updating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @Operation(summary = "Confirm success order")
    @PutMapping("/confirm-order/{orderId}")
    @PreAuthorize("hasAuthority('USER')")
    public ResponseEntity<?> confirmSuccessOrder(@PathVariable Integer orderId) {
        try {
            return orderService.confirmOrder(orderId);
        } catch (Exception ex) {
            log.error("Error while confirm order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @GetMapping("/confirm-order")
    public ResponseEntity<?> triggerConfirmOrderLikeJob() {
        orderService.confirmOrderByJob();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * list gifts details of an order with paging option
     *
     * @param request
     * @return
     * <AUTHOR>
     * @since 11/7/24
     */
    @GetMapping(value = "/gifts")
    public ResponseEntity<GetOrderGiftsDetailDto> getOrderGiftDetails(@ModelAttribute GetOrderGiftsDetailRequest request) {
        try {
            request.doValidate();
            return orderService.getOrderGiftsDetail(request);
        } catch (Exception ex) {
            log.error("Error while getting order gift details", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @Operation
    @PostMapping
    public ResponseEntity<CreateOrderResponse> create(@RequestBody @Valid CreateOrderRequest request) {
        try {
            request.doValidate();
            AuthorizationResponse authResp = AuthenticationHelper.getCurrentUser();
            if (Objects.nonNull(authResp) && Objects.nonNull(authResp.getUserId())) {
                request.setUserId(authResp.getUserId().intValue());
            }
            return orderService.createOrder(request);
        } catch (ApplicationException aex) {
            log.error("Application throw error while creating order", aex);
            return responseFactory.errorDto(aex);
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @Operation
    @GetMapping("/details")
    public ResponseEntity<OrderDetailsResponse> findDetails(Integer id, String orderCode, String deliveryCode) {
        try {
            return orderService.details(id, orderCode, deliveryCode);
        } catch (ApplicationException aex) {
            log.error("Application throw error while creating order", aex);
            return responseFactory.errorDto(aex.getMessage());
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @GetMapping("/count-by-status")
    public ResponseEntity<OrderCountResponseDto> countByStatus() {
        try {
            List<OrderCountDto> result = orderService.getOrderCountByStatus();
            return responseFactory.successDto(new OrderCountResponseDto(result));
        } catch (ApplicationException aex) {
            log.error("Application throw error while get order count by staus", aex);
            return responseFactory.errorDto(aex.getMessage());
        } catch (Exception ex) {
            log.error("Error while get order count by status", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @Operation
    @GetMapping("/user/{userId}")
    public ResponseEntity<GetOrderDto> findOrderOfUser(@ModelAttribute ListOrderByUserRequest request, @PathVariable(value = "userId", required = false) Integer userId) {
        try {
            if (userId != null) {
                request.setUserId(userId);
            }
            request.doValidate();
            return orderService.listUserOrder(request);
        } catch (ApplicationException aex) {
            log.error("Application throw error while listing user order", aex);
            return responseFactory.errorDto(aex.getMessage());
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @Operation
    @PreAuthorize("hasAnyAuthority({'ADMIN','INTERNAL'})")
    @PatchMapping("/{id}/show-shipping-fee")
    public ResponseEntity<EntityResponse<Boolean>> toggleShowShippingFee(@PathVariable("id") Integer id, boolean isShowShippingFee) {
        try {
            boolean result = orderService.toggleShowShippingFee(id, isShowShippingFee);
            if (!result) {
                return responseFactory.errorDto("Cannot update showShippingFee of order");
            }
            return responseFactory.successDto(EntityResponse.<Boolean>entityBuilder()
                    .data(result)
                    .statusCode(HttpStatus.OK)
                    .msg("ok")
                    .build());
        } catch (ApplicationException aex) {
            log.error("Application throw error while update showShippingFee of order", aex);
            throw aex;
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
