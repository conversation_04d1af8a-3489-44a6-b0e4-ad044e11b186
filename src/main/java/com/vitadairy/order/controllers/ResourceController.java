package com.vitadairy.order.controllers;


import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.enums.OrderDeliveryStatusCodeEnum;
import com.vitadairy.order.enums.OrderStatusCodeEnum;
import com.vitadairy.order.enums.OrderTypeCodeEnum;
import com.vitadairy.order.dtos.response.GetResourceResponseDto;
import com.vitadairy.order.dtos.ResourceDto;
import com.vitadairy.order.services.ResourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("v4/os/resource")
@RequiredArgsConstructor
public class ResourceController {

    private final ResponseFactory responseFactory;
    private final ResourceService resourceService;

    @GetMapping("/statuses")
    public ResponseEntity<GetResourceResponseDto> getOrderStatuses() {
        List<OrderStatusCodeEnum> orderStatusCodeEnums = OrderStatusCodeEnum.getAll();
        List<ResourceDto> resourceDtos = orderStatusCodeEnums.stream().map(ResourceDto::fromOrderStatusCodeEnum).toList();

        GetResourceResponseDto response = GetResourceResponseDto
                .resourceBuilder()
                .data(resourceDtos)
                .statusCode(HttpStatus.OK)
                .msg("OK")
                .build();

        return responseFactory.success(response);
    }

    @GetMapping("/types")
    public ResponseEntity<GetResourceResponseDto> getOrderTypes() {
        List<OrderTypeCodeEnum> orderStatusCodeEnums = OrderTypeCodeEnum.getAll();

        List<ResourceDto> resourceDtos = orderStatusCodeEnums.stream().map(ResourceDto::fromOrderTypeCodeEnum).toList();

        GetResourceResponseDto response = GetResourceResponseDto
                .resourceBuilder()
                .data(resourceDtos)
                .statusCode(HttpStatus.OK)
                .msg("OK")
                .build();

        return responseFactory.success(response);
    }

    @GetMapping("/delivery-statuses")
    public ResponseEntity<GetResourceResponseDto> getDeliveryProviderStatuses() {
        List<OrderDeliveryStatusCodeEnum> orderStatusCodeEnums = OrderDeliveryStatusCodeEnum.getAll();
        List<ResourceDto> resourceDtos = orderStatusCodeEnums.stream().map(ResourceDto::fromOrderDeliveryStatusCodeEnum).toList();

        GetResourceResponseDto response = GetResourceResponseDto
                .resourceBuilder()
                .data(resourceDtos)
                .statusCode(HttpStatus.OK)
                .msg("OK")
                .build();

        return responseFactory.success(response);
    }

    @GetMapping("/delivery-provider-info")
    public ResponseEntity<GetResourceResponseDto> getDeliveryProviderInfos() {
        List<ResourceDto> resourceDtos = resourceService.getAllDeliveryProviderInfos();

        GetResourceResponseDto response = GetResourceResponseDto
                .resourceBuilder()
                .data(resourceDtos)
                .statusCode(HttpStatus.OK)
                .msg("OK")
                .build();
        return responseFactory.success(response);
    }
}

