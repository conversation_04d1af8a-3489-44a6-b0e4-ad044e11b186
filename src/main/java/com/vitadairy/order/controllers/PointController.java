package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.dtos.GetHistoryReturnPointDto;
import com.vitadairy.order.dtos.request.HistoryReturnPointRequest;
import com.vitadairy.order.services.PointService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"v4/os/points"})
@RequiredArgsConstructor
@Slf4j
public class PointController {

    private final ResponseFactory responseFactory;
    private final PointService pointService;

    @GetMapping("/history-return-point")
    public ResponseEntity<GetHistoryReturnPointDto> findOrder(@ModelAttribute HistoryReturnPointRequest request) {
        try {
            return pointService.listHistoryReturnPoint(request);
        } catch (Exception ex) {
            log.error("Error while finding order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
    
    @PostMapping("/history-return-point/export")
    public ResponseEntity<?> exportHistoryReturnPoint(@ModelAttribute HistoryReturnPointRequest request) {
        try {
            return pointService.exportHistoryReturnPoint(request);
        } catch (Exception ex) {
            log.error("Error while exporting history return point", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
