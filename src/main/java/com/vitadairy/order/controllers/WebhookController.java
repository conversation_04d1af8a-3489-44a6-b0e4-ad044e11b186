package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.services.DeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v4/os/orders/webhook")
@RequiredArgsConstructor
@Slf4j
public class WebhookController {
    private final DeliveryService deliveryService;
    private final ResponseFactory responseFactory;

    @Operation(summary = "Update Delivery Info")
    @PostMapping("/update-delivery")
    public ResponseEntity<?> updateDelivery(@RequestParam(required = false) String hashKey, @RequestBody Object request) {
        try {
            deliveryService.updateDeliveryStatus(hashKey, request);
        } catch (Exception ex) {
            log.error("Error while updating delivery info", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
