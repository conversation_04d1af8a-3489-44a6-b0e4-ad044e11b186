package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.common.ExportTypeDefs;
import com.vitadairy.order.dtos.request.ExportOrderRequest;
import com.vitadairy.order.services.ExportOrderService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("v4/os/admin/orders/export")
@Slf4j
@PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'read'))")
@Tag(name = "ExportOrder Controller", description = "Controller to export order data")
public class AdminExportOrderController {

    private final ResponseFactory responseFactory;
    private final ExportOrderService exportOrderService;

    // todo: delete exported and impored file from local server
    @PostMapping("orders")
    public ResponseEntity<?> exportOrder(@Valid @RequestBody ExportOrderRequest request) {
        try {
            request.doValidate();

            return exportOrderService.export(ExportTypeDefs.EXPORT_ORDER, request.toFetchRequest());
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
