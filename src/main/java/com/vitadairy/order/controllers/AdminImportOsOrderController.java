package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.response.ImportResponse;
import com.vitadairy.order.common.ImportTypeDefs;
import com.vitadairy.order.services.ImportOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;


/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("v4/os/admin/orders/import")
@Slf4j
@PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'create'))")
public class AdminImportOsOrderController {

    private final ResponseFactory responseFactory;
    private final ImportOrderService importOrderService;

    @PostMapping("create")
    public ResponseEntity<ImportResponse> importOrder(@RequestParam("file") MultipartFile file,
                                                      RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_DELIVERY_CODE);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("full-redelivery")
    public ResponseEntity<ImportResponse> importFullReCreateOrder(@RequestParam("file") MultipartFile file,
                                                                  RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_FULL_REDELIVERY_CODE);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("delivery-provider")
    public ResponseEntity<ImportResponse> importDeliveryProvider(@RequestParam("file") MultipartFile file,
                                                                 RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_DELIVERY_PROVIDER_CODE);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("part-redelivery")
    public ResponseEntity<ImportResponse> importPartReCreateOrder(@RequestParam("file") MultipartFile file,
                                                                  RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_PART_REDELIVERY_CODE);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("order-gift")
    public ResponseEntity<ImportResponse> importOrderGift(@RequestParam("file") MultipartFile file,
                                                          RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_ORDER_GIFT);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("return-point")
    public ResponseEntity<ImportResponse> importReturnPoint(@RequestParam("file") MultipartFile file,
                                                            RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importOrderService.doImport(file, ImportTypeDefs.IMPORT_ORDER_RETURN_POINT);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
