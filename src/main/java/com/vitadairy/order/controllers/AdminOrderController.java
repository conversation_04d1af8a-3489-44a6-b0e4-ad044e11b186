package com.vitadairy.order.controllers;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.order.dtos.GetOrderDto;
import com.vitadairy.order.dtos.GetOrderGiftsDetailDto;
import com.vitadairy.order.dtos.request.*;
import com.vitadairy.order.dtos.response.CreateOrderResponse;
import com.vitadairy.order.dtos.response.OrderDetailsResponseDto;
import com.vitadairy.order.services.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping({"v4/os/admin/orders"})
@RequiredArgsConstructor
@Slf4j
public class AdminOrderController {
    private final ResponseFactory responseFactory;
    private final OrderService orderService;

    /**
     * list order with paging option
     *
     * @param request
     * @return
     * <AUTHOR>
     * @since 11/7/24
     */
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'read'))")
    @GetMapping
    public ResponseEntity<GetOrderDto> findOrder(@ModelAttribute ListOrderRequest request) {
        try {
            request.doValidate();
            ResponseEntity<GetOrderDto> response = orderService.listOrder(request);
            return response;
        } catch (Exception ex) {
            log.error("Error while finding order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    /**
     * update order
     *
     * @param id
     * @param request
     * @return
     * <AUTHOR>
     * @since 14/7/24
     */
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'update'))")
    @Operation(summary = "Update order")
    @PutMapping("/{id}")
    public ResponseEntity<OrderDetailsResponseDto> updateOrder(@PathVariable Integer id, @RequestBody @Valid UpdateOrderRequest request) {
        try {
            request.doValidate();
            return orderService.updateOrder(id, request);
        } catch (Exception ex) {
            log.error("Error while updating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    /**
     * list gifts details of an order with paging option
     *
     * @param request
     * @return
     * <AUTHOR>
     * @since 11/7/24
     */
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'read'))")
    @GetMapping(value = "/gifts")
    public ResponseEntity<GetOrderGiftsDetailDto> getOrderGiftDetails(@ModelAttribute GetOrderGiftsDetailRequest request) {
        try {
            request.doValidate();
            return orderService.getOrderGiftsDetail(request);
        } catch (Exception ex) {
            log.error("Error while getting order gift details", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'create'))")
    @Operation
    @PostMapping
    public ResponseEntity<CreateOrderResponse> create(@RequestBody @Valid CreateOrderRequest request) {
        try {
            request.doValidate();
            AuthorizationResponse authResp = AuthenticationHelper.getCurrentUser();
            if (Objects.nonNull(authResp) && Objects.nonNull(authResp.getUserId())) {
                request.setUserId(authResp.getUserId().intValue());
            }
            return orderService.createOrder(request);
        } catch (ApplicationException aex) {
            log.error("Application throw error while creating order", aex);
            return responseFactory.errorDto(aex);
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-don-hang', T(java.util.List).of('full-access', 'update'))")
    @Operation
//    @PreAuthorize("hasAnyAuthority({'ADMIN','INTERNAL'})")
    @PatchMapping("/{id}/show-shipping-fee")
    public ResponseEntity<EntityResponse<Boolean>> toggleShowShippingFee(@PathVariable("id") Integer id, boolean isShowShippingFee) {
        try {
            boolean result = orderService.toggleShowShippingFee(id, isShowShippingFee);
            if (!result) {
                return responseFactory.errorDto("Cannot update showShippingFee of order");
            }
            return responseFactory.successDto(EntityResponse.<Boolean>entityBuilder()
                    .data(result)
                    .statusCode(HttpStatus.OK)
                    .msg("ok")
                    .build());
        } catch (ApplicationException aex) {
            log.error("Application throw error while update showShippingFee of order", aex);
            throw aex;
        } catch (Exception ex) {
            log.error("Error while creating order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
