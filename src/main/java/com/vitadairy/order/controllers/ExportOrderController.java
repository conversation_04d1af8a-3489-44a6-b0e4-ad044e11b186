package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.common.ExportTypeDefs;
import com.vitadairy.order.dtos.request.ExportOrderRequest;
import com.vitadairy.order.services.ExportOrderService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("v4/os/orders/export")
@Slf4j
@Tag(name = "ExportOrder Controller", description = "Controller to export order data")
public class ExportOrderController {

    private final ResponseFactory responseFactory;
    private final ExportOrderService exportOrderService;

    // todo: delete exported and impored file from local server
    @PostMapping("orders")
    public ResponseEntity<?> exportOrder(@Valid @RequestBody ExportOrderRequest request) {
        try {
            request.doValidate();

            return exportOrderService.export(ExportTypeDefs.EXPORT_ORDER, request.toFetchRequest());
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
