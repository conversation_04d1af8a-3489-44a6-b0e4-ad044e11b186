package com.vitadairy.order.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.order.dtos.response.DeliveryDetailsResponseDto;
import com.vitadairy.order.services.DeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v4/os/orders/delivery")
@RequiredArgsConstructor
@Slf4j
public class DeliveryController {
    private final DeliveryService deliveryService;
    private final ResponseFactory responseFactory;

    @Operation(summary = "Get Delivery Info")
    @GetMapping("/{trackingCode}")
    @PreAuthorize("hasAuthority('ADMIN')")
    public ResponseEntity<DeliveryDetailsResponseDto> getDeliveryInfo(@PathVariable String trackingCode){
        try{
            return deliveryService.getDeliveryInfo(trackingCode);
        }
        catch (Exception ex){
            log.error("Error while getting delivery info", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
