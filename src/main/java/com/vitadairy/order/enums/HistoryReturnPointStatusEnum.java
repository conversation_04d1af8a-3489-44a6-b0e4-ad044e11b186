package com.vitadairy.order.enums;

import lombok.Getter;

@Getter
public enum HistoryReturnPointStatusEnum {

    DONE("DON<PERSON>", "Đã hoàn xu");

    private final String code;
    private final String name;

    HistoryReturnPointStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static HistoryReturnPointStatusEnum fromCode(String code) {
        for (HistoryReturnPointStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
