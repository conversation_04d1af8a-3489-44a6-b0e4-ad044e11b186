package com.vitadairy.order.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum OrderDeliveryStatusCodeEnum {
    WAITING_FOR_PROGRESS("Ch<PERSON> xử lý", "Đơn hàng vừa được đặt."),
    ACCEPTED("Đã tiếp nhận", "Đơn hàng đã được tiếp nhận."),
    DELIVERY_IN_PROGRESS("Đang vận chuyển", "Đơn hàng đang được giao."),
    SUCCESS("Giao thành công", "Đơn hàng được giao thành công."),
    FAILURE("Giao thất bại", "Đơn hàng giao thất bại.");

    private final String name;
    private final String description;

    OrderDeliveryStatusCodeEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public static OrderDeliveryStatusCodeEnum fromCode(String code) {
        for (OrderDeliveryStatusCodeEnum value : values()) {
            if (value.toString().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static OrderStatusCodeEnum toOrderStatus(OrderDeliveryStatusCodeEnum deliveryStatus) {
        return switch (deliveryStatus) {
            case WAITING_FOR_PROGRESS -> OrderStatusCodeEnum.IN_PROCESS;
            case ACCEPTED -> OrderStatusCodeEnum.REQUEST_RECEIVED;
            case DELIVERY_IN_PROGRESS -> OrderStatusCodeEnum.DELIVERING;
            case SUCCESS -> OrderStatusCodeEnum.SUCCESS;
            case FAILURE -> OrderStatusCodeEnum.FAILURE;
            default -> null;
        };
    }

    public static List<OrderDeliveryStatusCodeEnum> getAll() {
        return Arrays.asList(
                OrderDeliveryStatusCodeEnum.WAITING_FOR_PROGRESS,
                OrderDeliveryStatusCodeEnum.ACCEPTED,
                OrderDeliveryStatusCodeEnum.DELIVERY_IN_PROGRESS,
                OrderDeliveryStatusCodeEnum.SUCCESS,
                OrderDeliveryStatusCodeEnum.FAILURE
        );
    }
}
