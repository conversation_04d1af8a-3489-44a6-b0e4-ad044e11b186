package com.vitadairy.order.enums;

import lombok.Getter;

@Getter
public enum GhtkDeliveryStatusCodeEnum {

    CANCELLED(-1, "Đơn hàng hủy"),
    WAITING_FOR_ACCEPTANCE(1, "Ch<PERSON>a tiếp nhận"),
    ACCEPTED(2, "Đã tiếp nhận"),
    PICKED_UP(3, "Đã lấy hàng/Đ<PERSON> nhập kho"),
    DELIVERING(4, "Đã điều phối giao hàng/<PERSON>ang giao hàng"),
    DELIVERED(5, "Đã giao hàng/Ch<PERSON>a đối soát"),
    RECONCILED(6, "Đã đối soát"),
    CANT_PICK_UP(7, "Không lấy được hàng"),
    DELAY_PICK_UP(8, "Hoãn lấy hàng"),
    CANT_DELIVER(9, "Không giao đượ<PERSON> hàng"),
    DELAY_DELIVER(10, "Delay giao hàng"),
    RECONCILED_RETURN(11, "Đ<PERSON> đối soát trả hàng"),
    PICKING_UP(12, "Đ<PERSON> điều phối lấy hàng/Đang lấy hàng"),
    RETURN_ORDER(13, "Bồi hoàn"),
    RETURNING(20, "Đang trả hàng (COD cầm hàng đi trả)"),
    RETURNED(21, "Đã trả hàng (COD đã trả xong hàng)"),
    SHIPPER_PICKED_UP(123, "Đã lấy hàng/Đã nhập kho"),
    SHIPPER_CANT_PICK_UP(127, "Đã lấy hàng/Đã nhập kho"),
    SHIPPER_DELAY_PICK_UP(128, "Delay lấy hàng"),
    SHIPPER_DELIVERED(45, "Đã giao hàng/ Chưa đối soát"),
    SHIPPER_CANT_DELIVER(49, "Không giao được hàng"),
    SHIPPER_DELAY_DELIVER(410, "Delay giao hàng");


    private final Integer code;
    private final String description;

    GhtkDeliveryStatusCodeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static GhtkDeliveryStatusCodeEnum fromCode(Integer code) {
        for (GhtkDeliveryStatusCodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
