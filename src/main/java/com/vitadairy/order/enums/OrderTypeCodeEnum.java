package com.vitadairy.order.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum OrderTypeCodeEnum {
    NORMAL("Th<PERSON><PERSON><PERSON>", "Đơn hàng thường"),
    REDELIVERY("Giao L<PERSON>", "Đơn hàng giao lại"),
    MANUAL("Manual", "Đơn hàng tách manual"),
    SEPARATED("Đã tách", "Đơn đã được tách manual");

    public final String name;
    public final String description;

    private OrderTypeCodeEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public static OrderTypeCodeEnum fromCode(String code) {
        for (OrderTypeCodeEnum value : values()) {
            if (value.toString().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<OrderTypeCodeEnum> getAll() {
        return Arrays.asList(
                OrderTypeCodeEnum.NORMAL,
                OrderTypeCodeEnum.REDELIVERY,
                OrderTypeCodeEnum.MANUAL,
                OrderTypeCodeEnum.SEPARATED
        );
    }
}
