package com.vitadairy.order.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum OrderStatusCodeEnum {
    IN_PROCESS("Chờ xử lý", "Đơn hàng vừa được đặt."),
    REQUEST_RECEIVED("Đã tiếp nhận", "Đã tiếp nhận đơn hàng."),
    DELIVERING("Đang giao hàng", "Đơn hàng đang được giao."),
    SUCCESS("Giao thành công", "Đơn hàng được giao thành công."),
    FAILURE("Giao thất bại", "Đơn hàng giao thất bại."),
    HIDDEN("Ẩn", "Đơn hàng đã bị ẩn.");

    public final String name;
    public final String description;

    OrderStatusCodeEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public static OrderStatusCodeEnum fromCode(String code) {
        for (OrderStatusCodeEnum value : values()) {
            if (value.toString().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<OrderStatusCodeEnum> getAll() {
        return Arrays.asList(
                OrderStatusCodeEnum.IN_PROCESS,
                OrderStatusCodeEnum.REQUEST_RECEIVED,
                OrderStatusCodeEnum.DELIVERING,
                OrderStatusCodeEnum.SUCCESS,
                OrderStatusCodeEnum.FAILURE
        );
    }
}
