package com.vitadairy.order.enums;

import lombok.Getter;

@Getter
public enum VtpDeliveryStatusCodeEnum {

    ACCEPTED(100, "Đã tiếp nhận"),
    VTP_CANCELLED_PICK_UP(101, "VTP hủy lấy hàng"),
    PICKING_UP_1(102, "<PERSON><PERSON> lấy hàng"),
    PICKING_UP_2(103, "<PERSON><PERSON> lấy hàng"),
    PICKING_UP_3(104, "<PERSON><PERSON> lấy hàng"),
    PICKED_UP(105, "Đã lấy hàng"),
    SHOP_CANCELLED_PICK_UP_1(106, "Shop hủy lấy"),
    SHOP_CANCELLED_PICK_UP_2(107, "Shop hủy lấy"),
    PICKED_UP_1(200, "Đã lấy hàng"),
    PICKED_UP_2(201, "Đã lấy hàng"),
    PICKED_UP_3(202, "<PERSON><PERSON> lấy hàng"),
    TRANSPORTING_1(300, "<PERSON><PERSON> vận chuyển"),
    TRANSPORTING_2(301, "<PERSON><PERSON> vận chuyển"),
    TRANSPORTING_3(302, "<PERSON><PERSON> vận chuyển"),
    TRANSPORTING_4(303, "Đang vận chuyển"),
    TRANSPORTING_5(400, "Đang vận chuyển"),
    TRANSPORTING_6(401, "Đang vận chuyển"),
    TRANSPORTING_7(402, "Đang vận chuyển"),
    TRANSPORTING_8(403, "Đang vận chuyển"),
    DELIVERING_1(500, "Đang giao hàng"),
    DELIVERED(501, "Giao thành công"),
    RETURNING_1(502, "Đang chuyển hoàn, Đã duyệt hoàn"),
    DESTROYED(503, "Tiêu hủy"),
    RETURNED(504, "Đã trả"),
    WAITING_FOR_PROGRESS_1(505, "Chờ xử lý"),
    WAITING_FOR_PROGRESS_2(507, "Chờ xử lý"),
    WAITING_FOR_REDELIVERY(506, "Chờ phát lại"),
    DELIVERING_2(508, "Phát tiếp"),
    DELIVERING_3(509, "Đang giao hàng"),
    DELIVERING_4(510, "Đang giao hàng"),
    RETURNING_2(515, "Đang chuyển hoàn"),
    DELIVERING_5(550, "Phát tiếp");

    private final Integer code;
    private final String description;

    VtpDeliveryStatusCodeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static VtpDeliveryStatusCodeEnum fromCode(Integer code) {
        for (VtpDeliveryStatusCodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
