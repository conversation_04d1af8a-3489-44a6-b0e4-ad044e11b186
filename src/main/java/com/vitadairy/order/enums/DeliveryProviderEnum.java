package com.vitadairy.order.enums;

import lombok.Getter;

@Getter
public enum DeliveryProviderEnum {

    VTP("VTP", "Viettel Post"),
    GHTK("GHTK", "Giao hàng tiết kiệm"),
    SLG("SLG", "Smart Log");

    private final String code;
    private final String name;

    DeliveryProviderEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DeliveryProviderEnum fromCode(String code) {
        for (DeliveryProviderEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
