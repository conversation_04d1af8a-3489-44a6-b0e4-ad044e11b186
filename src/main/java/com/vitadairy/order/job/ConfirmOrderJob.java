package com.vitadairy.order.job;

import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.order.services.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("confirmOrderJob")
@Slf4j
@RequiredArgsConstructor
public class ConfirmOrder<PERSON>ob implements ScheduledJobRunner {

    private final OrderService orderService;
    @Override
    public void run() {
        orderService.confirmOrderByJob();
    }
}
