package com.vitadairy.warehouse.dto;

import org.springframework.http.ResponseEntity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ResponseDto {
    private Object data;
    private Meta meta;

    @AllArgsConstructor
    @Getter
    @Setter
    public static class Meta {
        private int code;
        private String msg;
    }

    public static ResponseEntity<Object> success(Object data) {
        return ResponseEntity.ok(new ResponseDto(data, new Meta(200, "success")));
    }

    public static ResponseEntity<Object> fail(Object data,int code, String msg) {
        return ResponseEntity.badRequest().body(new ResponseDto(data, new Meta(code, msg)));
    }
}
