package com.vitadairy.warehouse.dto;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.http.HttpStatusCode;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class HistoryLuckyTransactionResponseDto extends EntityResponse<HistoryLuckyTransactionDto> {
    public HistoryLuckyTransactionResponseDto(HistoryLuckyTransactionDto data) {
        super(data, HttpStatusCode.valueOf(200), "ok");
    }
}