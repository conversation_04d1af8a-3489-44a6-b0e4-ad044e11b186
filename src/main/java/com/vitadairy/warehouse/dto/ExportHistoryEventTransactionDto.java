package com.vitadairy.warehouse.dto;

import com.vitadairy.warehouse.entities.HistoryLuckyTransaction;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExportHistoryEventTransactionDto {
    private Long userId;
    private String phoneNumber;
    private String userName;
    private String sku;
    private String giftName;
    private String brand;
    private String transactionCode;
    private Date receivedDate;
    private Date registeredDate;
    private String qrCode;
    private String spoonCode;
    private String province;
    private String status;

    public static ExportHistoryEventTransactionDto fromEntity(HistoryLuckyTransaction entity) {
        return new ExportHistoryEventTransactionDto()
                .setUserId(entity.getUserId())
                .setPhoneNumber(entity.getPhoneNumber())
                .setUserName(entity.getUserName())
                .setSku(entity.getSku())
                .setGiftName(entity.getGiftName())
                .setBrand(entity.getBrand())
                .setTransactionCode(entity.getTransactionCode())
                .setReceivedDate(Objects.nonNull(entity.getReceivedDate()) ? Date.from(entity.getReceivedDate().atZone(ZoneOffset.UTC).toInstant()) : null)
                .setRegisteredDate(Objects.nonNull(entity.getRegisteredDate()) ? Date.from(entity.getRegisteredDate().atZone(ZoneOffset.UTC).toInstant()) : null)
                .setQrCode(entity.getQrCode())
                .setSpoonCode(entity.getSpoonCode())
                .setProvince(entity.getProvince())
                .setStatus(entity.getStatus());
    }
}