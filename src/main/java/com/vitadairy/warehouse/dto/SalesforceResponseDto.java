package com.vitadairy.warehouse.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpStatus;

import java.time.Instant;

@Getter
@Setter
@Accessors(chain = true)
public class SalesforceResponseDto {
    @JsonIgnore
    private HttpStatus httpStatus;
    @JsonIgnore
    private Instant sentAt;
    @JsonIgnore
    private Instant receivedAt;
    private Integer status;
    private String response;

    @Getter
    @Setter
    public static class Payload {
        private Integer status;
        private String message;
        @JsonProperty("ref_id")
        private String refId;
        @JsonProperty("ref_sf_id")
        private String refSfId;
        @JsonProperty("sf_id")
        private String sfId;

        @Override
        public String toString() {
            return "Payload{" +
                    "status=" + status +
                    ", message='" + message + '\'' +
                    ", refId='" + refId + '\'' +
                    ", refSfId='" + refSfId + '\'' +
                    ", sfId='" + sfId + '\'' +
                    '}';
        }
    }
}
