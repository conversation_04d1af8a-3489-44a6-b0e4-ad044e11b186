package com.vitadairy.warehouse.dto;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.main.request.ExportRequest;

public class ExportHistoryEventRequest extends ExportRequest<ListHistoryLuckyTransactionRequest, Integer> {
    @Override
    public void doValidate() throws Exception {
        super.doValidate();
    }

    @Override
    public FetchRequest<ListHistoryLuckyTransactionRequest> toFetchRequest() {
        return super.toFetchRequest();
    }
}
