package com.vitadairy.warehouse.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vitadairy.warehouse.entities.HistoryLuckyTransaction;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class HistoryLuckyTransactionDto {
    private Long id;
    private Long userId;
    private String phoneNumber;
    private String userName;
    private String sku;
    private String giftName;
    private String brand;
    private String transactionCode;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime receivedDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime registeredDate;
    private String qrCode;
    private String spoonCode;
    private String province;
    private String status;
    private Long eventId;
    private String eventName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime createdAt;

    public static HistoryLuckyTransactionDto fromEntity(HistoryLuckyTransaction entity){
        return new HistoryLuckyTransactionDto()
                .setId(entity.getId())
                .setUserId(entity.getUserId())
                .setPhoneNumber(entity.getPhoneNumber())
                .setUserName(entity.getUserName())
                .setSku(entity.getSku())
                .setGiftName(entity.getGiftName())
                .setBrand(entity.getBrand())
                .setTransactionCode(entity.getTransactionCode())
                .setReceivedDate(entity.getReceivedDate())
                .setRegisteredDate(entity.getRegisteredDate())
                .setQrCode(entity.getQrCode())
                .setSpoonCode(entity.getSpoonCode())
                .setProvince(entity.getProvince())
                .setStatus(entity.getStatus())
                .setEventId(entity.getEventId())
                .setEventName(entity.getEventName())
                .setCreatedAt(entity.getCreatedAt());
    }

}