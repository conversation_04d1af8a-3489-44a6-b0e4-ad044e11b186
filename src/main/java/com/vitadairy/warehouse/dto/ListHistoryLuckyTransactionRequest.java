package com.vitadairy.warehouse.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vitadairy.main.request.PageableRequest;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Data
public class ListHistoryLuckyTransactionRequest extends PageableRequest {
    private String giftName;
    private List<String> brand;
    private String eventName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime fromDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime toDate;

    public void doValidate() {
        if (StringUtils.isEmpty(giftName)) {
            giftName = null;
        }
        if(CollectionUtils.isEmpty(brand)){
            brand = null;
        }
        if (StringUtils.isEmpty(eventName)) {
            eventName = null;
        }
    }
}
