package com.vitadairy.warehouse.dto;

import com.vitadairy.main.response.PageableResponse;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatusCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class GetHistoryLuckyTransactionDto extends PageableResponse<HistoryLuckyTransactionDto> {

    @Builder(builderMethodName = "historyLuckTransactionBuilder")
    public GetHistoryLuckyTransactionDto(List<HistoryLuckyTransactionDto> items, Long total, Integer page, Integer pageSize,
                                         HttpStatusCode statusCode, String msg) {
        super(items, total, 0L, page, pageSize, statusCode, msg);
    }

}
