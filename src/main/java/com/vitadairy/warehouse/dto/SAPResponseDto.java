package com.vitadairy.warehouse.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpStatus;

import java.time.Instant;

@Getter
@Setter
@Accessors(chain = true)
public class SAPResponseDto {
    @JsonIgnore
    private HttpStatus httpStatus;
    @JsonIgnore
    private Instant sentAt;
    @JsonIgnore
    private Instant receivedAt;
    private String response;

    @Getter
    @Setter
    public static class Payload {
        private String msgCode;
        private String message;

        @Override
        public String toString() {
            return "Payload{" +
                    "msgCode='" + msgCode + '\'' +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}
