package com.vitadairy.warehouse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.dto.ThirdPartyTransactionDto;
import com.vitadairy.main.dto.TransactionDto;
import com.vitadairy.warehouse.client.TransactionClientFactory;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.ThirdPartyTransactionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;

@ConditionalOnWebApplication
@Service
@Slf4j
@AllArgsConstructor
public class Consumer {
    private final ObjectMapper objectMapper;
    private final TransactionClientFactory transactionClientFactory;
    private final TransactionRepository transactionRepository;
    private final ThirdPartyTransactionService thirdPartyTransactionService;

    // @KafkaListener(topics = "${spring.kafka.wh.3rd.transaction.topic}", groupId = "${spring.kafka.wh.transaction.consumer.group}")
    public void consume3rdTransaction(@Payload String message,
                                      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                      @Header(KafkaHeaders.RECEIVED_TIMESTAMP) Long eventTimestamp,
                                      @Header(KafkaHeaders.OFFSET) Long offset,
                                      Acknowledgment ack) {
        try {
            log.info("<--- Partition: {}, Offset: {}, Message: {} on {}", partition, offset, message, eventTimestamp);
            var dto = objectMapper.readValue(message, ThirdPartyTransactionDto.class);
            thirdPartyTransactionService.save3rdTransaction(dto);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("Error while save transaction dto partition={}, message={}, error={}",
                    partition, message, ExceptionUtils.getStackTrace(e));
        }

    }

    // @KafkaListener(topics = "${spring.kafka.wh.transaction.topic}", groupId = "${spring.kafka.wh.transaction.consumer.group}")
    public void consume(@Payload String message,
                        @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                        @Header(KafkaHeaders.RECEIVED_TIMESTAMP) Long eventTimestamp,
                        @Header(KafkaHeaders.OFFSET) Long offset,
                        Acknowledgment ack) {
        try {
            log.info("<--- Partition: {}, Offset: {}, Message: {} on {}", partition, offset, message, eventTimestamp);
            var dto = objectMapper.readValue(message, TransactionDto.class);
            var transaction = new Transaction()
                    .setCode(dto.getCode())
                    .setPayload(dto.getPayload())
                    .setTListPayload(dto.getTListPayload())
                    .setDestination(dto.getDestination().name())
                    .setCreatedAt(dto.getCreatedAt().truncatedTo(ChronoUnit.SECONDS));

            transaction = this.transactionRepository.save(transaction);
            transaction.setInitSend(true);
            this.transactionClientFactory.send(transaction);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("Error while save transaction dto partition={}, message={}, error={}",
                    partition, message, ExceptionUtils.getStackTrace(e));
        }

    }
}
