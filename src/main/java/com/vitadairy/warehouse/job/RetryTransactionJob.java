package com.vitadairy.warehouse.job;

import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.warehouse.client.TransactionClientFactory;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.WsApiConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Service("retryTransactionJob")
@Slf4j
public class RetryTransactionJob implements ScheduledJobRunner {
    private final TransactionRepository transactionRepository;
    private final WsApiConfigService wsApiConfigService;
    private final TransactionClientFactory transactionClientFactory;

    public RetryTransactionJob(
            @Qualifier("warehouseTransactionRepository") TransactionRepository transactionRepository,
            WsApiConfigService wsApiConfigService,
            TransactionClientFactory transactionClientFactory) {
        this.transactionRepository = transactionRepository;
        this.wsApiConfigService = wsApiConfigService;
        this.transactionClientFactory = transactionClientFactory;
    }

    @Override
    public void run() {
        log.info("[RetryTransactionJob] Starting");
        var endRange = Instant.now();
        var startRange = endRange.minus(12, ChronoUnit.HOURS);

        log.info("[RetryTransactionJob] Query range: Start: {}, End: {}", startRange, endRange);
        List<Transaction> transactions = this.transactionRepository.getRetryTransactionBetween(startRange, endRange);
        log.info("[RetryTransactionJob] Retry {} transactions", transactions.size());

        var apiMap = this.wsApiConfigService.getAllApiConfigMap();

        for (var transaction : transactions) {
            if ("TRIPLAYZ".equals(transaction.getDestination())) {
                this.transactionClientFactory.send(transaction, null);
            } else {
                var destinationApiMap = apiMap.get(transaction.getDestination());
                if (destinationApiMap == null) {
                    log.warn("[RetryTransactionJob] Destination {} does not have api config", transaction.getDestination());
                    continue;
                }
                var apiConfig = destinationApiMap.get(transaction.getCode());
                this.transactionClientFactory.send(transaction, apiConfig);
            }
        }
    }
}
