package com.vitadairy.warehouse.job;

import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.warehouse.repositories.SalesforcePayloadPairingRepository;

import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("deleteExpiredSfPairingJob")
public class DeleteExpiredSfPairingJob implements ScheduledJobRunner    {
    private final SalesforcePayloadPairingRepository salesforcePayloadPairingRepository;
    private final Integer expireDays;

    public DeleteExpiredSfPairingJob(SalesforcePayloadPairingRepository salesforcePayloadPairingRepository,
                                    @Value("${salesforce.payload.pairing.expire.days:180}") Integer expireDays) {
        this.salesforcePayloadPairingRepository = salesforcePayloadPairingRepository;
        this.expireDays = expireDays;
    }

    @Override
    public void run() {
        Instant cutoffDate = Instant.now().minus(expireDays, ChronoUnit.DAYS);
        log.info("Deleting expired SF pairing, Cutoff date: {}", cutoffDate);
        int count = this.salesforcePayloadPairingRepository.countByUpdatedAtBefore(cutoffDate);
        log.info("Count of expired SF pairing: {}", count);
        int deletedRows = this.salesforcePayloadPairingRepository.deleteAllByUpdatedAtBefore(cutoffDate);
        log.info("Deleted {} expired SF pairing", deletedRows);
    }
}
