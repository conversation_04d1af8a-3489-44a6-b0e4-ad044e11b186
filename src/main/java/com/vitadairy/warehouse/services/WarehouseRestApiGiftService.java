package com.vitadairy.warehouse.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.main.response.RestApiBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WarehouseRestApiGiftService {
    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;

    public WarehouseRestApiGiftService(
            @Qualifier("restTemplateWHOsGiftClient") RestTemplateClient restTemplateClient,
            ObjectMapper objectMapper) {
        this.restTemplateClient = restTemplateClient;
        this.objectMapper = objectMapper;
    }

    public RestApiBaseResponse updateSfGift(String token, List<String> requestData) {
        restTemplateClient.buildAuth(token);
        try {
            List request = objectMapper.readValue(objectMapper.writeValueAsString(requestData), List.class);
            return restTemplateClient.post("/in/gs/gifts/update-sf-gift", request, new ParameterizedTypeReference<>() {});
        } catch (JsonProcessingException ex) {
            log.error("Error when parse request to api", ex);
            throw new RuntimeException(ex.getMessage());
        }
    }
}
