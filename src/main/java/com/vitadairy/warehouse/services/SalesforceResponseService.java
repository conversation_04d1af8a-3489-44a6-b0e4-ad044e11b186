package com.vitadairy.warehouse.services;

import com.vitadairy.warehouse.dto.SalesforceResponseDto;
import com.vitadairy.warehouse.entities.SalesforceResponse;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.SalesforceResponseRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service("warehouseJobSalesforceResponseService")
public class SalesforceResponseService {
    private final SalesforceResponseRepository salesforceResponseRepository;

    public SalesforceResponseService(
            @Qualifier("warehouseJobSalesforceResponseRepository") SalesforceResponseRepository salesforceResponseRepository
    ) {
        this.salesforceResponseRepository = salesforceResponseRepository;
    }

    public SalesforceResponse save(Transaction transaction, SalesforceResponseDto dto) {
        var responseEntity = new SalesforceResponse()
                .setStatus(dto.getStatus())
                .setTransactionId(transaction.getId())
                .setHttpCode(dto.getHttpStatus().value())
                .setResponse(dto.getResponse())
                .setSendAt(dto.getSentAt())
                .setReceivedAt(dto.getReceivedAt());

        return this.salesforceResponseRepository.save((SalesforceResponse)responseEntity);
    }
}
