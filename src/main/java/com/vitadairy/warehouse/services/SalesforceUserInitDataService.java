package com.vitadairy.warehouse.services;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.warehouse.client.TransactionClientFactory;
import com.vitadairy.warehouse.dto.SalesforceUserInitDataRequestDto;
import com.vitadairy.warehouse.dto.SalesforceUserInitDataResponseDto;
import com.vitadairy.warehouse.entities.SalesforceUserInitData;
import com.vitadairy.warehouse.repositories.SalesforceUserInitDataRepository;
import com.vitadairy.warehouse.repositories.TransactionRepository;

import lombok.extern.slf4j.Slf4j;

@Service("warehouseSalesforceUserInitDataService")
@Slf4j
public class SalesforceUserInitDataService {
    private final SalesforceUserInitDataRepository salesforceUserInitDataRepository;
    private final TransactionRepository transactionRepository;
    private final TransactionClientFactory transactionClientFactory;
    private final TaskExecutor retryFailedTransactionExecutor;

    public SalesforceUserInitDataService(SalesforceUserInitDataRepository salesforceUserInitDataRepository, 
                                        @Qualifier("warehouseTransactionRepository") TransactionRepository transactionRepository, 
                                        TransactionClientFactory transactionClientFactory,
                                        @Qualifier("salesforceTransactionHandlerExecutor") TaskExecutor retryFailedTransactionExecutor) {
        this.transactionRepository = transactionRepository;
        this.transactionClientFactory = transactionClientFactory;
        this.salesforceUserInitDataRepository = salesforceUserInitDataRepository;
        this.retryFailedTransactionExecutor = retryFailedTransactionExecutor;
    }

    public SalesforceUserInitData save(SalesforceUserInitData salesforceUserInitData) {
        return this.salesforceUserInitDataRepository.save(salesforceUserInitData);
    }

    public SalesforceUserInitDataResponseDto updateSalesforceUserInitData(SalesforceUserInitDataRequestDto dto) {
        var status = dto.getStatus();
        var entity = this.salesforceUserInitDataRepository.findByUserId(dto.getUserId())
                .map(existing -> {
                    if (status == SalesforceUserInitDataRequestDto.Status.SUCCESS) {
                        if (existing.getTriggerEndTime() == null) {
                            existing.setTriggerEndTime(Instant.now());
                        }

                        if (existing.getTriggerStartTime() == null) {
                            existing.setTriggerStartTime(Instant.now());
                        }
                    } else {
                        if (existing.getTriggerStartTime() == null) {
                            existing.setTriggerStartTime(Instant.now());
                        }
                    }
                    return this.salesforceUserInitDataRepository.save(existing);
                })
                .orElseGet(() -> {
                    SalesforceUserInitData e = new SalesforceUserInitData();
                    e.setUserId(dto.getUserId());
                    if (status == SalesforceUserInitDataRequestDto.Status.SUCCESS) {
                        e.setTriggerStartTime(Instant.now());
                        e.setTriggerEndTime(Instant.now());
                    } else {
                        e.setTriggerStartTime(Instant.now());
                        e.setTriggerEndTime(null);
                    }
                    return this.salesforceUserInitDataRepository.save(e);
                });

        int retryCount = 0;
        if (entity.getTriggerEndTime() != null) {
            retryCount = this.retrySyncFailedTransaction(entity);
        }

        return new SalesforceUserInitDataResponseDto()
            .setUserId(entity.getUserId())
            .setTriggerStartTime(entity.getTriggerStartTime())
            .setTriggerEndTime(entity.getTriggerEndTime())
            .setRetryCount(retryCount);
    }

    private int retrySyncFailedTransaction(SalesforceUserInitData entity) {
        var userId = entity.getUserId();
        var startTime = entity.getTriggerStartTime();
        var endTime = entity.getTriggerEndTime();

        var transactions = this.transactionRepository.getFailedTransactionByUserIdBetween(String.valueOf(userId), startTime, endTime);
        var count = transactions.size();

        // retry sync FAILED transaction async
        CompletableFuture.runAsync(() -> {
            for (var transaction : transactions) {
                transaction.setStatus(TransactionStatus.RETRY);
                this.transactionClientFactory.send(transaction);
            }
        }, this.retryFailedTransactionExecutor);

        return count;
    }
}
