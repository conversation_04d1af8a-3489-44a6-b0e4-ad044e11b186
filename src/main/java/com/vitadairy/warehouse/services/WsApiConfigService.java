package com.vitadairy.warehouse.services;

import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.services.apiconfig.ApiConfigService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WsApiConfigService {
    private final List<ApiConfigService> apiConfigServices;
    private Map<String, Map<String, ? extends WsBaseApiConfig>> apiConfigMap;

    public WsApiConfigService(List<ApiConfigService> apiConfigServices) {
        this.apiConfigServices = apiConfigServices;
    }

    public Map<String, Map<String, ? extends WsBaseApiConfig>> getAllApiConfigMap() {
        if (this.apiConfigMap == null) {
            this.apiConfigMap = apiConfigServices.stream().collect(Collectors.toMap(
                    s -> s.getDestination().name(), ApiConfigService::getApiMap)
            );
        }
        return this.apiConfigMap;
    }
}
