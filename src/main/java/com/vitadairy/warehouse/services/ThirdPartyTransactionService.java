package com.vitadairy.warehouse.services;

import com.vitadairy.main.dto.ThirdPartyTransactionDto;
import com.vitadairy.warehouse.entities.ThirdPartyTransaction;
import com.vitadairy.warehouse.repositories.ThirdPartyTransactionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ThirdPartyTransactionService {

    private final ThirdPartyTransactionRepository thirdPartyTransactionRepository;

    public void save3rdTransaction(ThirdPartyTransactionDto dto) {
        var transaction = new ThirdPartyTransaction()
                .setTransactionCode(dto.getTransactionCode())
                .setRequest(dto.getRequest())
                .setResponse(dto.getResponse())
                .setStatus(dto.getStatus())
                .setNotes(dto.getNotes())
                .setCreatedAt(dto.getCreatedAt());
        thirdPartyTransactionRepository.save(transaction);
    }
}
