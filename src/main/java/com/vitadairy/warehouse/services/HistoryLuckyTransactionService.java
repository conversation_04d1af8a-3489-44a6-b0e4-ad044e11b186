package com.vitadairy.warehouse.services;

import com.opencsv.CSVWriter;
import com.vitadairy.main.configs.ApplicationProperties;
import com.vitadairy.main.dto.HistoryLuckyTransactionUpdateRequestDto;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.warehouse.common.DeletableFileInputStream;
import com.vitadairy.warehouse.dto.ExportHistoryEventRequest;
import com.vitadairy.warehouse.dto.GetHistoryLuckyTransactionDto;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionDto;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionRequestDto;
import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import com.vitadairy.warehouse.entities.HistoryLuckyTransaction;
import com.vitadairy.warehouse.repositories.HistoryLuckyTransactionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HistoryLuckyTransactionService {
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    private final String hashedKey;
    private final HistoryLuckyTransactionRepository historyLuckyTransactionRepository;
    private final ApplicationProperties applicationProperties;

    public HistoryLuckyTransactionService(HistoryLuckyTransactionRepository historyLuckyTransactionRepository,
                                          @Value("${public.history-event.hashed.key}") String hashedKey,
                                          ApplicationProperties applicationProperties
    ) {
        this.historyLuckyTransactionRepository = historyLuckyTransactionRepository;
        this.hashedKey = hashedKey;
        this.applicationProperties = applicationProperties;
    }

    public GetHistoryLuckyTransactionDto getListHistoryLuckyTransaction(ListHistoryLuckyTransactionRequest request) {
        Page<HistoryLuckyTransaction> transactions = historyLuckyTransactionRepository.getListHistoryLuckyTransaction(request, request.getPageable());
        return GetHistoryLuckyTransactionDto.historyLuckTransactionBuilder()
                .items(transactions.getContent().stream().map(HistoryLuckyTransactionDto::fromEntity).collect(Collectors.toList()))
                .total(transactions.getTotalElements())
                .page(request.getPage())
                .pageSize(request.getSize())
                .build();
    }

    public HistoryLuckyTransactionDto findById(Long id) {
        return historyLuckyTransactionRepository.findById(id)
                .map(HistoryLuckyTransactionDto::fromEntity)
                .orElseThrow(() -> new RuntimeException("History lucky transaction not found"));
    }

    public void validateHashKey(String hashKey) {
        if (StringUtils.isEmpty(hashKey) || !hashedKey.equals(hashKey)) {
            throw new ApplicationException("Invalid request", HttpStatus.FORBIDDEN);
        }
    }

    public HistoryLuckyTransactionDto saveTransaction(HistoryLuckyTransactionRequestDto dto) {
        List<HistoryLuckyTransaction> transactions = historyLuckyTransactionRepository.findAllByTransactionCode(dto.getTransactionCode());
        if (!transactions.isEmpty()) {
            throw new ApplicationException("Transaction code already exists", HttpStatus.BAD_REQUEST);
        }
        var transaction = new HistoryLuckyTransaction()
                .setUserId(dto.getUserId())
                .setPhoneNumber(dto.getPhoneNumber())
                .setUserName(dto.getUserName())
                .setSku(dto.getSku())
                .setGiftName(dto.getGiftName())
                .setBrand(dto.getBrand())
                .setTransactionCode(dto.getTransactionCode())
                .setReceivedDate(Objects.nonNull(dto.getReceivedDate()) ? LocalDateTime.ofInstant(dto.getReceivedDate().toInstant(), ZoneId.of("Asia/Bangkok")) : null)
                .setRegisteredDate(Objects.nonNull(dto.getRegisteredDate()) ? LocalDateTime.ofInstant(dto.getRegisteredDate().toInstant(), ZoneId.of("Asia/Bangkok")) : null)
                .setQrCode(dto.getQrCode())
                .setSpoonCode(dto.getSpoonCode())
                .setProvince(dto.getProvince())
                .setStatus(dto.getStatus())
                .setCreatedAt(LocalDateTime.now())
                .setEventId(dto.getEventId())
                .setEventName(dto.getEventName());
        historyLuckyTransactionRepository.save(transaction);
        return HistoryLuckyTransactionDto.fromEntity(transaction);
    }

    public HistoryLuckyTransactionDto updateTransaction(HistoryLuckyTransactionUpdateRequestDto dto) {
        List<HistoryLuckyTransaction> transactions = historyLuckyTransactionRepository.findAllByTransactionCode(dto.getTransactionCode());
        if (!transactions.isEmpty()) {
            transactions.forEach(transaction -> transaction.setStatus(dto.getStatus()));
            historyLuckyTransactionRepository.saveAll(transactions);
            return HistoryLuckyTransactionDto.fromEntity(transactions.get(0));
        } else {
            throw new ApplicationException("Transaction not found", HttpStatus.NOT_FOUND);
        }
    }

    public ResponseEntity<?> exportHistoryLuckyTransaction(ExportHistoryEventRequest fetchRequest) throws Exception {
        ListHistoryLuckyTransactionRequest request = fetchRequest.getFetchRequest();
        request.doValidate();
        String fileName = IdUtils.generateId("HISTORY_EVENT_") + ".csv";
        String filePath = applicationProperties.getExportConfig().getPath() + "/" + fileName;
        int batch = 5000;
        int index = 0;
        writeToFile(null, filePath, -1);
        while (true) {
            Pageable pageable = PageRequest.of(index, batch);
            Page<HistoryLuckyTransaction> transactions = historyLuckyTransactionRepository.getListHistoryLuckyTransaction(request, pageable);
            if (transactions.isEmpty()) {
                break;
            }

            writeToFile(transactions.getContent(), filePath, index);

            if (transactions.getTotalPages() == 1 || transactions.getTotalPages() == index + 1) {
                break;
            }
            index++;
        }
        File file = new File(filePath);
        DeletableFileInputStream inputStream = new DeletableFileInputStream(file);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", fileName);
        httpHeaders.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        httpHeaders.setAccessControlExposeHeaders(List.of("Content-Disposition"));
        httpHeaders.setContentLength(inputStream.available());
        return ResponseEntity.ok()
                .headers(httpHeaders)
                .body(new InputStreamResource(inputStream));
    }

    private void writeToFile(List<HistoryLuckyTransaction> transactions, String filePath, Integer index) {
        try (CSVWriter csvWriter = new CSVWriter(new FileWriter(filePath, true))) {
            if (index == -1) {
                String[] header = {"User ID", "Phone number", "User name", "Sku", "Gift name", "Brand", "Transaction External Id", "Gift received date", "User registered date", "QR code", "Spoon code", "Province", "Status"};
                csvWriter.writeNext(header);
            } else {
                if (!transactions.isEmpty()) {
                    for (HistoryLuckyTransaction transaction : transactions) {
                        String[] data = {
                                transaction.getUserId().toString(),
                                transaction.getPhoneNumber(),
                                transaction.getUserName(),
                                transaction.getSku(),
                                transaction.getGiftName(),
                                transaction.getBrand(),
                                transaction.getTransactionCode(),
                                Objects.nonNull(transaction.getReceivedDate()) ? transaction.getReceivedDate().format(formatter) : null,
                                Objects.nonNull(transaction.getRegisteredDate()) ? transaction.getRegisteredDate().format(formatter) : null,
                                transaction.getQrCode(),
                                transaction.getSpoonCode(),
                                transaction.getProvince(),
                                transaction.getStatus()
                        };
                        csvWriter.writeNext(data);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while writing to file", e);
        }
    }
}
