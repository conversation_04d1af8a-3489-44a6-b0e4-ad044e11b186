package com.vitadairy.warehouse.services;

import com.vitadairy.warehouse.dto.SAPResponseDto;
import com.vitadairy.warehouse.entities.SapResponse;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.SapResponseRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service("warehouseSAPResponseService")
public class SapResponseService {
    private final SapResponseRepository sapResponseRepository;

    public SapResponseService(
            @Qualifier("warehouseSAPResponseRepository") SapResponseRepository sapResponseRepository
    ) {
        this.sapResponseRepository = sapResponseRepository;
    }

    public SapResponse save(Transaction transaction, SAPResponseDto dto) {
        var responseEntity = new SapResponse()
                .setTransactionId(transaction.getId())
                .setHttpCode(dto.getHttpStatus().value())
                .setResponse(dto.getResponse())
                .setSendAt(dto.getSentAt())
                .setReceivedAt(dto.getReceivedAt());


        return this.sapResponseRepository.save((SapResponse) responseEntity);
    }
}
