package com.vitadairy.warehouse.services;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.List;
import java.util.Objects;

@Service("WhHealthService")
@Log4j2
public class WhHealthService {
    private final TransactionRepository transactionRepository;

    public WhHealthService(
            @Qualifier("warehouseTransactionRepository") TransactionRepository transactionRepository
    ) {
        this.transactionRepository = transactionRepository;
    }

    public ResponseEntity<String> checkTransactionStillWork() {
        //log.debug("Begin startupProbe");
        boolean validateTransactionStillWork = this.validateTransactionStillWork();
        if (!validateTransactionStillWork) return ResponseEntity.status(500).body("WH Transaction Sync die");

        return ResponseEntity.ok().body("ok");
    }

    private boolean validateTransactionStillWork() {
        try {
            List<Transaction> nonStatusTransaction = this.transactionRepository.getNonStatusTransaction();
            if (Objects.isNull(nonStatusTransaction)) {
                return true;
            }
            if (nonStatusTransaction.size() < 5) {
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("Failed to validateTransactionStillWork: ", e);
            return false;
        }
    }
}
