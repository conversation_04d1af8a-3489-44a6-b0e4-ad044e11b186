package com.vitadairy.warehouse.services;

import com.vitadairy.warehouse.dto.TriplayzResponseDto;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.TriplayzResponse;
import com.vitadairy.warehouse.repositories.TriplayzResponseRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service("warehouseTriplayzResponseService")
public class TriplayzResponseService {
    private final TriplayzResponseRepository triplayzResponseRepository;

    public TriplayzResponseService(
            @Qualifier("warehouseTriplayzResponseRepository") TriplayzResponseRepository triplayzResponseRepository
    ) {
        this.triplayzResponseRepository = triplayzResponseRepository;
    }

    public TriplayzResponse save(Transaction transaction, TriplayzResponseDto dto) {
        var responseEntity = new TriplayzResponse()
                .setTransactionId(transaction.getId())
                .setHttpCode(dto.getHttpStatus().value())
                .setResponse(dto.getResponse())
                .setSendAt(dto.getSentAt())
                .setReceivedAt(dto.getReceivedAt());


        return this.triplayzResponseRepository.save((TriplayzResponse) responseEntity);
    }
}
