package com.vitadairy.warehouse.services.export;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.warehouse.dto.ExportHistoryEventTransactionDto;
import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import com.vitadairy.warehouse.entities.HistoryLuckyTransaction;
import com.vitadairy.warehouse.repositories.HistoryLuckyTransactionRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FetchExportHistoryEventService implements FetchDataService<ExportHistoryEventTransactionDto, ListHistoryLuckyTransactionRequest> {

    private HistoryLuckyTransactionRepository historyLuckyTransactionRepository;

    @Override
    public List<ExportHistoryEventTransactionDto> fetch(FetchRequest<ListHistoryLuckyTransactionRequest> fetchRequest) {
        ListHistoryLuckyTransactionRequest request = fetchRequest.getRequest();
        request.doValidate();
        Page<HistoryLuckyTransaction> historyEvents = historyLuckyTransactionRepository.getListHistoryLuckyTransaction(request, request.getPageable());
        return historyEvents.getContent().stream().map(ExportHistoryEventTransactionDto::fromEntity).collect(Collectors.toList());
    }
}
