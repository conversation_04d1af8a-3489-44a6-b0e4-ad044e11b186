package com.vitadairy.warehouse.services.export;

import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.warehouse.common.ExportTypeDefs;
import com.vitadairy.warehouse.dto.ExportHistoryEventRequest;
import com.vitadairy.warehouse.dto.ExportHistoryEventTransactionDto;
import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WriteExportHistoryEventServiceStrategy {
    private final WriteExportFileService<ExportHistoryEventTransactionDto, ListHistoryLuckyTransactionRequest> writeExportFileHistoryEventService;

    public WriteExportFileService<?, ?> getProcessExportFileService(String type) {
        if (Objects.isNull(type)) {
            return null;
        }
        switch (type) {
            case ExportTypeDefs.HISTORY_EVENT:
                return writeExportFileHistoryEventService;
            default:
                return null;
        }
    }
}
