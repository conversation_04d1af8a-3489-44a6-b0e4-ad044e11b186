package com.vitadairy.warehouse.services.export;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.libraries.importexport.common.ExportResponse;
import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.dto.Page;
import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.main.configs.ApplicationProperties;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.warehouse.common.ExportTypeDefs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Service("warehouseExportService")
public class ExportService {
    private final WriteExportHistoryEventServiceStrategy writeExportFileServiceStrategy;
    private final ApplicationProperties applicationProperties;
    private final ObjectMapper objectMapper;

    private Path rootExportFolder;

    public ExportResponse doExport(String type, String fileName, FetchRequest request) throws Exception {
        WriteExportFileService<?, ?> writeExportFileService = writeExportFileServiceStrategy.getProcessExportFileService(type);
        if (Objects.isNull(writeExportFileService)) {
            throw new ApplicationException("Not support type: " + type);
        }
        return writeExportFileService.exportFile(
                rootExportFolder.toAbsolutePath().toString().replace("./", ""),
                fileName,
                request);
    }

    public ExportResponse doExport(String type, FetchRequest request) throws Exception {
        String rootPath = rootExportFolder.toAbsolutePath().toString().replace("./", "");
        if (rootPath.equals("/")) {
            throw new ApplicationException("Export configuration error");
        }
        String fileName = genFileName(type) + ".xlsx";
        if (StringUtils.isEmpty(fileName)) {
            throw new ApplicationException("Cannot generate file name for file export");
        }
        return doExport(type, fileName, request);
    }

    public ResponseEntity<?> export(String type, FetchRequest request) throws Exception {
        if (Objects.isNull(request.getPageable())
                && Objects.nonNull(request.getFetchAll())
                && Boolean.TRUE.equals(request.getFetchAll())) {
            request.setPageable(new Page(0, applicationProperties.getExportConfig().getBatchSize()));
        }
        ExportResponse exportResponse = doExport(type, request);
        if (Objects.isNull(exportResponse) || exportResponse.getRc() != 0) {
            throw new ApplicationException("Export file error");
        }
        String filePath = exportResponse.getFilePath();
        if (StringUtils.isEmpty(filePath)) {
            throw new ApplicationException("Export file error");
        }
        log.info("Export response {}", objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(exportResponse));

        InputStream inputStream = new FileInputStream(filePath);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", exportResponse.getFileName());
        httpHeaders.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        httpHeaders.setAccessControlExposeHeaders(List.of("Content-Disposition"));
        httpHeaders.setContentLength(inputStream.available());
        return ResponseEntity.ok()
                .headers(httpHeaders)
                .body(new InputStreamResource(inputStream));

    }

    protected String genFileName(String type) {
        String prefix;
        switch (type) {
            case ExportTypeDefs.HISTORY_EVENT:
                prefix = "HISTORY_EVENT_";
                break;
            default:
                prefix = "EXPORT_";
                break;
        }
        return IdUtils.generateId(prefix);
    }

    @Autowired
    public void setRootExportFolder(@Qualifier("rootExportFolder") Path rootExportFolder) {
        this.rootExportFolder = rootExportFolder;
    }

}
