package com.vitadairy.warehouse.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.dto.SfUserInfoResponseDto;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.client.auth.SalesforceAuthProvider;
import com.vitadairy.warehouse.configs.SalesforceProperties;
import com.vitadairy.warehouse.dto.SalesforceResponseDto;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;

@Service
@Slf4j
public class SalesForceInternalService {
    private final SalesforceProperties sfProp;
    private final SalesforceApiConfigService salesforceApiConfigService;
    private final SalesforceAuthProvider salesforceAuthProvider;
    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;
    private final TransactionRepository transactionRepository;
    private final SalesforceResponseService salesforceResponseService;

    public SalesForceInternalService(SalesforceProperties sfProp,
                            SalesforceAuthProvider salesforceAuthProvider,
                            RestTemplate restTemplate,
                            SalesforceApiConfigService salesforceApiConfigService,
                            SalesforceResponseService salesforceResponseService,
                            TransactionRepository transactionRepository,
                            ObjectMapper objectMapper) {
        this.sfProp = sfProp;
        this.salesforceAuthProvider = salesforceAuthProvider;
        this.salesforceApiConfigService = salesforceApiConfigService;
        this.salesforceResponseService = salesforceResponseService;
        this.transactionRepository = transactionRepository;
        this.objectMapper = objectMapper;
        this.restTemplateClient = new RestTemplateClient(sfProp.getEndpoint(), restTemplate);
    }

    public SfUserInfoResponseDto getUserInfo(Long userId) {
        var apiMap = this.salesforceApiConfigService.getApiMap();
        var apiConfig = apiMap.get("USER_INFO");
        if (apiConfig == null) {
            return null;
        }
        Transaction transaction = new Transaction();
        transaction.setCode("USER_INFO_" + userId);
        transaction.setDestination("SF");
        SalesforceResponseDto dto = new SalesforceResponseDto();
        var sentAt = Instant.now();
        var sfResponse = new SfUserInfoResponseDto();
        try {
            var token = this.salesforceAuthProvider.getToken();
            this.restTemplateClient.buildAuth("Bearer " + token);
            sfResponse = this.restTemplateClient.get(apiConfig.getPath() + userId.toString(), new ParameterizedTypeReference<SfUserInfoResponseDto>() {
            });
            dto.setResponse(this.objectMapper.writeValueAsString(sfResponse));
            dto.setStatus(sfResponse.getStatus());
            dto.setHttpStatus(HttpStatus.OK);
            dto.setReceivedAt(Instant.now());
            transaction.setStatus(TransactionStatus.SUCCESS);
        } catch (Exception e) {
            transaction.setStatus(TransactionStatus.FAILED);
            dto = this.handleException(e);
        }

        transaction = this.transactionRepository.save(transaction);
        dto.setSentAt(sentAt);
        this.salesforceResponseService.save(transaction, dto);
        return sfResponse;
    }

    private SalesforceResponseDto handleException(Exception e) {
        log.info(e.getMessage());
        var statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        String response = e.getMessage();
        Integer status = null;

        if (e instanceof HttpClientErrorException err) {
            var sfResponse = err.getResponseBodyAs(SalesforceResponseDto.Payload.class);
            if (sfResponse != null) {
                status = sfResponse.getStatus();
            }

            response = err.getResponseBodyAsString();
            statusCode = HttpStatus.valueOf(err.getStatusCode().value());
        }

        return new SalesforceResponseDto()
                .setResponse(response)
                .setStatus(status)
                .setHttpStatus(statusCode)
                .setReceivedAt(Instant.now());
    }
}
