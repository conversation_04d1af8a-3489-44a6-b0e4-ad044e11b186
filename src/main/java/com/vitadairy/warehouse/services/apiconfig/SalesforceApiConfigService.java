package com.vitadairy.warehouse.services.apiconfig;

import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.warehouse.entities.SalesforceApiConfig;
import com.vitadairy.warehouse.repositories.SalesforceApiConfigRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service("warehouseSalesforceApiConfigService")
public class SalesforceApiConfigService extends ApiConfigService {
    private final SalesforceApiConfigRepository salesforceApiConfigRepository;

    public SalesforceApiConfigService(
            @Qualifier("warehouseSalesforceApiConfigRepository") SalesforceApiConfigRepository salesforceApiConfigRepository
    ) {
        this.salesforceApiConfigRepository = salesforceApiConfigRepository;
    }

    public Map<String, SalesforceApiConfig> getApiMap() {
        return this.salesforceApiConfigRepository.findAll()
                .stream()
                .collect(Collectors.toMap(SalesforceApiConfig::getTransactionCode, c -> c));
    }

    @Override
    public DestinationEnum getDestination() {
        return DestinationEnum.SF;
    }
}
