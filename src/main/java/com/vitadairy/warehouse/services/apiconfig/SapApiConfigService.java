package com.vitadairy.warehouse.services.apiconfig;

import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.warehouse.entities.SapApiConfig;
import com.vitadairy.warehouse.repositories.SapApiConfigRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service("warehouseSAPApiConfigService")
public class SapApiConfigService extends ApiConfigService {
    private final SapApiConfigRepository sapApiConfigRepository;

    public SapApiConfigService(
            @Qualifier("warehouseSAPApiConfigRepository") SapApiConfigRepository sapApiConfigRepository
    ) {
        this.sapApiConfigRepository = sapApiConfigRepository;
    }

    public Map<String, SapApiConfig> getApiMap() {
        return this.sapApiConfigRepository.findAll()
                .stream()
                .collect(Collectors.toMap(SapApiConfig::getTransactionCode, c -> c));
    }

    @Override
    public DestinationEnum getDestination() {
        return DestinationEnum.SAP;
    }
}
