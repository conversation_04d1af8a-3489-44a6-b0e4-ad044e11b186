package com.vitadairy.warehouse.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class DeletableFileInputStream extends FileInputStream {

    private final File file;

    public DeletableFileInputStream(File file) throws IOException {
        super(file);
        this.file = file;
    }

    @Override
    public void close() throws IOException {
        super.close();
        if (file.exists()) {
            file.delete();
        }
    }
}