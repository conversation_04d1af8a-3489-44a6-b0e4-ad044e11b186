package com.vitadairy.warehouse.client;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TransactionClientFactory {
    private final Map<String, TransactionClient> transactionClientMap;

    public TransactionClientFactory(
            List<TransactionClient> transactionClients
    ) {
        this.transactionClientMap = transactionClients.stream()
                .collect(Collectors.toMap(TransactionClient::getDestinationCode, c -> c));
    }

    public void send(Transaction transaction) {
        var destination = transaction.getDestination();
        var client = this.transactionClientMap.get(destination);
        if (client == null) {
            log.error("Client {} does not have implementation", destination);
            return;
        }

        client.send(transaction);
    }

    public void send(Transaction transaction, WsBaseApiConfig apiConfig) {
        var destination = transaction.getDestination();
        var client = this.transactionClientMap.get(destination);
        if (client == null) {
            log.error("Client {} does not have implementation", destination);
            return;
        }

        client.send(transaction, apiConfig);
    }
}
