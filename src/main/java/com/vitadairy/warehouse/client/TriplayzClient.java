package com.vitadairy.warehouse.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.client.auth.TriplayzAuthProvider;
import com.vitadairy.warehouse.configs.TriplayzProperties;
import com.vitadairy.warehouse.dto.TriplayzResponseDto;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.TriplayzResponseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;

@Service
@Slf4j
public class TriplayzClient implements TransactionClient {
    private final TriplayzProperties prop;
    private final RestTemplateClient restTemplateClient;
    private final TriplayzAuthProvider authProvider;
    private final TriplayzResponseService triplayzResponseService;
    private final TransactionRepository transactionRepository;
    private final ObjectMapper objectMapper;

    public TriplayzClient(TriplayzProperties prop,
                          RestTemplate restTemplate,
                          TriplayzAuthProvider authProvider,
                          TriplayzResponseService triplayzResponseService,
                          TransactionRepository transactionRepository,
                          ObjectMapper objectMapper) {
        this.prop = prop;
        this.authProvider = authProvider;
        this.restTemplateClient = new RestTemplateClient(prop.getEndpoint(), restTemplate);
        this.triplayzResponseService = triplayzResponseService;
        this.transactionRepository = transactionRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    public String getDestinationCode() {
        return DestinationEnum.TRIPLAYZ.name();
    }

    @Override
    public void send(Transaction transaction) {
        var dto = new TriplayzResponseDto();
        var sentAt = Instant.now();
        try {
            // get token
            var token = this.authProvider.getToken();
            log.info("Send Triplayz with token: {}", token);

            // builder header
            this.restTemplateClient.buildAuth("Bearer " + token);
            var header = this.restTemplateClient.defaultHeader();

            // send request
            var payload = CollectionUtils.isEmpty(transaction.getPayload()) ? transaction.getTListPayload() : transaction.getPayload();
            var resp = this.restTemplateClient.postEntity("", header, payload, null, new ParameterizedTypeReference<TriplayzResponseDto.Payload>() {});
            var body = resp.getBody();

            dto.setReceivedAt(Instant.now());
            dto.setHttpStatus(HttpStatus.valueOf(resp.getStatusCode().value()));
            dto.setResponse(this.objectMapper.writeValueAsString(body));

            var newStatus = resolveTransactionStatus(transaction, dto, body);
            transaction.setStatus(newStatus);
        } catch (Exception e) {
            dto = this.handleException(e);
            var newStatus = resolveTransactionStatus(transaction, dto, null);
            transaction.setStatus(newStatus);
        }

        transaction = this.transactionRepository.save(transaction);
        log.info("Upsert transaction with id {}", transaction.getId());
        dto.setSentAt(sentAt);
        this.triplayzResponseService.save(transaction, dto);
    }

    private TransactionStatus resolveTransactionStatus(Transaction trans, TriplayzResponseDto dto, TriplayzResponseDto.Payload resPayload) {
        var newStatus = TransactionStatus.RETRY;
        if (dto.getHttpStatus().is2xxSuccessful() && resPayload != null && resPayload.isSuccess()) {
            newStatus = TransactionStatus.SUCCESS;
        } else {
            trans.incrementRetryCount();
            if (trans.getStatus() == TransactionStatus.RETRY && trans.getRetryCount() == this.prop.getMaxRetryCount()) {
                newStatus = TransactionStatus.FAILED;
            }
        }

        return newStatus;
    }

    private TriplayzResponseDto handleException(Exception e) {
        var statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        String response = e.getMessage();

        if (e instanceof HttpStatusCodeException err) {
            response = err.getResponseBodyAsString();
            statusCode = HttpStatus.valueOf(err.getStatusCode().value());
        }

        return new TriplayzResponseDto()
                .setResponse(response)
                .setHttpStatus(statusCode)
                .setReceivedAt(Instant.now());
    }

    @Override
    public void send(Transaction transaction, WsBaseApiConfig apiConfig) {
        this.send(transaction);
    }
}
