package com.vitadairy.warehouse.client.salesforce.converter;

import org.springframework.data.util.Pair;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;

public interface TransactionConverter {
    Pair<Transaction, WsBaseApiConfig> convert(Transaction transaction);
    boolean canConvert(Transaction transaction);
    String getCode();
    default boolean isSaveDb() {
        return false;
    }
}
