package com.vitadairy.warehouse.client.salesforce.converter;

import java.util.List;

import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;

@Service
public class ReturnPointV2Converter extends SFV2Converter {

    public ReturnPointV2Converter(SalesforceApiConfigService apiConfigService) {
        super(apiConfigService);
    }

    @Override
    public Pair<Transaction, WsBaseApiConfig> convert(Transaction transaction) {
        var code = String.format("RETURN_POINT_TLIST_V2", transaction.getCode());
        var apiConfig = this.apiConfigService.getApiMap().get(code);
        
        var payload = transaction.getPayload();

        if (payload != null) {
            transaction.setPayload(null);
            transaction.setTListPayload(List.of(payload));
        }

        transaction.setCode(code);
        return Pair.of(transaction, apiConfig);
    }

    @Override
    public String getCode() {
        return "RETURN_POINT";
    }
}