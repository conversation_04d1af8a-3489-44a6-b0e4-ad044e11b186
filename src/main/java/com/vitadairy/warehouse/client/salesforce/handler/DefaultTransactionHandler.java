package com.vitadairy.warehouse.client.salesforce.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.client.auth.SalesforceAuthProvider;
import com.vitadairy.warehouse.client.salesforce.converter.TransactionConverter;
import com.vitadairy.warehouse.configs.SalesforceProperties;
import com.vitadairy.warehouse.dto.SalesforceResponseDto;
import com.vitadairy.warehouse.entities.SalesforceApiConfig;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.SalesforceResponseService;
import com.vitadairy.warehouse.services.WarehouseRestApiGiftService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.annotation.Order;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Order()
public class DefaultTransactionHandler implements TransactionHandler {
    private final SalesforceProperties sfProp;
    private final SalesforceAuthProvider salesforceAuthProvider;
    private final RestTemplateClient restTemplateClient;
    private final SalesforceResponseService salesforceResponseService;
    private final TransactionRepository transactionRepository;
    private final ObjectMapper objectMapper;
    private final WarehouseRestApiGiftService restApiGiftService;
    private final Map<String, TransactionConverter> transactionConverterMap;

    public DefaultTransactionHandler(SalesforceProperties sfProp,
                                     SalesforceAuthProvider salesforceAuthProvider,
                                     RestTemplate restTemplate,
                                     SalesforceResponseService salesforceResponseService,
                                     TransactionRepository transactionRepository,
                                     ObjectMapper objectMapper,
                                     WarehouseRestApiGiftService restApiGiftService,
                                     List<TransactionConverter> transactionConverters) {
        this.sfProp = sfProp;
        this.salesforceAuthProvider = salesforceAuthProvider;
        this.restTemplateClient = new RestTemplateClient(sfProp.getEndpoint(), restTemplate);
        this.salesforceResponseService = salesforceResponseService;
        this.transactionRepository = transactionRepository;
        this.objectMapper = objectMapper;
        this.restApiGiftService = restApiGiftService;
        this.transactionConverterMap = transactionConverters.stream()
                .collect(Collectors.toMap(TransactionConverter::getCode, Function.identity()));
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        return true; // Default handler handles all transactions unless overridden
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        Pair<SalesforceResponseDto, List<SalesforceResponseDto.Payload>> pair;
        var sentAt = Instant.now();

        var converter = this.transactionConverterMap.get(transaction.getCode());
        if (converter != null && converter.canConvert(transaction)) {
            var convertedPair = converter.convert(transaction);
            transaction = convertedPair.getFirst();
            baseApiConfig = convertedPair.getSecond();
        }

        if (transaction.getUserId() == null) {
            transaction.setUserId(this.extractUserId(transaction));
        }

        try {
            pair = this.trySendToSF(transaction, baseApiConfig);
        } catch (Exception e) {
            pair = this.handleException(e, transaction, baseApiConfig, true);
        }

        this.saveResponse(transaction, pair.getFirst(), pair.getSecond(), sentAt);
        return transaction.getStatus() == TransactionStatus.SUCCESS;
    }

    private Pair<SalesforceResponseDto, List<SalesforceResponseDto.Payload>> handleException(Exception e, Transaction transaction, WsBaseApiConfig baseApiConfig, boolean catchUnauthorized) {
        log.info(e.getMessage());
        var statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        String response = e.getMessage();
        Integer status = null;

        if (e.getCause() instanceof HttpClientErrorException err) {
            response = err.getResponseBodyAsString();
            statusCode = HttpStatus.valueOf(err.getStatusCode().value());

            // special case: Unauthorized exception
            if (statusCode == HttpStatus.UNAUTHORIZED && catchUnauthorized) {
                log.info("Got unauthorized exception. Refresh token and retry transaction.");
                // try to refresh token
                this.salesforceAuthProvider.refreshToken();

                // retry immediately
                try {
                    return this.trySendToSF(transaction, baseApiConfig);
                } catch (Exception retryEx) {
                    // set catchUnauthorized = false to stop retry send to SF if got UNAUTHORIZED again
                    return this.handleException(e, transaction, baseApiConfig, false);
                }
            }

            try {
                var sfResponse = err.getResponseBodyAs(SalesforceResponseDto.Payload.class);
                if (sfResponse != null) {
                    status = sfResponse.getStatus();
                }
            } catch (Exception parseJsonEx) {
                // ignore exception because the response has different format
            }
        }

        TransactionStatus newStatus = TransactionStatus.RETRY;
        if (status != null && status == 0) {
            newStatus = TransactionStatus.SUCCESS;
        } else {
            transaction.incrementRetryCount();
            if (transaction.getStatus() == TransactionStatus.RETRY && transaction.getRetryCount() >= this.sfProp.getMaxRetryCount()) {
                newStatus = TransactionStatus.FAILED;
            }
        }
        transaction.setStatus(newStatus);

        return Pair.of(new SalesforceResponseDto()
                .setResponse(response)
                .setStatus(status)
                .setHttpStatus(statusCode)
                .setReceivedAt(Instant.now()), new ArrayList<>());
    }

    private Pair<SalesforceResponseDto, List<SalesforceResponseDto.Payload>> trySendToSF(Transaction transaction, WsBaseApiConfig baseApiConfig) throws Exception {
        var apiConfig = (SalesforceApiConfig) baseApiConfig;
        var token = this.salesforceAuthProvider.getToken();
        this.restTemplateClient.buildAuth("Bearer " + token);

        if (!CollectionUtils.isEmpty(transaction.getPayload())) {
            return this.sendSfWithPayload(transaction, apiConfig);
        } else {
            return this.sendSfWithTListPayload(transaction, apiConfig);
        }
    }

    private Pair<SalesforceResponseDto, List<SalesforceResponseDto.Payload>> sendSfWithPayload(Transaction transaction, SalesforceApiConfig apiConfig) throws Exception {
        var dto = new SalesforceResponseDto();
        var sfResponse = this.restTemplateClient.post(apiConfig.getPath(), transaction.getPayload(), new ParameterizedTypeReference<SalesforceResponseDto.Payload>() {
        });
        dto.setResponse(this.objectMapper.writeValueAsString(sfResponse));
        dto.setStatus(sfResponse.getStatus());
        dto.setHttpStatus(HttpStatus.OK);
        dto.setReceivedAt(Instant.now());
        transaction.setStatus(TransactionStatus.SUCCESS);

        return Pair.of(dto, new ArrayList<>());
    }

    private Pair<SalesforceResponseDto, List<SalesforceResponseDto.Payload>> sendSfWithTListPayload(Transaction transaction, SalesforceApiConfig apiConfig) throws Exception {
        var dto = new SalesforceResponseDto();
        var sfListResponse = this.restTemplateClient.post(apiConfig.getPath(), transaction.getTListPayload(), new ParameterizedTypeReference<List<SalesforceResponseDto.Payload>>() {
        });
        dto.setResponse(this.objectMapper.writeValueAsString(sfListResponse));
        var numOfPayload = transaction.getTListPayload().size();
        var sum = sfListResponse.stream().mapToInt(SalesforceResponseDto.Payload::getStatus).sum();
        if (sum == 0) {
            dto.setStatus(sum);
            transaction.setStatus(TransactionStatus.SUCCESS);
        } else if (sum == numOfPayload) {
            dto.setStatus(1);
            transaction.setStatus(TransactionStatus.FAILED);
        } else {
            dto.setStatus(0);
            transaction.setStatus(TransactionStatus.PARTIAL_SUCCESS);
        }

        dto.setHttpStatus(HttpStatus.OK);
        dto.setReceivedAt(Instant.now());
        return Pair.of(dto, sfListResponse);
    }

    private void saveResponse(Transaction transaction, SalesforceResponseDto dto, List<SalesforceResponseDto.Payload> sfListResponse, Instant sentAt) {
        transaction = this.transactionRepository.save(transaction);
        log.info("Upsert transaction with id {}", transaction.getId());

        if (dto == null) {
            log.error("SalesforceResponseDto is null");
            return;
        }

        dto.setSentAt(sentAt);
        this.salesforceResponseService.save(transaction, dto);
        
        if ("RETURN_POINT_TLIST".equals(transaction.getCode()) || "RETURN_POINT_TLIST_V2".equals(transaction.getCode())) {
            List<String> transactionIds = new ArrayList<>();
            if (transaction.getStatus() == TransactionStatus.SUCCESS) {
                if (!CollectionUtils.isEmpty(transaction.getTListPayload())) {
                    transaction.getTListPayload().forEach(tList -> {
                        transactionIds.add(tList.get("Transaction_Trigger_External_ID__c").toString());
                    });
                }
            }
            if (transaction.getStatus() == TransactionStatus.PARTIAL_SUCCESS && !CollectionUtils.isEmpty(sfListResponse)) {
                Transaction finalTransaction = transaction;
                sfListResponse.forEach(sfResponse -> {
                    if (sfResponse.getStatus() == 0) {
                        Map<String, Object> tran = finalTransaction.getTListPayload().stream()
                                .filter(tList -> tList.get("ref_id").toString().equals(sfResponse.getRefId())).findFirst().orElse(null);
                        String transactionCode = Objects.nonNull(tran) ? tran.get("Transaction_Trigger_External_ID__c").toString() : null;
                        if (Objects.nonNull(transactionCode)) {
                            transactionIds.add(transactionCode);
                        }
                    }
                });
            }

            if (!CollectionUtils.isEmpty(transactionIds)) {
                restApiGiftService.updateSfGift("", transactionIds);
            }
        }
    }

    public int getMaxRetryCount() {
        return this.sfProp.getMaxRetryCount();
    }

    public Long extractUserId(Transaction transaction) {
        var payload = transaction.getPayload();
        var tListPayload = transaction.getTListPayload();
        String userId = null;

        if (payload != null) {
            userId = payload.get("user_id").toString();
        } else if (!CollectionUtils.isEmpty(tListPayload)) {
            Object userIdObj = tListPayload.get(0).get("user_id");
            if (userIdObj != null) {
                userId = userIdObj.toString();
            }
        }

        if (userId == null) {
            return null;
        }

        return Long.parseLong(userId);
    }
} 