package com.vitadairy.warehouse.client.salesforce.converter;

import org.springframework.stereotype.Service;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;

@Service
public class DeliveryTransactionV2Converter extends SFV2Converter {
    
    public DeliveryTransactionV2Converter(SalesforceApiConfigService apiConfigService) {
        super(apiConfigService);
    }

    @Override
    public String getCode() {
        return "DELIVERY_TRANSACTION";
    }
}
