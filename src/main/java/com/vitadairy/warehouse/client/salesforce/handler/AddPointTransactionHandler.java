package com.vitadairy.warehouse.client.salesforce.handler;

import lombok.extern.slf4j.Slf4j;
import com.vitadairy.warehouse.repositories.SalesforcePayloadPairingRepository;
import com.vitadairy.warehouse.entities.SalesforcePayloadPairing;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@Order(1)
public class AddPointTransactionHandler implements TransactionHandler
{
    private final DefaultTransactionHandler defaultHandler;
    private final SalesforcePayloadPairingRepository salesforcePayloadPairingRepository;

    private static final String ADD_POINT = "ADD_POINT";
    private static final String TRANSACTION_EXTERNAL_ID_KEY = "Transaction_External_ID__c";

    public AddPointTransactionHandler(DefaultTransactionHandler defaultHandler,
                                      SalesforcePayloadPairingRepository salesforcePayloadPairingRepository) {
        this.defaultHandler = defaultHandler;
        this.salesforcePayloadPairingRepository = salesforcePayloadPairingRepository;
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        if (CollectionUtils.isEmpty(transaction.getTListPayload())) {
            return false;
        }

        if (!ADD_POINT.equals(transaction.getCode())) {
            return false;
        }

        String externalId = transaction.getTListPayload().getFirst().get(TRANSACTION_EXTERNAL_ID_KEY).toString();
        return StringUtils.hasText(externalId);
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        // Then proceed with the default handling
        var success = defaultHandler.handle(transaction, baseApiConfig);

        // If success, save the Transaction_External_ID__c to Redis with 1 minute TTL
        if (success) {
            String externalId = transaction.getTListPayload().getFirst().get(TRANSACTION_EXTERNAL_ID_KEY).toString();
            this.cacheTransactionExternalId(externalId);
        }

        return success;
    }

    private void cacheTransactionExternalId(String externalId) {
        // save to database in case of long time look up
        this.salesforcePayloadPairingRepository.save(new SalesforcePayloadPairing(TRANSACTION_EXTERNAL_ID_KEY, externalId));
        log.info("Saved {} {} to pairing table", TRANSACTION_EXTERNAL_ID_KEY, externalId);
    }
} 