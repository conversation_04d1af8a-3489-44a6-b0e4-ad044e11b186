package com.vitadairy.warehouse.client.salesforce.handler;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;

public interface TransactionHandler {
    boolean canHandle(Transaction transaction);
    /**
     * Handle the transaction and return true if successful, false if failed or needs retry
     */
    boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig);
    default boolean handleRetry(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        return this.handle(transaction, baseApiConfig);
    }
}