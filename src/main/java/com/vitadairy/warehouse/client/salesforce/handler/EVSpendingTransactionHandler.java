package com.vitadairy.warehouse.client.salesforce.handler;

import com.vitadairy.warehouse.entities.SalesforcePayloadPairing;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.SalesforcePayloadPairingRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

@Slf4j
@Component
@Order(2) // Priority after AddPointTransactionHandler but before DefaultTransactionHandler
public class EVSpendingTransactionHandler implements TransactionHandler {
    private final DefaultTransactionHandler defaultHandler;
    private final SalesforcePayloadPairingRepository salesforcePayloadPairingRepository;

    private static final String TYPE_GIFTING = "Spending";
    private static final String TRANSACTION_TYPE_EVENT = "Gift Exchange";
    private static final String TYPE_FIELD = "Type__c";
    private static final String TRANSACTION_TYPE_FIELD = "Transaction_Type__c";
    private static final String EXCHANGE_VOUCHER = "EXCHANGE_VOUCHER";
    private static final String REF_ID_FIELD = "ref_id";
    private static final String VOUCHER_REF_ID_FIELD = "Voucher_RefId__c";

    public EVSpendingTransactionHandler(DefaultTransactionHandler defaultHandler,
                                        SalesforcePayloadPairingRepository salesforcePayloadPairingRepository) {
        this.defaultHandler = defaultHandler;
        this.salesforcePayloadPairingRepository = salesforcePayloadPairingRepository;
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        if (CollectionUtils.isEmpty(transaction.getPayload())) {
            return false;
        }

        if (!EXCHANGE_VOUCHER.equals(transaction.getCode())) {
            return false;
        }

        Map<String, Object> payload = transaction.getPayload();
        String type = String.valueOf(payload.get(TYPE_FIELD));
        String transactionType = String.valueOf(payload.get(TRANSACTION_TYPE_FIELD));
        String voucherRefId = String.valueOf(payload.get(VOUCHER_REF_ID_FIELD));

        return TYPE_GIFTING.equals(type) // Type__c = "Spending"
                && TRANSACTION_TYPE_EVENT.equals(transactionType) // Transaction_Type__c = "Gift Exchange"
                && !StringUtils.hasText(voucherRefId); // empty Voucher_RefId__c
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        // Then proceed with the default handling
        var success = defaultHandler.handle(transaction, baseApiConfig);

        // If success, save the ref_id to database
        if (success) {
            String refId = transaction.getPayload().get(REF_ID_FIELD).toString();
            cacheRefId(refId);
        }

        return success;
    }

    private void cacheRefId(String refId) {
        // save to database in case of long time look up
        this.salesforcePayloadPairingRepository.save(new SalesforcePayloadPairing(REF_ID_FIELD, refId));
        log.info("Saved ref_id {} to pairing table", refId);
    }
} 