package com.vitadairy.warehouse.client.salesforce.converter;

import java.util.List;

import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;

@Service
public class ReturnPointListV2Converter extends SFV2Converter {

    public ReturnPointListV2Converter(SalesforceApiConfigService apiConfigService) {
        super(apiConfigService);
    }

    @Override
    public String getCode() {
        return "RETURN_POINT_TLIST";
    }
}