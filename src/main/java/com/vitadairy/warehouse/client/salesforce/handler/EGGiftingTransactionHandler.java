package com.vitadairy.warehouse.client.salesforce.handler;

import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.warehouse.dto.SalesforceResponseDto;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.SalesforcePayloadPairingRepository;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.SalesforceResponseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@Order(2) // Priority after AddPointTransactionHandler but before DefaultTransactionHandler
public class EGGiftingTransactionHandler implements TransactionHandler {
    private final DefaultTransactionHandler defaultHandler;
    private final TransactionRepository transactionRepository;
    private final SalesforcePayloadPairingRepository salesforcePayloadPairingRepository;
    private final SalesforceResponseService salesforceResponseService;
    private final TaskExecutor executor;
    private final long checkDurationMs;

    private static final String TYPE_GIFTING = "Gifting";
    private static final String TRANSACTION_TYPE_EVENT = "Event";
    private static final String TYPE_FIELD = "Type__c";
    private static final String TRANSACTION_TYPE_FIELD = "Transaction_Type__c";
    private static final String EXCHANGE_GIFT = "EXCHANGE_GIFT";
    private static final String TRANSACTION_TRIGGER_EXTERNAL_ID_FIELD = "Transaction_Trigger_External_ID__c";
    private static final String TRANSACTION_EXTERNAL_ID_KEY = "Transaction_External_ID__c";

    public EGGiftingTransactionHandler(
            DefaultTransactionHandler defaultHandler,
            TransactionRepository transactionRepository,
            SalesforcePayloadPairingRepository salesforcePayloadPairingRepository,
            SalesforceResponseService salesforceResponseService,
            @Qualifier("salesforceTransactionHandlerExecutor") TaskExecutor executor,
            @Value("${salesforce.transaction.handler.check.duration:2000}") long checkDurationMs) {
        this.defaultHandler = defaultHandler;
        this.transactionRepository = transactionRepository;
        this.salesforcePayloadPairingRepository = salesforcePayloadPairingRepository;
        this.salesforceResponseService = salesforceResponseService;
        this.executor = executor;
        this.checkDurationMs = checkDurationMs;
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        if (CollectionUtils.isEmpty(transaction.getPayload())) {
            return false;
        }

        if (!EXCHANGE_GIFT.equals(transaction.getCode())) {
            return false;
        }

        Map<String, Object> payload = transaction.getPayload();
        String type = String.valueOf(payload.get(TYPE_FIELD));
        String transactionType = String.valueOf(payload.get(TRANSACTION_TYPE_FIELD));
        String tExtId = String.valueOf(payload.get(TRANSACTION_TRIGGER_EXTERNAL_ID_FIELD));

        return TYPE_GIFTING.equals(type) // Type__c = "Gifting"
                && TRANSACTION_TYPE_EVENT.equals(transactionType) // Transaction_Type__c = "Event"
                && StringUtils.hasText(tExtId)
                && !"null".equals(tExtId);
    }

    @Override
    public boolean handleRetry(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        final String externalId = String.valueOf(transaction.getPayload().get(TRANSACTION_TRIGGER_EXTERNAL_ID_FIELD));
        var updatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(TRANSACTION_EXTERNAL_ID_KEY, externalId);
        if (updatedRows > 0) {
            handleIfCacheFound(transaction, baseApiConfig);
        } else {
            log.info("Transaction_External_ID__c {} not found in pairing table", externalId);
            handleIfCacheNotFound(transaction, externalId);
        }

        return true;
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        final String externalId = String.valueOf(transaction.getPayload().get(TRANSACTION_TRIGGER_EXTERNAL_ID_FIELD));

        // First check
        var updatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(TRANSACTION_EXTERNAL_ID_KEY, externalId);
        if (updatedRows > 0) {
            log.info("Found and updated time of external ID {} in pairing table, sending to SF", externalId);
            handleIfCacheFound(transaction, baseApiConfig);
        } else {
            log.info("External ID {} not found in pairing table on first check, scheduling second check after {}ms", externalId, checkDurationMs);

            // Second check
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(checkDurationMs);
                    var secondUpdatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(TRANSACTION_EXTERNAL_ID_KEY, externalId);
                    if (secondUpdatedRows > 0) {
                        log.info("Found and updated time of external ID {} in pairing table, sending to SF", externalId);
                        handleIfCacheFound(transaction, baseApiConfig);
                    } else {
                        log.info("External ID {} not found in pairing table, mark as not SUCCESS", externalId);
                        this.handleIfCacheNotFound(transaction, externalId);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Interrupted while waiting for pairing table check", e);
                }
            }, executor);
        }

        return true;
    }

    private void handleIfCacheFound(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        defaultHandler.handle(transaction, baseApiConfig);
    }

    private void handleIfCacheNotFound(Transaction transaction, String checkingValue) {
        TransactionStatus newStatus = TransactionStatus.RETRY;
        transaction.incrementRetryCount();
        if (transaction.getStatus() == TransactionStatus.RETRY && transaction.getRetryCount() >= this.defaultHandler.getMaxRetryCount()) {
            newStatus = TransactionStatus.FAILED;
        }

        // Save transaction
        transaction.setStatus(newStatus);
        Transaction savedTransaction = transactionRepository.save(transaction);
        log.info("Saved transaction with id {} as {}", savedTransaction.getId(), newStatus);

        // Create and save response DTO
        var responseDto = new SalesforceResponseDto()
                .setResponse("Transaction External ID not found in pairing table: " + checkingValue)
                .setStatus(400)
                .setHttpStatus(HttpStatus.BAD_REQUEST)
                .setSentAt(Instant.now())
                .setReceivedAt(Instant.now());

        salesforceResponseService.save(savedTransaction, responseDto);
        log.info("Saved SalesforceResponseDto for transaction {}", savedTransaction.getId());
    }
} 