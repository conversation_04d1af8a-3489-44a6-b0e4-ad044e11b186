package com.vitadairy.warehouse.client.salesforce.handler;

import java.util.List;
import java.util.Map;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.TransactionRepository;

@Component
@Order(1)
public class HoldTransactionHandler implements TransactionHandler {
    private final TransactionRepository transactionRepository;

    private static final List<String> HOLD_CONDITIONS = List.of(
        "Program_Name__c",
        "Loyalty_Member__c"
    );

    public HoldTransactionHandler(TransactionRepository transactionRepository) {
        this.transactionRepository = transactionRepository;
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        switch (transaction.getCode()) {
            case "EXCHANGE_GIFT", "EXCHANGE_VOUCHER", "ADD_POINT":
                var payload = transaction.getPayload();
                if (payload != null) {
                    return this.isPassHoldCondition(payload);
                } else if (transaction.getTListPayload() != null) {
                    for (var payloadItem : transaction.getTListPayload()) {
                        if (this.isPassHoldCondition(payloadItem)) {
                            return true;
                        }
                    }
                }
                return false;
            default:
                return false;
        }
    }

    private boolean isPassHoldCondition(Map<String, Object> payload) {
        for (var field : HOLD_CONDITIONS) {
            var value = payload.get(field);
            if (value == null || value.toString().isEmpty()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        transaction.setStatus(TransactionStatus.HOLD);
        this.transactionRepository.save(transaction);
        return true;
    }
}
