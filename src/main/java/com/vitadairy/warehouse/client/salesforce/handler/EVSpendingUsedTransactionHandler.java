package com.vitadairy.warehouse.client.salesforce.handler;

import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.warehouse.dto.SalesforceResponseDto;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.SalesforcePayloadPairingRepository;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.SalesforceResponseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@Order(6) // After GiftingEventTransactionHandler
public class EVSpendingUsedTransactionHandler implements TransactionHandler {
    private final DefaultTransactionHandler defaultHandler;
    private final TransactionRepository transactionRepository;
    private final SalesforcePayloadPairingRepository salesforcePayloadPairingRepository;
    private final SalesforceResponseService salesforceResponseService;
    private final TaskExecutor executor;
    private final long checkDurationMs;

    private static final String TYPE_SPENDING = "Spending";
    private static final String TRANSACTION_TYPE_GIFT_EXCHANGE = "Gift Exchange";
    private static final String TYPE_FIELD = "Type__c";
    private static final String TRANSACTION_TYPE_FIELD = "Transaction_Type__c";
    private static final String EXCHANGE_VOUCHER = "EXCHANGE_VOUCHER";
    private static final String VOUCHER_REF_ID_FIELD = "Voucher_RefId__c";
    private static final String REF_ID_FIELD = "ref_id";

    public EVSpendingUsedTransactionHandler(
            DefaultTransactionHandler defaultHandler,
            TransactionRepository transactionRepository,
            SalesforcePayloadPairingRepository salesforcePayloadPairingRepository,
            SalesforceResponseService salesforceResponseService,
            @Qualifier("salesforceTransactionHandlerExecutor") TaskExecutor executor,
            @Value("${salesforce.transaction.handler.check.duration:2000}") long checkDurationMs) {
        this.defaultHandler = defaultHandler;
        this.transactionRepository = transactionRepository;
        this.salesforcePayloadPairingRepository = salesforcePayloadPairingRepository;
        this.salesforceResponseService = salesforceResponseService;
        this.executor = executor;
        this.checkDurationMs = checkDurationMs;
    }

    @Override
    public boolean canHandle(Transaction transaction) {
        if (CollectionUtils.isEmpty(transaction.getPayload())) {
            return false;
        }

        if (!EXCHANGE_VOUCHER.equals(transaction.getCode())) {
            return false;
        }

        Map<String, Object> payload = transaction.getPayload();
        String type = String.valueOf(payload.get(TYPE_FIELD));
        String transactionType = String.valueOf(payload.get(TRANSACTION_TYPE_FIELD));
        String voucherRefId = String.valueOf(payload.get(VOUCHER_REF_ID_FIELD));

        return TYPE_SPENDING.equals(type) // Type__c = "Spending"
                && TRANSACTION_TYPE_GIFT_EXCHANGE.equals(transactionType) // Transaction_Type__c = "Gift Exchange"
                && StringUtils.hasText(voucherRefId) // Voucher_RefId__c is not empty
                && !"null".equals(voucherRefId);
    }

    @Override
    public boolean handleRetry(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        final String refId = String.valueOf(transaction.getPayload().get(REF_ID_FIELD));
        var updatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(REF_ID_FIELD, refId);
        if (updatedRows > 0) {
            handleIfCacheFound(transaction, baseApiConfig);
        } else {
            log.info("ref_id {} not found in cache", refId);
            handleIfCacheNotFound(transaction, refId);
        }

        return true;
    }

    @Override
    public boolean handle(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        final String refId = String.valueOf(transaction.getPayload().get(REF_ID_FIELD));

        // First check
        var updatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(REF_ID_FIELD, refId);
        if (updatedRows > 0) {
            log.info("Found and updated time of ref_id {} in pairing table, sending to SF", refId);
            handleIfCacheFound(transaction, baseApiConfig);
        } else {
            log.info("ref_id {} not found in pairing table on first check, scheduling second check after {}ms", refId, checkDurationMs);

            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(checkDurationMs);
                    var secondUpdatedRows = this.salesforcePayloadPairingRepository.updateTimeUpdatedAtByFieldAndValue(REF_ID_FIELD, refId);
                    if (secondUpdatedRows > 0) {
                        log.info("Found and updated time of ref_id {} in pairing table, sending to SF", refId);
                        handleIfCacheFound(transaction, baseApiConfig);
                    } else {
                        log.info("ref_id {} not found in pairing table on second check", refId);
                        handleIfCacheNotFound(transaction, refId);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Interrupted while waiting for pairing table check", e);
                }
            }, executor);
        }

        return true;
    }

    private void handleIfCacheFound(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        defaultHandler.handle(transaction, baseApiConfig);
    }

    private void handleIfCacheNotFound(Transaction transaction, String checkingValue) {
        TransactionStatus newStatus = TransactionStatus.RETRY;
        transaction.incrementRetryCount();
        if (transaction.getStatus() == TransactionStatus.RETRY && transaction.getRetryCount() >= this.defaultHandler.getMaxRetryCount()) {
            newStatus = TransactionStatus.FAILED;
        }

        // Save transaction
        transaction.setStatus(newStatus);
        Transaction savedTransaction = transactionRepository.save(transaction);
        log.info("Saved transaction with id {} as {}", savedTransaction.getId(), newStatus);

        // Create and save response DTO
        var responseDto = new SalesforceResponseDto()
                .setResponse("ref_id not found in pairing table: " + checkingValue)
                .setStatus(400)
                .setHttpStatus(HttpStatus.BAD_REQUEST)
                .setSentAt(Instant.now())
                .setReceivedAt(Instant.now());

        salesforceResponseService.save(savedTransaction, responseDto);
        log.info("Saved SalesforceResponseDto for transaction {}", savedTransaction.getId());
    }
} 