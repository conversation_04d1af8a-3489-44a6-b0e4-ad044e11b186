package com.vitadairy.warehouse.client.salesforce.converter;

import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;

import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;

public abstract class SFV2Converter implements TransactionConverter {
    protected final SalesforceApiConfigService apiConfigService;

    public SFV2Converter(SalesforceApiConfigService apiConfigService) {
        this.apiConfigService = apiConfigService;
    }
    
    @Override
    public Pair<Transaction, WsBaseApiConfig> convert(Transaction transaction) {
        var code = String.format("%s_V2", transaction.getCode());
        var apiConfig = this.apiConfigService.getApiMap().get(code);
        if (apiConfig != null) {
            transaction.setCode(code);
        }
        return Pair.of(transaction, apiConfig);
    }
    
    @Override
    public boolean canConvert(Transaction transaction) {
        var payload = transaction.getPayload();
        var tListPayload = transaction.getTListPayload();

        if (payload != null) {
            if (!"".equals(payload.getOrDefault("Program_Name__c", "").toString()) && 
                !"".equals(payload.getOrDefault("Loyalty_Member__c", "").toString())) {
                return true;
            }
        }

        if (!CollectionUtils.isEmpty(tListPayload)) {
            return tListPayload.stream().anyMatch(item -> 
                !"".equals(item.getOrDefault("Program_Name__c", "").toString()) && 
                !"".equals(item.getOrDefault("Loyalty_Member__c", "").toString())
            );
        }
        
        return false;
    }
}
