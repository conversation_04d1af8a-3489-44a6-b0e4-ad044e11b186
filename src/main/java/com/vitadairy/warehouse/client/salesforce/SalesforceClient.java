package com.vitadairy.warehouse.client.salesforce;

import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.warehouse.client.TransactionClient;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.services.apiconfig.SalesforceApiConfigService;
import com.vitadairy.warehouse.client.salesforce.handler.TransactionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SalesforceClient implements TransactionClient {
    private final SalesforceApiConfigService salesforceApiConfigService;
    private final List<TransactionHandler> transactionHandlers;

    public SalesforceClient(SalesforceApiConfigService salesforceApiConfigService,
                            List<TransactionHandler> transactionHandlers) {
        this.salesforceApiConfigService = salesforceApiConfigService;
        // Sort handlers by their @Order annotation
        this.transactionHandlers = transactionHandlers;
        AnnotationAwareOrderComparator.sort(this.transactionHandlers);
    }

    public void send(Transaction transaction) {
        var apiMap = this.salesforceApiConfigService.getApiMap();
        var apiConfig = apiMap.get(transaction.getCode());

        if (apiConfig == null) {
            log.warn("Transaction code {} has no api config", transaction.getCode());
            return;
        }

        this.send(transaction, apiConfig);
    }

    @Override
    public void send(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        // Find the first handler that can handle this transaction
        // Since handlers are sorted by @Order, specialized handlers will be checked first
        TransactionHandler handler = transactionHandlers.stream()
                .filter(h -> h.canHandle(transaction))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No handler found for transaction"));

        if (transaction.getStatus() == TransactionStatus.RETRY) {
            handler.handleRetry(transaction, baseApiConfig);
        } else {
            handler.handle(transaction, baseApiConfig);
        }
    }

    @Override
    public String getDestinationCode() {
        return DestinationEnum.SF.name();
    }
}
