package com.vitadairy.warehouse.client.auth;

import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.configs.SapAuthProperties;
import com.vitadairy.warehouse.dto.SAPAuthResponseDto;
import com.vitadairy.zoo.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class SapAuthProvider extends ClientAuthProvider {
    private final SapAuthProperties authProperties;
    private final RestTemplateClient restTemplateClient;

    public SapAuthProvider(SapAuthProperties authProperties,
                           RedisTemplate<String, String> redisTemplate,
                           RestTemplate restTemplate) {
        super(redisTemplate);
        this.authProperties = authProperties;
        this.restTemplateClient = new RestTemplateClient(authProperties.getEndpoint(), restTemplate);
    }

    @Override
    public String requestToken() {
        // Create the headers and set the content type
        var header = this.restTemplateClient.defaultHeader();
        header.set("Ocp-Apim-Subscription-Key", authProperties.getHeaderSubscriptionKey());
        header.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // Create the form parameters (payload)
        LinkedMultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
        formParams.add(Constant.SapRequestParams.GRANT_TYPE, authProperties.getGrantType());
        formParams.add(Constant.SapRequestParams.CLIENT_ID, authProperties.getClientId());
        formParams.add(Constant.SapRequestParams.CLIENT_SECRET, authProperties.getClientSecret());
        formParams.add(Constant.SapRequestParams.SCOPE, authProperties.getScope());

        var response = this.restTemplateClient.postEntity("", header, formParams, null, new ParameterizedTypeReference<SAPAuthResponseDto>() {
        });
        var body = response.getBody();
        if (body != null) {
            return body.getAccessToken();
        }

        return null;
    }

    @Override
    public String getTokenCacheKey() {
        return Constant.Cache.SAP_TOKEN;
    }
}
