package com.vitadairy.warehouse.client.auth;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.time.Duration;

public abstract class ClientAuthProvider {
    private final RedisTemplate<String, String> redisTemplate;

    protected ClientAuthProvider(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public abstract String requestToken();
    public abstract String getTokenCacheKey();

    public String getToken() {
        String crmToken = this.redisTemplate.opsForValue().get(this.getTokenCacheKey());
        if(StringUtils.hasText(crmToken)) {
            return crmToken;
        }

        var newToken = this.requestToken();
        redisTemplate.opsForValue().set(this.getTokenCacheKey(), newToken, Duration.ofHours(1));
        return newToken;
    }

    public void refreshToken() {
        var newToken = this.requestToken();
        redisTemplate.opsForValue().set(this.getTokenCacheKey(), newToken, Duration.ofHours(1));
    }
}
