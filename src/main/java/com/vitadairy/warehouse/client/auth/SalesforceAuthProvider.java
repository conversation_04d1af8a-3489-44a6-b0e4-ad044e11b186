package com.vitadairy.warehouse.client.auth;

import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.configs.SalesforceProperties;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.responses.CrmAuthResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Service
@Slf4j
public class SalesforceAuthProvider extends ClientAuthProvider {
    private final SalesforceProperties sfProp;
    private final RestTemplateClient restTemplateClient;

    public SalesforceAuthProvider(SalesforceProperties sfProp,
                                  RedisTemplate<String, String> redisTemplate,
                                  RestTemplate restTemplate) {
        super(redisTemplate);
        this.sfProp = sfProp;
        this.restTemplateClient = new RestTemplateClient(sfProp.getEndpoint(), restTemplate);
    }

    @Override
    public String requestToken() {
        String url = sfProp.getApiAuthToken();

        LinkedMultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add(Constant.CrmRequestParams.GRANT_TYPE, sfProp.getGrantType());
        params.add(Constant.CrmRequestParams.CLIENT_ID, sfProp.getClientId());
        params.add(Constant.CrmRequestParams.CLIENT_SECRET, sfProp.getClientSecret());
        params.add(Constant.CrmRequestParams.REFRESH_TOKEN, sfProp.getRefreshToken());
        System.out.println("SF Client id: " + sfProp.getClientId());
        try {
            var responseObj = restTemplateClient.postEntity(sfProp.getApiAuthToken(), null, params, new ParameterizedTypeReference<CrmAuthResponse>() {
            });
            if (!HttpStatus.OK.equals(responseObj.getStatusCode()) || Objects.isNull(responseObj.getBody())) {
                log.error("[CrmServiceUtil][authToken] Request to SF with url : {}", url);
                log.error("[CrmServiceUtil][authToken] response : {}", responseObj);
                log.error("[CrmServiceUtil][authToken] status : {}", responseObj.getStatusCode());
                throw new ApplicationException("SF API FAIled");
            }
            var body = responseObj.getBody();
            if (!ObjectUtils.isEmpty(body.getError())) {
                log.error("[CrmServiceUtil][authToken] Request to SF with url : {}", url);
                log.error("[CrmServiceUtil][authToken] response : {} {}", body.getError(), body.getErrorDescription());
                throw new ApplicationException(body.getError());
            }

            return body.getAccessToken();
        } catch (Exception ex) {
            log.error("[CrmServiceUtil][authToken] Request to SF with url : {}", url);
            log.error("[CrmServiceUtil][authToken] Exception : {}", ex);
            throw new ApplicationException(ex.getMessage());
        }
    }

    @Override
    public String getTokenCacheKey() {
        return Constant.Cache.CRM_TOKEN;
    }
}
