package com.vitadairy.warehouse.client.auth;

import com.vitadairy.warehouse.configs.TriplayzProperties;
import com.vitadairy.zoo.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TriplayzAuthProvider extends ClientAuthProvider {
    private final TriplayzProperties prop;

    public TriplayzAuthProvider(TriplayzProperties prop,
                                RedisTemplate<String, String> redisTemplate) {
        super(redisTemplate);
        this.prop = prop;
    }

    @Override
    public String requestToken() {
        return prop.getToken();
    }

    @Override
    public String getTokenCacheKey() {
        return Constant.Cache.TRIPLAYZ_TOKEN;
    }
}
