package com.vitadairy.warehouse.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.warehouse.client.auth.SapAuthProvider;
import com.vitadairy.warehouse.configs.SapAuthProperties;
import com.vitadairy.warehouse.configs.SapProperties;
import com.vitadairy.warehouse.dto.SAPResponseDto;
import com.vitadairy.warehouse.entities.SapApiConfig;
import com.vitadairy.warehouse.entities.Transaction;
import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import com.vitadairy.warehouse.repositories.TransactionRepository;
import com.vitadairy.warehouse.services.SapResponseService;
import com.vitadairy.warehouse.services.apiconfig.SapApiConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SapClient implements TransactionClient {
    private final SapProperties sapProp;
    private final SapAuthProperties sapAuthProp;
    private final SapAuthProvider sapAuthProvider;
    private final RestTemplateClient restTemplateClient;
    private final SapResponseService sapResponseService;
    private final SapApiConfigService sapApiConfigService;
    private final TransactionRepository transactionRepository;
    private final ObjectMapper objectMapper;

    public SapClient(SapProperties sapProp,
                     SapAuthProperties sapAuthProp,
                     SapAuthProvider sapAuthProvider,
                     RestTemplate restTemplate,
                     SapResponseService salesforceResponseService,
                     SapApiConfigService sapApiConfigService,
                     TransactionRepository transactionRepository,
                     ObjectMapper objectMapper) {
        this.sapProp = sapProp;
        this.sapAuthProp = sapAuthProp;
        this.sapAuthProvider = sapAuthProvider;
        this.sapApiConfigService = sapApiConfigService;
        this.restTemplateClient = new RestTemplateClient(sapProp.getEndpoint(), restTemplate);
        this.sapResponseService = salesforceResponseService;
        this.transactionRepository = transactionRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    public String getDestinationCode() {
        return DestinationEnum.SAP.name();
    }

    @Override
    public void send(Transaction transaction) {
        var apiMap = this.sapApiConfigService.getApiMap();
        var apiConfig = apiMap.get(transaction.getCode());

        if (apiConfig == null) {
            log.warn("Transaction code {} has no api config", transaction.getCode());
            return;
        }

        this.send(transaction, apiConfig);
    }

    @Override
    public void send(Transaction transaction, WsBaseApiConfig baseApiConfig) {
        var apiConfig = (SapApiConfig) baseApiConfig;
        SAPResponseDto dto = new SAPResponseDto();
        var sentAt = Instant.now();
        try {
            // get token
            var token = this.sapAuthProvider.getToken();
            log.info("Send SAP with token: {}", token);

            // builder header
            this.restTemplateClient.buildAuth("Bearer " + token);
            var header = this.restTemplateClient.defaultHeader();
            header.set("Ocp-Apim-Subscription-Key", sapAuthProp.getHeaderSubscriptionKey());
            header.set("function", apiConfig.getFunction());
            header.set("partner", apiConfig.getPartner());

            // build payload
            Object sapPayload = null;
            if (transaction.getPayload() != null) {
                var payload = transaction.getPayload();
                if (!payload.containsKey("Tranx_type")) {
                    payload.put("Tranx_type", apiConfig.getTranxType());
                }
                sapPayload = Map.of("data", List.of(transaction.getPayload()));
            } else if (transaction.getTListPayload() != null) {
                var tListPayload = transaction.getTListPayload();
                for (var p : tListPayload) {
                    if (!p.containsKey("Tranx_type")) {
                        p.put("Tranx_type", apiConfig.getTranxType());
                    }
                }
                sapPayload = Map.of("data", transaction.getTListPayload());
            }

            // send request
            var sapResponse = this.restTemplateClient.post("", header, sapPayload, new ParameterizedTypeReference<>() {
            });
            dto.setResponse(this.objectMapper.writeValueAsString(sapResponse));
            dto.setHttpStatus(HttpStatus.OK);
            dto.setReceivedAt(Instant.now());
            transaction.setStatus(TransactionStatus.SUCCESS);
        } catch (Exception e) {
            dto = this.handleException(e);
            TransactionStatus newStatus = TransactionStatus.RETRY;
            if (dto.getHttpStatus() == HttpStatus.OK) {
                newStatus = TransactionStatus.SUCCESS;
            } else {
                transaction.incrementRetryCount();
                if (transaction.getStatus() == TransactionStatus.RETRY && transaction.getRetryCount() == this.sapProp.getMaxRetryCount()) {
                    newStatus = TransactionStatus.FAILED;
                }
            }
            transaction.setStatus(newStatus);
        }

        transaction = this.transactionRepository.save(transaction);
        log.info("Upsert transaction with id {}", transaction.getId());
        dto.setSentAt(sentAt);
        this.sapResponseService.save(transaction, dto);
    }

    private SAPResponseDto handleException(Exception e) {
        var statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        String response = e.getMessage();

        if (e instanceof HttpStatusCodeException err) {
            response = err.getResponseBodyAsString();
            statusCode = HttpStatus.valueOf(err.getStatusCode().value());
        }

        return new SAPResponseDto()
                .setResponse(response)
                .setHttpStatus(statusCode)
                .setReceivedAt(Instant.now());
    }
}
