package com.vitadairy.warehouse.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
public class SapAuthProperties {
    @Value("${sap.auth.endpoint:unknown}")
    private String endpoint;
    @Value("${sap.auth.header.subscription-key:unknown}")
    private String headerSubscriptionKey;
    @Value("${sap.auth.grantType:unknown}")
    private String grantType;
    @Value("${sap.auth.clientId:unknown}")
    private String clientId;
    @Value("${sap.auth.clientSecret:unknown}")
    private String clientSecret;
    @Value("${sap.auth.scope:unknown}")
    private String scope;
}
