package com.vitadairy.warehouse.configs;

import com.vitadairy.warehouse.services.export.ImportLogger;
import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.WriteRowProcessor;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.libraries.importexport.service.WriteDataDateService;
import com.vitadairy.libraries.importexport.service.WriteDataDefaultService;
import com.vitadairy.libraries.importexport.service.WriteDataNumberService;
import com.vitadairy.libraries.importexport.service.WriteDataServiceStrategy;
import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.libraries.importexport.service.WriteExportFileServiceImpl;
import com.vitadairy.warehouse.dto.ExportHistoryEventTransactionDto;
import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class ExportHistoryEventConfiguration {
    @Bean(name = "exportHistoryEventMetaData")
    public List<CellMetaData> exportHistoryEventsMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("User ID").dataType(DataType.NUMBER).fieldName("userId").build());
        metaDataList.add(CellMetaData.builder().name("Phone number").dataType(DataType.STRING).fieldName("phoneNumber").build());
        metaDataList.add(CellMetaData.builder().name("User name").dataType(DataType.STRING).fieldName("userName").build());
        metaDataList.add(CellMetaData.builder().name("Sku").dataType(DataType.STRING).fieldName("sku").build());
        metaDataList.add(CellMetaData.builder().name("Gift name").dataType(DataType.STRING).fieldName("giftName").build());
        metaDataList.add(CellMetaData.builder().name("Brand").dataType(DataType.STRING).fieldName("brand").build());
        metaDataList.add(CellMetaData.builder().name("Transaction External Id").dataType(DataType.STRING).fieldName("transactionCode").build());
        metaDataList.add(CellMetaData.builder().name("Gift received date").dataType(DataType.DATE).fieldName("receivedDate").build());
        metaDataList.add(CellMetaData.builder().name("User registered date").dataType(DataType.DATE).fieldName("registeredDate").build());
        metaDataList.add(CellMetaData.builder().name("QR code").dataType(DataType.STRING).fieldName("qrCode").build());
        metaDataList.add(CellMetaData.builder().name("Spoon code").dataType(DataType.STRING).fieldName("spoonCode").build());
        metaDataList.add(CellMetaData.builder().name("Province").dataType(DataType.STRING).fieldName("province").build());
        metaDataList.add(CellMetaData.builder().name("Status").dataType(DataType.STRING).fieldName("status").build());
        return metaDataList;
    }

    @Bean(name = "exportHistoryEventLogger")
    public ILogger exportHistoryEventLogger() {
        return new ImportLogger("ExportHistoryEvent");
    }

    @Bean(name = "exportHistoryEventRowProcessor")
    public WriteRowProcessor exportHistoryEventRowProcessor(
            @Qualifier("exportHistoryEventMetaData") List<CellMetaData> metaDataList,
            @Qualifier("exportHistoryEventLogger") ILogger logger) {
        return new WriteRowProcessor(
                new WriteDataServiceStrategy(
                        new WriteDataDefaultService(),
                        new WriteDataNumberService(),
                        new WriteDataDateService("dd/MM/yyyy HH:mm:ss", logger)),
                metaDataList,
                logger);
    }

    @Bean(name = "exportHistoryEventWriter")
    public WriteExportFileService<ExportHistoryEventTransactionDto, ListHistoryLuckyTransactionRequest> exportHistoryEventWriter(
            FetchDataService<ExportHistoryEventTransactionDto, ListHistoryLuckyTransactionRequest> fetchDataService,
            @Qualifier("exportHistoryEventRowProcessor") WriteRowProcessor rowProcessor,
            @Qualifier("exportHistoryEventLogger") ILogger logger) {
        return new WriteExportFileServiceImpl<>(fetchDataService, rowProcessor, logger);
    }
}
