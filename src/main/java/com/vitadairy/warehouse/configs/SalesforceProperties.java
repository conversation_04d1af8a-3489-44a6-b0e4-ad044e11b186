package com.vitadairy.warehouse.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
public class SalesforceProperties {
    @Value("${salesforce.job.max-retry-count:4}")
    private int maxRetryCount;
    @Value("${salesforce.endpoint:unknown}")
    private String endpoint;
    @Value("${salesforce.api.auth-token:unknown}")
    private String apiAuthToken;
    @Value("${salesforce.grantType:unknown}")
    private String grantType;
    @Value("${salesforce.clientId:unknown}")
    private String clientId;
    @Value("${salesforce.clientSecret:unknown}")
    private String clientSecret;
    @Value("${salesforce.refreshToken:unknown}")
    private String refreshToken;
}
