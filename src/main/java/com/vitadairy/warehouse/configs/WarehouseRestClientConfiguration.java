package com.vitadairy.warehouse.configs;

import com.vitadairy.main.helper.RestTemplateClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class WarehouseRestClientConfiguration {

    @Value("${rest.url.gift-service}")
    private String giftServiceUrl;

    @Bean
    public RestTemplateClient restTemplateWHOsGiftClient(RestTemplate restTemplate) {
        return new RestTemplateClient(giftServiceUrl, restTemplate);
    }
}
