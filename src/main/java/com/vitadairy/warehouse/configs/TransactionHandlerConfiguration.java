package com.vitadairy.warehouse.configs;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class TransactionHandlerConfiguration {
    @Bean("salesforceTransactionHandlerExecutor")
    public ThreadPoolTaskExecutor salesforceTransactionHandlerExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(30);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("salesforce-transaction-handler-");
        executor.initialize();
        return executor;
    }

    @Bean("retryFailedTransactionExecutor")
    public ThreadPoolTaskExecutor retryFailedTransactionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("retry-failed-transaction-");
        executor.initialize();
        return executor;
    }
} 