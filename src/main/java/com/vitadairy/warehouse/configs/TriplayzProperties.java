package com.vitadairy.warehouse.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
public class TriplayzProperties {
    @Value("${triplayz.endpoint}")
    private String endpoint;
    @Value("${triplayz.token}")
    private String token;
    @Value("${triplayz.job.max-retry-count:4}")
    private int maxRetryCount;
}
