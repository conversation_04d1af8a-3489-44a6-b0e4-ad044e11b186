package com.vitadairy.warehouse.configs;

import com.zaxxer.hikari.HikariDataSource;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration("warehouseDatasourceConfiguration")
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.vitadairy.warehouse.repositories",
        entityManagerFactoryRef = "warehouseEntityManagerFactory",
        transactionManagerRef = "warehouseTransactionManager")
public class WarehouseDatasourceConfiguration {

    @Value("${spring.flyway.warehouse.enabled}")
    private Boolean flywayEnabled;
    @Bean(name = "warehouseDataSource")
    @ConfigurationProperties("spring.datasource.warehouse")
    public DataSource dataSource(
            @Value("${spring.datasource.warehouse.hikari.pool-name:HikariWHCP}") String poolName,
            @Value("${spring.datasource.warehouse.hikari.maximum-pool-size:10}") Integer maximumPoolSize,
            @Value("${spring.datasource.warehouse.hikari.minimum-idle:2}") Integer minimumIdle,
            @Value("${spring.datasource.warehouse.hikari.idle-timeout:120000}") Integer idleTimeout,
            @Value("${spring.datasource.warehouse.hikari.connection-timeout:30000}") Integer connectionTimeout,
            @Value("${spring.datasource.warehouse.hikari.max-lifetime:1800000}") Integer maxLifeTime
    ) {
        /*return DataSourceBuilder.create()
                .build();*/
        HikariDataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        dataSource.setConnectionTimeout(connectionTimeout);
        dataSource.setMaximumPoolSize(maximumPoolSize);
        dataSource.setMinimumIdle(minimumIdle);
        dataSource.setIdleTimeout(idleTimeout);
        dataSource.setMaxLifetime(maxLifeTime);
        dataSource.setPoolName(poolName);

        return dataSource;
    }

    @Bean(name = "warehouseTransactionManager")
    @Autowired
    JpaTransactionManager dataSourceTransactionManager(
            @Qualifier("warehouseEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(
                entityManagerFactory.getObject());
        return transactionManager;
    }

    @Bean(name = "warehouseEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("warehouseDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.vitadairy.warehouse.entities")
                .persistenceUnit("warehouse")
                .build();
    }

    @Bean
    public FlywayMigrationInitializer dbWarehouseFlywayInitializer(@Qualifier("warehouseDataSource") DataSource dataSource) {
        if(flywayEnabled){
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:dbchange/warehouse")
                    .baselineOnMigrate(true)
                    .validateMigrationNaming(true)
                    .outOfOrder(true)
                    .load();
            return new FlywayMigrationInitializer(flyway);
        }
        else {
            return null;
        }
    }
}
