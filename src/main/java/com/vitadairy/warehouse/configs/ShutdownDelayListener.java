package com.vitadairy.warehouse.configs;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class ShutdownDelayListener {

    private final int delay;
    private final ThreadPoolTaskExecutor executor;
    private final KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry;

    public ShutdownDelayListener(@Value("${spring.delay.shutdown}") int delay,
                                 @Qualifier("salesforceTransactionHandlerExecutor") ThreadPoolTaskExecutor executor,
                                 KafkaListenerEndpointRegistry kafkaListenerEndpointRegistry) {
        this.delay = delay;
        this.executor = executor;
        this.kafkaListenerEndpointRegistry = kafkaListenerEndpointRegistry;
    }

    @EventListener
    public void onShutdown(ContextClosedEvent event) throws InterruptedException {
        log.info("Application is shutting down. Stopping Kafka consumers...");
        
        // Stop all Kafka consumers
        kafkaListenerEndpointRegistry.getAllListenerContainers().forEach(container -> {
            log.info("Stopping Kafka consumer: {}", container.getListenerId());
            container.stop();
        });
        
        log.info("All Kafka consumers stopped. Delaying shutdown for {}ms for salesforce transaction handler to finish all remaining works...", delay);
        Thread.sleep(delay); // Delay thread

        ThreadPoolExecutor pool = executor.getThreadPoolExecutor();

        // Get the number of active threads
        int activeThreads = pool.getActiveCount();

        // Get the total number of completed tasks
        long completedTasks = pool.getCompletedTaskCount();

        // Get the number of tasks in the queue
        int queuedTasks = pool.getQueue().size();

        // Print the status
        log.info("SalesforceTransactionHandlerExecutor Active threads: {}", activeThreads);
        log.info("SalesforceTransactionHandlerExecutor Completed tasks: {}", completedTasks);
        log.info("SalesforceTransactionHandlerExecutor Queued tasks: {}", queuedTasks);

        // Detect if all tasks are done
        if (activeThreads == 0 && queuedTasks == 0) {
            log.info("SalesforceTransactionHandlerExecutor is idle, all tasks are done.");
        } else {
            log.info("SalesforceTransactionHandlerExecutor is still working.");
        }
        log.info("Application starts to shut down");
    }
}
