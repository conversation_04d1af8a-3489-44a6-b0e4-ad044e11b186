package com.vitadairy.warehouse.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component("warehouseKafkaPropertiesConfig")
@Getter
@Setter
public class KafkaPropertyConfig {

    @Value("${spring.kafka.bootstrap-servers:}")
    private String bootStrapServer;
    @Value("${spring.kafka.ssl.protocol:PLAINTEXT}")
    private String sslProtocol;
    @Value("${spring.kafka.producer.enable.idempotence:false}")
    private Boolean producerEnableIdempotence;
    @Value("${spring.kafka.wh.transaction.consumer.concurrent:2}")
    private Integer consumerConcurrent;
    @Value("${spring.kafka.wh.transaction.consumer.max.poll.interval.ms:900000}")
    private Integer consumerMaxPollIntervalMs;
    @Value("${spring.kafka.wh.transaction.consumer.max.poll.records:100}")
    private Integer consumerMaxPollRecords;
    @Value("${spring.kafka.wh.transaction.consumer.enable.auto.commit:false}")
    private Boolean enableAutoCommit;
    @Value("${spring.kafka.wh.transaction.container.ack-mode:MANUAL}")
    private String containerAckMode;
}

