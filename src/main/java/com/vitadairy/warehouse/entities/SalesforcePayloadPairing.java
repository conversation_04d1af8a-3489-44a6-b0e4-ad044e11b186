package com.vitadairy.warehouse.entities;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Getter
@Setter
@Table(name = "ws_sf_payload_pairing")
public class SalesforcePayloadPairing {
    @EmbeddedId
    private Pk pk;

    @Column(name = "updated_at", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant updatedAt;

    public SalesforcePayloadPairing(String field, String value) {
        this.pk = new Pk(field, value);
    }

    @Data
    @Embeddable
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Pk {
        @Column(name = "field")
        private String field;

        @Column(name = "value")
        private String value;
    }
}
