package com.vitadairy.warehouse.entities;

import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "ws_sf_api_config")
public class SalesforceApiConfig extends WsBaseApiConfig {
    @Column(name = "path")
    private String path;

    @Column(name = "http_method")
    private String httpMethod;

    public SalesforceApiConfig() {

    }
}
