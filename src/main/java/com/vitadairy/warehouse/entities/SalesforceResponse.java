package com.vitadairy.warehouse.entities;

import com.vitadairy.warehouse.entities.base.WsBaseResponse;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Getter
@Setter
@Table(name = "ws_sf_response")
public class SalesforceResponse extends WsBaseResponse {
    @Column(name = "status")
    private Integer status;
}
