package com.vitadairy.warehouse.entities.base;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.Instant;

@Getter
@Setter
@Accessors(chain = true)
@MappedSuperclass
public class WsBaseResponse {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "transaction_id", nullable = false)
    private Integer transactionId;

    @Column(name = "http_code", nullable = false)
    private Integer httpCode;

    @Column(name = "response")
    private String response;

    @Column(name = "sent_at", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant sendAt;

    @Column(name = "received_at", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant receivedAt;
}
