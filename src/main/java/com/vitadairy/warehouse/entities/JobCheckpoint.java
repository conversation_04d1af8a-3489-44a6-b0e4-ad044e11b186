package com.vitadairy.warehouse.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.Instant;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "ws_job_checkpoint")
@Accessors(chain = true)
public class JobCheckpoint {
    @Id
    private String job;

    @Column(name = "previous_checkpoint", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant previousCheckpoint;

    @Column(name = "current_checkpoint", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant currentCheckpoint;
}
