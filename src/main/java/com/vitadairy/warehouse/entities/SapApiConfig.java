package com.vitadairy.warehouse.entities;

import com.vitadairy.warehouse.entities.base.WsBaseApiConfig;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "ws_sap_api_config")
public class SapApiConfig extends WsBaseApiConfig {
    @Column(name = "function")
    private String function;

    @Column(name = "partner")
    private String partner;

    @Column(name = "tranx_type")
    private String tranxType;

    public SapApiConfig() {}
}
