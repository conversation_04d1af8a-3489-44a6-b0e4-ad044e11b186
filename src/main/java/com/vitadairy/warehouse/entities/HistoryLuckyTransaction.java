package com.vitadairy.warehouse.entities;

import com.vitadairy.main.enums.TransactionStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "ws_history_lucky_transaction")
public class HistoryLuckyTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "sku")
    private String sku;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "gift_name")
    private String giftName;

    @Column(name = "brand")
    private String brand;

    @Column(name = "transaction_code")
    private String transactionCode;

    @Column(name = "gift_received_date", columnDefinition = "TIMESTAMP")
    private LocalDateTime receivedDate;

    @Column(name = "user_registered_date", columnDefinition = "TIMESTAMP")
    private LocalDateTime registeredDate;

    @Column(name = "qr_code")
    private String qrCode;

    @Column(name = "spoon_code")
    private String spoonCode;

    @Column(name = "province")
    private String province;

    @Column(name = "status")
    private String status;

    @Column(name = "event_id")
    private Long eventId;

    @Column(name = "event_name")
    private String eventName;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false, columnDefinition = "TIMESTAMP")
    private LocalDateTime createdAt;
}
