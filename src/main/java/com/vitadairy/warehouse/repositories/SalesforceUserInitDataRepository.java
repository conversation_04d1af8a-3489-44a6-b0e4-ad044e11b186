package com.vitadairy.warehouse.repositories;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.vitadairy.warehouse.entities.SalesforceUserInitData;

@Repository("warehouseSalesforceUserInitDataRepository")
@Transactional("warehouseTransactionManager")
public interface SalesforceUserInitDataRepository extends JpaRepository<SalesforceUserInitData, Long> {

    Optional<SalesforceUserInitData> findByUserId(Long userId);

    @Transactional
    @Query(value = """
        INSERT INTO ws_sf_user_init_data as t (user_id, trigger_start_time, trigger_end_time)
        VALUES (:userId, :startTime, :endTime)
        ON CONFLICT (user_id) DO UPDATE
        SET trigger_start_time = CASE 
                                    WHEN t.trigger_start_time IS NULL AND EXCLUDED.trigger_end_time IS NOT NULL 
                                    THEN EXCLUDED.trigger_end_time 
                                    ELSE COALESCE(t.trigger_start_time, EXCLUDED.trigger_start_time) 
                                END,
            trigger_end_time   = COALESCE(t.trigger_end_time, EXCLUDED.trigger_end_time)
        RETURNING *
        """, nativeQuery = true)
    SalesforceUserInitData upsertSalesforceUserInitData(Long userId, Instant startTime, Instant endTime);

    @Query("SELECT t FROM SalesforceUserInitData t WHERE t.triggerEndTime IS NULL")
    List<SalesforceUserInitData> getUserNotSuccessInitData();
}
