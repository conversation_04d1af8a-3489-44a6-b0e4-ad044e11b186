package com.vitadairy.warehouse.repositories;

import com.vitadairy.warehouse.entities.JobCheckpoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository("warehouseJobJobCheckpointRepository")
@Transactional("warehouseTransactionManager")
public interface JobCheckpointRepository extends JpaRepository<JobCheckpoint, String> {
    @Query("SELECT jc FROM JobCheckpoint jc WHERE jc.job = :job")
    Optional<JobCheckpoint> getJobCheckpointByJob(@Param("job") String job);
}
