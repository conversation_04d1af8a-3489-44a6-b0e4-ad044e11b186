package com.vitadairy.warehouse.repositories;

import com.vitadairy.warehouse.entities.SalesforcePayloadPairing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import java.time.Instant;

@Repository("warehouseSalesforcePayloadPairingRepository")
@Transactional("warehouseTransactionManager")
public interface SalesforcePayloadPairingRepository extends JpaRepository<SalesforcePayloadPairing, SalesforcePayloadPairing.Pk> {
    @Query("UPDATE SalesforcePayloadPairing SET updatedAt = CURRENT_TIMESTAMP WHERE pk.field = :field AND pk.value = :value")
    @Modifying
    int updateTimeUpdatedAtByFieldAndValue(String field, String value); 

    @Query("SELECT COUNT(*) FROM SalesforcePayloadPairing p WHERE p.updatedAt IS NOT NULL AND p.updatedAt < :cutoffDate")
    int countByUpdatedAtBefore(Instant cutoffDate);

    @Query("DELETE FROM SalesforcePayloadPairing p WHERE p.updatedAt IS NOT NULL AND p.updatedAt < :cutoffDate")
    @Modifying
    int deleteAllByUpdatedAtBefore(Instant cutoffDate);
}
