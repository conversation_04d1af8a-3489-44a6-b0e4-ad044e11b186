package com.vitadairy.warehouse.repositories;

import com.vitadairy.warehouse.entities.Transaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Repository("warehouseTransactionRepository")
@Transactional("warehouseTransactionManager")
public interface TransactionRepository extends JpaRepository<Transaction, Integer> {
    @Query("SELECT t FROM Transaction t " +
            "WHERE t.createdAt BETWEEN :start AND :end " +
            "AND t.status = com.vitadairy.main.enums.TransactionStatus.RETRY")
    List<Transaction> getRetryTransactionBetween(@Param("start") Instant start, @Param("end") Instant end);

    @Query(value = "SELECT t.* FROM ws_transaction t " +
            "WHERE t.created_at >= date_trunc('day', NOW()) and t.created_at <= NOW() - INTERVAL '5 minutes' " +
            "AND t.status IS NULL offset 0 limit 10",  nativeQuery = true)
    List<Transaction> getNonStatusTransaction();

    @Query(value = "SELECT 1 FROM ws_transaction " +
            "WHERE created_at BETWEEN (NOW() - INTERVAL '1 day') AND NOW() " +
            "AND destination = 'SF' " +
            "AND code = 'ADD_POINT' " +
            "AND payload_array @> :fieldValue", nativeQuery = true)
    Boolean existsByPayloadArrayWithFieldValueWithin1Day(String fieldValue);

    @Query(value = "SELECT 1 FROM ws_transaction " +
            "WHERE created_at BETWEEN (NOW() - INTERVAL '1 day') AND NOW() " +
            "AND destination = 'SF' " +
            "AND code = 'ADD_POINT' " +
            "AND payload @> :fieldValue", nativeQuery = true)
    Boolean existsByPayloadWithFieldValueWithin1Day(String fieldValue);
}
