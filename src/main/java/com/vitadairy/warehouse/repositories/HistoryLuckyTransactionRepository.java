package com.vitadairy.warehouse.repositories;

import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import com.vitadairy.warehouse.entities.HistoryLuckyTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional("warehouseTransactionManager")
public interface HistoryLuckyTransactionRepository extends JpaRepository<HistoryLuckyTransaction, Long> {

    @Query("SELECT hlt FROM HistoryLuckyTransaction hlt WHERE (COALESCE(:#{#request.giftName}) IS NULL OR hlt.giftName ILIKE '%' || :#{#request.giftName} || '%') " +
            "AND (COALESCE(:#{#request.brand}, null) IS NULL OR hlt.brand IN (:#{#request.brand})) " +
            "AND (COALESCE(:#{#request.eventName}) IS NULL OR hlt.eventName ILIKE '%' || :#{#request.eventName} || '%') " +
            "AND ((COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NOT NULL AND hlt.receivedDate between :#{#request.fromDate} AND :#{#request.toDate}) " +
                "OR (COALESCE(:#{#request.fromDate}) IS NOT NULL AND COALESCE(:#{#request.toDate}) IS NULL AND hlt.receivedDate >= :#{#request.fromDate}) " +
                "OR (COALESCE(:#{#request.toDate}) IS NOT NULL AND COALESCE(:#{#request.fromDate}) IS NULL AND hlt.receivedDate <= :#{#request.toDate}) " +
                "OR (COALESCE(:#{#request.fromDate}) IS NULL AND COALESCE(:#{#request.toDate}) IS NULL)) ORDER BY hlt.receivedDate DESC")
    Page<HistoryLuckyTransaction> getListHistoryLuckyTransaction(ListHistoryLuckyTransactionRequest request, Pageable pageable);


    List<HistoryLuckyTransaction> findAllByTransactionCode(String transactionCode);
}
