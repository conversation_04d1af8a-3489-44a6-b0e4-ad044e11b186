package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.services.WarehouseTransactionService;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v4/ws/int")
public class InternalSendController {
    private final WarehouseTransactionService whTransactionService;

    public InternalSendController(WarehouseTransactionService whTransactionService) {
        this.whTransactionService = whTransactionService;
    }

    @PostMapping("/send")
    @PreAuthorize("hasAuthority('INTERNAL')")
    public ResponseEntity<Object> sendTransaction(@Valid @RequestBody TransactionBatchDto batch) {
        this.whTransactionService.send(batch);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
