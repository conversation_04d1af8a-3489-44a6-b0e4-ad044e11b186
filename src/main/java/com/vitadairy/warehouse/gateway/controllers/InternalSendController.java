package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.warehouse.dto.ResponseDto;
import com.vitadairy.warehouse.dto.SalesforceUserInitDataRequestDto;
import com.vitadairy.warehouse.services.SalesforceUserInitDataService;

import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v4/ws/int")
public class InternalSendController {
    private final WarehouseTransactionService whTransactionService;
    private final SalesforceUserInitDataService salesforceUserInitDataService;

    public InternalSendController(WarehouseTransactionService whTransactionService,
                                  SalesforceUserInitDataService salesforceUserInitDataService) {
        this.whTransactionService = whTransactionService;
        this.salesforceUserInitDataService = salesforceUserInitDataService;
    }

    @PostMapping("/send")
    @PreAuthorize("hasAuthority('INTERNAL')")
    public ResponseEntity<Object> sendTransaction(@Valid @RequestBody TransactionBatchDto batch) {
        this.whTransactionService.send(batch);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/sync-init-sf")
    @PreAuthorize("hasAuthority('INTERNAL')")
    public ResponseEntity<Object> updateSalesforceUserInitData(@Valid @RequestBody SalesforceUserInitDataRequestDto dto) {
        var resp = this.salesforceUserInitDataService.updateSalesforceUserInitData(dto);
        return ResponseDto.success(resp);
    }
}
