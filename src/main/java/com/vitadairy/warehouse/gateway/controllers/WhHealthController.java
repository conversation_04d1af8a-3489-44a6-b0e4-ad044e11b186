package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.warehouse.services.WhHealthService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("WhHealthController")
@RequestMapping("v4/ws/no-auth-token/health")
@AllArgsConstructor
public class WhHealthController {
    @Qualifier("WhHealthService")
    private final WhHealthService whHealthService;

    @GetMapping("/transaction-sync")
    public ResponseEntity<String> checkTransactionStillWork() {
        return this.whHealthService.checkTransactionStillWork();
    }
}
