package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionRequestDto;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionResponseDto;
import com.vitadairy.main.dto.HistoryLuckyTransactionUpdateRequestDto;
import com.vitadairy.warehouse.services.HistoryLuckyTransactionService;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v4/ws/no-auth-token/history-event")
@AllArgsConstructor
@Slf4j
public class PublicHistoryLuckyController {
    private final HistoryLuckyTransactionService historyLuckyTransactionService;
    private final ResponseFactory responseFactory;

    @PostMapping()
    public ResponseEntity<?> createTransaction(@Valid @RequestBody HistoryLuckyTransactionRequestDto dto, @RequestParam String hashedKey) {
        try{
            historyLuckyTransactionService.validateHashKey(hashedKey);
            return responseFactory.successDto(new HistoryLuckyTransactionResponseDto(historyLuckyTransactionService.saveTransaction(dto)));
        }
        catch (Exception ex){
            log.error("Error while creating history lucky transaction", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PutMapping("/update")
    public ResponseEntity<?> updateGiftStatus(@Valid @RequestBody HistoryLuckyTransactionUpdateRequestDto dto, @RequestParam String hashedKey) {
        try{
            historyLuckyTransactionService.validateHashKey(hashedKey);
            return responseFactory.successDto(new HistoryLuckyTransactionResponseDto(historyLuckyTransactionService.updateTransaction(dto)));
        }
        catch (Exception ex){
            log.error("Error while creating history lucky transaction", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
