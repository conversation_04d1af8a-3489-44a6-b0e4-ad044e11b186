package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.warehouse.services.SalesForceInternalService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("in/ws/query-sf")
@AllArgsConstructor
@Slf4j
public class InternalQuerySFController {
    private final SalesForceInternalService salesforceClient;
    private final ResponseFactory responseFactory;

    @GetMapping("/user-info")
    public ResponseEntity<?> getUserInfo(@RequestParam Long userId) {
        try {
            return new ResponseEntity<>(salesforceClient.getUserInfo(userId), HttpStatus.OK);
        } catch (Exception ex) {
            log.error("Error while get user info", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
