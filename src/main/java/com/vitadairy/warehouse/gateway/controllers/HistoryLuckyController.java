package com.vitadairy.warehouse.gateway.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.warehouse.dto.ExportHistoryEventRequest;
import com.vitadairy.warehouse.dto.GetHistoryLuckyTransactionDto;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionRequestDto;
import com.vitadairy.warehouse.dto.HistoryLuckyTransactionResponseDto;
import com.vitadairy.main.dto.HistoryLuckyTransactionUpdateRequestDto;
import com.vitadairy.warehouse.dto.ListHistoryLuckyTransactionRequest;
import com.vitadairy.warehouse.services.HistoryLuckyTransactionService;
import com.vitadairy.warehouse.services.export.ExportService;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"v4/ws/history-event", "v4/int/ws/history-event"})
@AllArgsConstructor
@Slf4j
public class HistoryLuckyController {
    private final HistoryLuckyTransactionService historyLuckyTransactionService;
    private final ResponseFactory responseFactory;

    private ExportService exportService;

    @PostMapping()
    public ResponseEntity<?> createTransaction(@Valid @RequestBody HistoryLuckyTransactionRequestDto dto) {
        try{
            return responseFactory.successDto(new HistoryLuckyTransactionResponseDto(historyLuckyTransactionService.saveTransaction(dto)));
        }
        catch (Exception ex){
            log.error("Error while creating history lucky transaction", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PutMapping("/update")
    public ResponseEntity<?> updateGiftStatus(@Valid @RequestBody HistoryLuckyTransactionUpdateRequestDto dto) {
        try{
            return responseFactory.successDto(new HistoryLuckyTransactionResponseDto(historyLuckyTransactionService.updateTransaction(dto)));
        }
        catch (Exception ex){
            log.error("Error while creating history lucky transaction", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<GetHistoryLuckyTransactionDto> findTransaction(@ModelAttribute ListHistoryLuckyTransactionRequest request) {
        try {
            request.doValidate();
            return responseFactory.successDto(historyLuckyTransactionService.getListHistoryLuckyTransaction(request));
        } catch (Exception ex) {
            log.error("Error while finding order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<HistoryLuckyTransactionResponseDto> findById(@PathVariable Long id) {
        try {
            return responseFactory.successDto(new HistoryLuckyTransactionResponseDto(historyLuckyTransactionService.findById(id)));
        } catch (Exception ex) {
            log.error("Error while finding order", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("/export")
    public ResponseEntity<?> export(@Valid @RequestBody ExportHistoryEventRequest request) {
        try {
            request.doValidate();
            return historyLuckyTransactionService.exportHistoryLuckyTransaction(request);
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
