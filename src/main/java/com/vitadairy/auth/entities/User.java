package com.vitadairy.auth.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Table
@Entity(name = "users")
@Getter
@Setter
public class User {
    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "province_id")
    private Long provinceId;

    @Column(name = "last_login_date", updatable = false, columnDefinition = "TIMESTAMP")
    private LocalDate lastLoginDate;
}
