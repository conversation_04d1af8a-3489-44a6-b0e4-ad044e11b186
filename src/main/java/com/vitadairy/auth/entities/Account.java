package com.vitadairy.auth.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Table
@Entity(name = "account")
@Getter
@Setter
public class Account {
    @Id
    private Long id;
    @Column(name = "email")
    private String email;
    @Column(name = "status")
    private String status;
}
