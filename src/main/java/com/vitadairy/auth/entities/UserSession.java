package com.vitadairy.auth.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Table
@Entity(name = "user_session")
@Getter
@Setter
public class UserSession {
    @Id
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "access_token")
    private String accessToken;

    @Column(name = "refresh_token")
    private String refreshToken;

    @Column(name = "device_token")
    private String deviceToken;

    @Column(name = "device_info")
    private String deviceInfo;

    @Column(name = "created_date", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant createdDate;

    @Column(name = "updated_date", updatable = false, columnDefinition = "TIMESTAMP")
    private Instant updatedDate;
}
