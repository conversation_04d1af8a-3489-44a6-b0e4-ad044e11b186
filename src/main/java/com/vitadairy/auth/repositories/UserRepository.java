package com.vitadairy.auth.repositories;

import com.vitadairy.auth.entities.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Repository("authUserRepository")
public interface UserRepository extends JpaRepository<User, Integer> {

    User getUserById(Long id);

    @Modifying
    @Transactional(value = "authTransactionManager", timeout = 1)
    @Query(value = "UPDATE users SET last_login_date = :lastLoginDate WHERE id = :id", nativeQuery = true)
    void updateUserLastLoginDate(@Param("id") Long id, @Param("lastLoginDate") LocalDateTime lastLoginDate);
}
