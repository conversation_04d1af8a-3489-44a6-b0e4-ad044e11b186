package com.vitadairy.auth.repositories;

import com.vitadairy.auth.entities.UserSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("authUserSessionRepository")
public interface UserSessionRepository extends JpaRepository<UserSession, Integer> {

    List<UserSession> getUserSessionByAccessToken(String accessToken);
}
