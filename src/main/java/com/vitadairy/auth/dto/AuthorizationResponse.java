package com.vitadairy.auth.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AuthorizationResponse {
    // for type 'USER'
    private Long sessionId;
    private Long userId;
    private String name;
    private String phoneNumber;
    private Long provinceId;

    // for type 'ADMIN'
    private Long accountId;
    private String email;
}