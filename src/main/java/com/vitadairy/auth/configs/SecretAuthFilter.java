package com.vitadairy.auth.configs;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
public class SecretAuthFilter extends OncePerRequestFilter {
    @Value("${clientcronjob.id}")
    private final String clientId = "clientid";
    @Value("${clientcronjob.secret}")
    private final String clientSecret = "secret";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filterChain)
            throws ServletException, IOException {
        // TODO Auto-generated method stub
        String xClientId = request.getHeader("x-vita-client-id");
        String xClientSecret = request.getHeader("x-vita-client-secret");
        if (xClientId == null || xClientSecret == null) {
            throw new UnsupportedOperationException("Unimplemented method 'doFilterInternal'");
        }
        try {
            if (!xClientId.equals(clientId) || !xClientSecret.equals(clientSecret)) {
                return;
            }
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            response.setStatus(401);
            filterChain.doFilter(request, response);
        }
    }

}
