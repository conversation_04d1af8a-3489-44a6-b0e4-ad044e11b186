package com.vitadairy.auth.configs;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@ConditionalOnWebApplication
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    private final JwtAuthFilter jwtAuthFilter;
    private final boolean authEnabled;

    public SecurityConfig(JwtAuthFilter jwtAuthFilter,
            @Value("${spring.security.auth.enabled:true}") boolean authEnabled) {
        this.jwtAuthFilter = jwtAuthFilter;
        this.authEnabled = authEnabled;
    }

    static String[] pathsAllow = new String[] { "health/check", "api-docs", "swagger-ui", "webhook/", "secret/", "no-auth-token/" };

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .cors((cors) -> cors
                        .configurationSource(corsConfigurationSource()))
                .httpBasic(AbstractHttpConfigurer::disable)
                .addFilterAfter(this.jwtAuthFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests((requests) -> {
                    if (this.authEnabled) {
                        requests
                                .requestMatchers(SecurityConfig::isAllowRequest).permitAll()
                                .requestMatchers("/v4/**").authenticated()
                                .anyRequest().permitAll();
                    } else {
                        requests.anyRequest().permitAll();
                    }
                })
                .build();
    }

    static boolean isAllowRequest(HttpServletRequest req) {
        String path = req.getRequestURI();
        return Arrays.stream(pathsAllow).anyMatch(path::contains);
    }

    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("*"));
        configuration.setAllowedMethods(List.of("*"));
        configuration.setAllowedHeaders(List.of("*"));
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * control @EnableMethodSecurity
     * If auth enabled, prePostEnabled = true, @PreAuthorize will be executed to
     * check api authen role
     * Otherwise, if auth disabled, prePostEnabled = false, @PreAuthorize will be
     * ignored
     */
    @ConditionalOnProperty(prefix = "spring.security.auth", name = "enabled", havingValue = "false")
    @EnableMethodSecurity(prePostEnabled = false)
    @Configuration
    public static class DisablePreAuthMethodSecurity {
    }

    @ConditionalOnProperty(prefix = "spring.security.auth", name = "enabled", havingValue = "true")
    @EnableMethodSecurity
    @Configuration
    public static class EnablePreAuthMethodSecurity {
    }
}
