package com.vitadairy.auth.configs;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration("authDatasourceConfiguration")
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.vitadairy.auth.repositories",
        entityManagerFactoryRef = "authEntityManagerFactory",
        transactionManagerRef = "authTransactionManager"
)
public class AuthDatasourceConfiguration {
    @Primary
    @Bean(name = "authDataSource")
    @ConfigurationProperties("spring.datasource.auth")
    public DataSource dataSource(
            @Value("${spring.datasource.auth.hikari.pool-name:HikariAuthCP}") String poolName,
            @Value("${spring.datasource.auth.hikari.maximum-pool-size:10}") Integer maximumPoolSize,
            @Value("${spring.datasource.auth.hikari.minimum-idle:2}") Integer minimumIdle,
            @Value("${spring.datasource.auth.hikari.idle-timeout:120000}") Integer idleTimeout,
            @Value("${spring.datasource.auth.hikari.connection-timeout:30000}") Integer connectionTimeout,
            @Value("${spring.datasource.auth.hikari.max-lifetime:1800000}") Integer maxLifeTime
    ) {
        /*return DataSourceBuilder.create()
                .build();*/
        HikariDataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        dataSource.setConnectionTimeout(connectionTimeout);
        dataSource.setMaximumPoolSize(maximumPoolSize);
        dataSource.setMinimumIdle(minimumIdle);
        dataSource.setIdleTimeout(idleTimeout);
        dataSource.setMaxLifetime(maxLifeTime);
        dataSource.setPoolName(poolName);

        return dataSource;
    }

    @Bean(name = "authTransactionManager")
    @Autowired
    JpaTransactionManager dataSourceTransactionManager(
            @Qualifier("authEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(
                entityManagerFactory.getObject());
        return transactionManager;
    }

    @Primary
    @Bean(name = "authEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("authDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.vitadairy.auth.entities")
                .persistenceUnit("auth")
                .build();
    }
}
