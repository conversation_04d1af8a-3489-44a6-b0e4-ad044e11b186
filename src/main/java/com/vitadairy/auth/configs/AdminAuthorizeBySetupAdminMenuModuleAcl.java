package com.vitadairy.auth.configs;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.zoo.enums.EnumCodeActionOnAdminMenuModule;
import com.vitadairy.zoo.enums.EnumCodeAdminMenuModule;
import com.vitadairy.zoo.enums.EnumCodeAdminRole;
import com.vitadairy.zoo.repositories.AdminAclRepository;
import com.vitadairy.zoo.repositories.AdminRoleRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component("adminAuthorizeAclGuard")
public class AdminAuthorizeBySetupAdminMenuModuleAcl {
    private final AdminRoleRepository adminRoleRepository;

    private final AdminAclRepository adminAclRepository;

    public AdminAuthorizeBySetupAdminMenuModuleAcl(
            @Qualifier("zooAdminRoleRepository") AdminRoleRepository adminRoleRepository,
            @Qualifier("zooAdminAclRepository") AdminAclRepository adminAclRepository) {
        this.adminRoleRepository = adminRoleRepository;
        this.adminAclRepository = adminAclRepository;
    }

    /**
     * Determines if the currently authenticated user has permission to access a specific controller and API action.
     *
     * @param authentication The current authentication object.
     * @param menuCode The code of the menu.
     * @param actionCodes The specific API action codes (e.g., full-access, read, etc.).
     *
     * @return true if the user is allowed to access; false otherwise.
     */
    public boolean canAccess(UsernamePasswordAuthenticationToken authentication, String menuCode, List<String> actionCodes) {
        if (authentication == null || !(authentication.getPrincipal() instanceof AuthorizationResponse res)) {
            return false;
        }

        Long accountId = res.getAccountId();
        if (accountId == null) {
            return false;
        }

        // Check if the user is a SUPER_ADMIN
        boolean isSuperAdmin = adminRoleRepository.countByAccountIdAndRoleCodeIn(
                accountId.intValue(),
                new String[]{EnumCodeAdminRole.SUPER_ADMIN.getValue()}
        ) > 0;

        if (isSuperAdmin) {
            return true;
        }

        // Check if user has full access via FULL_MENU
        boolean hasFullMenuAccess = adminAclRepository.countSetupByAccountIdAndAdminMenuModuleCodeAndActionCodeIn(
                accountId.intValue(),
                EnumCodeAdminMenuModule.FULL_MENU.getValue(),
                new String[]{EnumCodeActionOnAdminMenuModule.FULL_ACCESS.getValue()}
        ) > 0;

        if (hasFullMenuAccess) {
            return true;
        }

        // Check if user has explicit access to the requested menu and action
        boolean hasSpecificAccess = adminAclRepository.countSetupByAccountIdAndAdminMenuModuleCodeAndActionCodeIn(
                accountId.intValue(),
                menuCode,
                actionCodes.toArray(new String[0])
        ) > 0;

        return hasSpecificAccess;
    }
}
