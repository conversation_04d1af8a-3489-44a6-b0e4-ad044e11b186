package com.vitadairy.auth.configs;

import com.auth0.jwt.JWT;
import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.auth.services.AuthService;
import com.vitadairy.main.exception.ApplicationException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.io.IOException;
import java.util.Date;
import java.util.List;

@ConditionalOnWebApplication
@Component
@Slf4j
public class JwtAuthFilter extends OncePerRequestFilter {
    public static final String BEARER = "Bearer";
    public static final String AUTHORIZATION = "Authorization";
    private final AuthService authService;
    private final HandlerExceptionResolver resolver;
    private final String internalContextPath;

    public JwtAuthFilter(@Qualifier("authService") AuthService authService,
                         @Qualifier("handlerExceptionResolver") HandlerExceptionResolver resolver,
                         @Value("${internal.servlet.context-path}") String internalContextPath) {
        this.authService = authService;
        this.resolver = resolver;
        this.internalContextPath = internalContextPath;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        UsernamePasswordAuthenticationToken authentication = null;

        if (StringUtils.hasLength(this.internalContextPath)
                && request.getRequestURI().contains(this.internalContextPath + "/")) {
            authentication = new UsernamePasswordAuthenticationToken(null, null, List.of(new SimpleGrantedAuthority("INTERNAL")));
        } else {
            String authHeader = request.getHeader(AUTHORIZATION);
            if (authHeader != null && authHeader.startsWith(BEARER)) {
                var authToken = authHeader.substring(BEARER.length()).strip();
                try {
                    authentication = this.validateToken(authToken);
                } catch (Exception e) {
                    log.error("[AuthFilter] Got exception: {}", ExceptionUtils.getStackTrace(e));
                    this.resolver.resolveException(request, response, null, e);
                    return;
                }
            }
        }

        if (authentication != null) {
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

            // Get logged in user info via static call from anywhere:
            // AuthenticationHelper.getCurrentUser()
            SecurityContext context = SecurityContextHolder.createEmptyContext();
            context.setAuthentication(authentication);
            SecurityContextHolder.setContext(context);
        }


        filterChain.doFilter(request, response);
    }

    private UsernamePasswordAuthenticationToken validateToken(String authToken) {
        UsernamePasswordAuthenticationToken authentication = null;

        AuthorizationResponse res = null;
        var decodedJWT = JWT.decode(authToken);
        var userType = decodedJWT.getClaim("UserType").asString();

        if ("USER".equals(userType)) {
            res = this.authService.authorizeUser(authToken);
        } else if ("ADMIN".equals(userType)) {
            // check expiration
            if (decodedJWT.getExpiresAt().before(new Date())) {
                log.warn("Token is expired");
                throw new ApplicationException("Token is expired", HttpStatus.UNAUTHORIZED);
            }

            var email = decodedJWT.getSubject();
            res = this.authService.authorizeAdmin(email);
        }

        if (res != null) {
            authentication = new UsernamePasswordAuthenticationToken(res, null, List.of(new SimpleGrantedAuthority(userType)));
        }

        return authentication;
    }
}
