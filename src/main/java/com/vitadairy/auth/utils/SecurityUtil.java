package com.vitadairy.auth.utils;

import com.vitadairy.auth.dto.AuthorizationResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Utility class to access authenticated admin information from the Spring Security context.
 */
public class SecurityUtil {

    /**
     * Retrieves the currently authenticated admin's details from the security context.
     *
     * @return the AuthorizationResponse containing admin details, or null if not authenticated.
     */
    public static AuthorizationResponse getCurrentAdmin() {
        var auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth instanceof UsernamePasswordAuthenticationToken token) {
            return (AuthorizationResponse) token.getPrincipal();
        }

        return null;
    }

    /**
     * Retrieves the ID of the currently authenticated admin.
     *
     * @return the admin's account ID, or null if not authenticated.
     */
    public static Long getCurrentAdminId() {
        var admin = getCurrentAdmin();
        return admin != null ? admin.getAccountId() : null;
    }

    /**
     * Retrieves the email of the currently authenticated admin.
     *
     * @return the admin's email address, or null if not authenticated.
     */
    public static String getCurrentAdminEmail() {
        var admin = getCurrentAdmin();
        return admin != null ? admin.getEmail() : null;
    }
}
