package com.vitadairy.auth.services;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.auth.repositories.UserRepository;
import com.vitadairy.auth.repositories.UserSessionRepository;
import com.vitadairy.main.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service("authUserService")
@Slf4j
public class UserService {
    private final UserSessionRepository userSessionRepository;
    private final UserRepository userRepository;

    public UserService(@Qualifier("authUserSessionRepository") UserSessionRepository userSessionRepository,
                       @Qualifier("authUserRepository") UserRepository userRepository) {
        this.userSessionRepository = userSessionRepository;
        this.userRepository = userRepository;
    }

    public AuthorizationResponse checkSession(String token) {
        if (!StringUtils.hasLength(token)) {
            throw new ApplicationException("Empty token", HttpStatus.UNAUTHORIZED);
        }

        var sessions = this.userSessionRepository.getUserSessionByAccessToken(token);
        if (sessions == null || sessions.size() != 1) {
            throw new ApplicationException("Session not found", HttpStatus.UNAUTHORIZED);
        }

        var session = sessions.get(0);
        var user = this.userRepository.getUserById(session.getUserId());
        if (user == null) {
            throw new ApplicationException("User not found", HttpStatus.UNAUTHORIZED);
        }

//        try {
//            userRepository.updateUserLastLoginDate(user.getId(), LocalDateTime.now());
//        }
//        catch (Exception e) {
//            log.warn("Error while updating last login date", e);
//        }

        return new AuthorizationResponse()
            .setSessionId(session.getId())
            .setUserId(session.getUserId())
            .setName(String.format("%s %s", user.getFirstName(), user.getLastName()))
            .setPhoneNumber(user.getPhoneNumber())
            .setProvinceId(user.getProvinceId());
    }

}
