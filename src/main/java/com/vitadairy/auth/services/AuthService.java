package com.vitadairy.auth.services;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.auth.repositories.AccountRepository;
import com.vitadairy.main.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service("authService")
public class AuthService {
    private final UserService userService;
    private final AccountRepository accountRepository;

    public AuthService(@Qualifier("authUserService") UserService userService,
                       @Qualifier("authAccountRepository") AccountRepository accountRepository) {
        this.userService = userService;
        this.accountRepository = accountRepository;
    }

    public AuthorizationResponse authorizeUser(String token) {
        return this.userService.checkSession(token);
    }

    public AuthorizationResponse authorizeAdmin(String email) {
        var account = this.accountRepository.findByEmail(email);
        if (account == null || !"ACTIVE".equals(account.getStatus())) {
            throw new ApplicationException("Invalid account email", HttpStatus.UNAUTHORIZED);
        }

        return new AuthorizationResponse()
                .setAccountId(account.getId())
                .setEmail(account.getEmail());
    }
}
