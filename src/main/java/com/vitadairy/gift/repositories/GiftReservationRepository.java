package com.vitadairy.gift.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.vitadairy.gift.entities.GiftReservation;

@Repository
public interface GiftReservationRepository extends JpaRepository<GiftReservation, Integer> {
    GiftReservation findByGiftId(Integer giftId);

    @Modifying
    @Transactional("giftTransactionManager")
    @Query("UPDATE GiftReservation gr SET gr.maximumReservationQuantity = gr.maximumReservationQuantity - 1 WHERE gr.id = :gRid AND gr.maximumReservationQuantity > 0")
    void decrementMaximumReservationQuantity(Integer gRid);

    @Modifying
    @Transactional("giftTransactionManager")
    @Query("UPDATE GiftReservation gr SET gr.maximumReservationQuantity = gr.maximumReservationQuantity + 1 WHERE gr.id = :gRid AND gr.maximumReservationQuantity > 0")
    void incrementMaximumReservationQuantity(Integer gRid);
}
