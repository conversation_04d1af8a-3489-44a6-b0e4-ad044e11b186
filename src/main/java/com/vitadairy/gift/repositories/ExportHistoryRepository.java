package com.vitadairy.gift.repositories;

import java.time.Instant;
import java.util.Optional;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.vitadairy.gift.constants.ExportType;
import com.vitadairy.gift.entities.ExportHistory;

import jakarta.transaction.Transactional;

import org.springframework.data.domain.Page;

public interface ExportHistoryRepository extends JpaRepository<ExportHistory, Long> {
    Page<ExportHistory> findByExportTypeOrderByCreatedAtDesc(ExportType exportType, Pageable pageable);

    @Query(value = """
        SELECT * FROM gs_export_history WHERE status = 'PENDING' AND export_type = :exportType ORDER BY created_at LIMIT 1 FOR UPDATE SKIP LOCKED
    """, nativeQuery = true)
    Optional<ExportHistory> findOnePendingJobLocked(@Param("exportType") String exportType);

    @Modifying
    @Transactional
    @Query(value = """
        UPDATE gs_export_history SET status = 'FAILED' WHERE status = 'PENDING' AND started_at < :startedAt
    """, nativeQuery = true)
    int expireAllPendingJobsBeforeTime(@Param("startedAt") Instant startedAt);
}
