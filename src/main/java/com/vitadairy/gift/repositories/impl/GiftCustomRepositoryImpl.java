package com.vitadairy.gift.repositories.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.repositories.GiftCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Repository
public class GiftCustomRepositoryImpl implements GiftCustomRepository {

    @PersistenceContext(unitName = "gift")
    private final EntityManager entityManager;
    private final ObjectMapper objectMapper;

    @Override
    @Modifying
    @Transactional(rollbackFor = Exception.class,
            transactionManager = "giftTransactionManager",
            propagation = Propagation.MANDATORY)
    public List<Gift> manualSaveAll(List<Gift> gifts) {
        List<Gift> res = new ArrayList<>();
        int batchSize = 50; // Adjust batch size as needed
        for (int i = 0; i < gifts.size(); i++) {
            res.add(manualSave(gifts.get(i)));
            if (i % batchSize == 0 && i > 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
        entityManager.flush();
        entityManager.clear();
        return res.stream()
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class,
            transactionManager = "giftTransactionManager",
            propagation = Propagation.REQUIRES_NEW)
    public Gift manualSave(Gift gift) {
        try {
            Query query = entityManager.createNativeQuery("""
                     insert into gs_gifts
                        (id, images, transport_type_code, status, name, badge_codes, category_code, tier_codes, type, point, price,
                         hidden_tags, sf_number, start_date, end_date, expire_date, expire_hour, sct_number, quantity, inventory, quantity_limit_for_booking,
                        purchase_option, quantity_reward, quantity_reservation, is_allow_reservation, priority, dynamic_data)
                    values (:id, :images, :transportTypeCode, :status, :name, :badgeCodes, :categoryCode, :tierCodes, :type, :point, :price,
                             :hiddenTags, :sfNumber, :startDate, :endDate, :expireDate, :expireHour, :sctNumber, :quantity, :inventory, :quantityLimitForBooking,
                             :purchaseOption, :quantityReward, :quantityReservation, :isAllowReservation, :priority, :dynamicData)
                    """);
            query.setParameter("id", gift.getId());
            query.setParameter("images", gift.getImages());
            query.setParameter("transportTypeCode", gift.getTransportTypeCode());
            query.setParameter("status", gift.getStatus());
            query.setParameter("name", gift.getName());
            query.setParameter("badgeCodes", gift.getBadgeCodes());
            query.setParameter("categoryCode", gift.getCategoryCode());
            query.setParameter("tierCodes", gift.getTierCodes());
            query.setParameter("type", gift.getType());
            query.setParameter("point", gift.getPoint());
            query.setParameter("price", gift.getPrice());
            query.setParameter("hiddenTags", gift.getHiddenTags());
            query.setParameter("sfNumber", gift.getSfNumber());
            query.setParameter("startDate", gift.getStartDate());
            query.setParameter("endDate", gift.getEndDate());
            query.setParameter("expireDate", gift.getExpireDate());
            query.setParameter("expireHour", gift.getExpireHour());
            query.setParameter("sctNumber", gift.getSctNumber());
            query.setParameter("quantity", gift.getQuantity());
            query.setParameter("inventory", gift.getInventory());
            query.setParameter("quantityLimitForBooking", gift.getQuantityLimitForBooking());
            query.setParameter("purchaseOption", gift.getPurchaseOption());
            query.setParameter("quantityReward", gift.getQuantityReward());
            query.setParameter("quantityReservation", gift.getQuantityReservation());
            query.setParameter("isAllowReservation", gift.getIsAllowReservation());
            query.setParameter("priority", gift.getPriority());
            query.setParameter("dynamicData", objectMapper.writeValueAsString(gift.getDynamicData()));

            query.executeUpdate();
            return gift;
        } catch (Exception ex) {
            log.error("Error while saving gift ", ex);
            throw new RuntimeException(ex);
        }
    }
}