package com.vitadairy.gift.repositories;

import com.vitadairy.gift.entities.MapOldGiftToNewGift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.features.gifts.dto.ExchangeGiftDto;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MapOldGiftToNewGiftRepository extends JpaRepository<MapOldGiftToNewGift, Integer> {
    Optional<MapOldGiftToNewGift> findFirstByOldGiftId(Long oldGiftId);
    Optional<MapOldGiftToNewGift> findFirstByNewGiftId(Integer newGiftId);

    List<MapOldGiftToNewGift> findByNewGiftIdIn(List<Long> newGiftIds);
}
