package com.vitadairy.gift.repositories;

import com.vitadairy.gift.features.gifts.dto.ReadOnlyUserGiftDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UserGiftExportRepository {

        private final EntityManager em;

        public UserGiftExportRepository(@Qualifier("giftEntityManager") EntityManager em) {
                this.em = em;
        }

        @Transactional(value = "giftTransactionManager", readOnly = true)
        public List<ReadOnlyUserGiftDto> getAdminList(UserGiftListRequest request) {

                StringBuilder query = new StringBuilder();
                query.append("SELECT ug.id, ug.user_id, ug.gift_id, ug.status, ug.quantity, ug.dynamic_data, ug.transaction_code, g.name as gift_name, g.price as gift_price, g.dynamic_data as gift_dynamic_data ");
                query.append("FROM gs_user_gifts ug JOIN gs_gifts g ON ug.gift_id = g.id ");
                
                List<String> whereClauses = new ArrayList<>();
                List<Object> parameters = new ArrayList<>();
                if (request.getUserId() != null) {
                        whereClauses.add("ug.user_id = ?");
                        parameters.add(request.getUserId());
                }
                if (request.getGiftId() != null) {
                        whereClauses.add("ug.gift_id = ?");
                        parameters.add(request.getGiftId());
                }
                if (request.getExtendedSearch() != null) {
                        whereClauses.add("ug.extended_search ILIKE ?");
                        parameters.add(String.format("%%s%", request.getExtendedSearch()));
                }
                if (CollectionUtils.isNotEmpty(request.getListStatuses())) {
                        whereClauses.add("ug.status IN ?");
                        parameters.add(request.getListStatuses());
                }
                if (CollectionUtils.isNotEmpty(request.getListGiftTypes())) {
                        whereClauses.add("ug.gift_snapshot->>'type' IN ?");
                        parameters.add(request.getListGiftTypes());
                }
                if (CollectionUtils.isNotEmpty(request.getListPreOrderStatus())) {
                        whereClauses.add("ug.pre_order_status IN ?");
                        parameters.add(request.getListPreOrderStatus());
                }
                if (request.getFromDate() != null) {
                        whereClauses.add("ug.created_at >= ?");
                        parameters.add(request.getFromDate());
                }
                if (request.getToDate() != null) {
                        whereClauses.add("ug.created_at <= ?");
                        parameters.add(request.getToDate());
                }

                if (whereClauses.size() > 0) {
                        query.append("WHERE ");
                        query.append(String.join(" AND ", whereClauses));
                }
                query.append(" ORDER BY ug.created_at DESC");

                var pageable = request.getPageable();
                if (pageable != null && pageable.isPaged()) {
                        query.append(" LIMIT ? OFFSET ?");
                        parameters.add(pageable.getPageSize());
                        parameters.add(pageable.getOffset());
                }

                var sql = query.toString();
                Query q = em.createNativeQuery(sql, ReadOnlyUserGiftDto.class);
                for (int i = 0; i < parameters.size(); i++) {
                        q.setParameter(i + 1, parameters.get(i));
                }
                
                @SuppressWarnings("unchecked")
                List<ReadOnlyUserGiftDto> result = q.getResultList();
                return result;
        }
}
