package com.vitadairy.gift.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.vitadairy.gift.entities.GiftTimeExchangeGiftDetail;

@Repository
public interface GiftTimeExchangeGiftDetailRepository extends JpaRepository<GiftTimeExchangeGiftDetail, Long> {
    @Query(value = "SELECT ted.id FROM gs_gift_time_exchange_gift_details ted WHERE ted.gift_time_exchange_gift_id = :giftTimeExchangeGiftId", nativeQuery = true)
    Optional<List<Long>> findIdsByGiftTimeExchangeGiftId(@Param("giftTimeExchangeGiftId") Long giftTimeExchangeGiftId);

    @Query(value = "SELECT ted.id FROM gs_gift_time_exchange_gift_details ted WHERE ted.gift_time_exchange_gift_id = :giftTimeExchangeGiftId AND ted.period_type = :periodType", nativeQuery = true)
    Optional<List<Long>> findIdsByGiftTimeExchangeGiftIdAndPeriodType(@Param("giftTimeExchangeGiftId") Long giftTimeExchangeGiftId, @Param("periodType") String periodType);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM gs_gift_time_exchange_gift_details ted WHERE ted.gift_time_exchange_gift_id IN (:ids)", nativeQuery = true)
    int deleteAllByGiftTimeExchangeGiftIdIn(@Param("ids") List<Long> ids);

    @Query(value = "SELECT ted.id FROM gs_gift_time_exchange_gift_details ted WHERE ted.gift_time_exchange_gift_id = :giftTimeExchangeGiftId AND ted.period_type = :periodType AND ted.end_date > :startDate AND ted.end_date <= :endDate", nativeQuery = true)
    Optional<List<Long>> findIdsByGiftTimeExchangeGiftIdAndPeriodTypeAndEndDateBetween(@Param("giftTimeExchangeGiftId") Long giftTimeExchangeGiftId, @Param("periodType") String periodType, @Param("startDate") String startDate, @Param("endDate") String endDate);

    @Query(value= "SELECT * FROM gs_gift_time_exchange_gift_details ted WHERE ted.gift_id = :giftId AND ted.start_date <= :targetDate AND ted.end_date >= :targetDate", nativeQuery = true)
    Optional<List<GiftTimeExchangeGiftDetail>> findByGiftIdAndTargetDateBetweenStartDateAndEndDate(@Param("giftId") Integer giftId, @Param("targetDate") String targetDate);

    @Query(value = "SELECT * " +
            "FROM gs_gift_time_exchange_gift_details ted " +
            "WHERE ted.gift_id = :giftId " +
            "AND ted.start_date <= :targetDate " +
            "AND ted.end_date >= :targetDate " +
            "AND ted.start_date <= :secondaryDate " +
            "AND ted.end_date >= :secondaryDate",
            nativeQuery = true)
    Optional<List<GiftTimeExchangeGiftDetail>> findByGiftIdAndTargetDateAndSecondaryDateBetweenStartDateAndEndDate(
            @Param("giftId") Integer giftId,
            @Param("targetDate") String targetDate,
            @Param("secondaryDate") String secondaryDate
    );
}
