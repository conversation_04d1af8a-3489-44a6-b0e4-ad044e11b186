package com.vitadairy.gift.repositories;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.dto.GiftWithDynamicDataOnlyDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftDynamicFilterRequestDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface GiftRepository extends JpaRepository<Gift, Integer>, JpaSpecificationExecutor<Gift> {
    List<Gift> findByIdIn(Collection<Integer> ids);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantityReservation = coalesce(g.quantityReservation, 0) + 1 WHERE g.id = :giftId")
    void incrementQuantityReservation(Integer giftId);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantityReservation = coalesce(g.quantityReservation, 0) - 1 WHERE g.id = :giftId AND coalesce(g.quantityReservation, 0) - 1 >= 0")
    int decrementQuantityReservation(Integer giftId);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantityReward = coalesce(g.quantityReward, 0) + :quantity WHERE g.id = :giftId")
    void incrementQuantityReward(Integer giftId, Integer quantity);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    // TODO: don't need check quantityReward >= quantity
    // TODO: keep negative value to tracking issue
    @Query("UPDATE Gift g SET g.quantityReward = coalesce(g.quantityReward, 0) - :quantity WHERE g.id = :giftId AND coalesce(g.quantityReward, 0) - :quantity >=0")
    int decrementQuantityReward(Integer giftId, Integer quantity);

    @Query(value = "SELECT * FROM gs_gifts g WHERE 1=1 " +
            "AND (coalesce(:tierCodes, null) IS NULL OR :tierCodes = ANY(g.tier_codes)) " +
            "AND (coalesce(:hiddenTags, null) IS NULL OR (g.hidden_tags && CAST(:hiddenTags AS text[]))) " +
            "AND status = 'ENABLED' " +
            "AND (coalesce(:point, null) IS NULL OR g.point <= :point) " +
            "AND g.end_date > now() " +
            "AND g.start_date <= now() " +
            "ORDER BY point DESC, name ASC", nativeQuery = true)
    List<Gift> sectionFindByHiddenTags(@Param("tierCodes") String tierCodes,  @Param("hiddenTags") String[] hiddenTags, Double point);

    @Query(value = "SELECT * FROM gs_gifts g WHERE 1=1 " +
            "AND g.tier_codes && CAST(:tierCodes AS text[]) " +
            "AND g.id NOT IN (:ids)" +
            "AND status = 'ENABLED' " +
            "AND (:point IS NULL OR g.point < :point) " +
            "AND g.end_date > now() " +
            "AND g.start_date <= now() " +
            "ORDER BY point DESC, name ASC LIMIT 3", nativeQuery = true)
    List<Gift> sectionFindByTierCodes(@Param("tierCodes") String tierCodes, @Param("ids") Integer[] ids,
                                      Double point);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantity = coalesce(g.quantity, 0) - 1 WHERE g.id = :giftId AND g.quantity - 1 >= 0")
    int decrementQuantity(Integer giftId);

    @Modifying
//    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantity = coalesce(g.quantity, 0) - :quantity WHERE g.id = :giftId AND g.quantity - :quantity >= 0")
    int decrementQuantity(Integer giftId, Integer quantity);

    @Modifying
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET g.quantity = coalesce(g.quantity, 0) + :quantity WHERE g.id = :giftId")
    void incrementQuantity(Integer giftId, Integer quantity);

    @Modifying
    @Transactional(value="giftTransactionManager")
    @Query("UPDATE Gift g SET " + 
            "g.quantity = coalesce(g.quantity, 0) + :quantity, " +
            "g.quantityReward = CASE WHEN coalesce(g.quantityReward, 0) - :quantity  < 0 THEN 0 ELSE coalesce(g.quantityReward, 0) - :quantity END " +
            "WHERE g.id = :giftId")
    void incrementQuantityAndReduceReward(Integer giftId, Integer quantity);

    @Modifying
    @Transactional(value="giftTransactionManager", propagation = Propagation.REQUIRES_NEW)
    @Query("UPDATE Gift g SET g.quantity = coalesce(g.quantity, 0) + 1, g.quantityReservation = coalesce(g.quantityReservation, 0) - 1 WHERE g.id = :giftId AND g.quantity > 0 AND coalesce(g.quantityReservation, 0) - 1 >= 0")
    int increaseQuantityAndDecreaseQuantityReservation(Integer giftId);

    @Transactional("giftTransactionManager")
    @Modifying
    @Query(value = "UPDATE gs_gifts SET status = :status WHERE id = :id", nativeQuery = true)
    int updateGiftStatusById(@Param("id") Integer id, @Param("status") String status);

    @Transactional("giftTransactionManager")
    @Modifying
    @Query(value = "UPDATE gs_gifts SET quantity = :qty, inventory = :inventory, quantity_limit_for_booking = :qtyLimit WHERE id = :id", nativeQuery = true)
    int updateGiftStockById(@Param("id") Integer id, @Param("qty") Long qty, @Param("inventory") Integer inventory, @Param("qtyLimit") Integer qtyLimit);

    List<Gift> findAllByPointGreaterThanEqualAndStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqualOrderByPointDesc(Integer point, GiftStatusEnum status, Timestamp startDate, Timestamp endDate, Pageable pageable);

    @Query(value = "SELECT * FROM gs_gifts WHERE (" +
            "    (:#{#dto.code == null} AND :#{#dto.valueCode == null} AND :#{#dto.firstLevelCode == null} AND :#{#dto.firstLevelValueCode == null} AND :#{#dto.secondLevelCode == null} AND :#{#dto.secondLevelValueCode == null})" +
            "    OR (dynamic_data -> 'dynamicFilter' != 'null' " +
            "    AND (:#{#dto.code == null} OR dynamic_data -> 'dynamicFilter' ->> 'code' = :#{#dto.code}) " +
            "    AND (:#{#dto.valueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'valueCode' = :#{#dto.valueCode})" +
            "    AND (:#{#dto.firstLevelCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'firstLevelCode' = :#{#dto.firstLevelCode})" +
            "    AND (:#{#dto.firstLevelValueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'firstLevelValueCode' = :#{#dto.firstLevelValueCode})" +
            "    AND (:#{#dto.secondLevelCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'secondLevelCode' = :#{#dto.secondLevelCode})" +
            "    AND (:#{#dto.secondLevelValueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'secondLevelValueCode' = :#{#dto.secondLevelValueCode}))" +
            ")" +
            "AND (:#{#dto.keyword == null} OR remove_accent(LOWER(name)) like remove_accent(LOWER('%' || :#{#dto.keyword} || '%')))" +
            "AND (:#{#dto.tierCode} = ANY(tier_codes))" +
            "AND (NOW() between start_date and end_date)" +
            "AND ((:#{#dto.minPoint == null} AND :#{#dto.maxPoint == null}) OR (point between :#{#dto.minPoint} AND :#{#dto.maxPoint})) " +
            "AND status = 'ENABLED'",
            countQuery = "SELECT COUNT(*) FROM gs_gifts WHERE (" +
                    "    (:#{#dto.code == null} AND :#{#dto.valueCode == null} AND :#{#dto.firstLevelCode == null} AND :#{#dto.firstLevelValueCode == null} AND :#{#dto.secondLevelCode == null} AND :#{#dto.secondLevelValueCode == null})" +
                    "    OR (dynamic_data -> 'dynamicFilter' != 'null' " +
                    "    AND (:#{#dto.code == null} OR dynamic_data -> 'dynamicFilter' ->> 'code' = :#{#dto.code}) " +
                    "    AND (:#{#dto.valueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'valueCode' = :#{#dto.valueCode})" +
                    "    AND (:#{#dto.firstLevelCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'firstLevelCode' = :#{#dto.firstLevelCode})" +
                    "    AND (:#{#dto.firstLevelValueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'firstLevelValueCode' = :#{#dto.firstLevelValueCode})" +
                    "    AND (:#{#dto.secondLevelCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'secondLevelCode' = :#{#dto.secondLevelCode})" +
                    "    AND (:#{#dto.secondLevelValueCode == null} OR dynamic_data -> 'dynamicFilter' ->> 'secondLevelValueCode' = :#{#dto.secondLevelValueCode}))" +
                    ")" +
                    "AND (:#{#dto.keyword == null} OR remove_accent(LOWER(name)) like remove_accent(LOWER('%' || :#{#dto.keyword} || '%')))" +
                    "AND (:#{#dto.tierCode} = ANY(tier_codes))" +
                    "AND (NOW() between start_date and end_date)" +
                    "AND ((:#{#dto.minPoint == null} AND :#{#dto.maxPoint == null}) OR (point between :#{#dto.minPoint} AND :#{#dto.maxPoint})) "+
                    "AND status = 'ENABLED'", nativeQuery = true)
    Page<Gift> filterByDynamicFilter(GiftDynamicFilterRequestDto dto , Pageable pageable);

    @Query(value = "SELECT id, dynamic_data FROM gs_gifts WHERE id IN (:ids)", nativeQuery = true)
    List<GiftWithDynamicDataOnlyDto> findDynamicDataByIdIn(@Param("ids") Collection<Integer> ids);
    
    @EntityGraph(attributePaths = "giftTimeExchangeGifts")
    Optional<Gift> findWithTimeExchangeGiftsById(Integer id);
}
