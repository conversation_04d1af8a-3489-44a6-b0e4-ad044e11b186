package com.vitadairy.gift.repositories;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.vitadairy.gift.entities.User;

@Repository("giftUserRepository")
public interface UserRepository extends JpaRepository<User, Integer> {
        @Modifying
        @Transactional("giftTransactionManager")
        @Query(value = "UPDATE gs_users SET fav_gift_ids = ARRAY_PREPEND(:giftId,ARRAY_REMOVE(fav_gift_ids,:giftId)) WHERE id = :userId", nativeQuery = true)
        void addGift(@Param("giftId") Integer giftId, @Param("userId") Long userId);

        @Modifying
        @Transactional("giftTransactionManager")
        @Query(value = "UPDATE gs_users SET fav_gift_ids = ARRAY_REMOVE(fav_gift_ids,:giftId) WHERE id = :userId", nativeQuery = true)
        void removeGift(@Param("giftId") Integer giftId, @Param("userId") Long userId);

        @Query(value = "SELECT array_length(fav_gift_ids, 1) FROM gs_users WHERE id = :userId", nativeQuery = true)
        Integer totalGiftByUserId(@Param("userId") Long userId);

        @Modifying
        @Transactional("giftTransactionManager")
        @Query("UPDATE User u SET u.coin = :coin WHERE u.id = :id")
        void updateCointById(Long id, Float coin);

        Optional<User> findById(Long userId);
}
