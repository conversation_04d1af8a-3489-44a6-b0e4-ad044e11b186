package com.vitadairy.gift.repositories;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.features.gifts.dto.ExchangeGiftDto;
import com.vitadairy.gift.features.gifts.dto.ReadOnlyUserGiftDto;
import com.vitadairy.gift.features.gifts.dto.UserGiftUserActiveSmsDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;

import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserGiftRepository extends JpaRepository<UserGift, Integer> {

        @Query("SELECT COUNT(ug) FROM UserGift ug WHERE ug.status = :status AND ug.userId = :userId AND ug.gift.id = :giftId")
        long countByStatusAndUserId(USER_GIFT_STATUS status, Long userId, Integer giftId);

        // @EntityGraph(attributePaths = { "gift" })
        UserGift findByIdAndUserIdAndStatusNot(Long id, Long userId, USER_GIFT_STATUS status);

        @Query(value = "SELECT ug FROM UserGift ug JOIN FETCH ug.gift g WHERE ug.userId = :userId ORDER BY ug.createdAt DESC",
                        countQuery = "SELECT COUNT(ug) FROM UserGift ug WHERE ug.userId = :userId")
        Page<UserGift> findByUserId(Integer userId, Pageable pageable);

        List<UserGift> findByIdIn(List<Long> ids);

        UserGift findFirstByUserIdAndGiftIdAndStatus(Long userId, Integer giftId,
                        UserGift.USER_GIFT_STATUS status);

        long countByUserIdAndGiftId(Long userId, Integer giftId);

        @Query(value = "SELECT ug FROM UserGift ug " + "WHERE ug.userId = :userId "
                        + "AND (COALESCE(:statuses) IS NULL OR ug.status IN :statuses AND (:preOrderStatus IS NULL OR ug.preOrderStatus IN :preOrderStatus)) "
                        + "AND (ug.status != 'LOCK') "
                        // + "AND (:preOrderStatus IS NULL OR ug.status IN :preOrderStatus)) "
                        + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'type') AS string) IN :giftTypes)) "
                        + "AND (COALESCE(:eventCode) IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'eventId') AS long) = :eventCode)) "
                        + "ORDER BY ug.createdAt DESC",
                        countQuery = "SELECT COUNT(ug) FROM UserGift ug WHERE ug.userId = :userId "
                                        + "AND (COALESCE(:statuses) IS NULL OR ug.status IN :statuses OR (:preOrderStatus IS NULL OR ug.preOrderStatus IN :preOrderStatus)) "
                                        + "AND (COALESCE(:eventCode) IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'eventCode') AS string) = :eventCode)) "
                                        + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'type') AS string) IN :giftTypes))")
        Page<UserGift> findByUserIdAndStatusIn(@Param("userId") Long userId,
                        @Param("statuses") List<UserGift.USER_GIFT_STATUS> statuses,
                        @Param("preOrderStatus") List<UserGift.USER_GIFT_STATUS> preOrderStatus,
                        @Param("giftTypes") String[] giftTypes, @Param("eventCode") Long eventCode, Pageable pageable);

        @Query(value = "SELECT ROW_NUMBER() OVER (ORDER BY DATE_PART('year',ug.created_at) DESC,COUNT(gift_id) DESC,g.name) AS ROW_NUMBER, "
                        + "g.images, g.name, g.price, g.point, ug.gift_id, "
                        + "COUNT(gift_id) as total_exchange " + "FROM gs_user_gifts AS ug   "
                        + "JOIN gs_gifts AS g ON ug.gift_id = g.id "
                        + "WHERE ug.status IN ('EXCHANGED','IN_PROCESS','PENDING','CANCELLED','PRE_ORDER') "
                        + "AND g.status = 'ENABLED' AND g.point IS NOT NULL AND g.point > 0 "
                        + "AND DATE_PART('year',ug.created_at) = DATE_PART('year',CURRENT_DATE) "
                        + "GROUP BY gift_id, DATE_PART('year',ug.created_at), g.name, g.price, g.point, g.images "
                        + "ORDER BY COUNT(gift_id) DESC, g.name", nativeQuery = true)
        List<ExchangeGiftDto> getTopExchangeGiftByCurrentYear(Pageable pageable);

        @Query(value = "SELECT ROW_NUMBER() OVER (ORDER BY DATE_PART('year',ug.created_at) DESC,DATE_PART('month',ug.created_at) DESC,COUNT(gift_id) DESC,g.name) AS ROW_NUMBER, "
                        + "g.images, g.name, g.price, g.point, ug.gift_id,  COUNT(gift_id) as total_exchange "
                        + "FROM gs_user_gifts AS ug   JOIN gs_gifts AS g ON ug.gift_id = g.id "
                        + "WHERE ug.status IN ('EXCHANGED','IN_PROCESS','PENDING','CANCELLED','PRE_ORDER') "
                        + "AND g.status = 'ENABLED' AND g.point IS NOT NULL AND g.point > 0 "
                        + "AND DATE_PART('year',ug.created_at) = DATE_PART('year',CURRENT_DATE) "
                        + "AND DATE_PART('month',ug.created_at) = DATE_PART('month',CURRENT_DATE) "
                        + "GROUP BY gift_id, DATE_PART('year',ug.created_at), DATE_PART('month',ug.created_at), g.name, g.price, g.point, g.images "
                        + "ORDER BY COUNT(gift_id) DESC, g.name", nativeQuery = true)
        List<ExchangeGiftDto> getTopExchangeGiftByCurrentMonth(Pageable pageable);

        @Query(value = "SELECT ROW_NUMBER() OVER (ORDER BY DATE_PART('year',ug.created_at) DESC,DATE_PART('month',ug.created_at) DESC,DATE_PART('week',ug.created_at) DESC,COUNT(gift_id) DESC,g.name) as row_number, "
                        + "g.images, g.name, g.price, g.point, ug.gift_id, COUNT(gift_id) as total_exchange "
                        + "FROM gs_user_gifts AS ug   JOIN gs_gifts AS g ON ug.gift_id = g.id "
                        + "WHERE ug.status IN ('EXCHANGED','IN_PROCESS','PENDING','CANCELLED','PRE_ORDER') AND DATE_PART('year',ug.created_at) = DATE_PART('year',CURRENT_DATE) "
                        + "AND g.status = 'ENABLED' AND g.point IS NOT NULL AND g.point > 0 "
                        + "AND DATE_PART('month',ug.created_at) = DATE_PART('month',CURRENT_DATE) "
                        + "AND DATE_PART('week',ug.created_at) = DATE_PART('week',CURRENT_DATE) "
                        + "GROUP BY gift_id, DATE_PART('year',ug.created_at), DATE_PART('month',ug.created_at), DATE_PART('week',ug.created_at), g.name, g.price, g.point, g.images "
                        + "ORDER BY COUNT(gift_id) DESC, g.name  ", nativeQuery = true)
        List<ExchangeGiftDto> getTopExchangeGiftByCurrentWeek(Pageable pageable);

        // @Query(value = "UPDATE gs_user_gifts SET status = 'EXPIRED' " +
        // "WHERE (dynamic_data->>'expiry_date')::timestamp < now() " +
        // "AND status = 'EXCHANGED'", nativeQuery = true)
        // void updateExpiredUserGifts();

        @Query("SELECT COUNT(ug) FROM UserGift ug " + "WHERE ug.holdingDate <= CURRENT_TIMESTAMP "
                        + "AND ug.status = 'PRE_ORDER'")
        long countByOutOfHoldingDate();

        @Query(value = "SELECT * FROM gs_user_gifts ug " + "WHERE ug.holding_date <= now() "
                        + "AND ug.status = 'PRE_ORDER' " + "LIMIT :limit OFFSET :skip",
                        nativeQuery = true)
        List<UserGift> findAllByOutOfHoldingDate(@Param("limit") Integer limit,
                        @Param("skip") Integer skip);

        @Modifying
        @Transactional("giftTransactionManager")
        @Query(value = "UPDATE gs_user_gifts SET status = 'EXPIRED' "
                        + "WHERE (dynamic_data->>'expiry_date')::timestamp < now() "
                        + "AND status IN ('EXCHANGED', 'PENDING')", nativeQuery = true)
        void updateExpiredUserGifts();

        @Modifying
        @Transactional("giftTransactionManager")
        @Query(value = "UPDATE gs_user_gifts SET status = 'EXPIRED' "
                + "WHERE id IN (:ids)", nativeQuery = true)
        void updateStatusToExpired(@Param("ids") Set<Long> ids);

        @Query(value = "SELECT * FROM gs_user_gifts "
                + "WHERE (dynamic_data->>'expiry_date')::timestamp < now() "
                + "AND status IN ('EXCHANGED', 'PENDING')", nativeQuery = true)
        List<UserGift> getExpiredUserGifts();

        @Query(value = 
                "SELECT ug.id as user_gift_id, ug.user_id, ug.quantity, ug.gift_id, g.name as gift_name " +
                "FROM gs_user_gifts ug JOIN gs_gifts g ON ug.gift_id = g.id " +
                "WHERE (ug.dynamic_data->>'expiry_date')::timestamp < now() " +
                "AND ug.status = 'PENDING'", nativeQuery = true)
        List<UserGiftUserActiveSmsDto> getExpiredUserGiftsWithPendingStatus();

        @Query(value = "SELECT ug FROM UserGift ug "
                        + "WHERE (:statuses IS NULL OR ug.status IN :statuses) "
                        + "AND (:preOrderStatus IS NULL OR ug.status IN :preOrderStatus) "
                        + "AND (:giftTypes IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'type') AS string) IN :giftTypes)) "
                        + "ORDER BY ug.createdAt DESC",
                        countQuery = "SELECT COUNT(ug) FROM UserGift ug WHERE (:statuses IS NULL OR ug.status IN :statuses) "
                                        + "AND (:preOrderStatus IS NULL OR ug.status IN :preOrderStatus) "
                                        + "AND (:giftTypes IS NULL OR (CAST(function('jsonb_extract_path_text', ug.giftSnapshot, 'type') AS string) IN :giftTypes))")
        Page<UserGift> findByStatusAndGiftType(
                        @Param("statuses") List<UserGift.USER_GIFT_STATUS> statuses,
                        @Param("preOrderStatus") List<UserGift.USER_GIFT_STATUS> preOrderStatus,
                        @Param("giftTypes") String[] giftTypes, Pageable pageable);

        @Query(value = "SELECT * FROM gs_user_gifts ug "
                        + "WHERE (dynamic_data->>'topup_transaction_status_time_check')::timestamp <= now() "
                        + "AND ug.status = 'TOPUP_IS_PROCESSING' "
                        + "ORDER BY ug.id LIMIT :limit OFFSET :skip", nativeQuery = true)
        List<UserGift> getListGotitNeedCheckTransaction(@Param("limit") Integer limit,
                        @Param("skip") Integer skip);

        Optional<UserGift> findFirstByTransactionCode(String transactionCode);

        @Query(value = "SELECT * FROM gs_user_gifts ug "
                        + "WHERE dynamic_data->>'voucher_ref_id' = :voucherRefId "
                        + "AND ug.id <> :id " + "LIMIT :limit OFFSET :skip", nativeQuery = true)
        List<UserGift> getListByVoucherRefIdAndIdNot(@Param("voucherRefId") String voucherRefId,
                        @Param("id") Long id, @Param("limit") Integer limit,
                        @Param("skip") Integer skip);

        @Query(value = "SELECT * FROM gs_user_gifts ug WHERE ug.holding_date::date = CURRENT_DATE + INTERVAL '1 day' "
                        + "AND ug.status = 'PRE_ORDER'", nativeQuery = true)
        List<UserGift> findUserGiftsByHoldingDateMinusOne();

        @Query(value = "SELECT * FROM gs_user_gifts ug " + "WHERE ug.user_id = :userId "
                        + "AND ug.status = 'PRE_ORDER' " + "AND ug.point <= :userTotalPoint "
                        + "AND (dynamic_data->'pushed_notify_enought_point_to_finish_pre_order' IS NULL OR dynamic_data->'pushed_notify_enought_point_to_finish_pre_order' <> 'true') "
                        + "ORDER BY ug.id", nativeQuery = true)
        List<UserGift> getFirstPreOrderUserGiftNotPushNotification(@Param("userId") Long userId,
                        @Param("userTotalPoint") Float userTotalPoint);

        @Query(value = "SELECT ug.* FROM gs_user_gifts ug "
                        + "WHERE (:userId IS NULL OR ug.user_id = :userId) "
                        + "AND (:giftId IS NULL OR ug.gift_id = :giftId) "
                        + "AND (COALESCE(:extendedSearch) IS NULL OR ug.extended_search ILIKE '%' || CAST(:extendedSearch AS TEXT) || '%') "
                        + "AND (COALESCE(:statues) IS NULL OR (ug.status IN (:statues))) "
                        + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(jsonb_extract_path_text(gift_snapshot, 'type') AS varchar) IN (:giftTypes))) "
                        + "AND (COALESCE(:preOrderStatus) IS NULL OR (ug.pre_order_status IN (:preOrderStatus))) "
                        + "AND ((:fromDate)::date IS NULL OR ug.created_at >= :fromDate) "
                        + "AND ((:toDate)::date IS NULL OR ug.created_at <= :toDate) "
                        + "ORDER BY ug.id ",
                        countQuery = "SELECT COUNT(*) FROM gs_user_gifts ug "
                                        + "WHERE (:userId IS NULL OR ug.user_id = :userId) "
                                        + "AND (:giftId IS NULL OR ug.gift_id = :giftId) "
                                        + "AND (COALESCE(:extendedSearch) IS NULL OR ug.extended_search ILIKE '%' || CAST(:extendedSearch AS TEXT) || '%') "
                                        + "AND (COALESCE(:statues) IS NULL OR (ug.status IN (:statues))) "
                                        + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(jsonb_extract_path_text(gift_snapshot, 'type') AS varchar) IN (:giftTypes))) "
                                        + "AND (COALESCE(:preOrderStatus) IS NULL OR (ug.pre_order_status IN (:preOrderStatus))) "
                                        + "AND ((:fromDate)::date IS NULL OR ug.created_at >= :fromDate) "
                                        + "AND ((:toDate)::date IS NULL OR ug.created_at <= :toDate) ",
                        nativeQuery = true)
        Page<UserGift> getAdminList(Long userId, Long giftId, String extendedSearch,
                        List<String> statues, List<String> giftTypes, List<String> preOrderStatus,
                        Instant fromDate, Instant toDate, Pageable pageable);

        @Query(value = "SELECT ug.* FROM gs_user_gifts ug "
                + "WHERE (ug.created_at >= '2024-10-31 17:00:00') "
                + "AND (:userId IS NULL OR ug.user_id = :userId) "
                + "AND (:giftId IS NULL OR ug.gift_id = :giftId) "
                + "AND (COALESCE(:extendedSearch) IS NULL OR ug.extended_search ILIKE '%' || CAST(:extendedSearch AS TEXT) || '%') "
                + "AND (COALESCE(:statues) IS NULL OR (ug.status IN (:statues))) "
                + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(jsonb_extract_path_text(gift_snapshot, 'type') AS varchar) IN (:giftTypes))) "
                + "AND (COALESCE(:preOrderStatus) IS NULL OR (ug.pre_order_status IN (:preOrderStatus))) "
                + "AND ((:fromDate)::date IS NULL OR ug.created_at >= :fromDate) "
                + "AND ((:toDate)::date IS NULL OR ug.created_at <= :toDate) "
                + "ORDER BY ug.id ",
                countQuery = "SELECT COUNT(*) FROM gs_user_gifts ug "
                        + "WHERE (ug.created_at >= '2024-10-31 17:00:00') "
                        + "AND (:userId IS NULL OR ug.user_id = :userId) "
                        + "AND (:giftId IS NULL OR ug.gift_id = :giftId) "
                        + "AND (COALESCE(:extendedSearch) IS NULL OR ug.extended_search ILIKE '%' || CAST(:extendedSearch AS TEXT) || '%') "
                        + "AND (COALESCE(:statues) IS NULL OR (ug.status IN (:statues))) "
                        + "AND (COALESCE(:giftTypes) IS NULL OR (CAST(jsonb_extract_path_text(gift_snapshot, 'type') AS varchar) IN (:giftTypes))) "
                        + "AND (COALESCE(:preOrderStatus) IS NULL OR (ug.pre_order_status IN (:preOrderStatus))) "
                        + "AND ((:fromDate)::date IS NULL OR ug.created_at >= :fromDate) "
                        + "AND ((:toDate)::date IS NULL OR ug.created_at <= :toDate) ",
                nativeQuery = true)
        Page<UserGift> getAdminListOnlyForPreOrderUserGift(Long userId, Long giftId, String extendedSearch,
                                    List<String> statues, List<String> giftTypes, List<String> preOrderStatus,
                                    Instant fromDate, Instant toDate, Pageable pageable);

        @Modifying
        @Transactional(value = "giftTransactionManager", propagation = Propagation.REQUIRED)
        @Query(value = "UPDATE gs_user_gifts SET transaction_code = :transactionCode WHERE id = :id", nativeQuery = true)
        void updateUserGiftTransactionCode(@Param("id") Long id, @Param("transactionCode") String transactionCode);

        @Modifying
        @Transactional("giftTransactionManager")
        @Query(value = "UPDATE gs_user_gifts SET last_notified_stage = :notifiedStage WHERE id IN (:ids)", nativeQuery = true)
        void updateNotifiedStageByIds(Collection<Long> ids, int notifiedStage);

        @Query(value = "SELECT ug.id as user_gift_id, ug.user_id, ug.quantity, ug.gift_id, g.name as gift_name " + 
                "FROM gs_user_gifts ug JOIN gs_gifts g ON ug.gift_id = g.id " + 
                "WHERE ug.status = 'PENDING' " +
                "AND (EXTRACT(EPOCH FROM now()) - EXTRACT(EPOCH FROM (ug.created_at))) / 86400 BETWEEN :from AND :to " +
                "AND (ug.last_notified_stage IS NULL OR ug.last_notified_stage < :notifiedStage)", nativeQuery = true)
        List<UserGiftUserActiveSmsDto> getUsersOfPendingUserGifts(int from, int to, int notifiedStage);

        @Query(value = "SELECT COUNT(ug.id) FROM gs_user_gifts ug " + 
                "WHERE ug.user_id = :userId AND ug.status = 'PENDING' " +
                "AND (EXTRACT(EPOCH FROM now()) - EXTRACT(EPOCH FROM (ug.created_at))) / 86400 BETWEEN :from AND :to", nativeQuery = true)
        Long countPendingUserGiftsByUserIdAndCreatedAtBefore(Long userId, int from, int to);
}
