package com.vitadairy.gift.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.vitadairy.gift.entities.GiftTimeExchangeGift;

@Repository
public interface GiftTimeExchangeGiftRepository extends JpaRepository<GiftTimeExchangeGift, Long> {
    Optional<List<GiftTimeExchangeGift>> findByGiftId(Integer giftId);
}
