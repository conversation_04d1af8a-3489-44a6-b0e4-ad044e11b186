package com.vitadairy.gift.repositories;

import com.vitadairy.gift.entities.Constant;

import io.lettuce.core.dynamic.annotation.Param;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Repository
public interface ConstantRepository extends JpaRepository<Constant, Integer> {
    List<Constant> findByKeyAlpha(String keyAlpha);

    List<Constant> findByKeyBeta(String keyBeta);

    Optional<Constant> findTopByKeyAlpha(String keyAlpha);

    List<Constant> findByKeyAlphaAndKeyBetaOrderByPriorityAsc(String keyAlpha, String keyBeta);

    @Cacheable(value = "constants", sync = true)
    List<Constant> findAllByKeyAlphaAndKeyBetaAndValueIn(String keyAlpha, String keyBeta, List<String> values);

    List<Constant> findByKeyAlphaAndKeyBetaOrderByIdDesc(String keyAlpha, String keyBeta);

    boolean existsByKeyAlphaAndKeyBetaAndValue(String keyAlpha, String keyBeta, String value);

    Constant findFirstByKeyAlphaAndKeyBeta(String keyAlpha, String keyBeta);

    List<Constant> findByKeyBetaAndValueIn(String keyBeta, List<String> values);

    @Transactional("giftTransactionManager")
    default Constant updateOrInsert(Constant entity) {
        Constant constant = findFirstByKeyAlphaAndKeyBetaAndValue(entity.getKeyAlpha(), entity.getKeyBeta(),
                entity.getValue());
        if (!Objects.isNull(constant)) {
            entity.setId(constant.getId());
        }
        return save(entity);
    }

    Constant findFirstByKeyAlphaAndKeyBetaAndValue(String keyAlpha, String keyBeta, String value);

    @Query("SELECT c FROM Constant c WHERE c.keyAlpha = :keyAlpha AND c.keyBeta = :keyBeta " +
            "AND (:isActive IS NULL OR c.isActive = :isActive)")
    List<Constant> findByKeyAlphaAndKeyBetaAndIsActive(
            @Param("keyAlpha") String keyAlpha,
            @Param("keyBeta") String keyBeta,
            @Param("isActive") Boolean isActive,
            Sort sort);

    @Query(value = """
        SELECT c.*
        FROM gs_constants c
        WHERE c.key_alpha = :keyAlpha
          AND c.key_beta  = :keyBeta
          AND c.is_active = :isActive
          AND ((c.dynamic_data->>'belong_cls')::boolean = :belongCLS )
        ORDER BY c.priority ASC
        """,
        nativeQuery = true)
    List<Constant> findByKeyAlphaAndKeyBetaAndIsActiveAndBelongCLS(
        @Param("keyAlpha") String keyAlpha,
        @Param("keyBeta")  String keyBeta,
        @Param("isActive") Boolean isActive,
        @Param("belongCLS") Boolean belongCLS);


    @Query(value = "select * from gs_constants where key_alpha = :keyAlpha and key_beta = :keyBeta " +
            " and jsonb_extract_path_text(dynamic_data, 'name') = :name " +
            " and is_active = true", nativeQuery = true)
    Constant findFirstActiveByKeyAlphaAndKeyBetaAndName(
            @Param("keyAlpha") String keyAlpha,
            @Param("keyBeta") String keyBeta,
            @Param("name") String name);

    @Query(value = "select * from gs_constants where key_alpha = 'GIFT' and key_beta = 'DYNAMIC_FILTER' " +
            " and jsonb_extract_path_text(dynamic_data, 'dynamic_filter', 'name') = :name " +
            " and is_active = true", nativeQuery = true)
    Constant findGiftFilterByName(@Param("name") String name);

    @Query(value = "SELECT c.* FROM gs_constants AS c WHERE c.key_alpha = 'GIFT'   AND c.key_beta = 'CATEGORY'   AND c.is_active = true   AND EXISTS (     SELECT 1     FROM gs_gifts AS g     WHERE c.value = g.category_code   AND g.status = 'ENABLED' AND NOW() between g.start_date and g.end_date AND :tierCode = ANY(g.tier_codes)) ORDER BY c.priority DESC;", nativeQuery = true)
    List<Constant> getActiveCategories(String tierCode);

    @Query("SELECT c FROM Constant c WHERE c.keyAlpha = :keyAlpha AND c.keyBeta = :keyBeta " +
            "AND (:isActive IS NULL OR c.isActive = :isActive)" + 
                "AND c.value NOT IN :value")
    List<Constant> findByKeyAlphaAndKeyBetaAndIsActiveAndNotValue(
            @Param("keyAlpha") String keyAlpha,
            @Param("keyBeta") String keyBeta,
            @Param("isActive") Boolean isActive,
            @Param("value") List<String> value,
            Sort sort);
}
