package com.vitadairy.gift.repositories;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.MapGiftToBrandPoint;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.dto.request.GiftDynamicFilterRequestDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface MapGiftToBrandPointRepository extends JpaRepository<MapGiftToBrandPoint, Integer>, JpaSpecificationExecutor<MapGiftToBrandPoint> {
    Optional<MapGiftToBrandPoint> findFirstByGiftId(Integer giftId);
}
