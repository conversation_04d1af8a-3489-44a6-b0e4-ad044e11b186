package com.vitadairy.gift.features.config.services;

import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.zoo.dto.NumberPointUsedInDayResponse;
import com.vitadairy.zoo.entities.UserNumberScan;
import com.vitadairy.zoo.repositories.SystemConfigRepository;
import com.vitadairy.zoo.repositories.UserNumberScanRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service("zooSystemConfigService")
@Slf4j
public class SystemConfigService {
    private final SystemConfigRepository systemConfigRepository;
    private final GiftRepository giftRepository;
    private final UserNumberScanRepository userNumberScanRepository;

    public SystemConfigService(@Qualifier("zooSystemConfigRepository") SystemConfigRepository systemConfigRepository,
                               GiftRepository giftRepository,
                               @Qualifier("zooUserNumberScanRepository") UserNumberScanRepository userNumberScanRepository) {
        this.systemConfigRepository = systemConfigRepository;
        this.giftRepository = giftRepository;
        this.userNumberScanRepository = userNumberScanRepository;
    }

    public NumberPointUsedInDayResponse getNumberPointUsed(Integer giftId, Integer total) {
        var config = systemConfigRepository.findById("TOTAL_POINT_ALLOW_USE_IN_DAY")
                .orElseThrow(() -> new ApplicationException("Config TOTAL_POINT_ALLOW_USE_IN_DAY not found"));
        var gift = giftRepository.findById(giftId).orElseThrow(() -> new ApplicationException("Gift not found"));
        Long userId = AuthenticationHelper.getCurrentUserId();

        if (userId == null) {
            throw new ApplicationException("User ID not found");
        }

        UserNumberScan userNum = userNumberScanRepository.findById(userId).orElse(null);
        if (userNum == null) {
            return new NumberPointUsedInDayResponse(gift.getPoint() * total, Float.parseFloat(config.getValue()));
        } else {
            return new NumberPointUsedInDayResponse(userNum.getNumberPointUsedInDay() + (gift.getPoint() * total), Float.parseFloat(config.getValue()));
        }
    }
}
