package com.vitadairy.gift.features.config.controllers;


import com.vitadairy.gift.features.config.services.SystemConfigService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.zoo.dto.NumberPointUsedInDayResponse;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("zooConfigController")
@RequestMapping("v4/gs/config")
public class ConfigController {
    private final SystemConfigService systemConfigService;
    private final ResponseFactory responseFactory;

    public ConfigController(@Qualifier("zooSystemConfigService") SystemConfigService systemConfigService,
                            ResponseFactory responseFactory) {
        this.systemConfigService = systemConfigService;
        this.responseFactory = responseFactory;
    }

    @GetMapping("/num-point-used")
    @PreAuthorize("hasAuthority('USER')")
    public ResponseEntity<EntityResponse<NumberPointUsedInDayResponse>> getNumberPointUsed(@RequestParam(name = "giftId") Integer giftId, @RequestParam(name = "total") Integer total) {
        return responseFactory.successEntity(systemConfigService.getNumberPointUsed(giftId, total));
    }
}
