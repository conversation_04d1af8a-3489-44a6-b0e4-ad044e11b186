package com.vitadairy.gift.features.dynamicFilter.services;

import com.vitadairy.gift.constants.DynamicFilterConstant;
import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.gift.repositories.ConstantRepository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class DynamicFilterService {

    private final ConstantRepository constantRepository;

    public DynamicFilterService(ConstantRepository constantRepository) {
        this.constantRepository = constantRepository;
    }

    public DynamicFilterDto createOrUpdateDynamicFilter(DynamicFilterDto dto) {
        DynamicFilterDto dynamicFilter = DynamicFilterDto.fromEntity(constantRepository.updateOrInsert(dto.toEntity()));
        return dynamicFilter;
    }

    public List<DynamicFilterDto> listDynamicFilter() {
        List<Constant> constants = constantRepository.findByKeyAlphaAndKeyBetaOrderByPriorityAsc(DynamicFilterConstant.KEY_ALPHA, DynamicFilterConstant.KEY_BETA);
        return constants.stream().map(DynamicFilterDto::fromEntity).toList();
    }

}
