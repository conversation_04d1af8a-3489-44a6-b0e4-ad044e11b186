package com.vitadairy.gift.features.dynamicFilter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vitadairy.gift.constants.DynamicFilterConstant;
import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Constant.ConstantDynamicData;
import com.vitadairy.gift.entities.DynamicFilter;
import com.vitadairy.gift.entities.DynamicFilter.*;
import lombok.*;

import java.util.List;
import java.util.Objects;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DynamicFilterDto {
    private String name;
    private String code;
    private int priority;
    @JsonProperty("isActive")
    private boolean isActive;
    List<DynamicFilterValueDto> value;

    public Constant toEntity() {
        DynamicFilter dynamicFilterEntity = DynamicFilter.builder()
                .name(this.name)
                .code(this.code)
                .priority(this.priority)
                .isActive(this.isActive)
                .value(this.value.stream().map(DynamicFilterValueDto::toEntity).toList())
                .build();


        ConstantDynamicData dynamicData = ConstantDynamicData.builder().dynamicFilter(dynamicFilterEntity).build();
        return Constant.builder()
                .keyAlpha(DynamicFilterConstant.KEY_ALPHA)
                .keyBeta(DynamicFilterConstant.KEY_BETA)
                .value(this.code)
                .priority(this.priority)
                .isActive(this.isActive)
                .dynamicData(dynamicData)
                .build();
    }

    public static DynamicFilterDto fromEntity(Constant entity) {

        return DynamicFilterDto.builder()
                .name(Objects.isNull(entity.getDynamicData())
                        || Objects.isNull(entity.getDynamicData().getDynamicFilter())
                        ? null : entity.getDynamicData().getDynamicFilter().getName())
                .code(entity.getValue())
                .priority(entity.getPriority())
                .isActive(entity.getIsActive())
                .value(Objects.isNull(entity.getDynamicData())
                        || Objects.isNull(entity.getDynamicData().getDynamicFilter())
                        ? null : entity.getDynamicData().getDynamicFilter().getValue()
                        .stream().map(DynamicFilterValueDto::fromEntity).toList())
                .build();
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterValueDto {
        private String name;
        private String code;
        List<DynamicFilterFirstLevelDto> firstLevel;

        public DynamicFilterValue toEntity() {
            return DynamicFilterValue.builder()
                    .name(this.name)
                    .code(this.code)
                    .firstLevel(Objects.isNull(this.firstLevel) ? null : this.firstLevel.stream().map(DynamicFilterFirstLevelDto::toEntity).toList())
                    .build();
        }

        public static DynamicFilterValueDto fromEntity(DynamicFilterValue entity) {
            return DynamicFilterValueDto.builder()
                    .name(entity.getName())
                    .code(entity.getCode())
                    .firstLevel(Objects.isNull(entity.getFirstLevel()) ? null : entity.getFirstLevel().stream().map(DynamicFilterFirstLevelDto::fromEntity).toList())
                    .build();
        }
    }


    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterFirstLevelDto {
        private String name;
        private String code;
        @JsonProperty("isActive")
        private boolean isActive;
        private List<DynamicFirstLevelValueDto> value;

        public DynamicFilterFirstLevel toEntity() {
            return DynamicFilterFirstLevel.builder()
                    .name(this.name)
                    .isActive(this.isActive)
                    .code(this.code)
                    .value(Objects.isNull(this.value) ? null : this.value.stream().map(DynamicFirstLevelValueDto::toEntity).toList())
                    .build();
        }

        public static DynamicFilterFirstLevelDto fromEntity(DynamicFilterFirstLevel entity) {
            return DynamicFilterFirstLevelDto.builder()
                    .name(entity.getName())
                    .isActive(entity.isActive())
                    .code(entity.getCode())
                    .value(Objects.isNull(entity.getValue()) ? null : entity.getValue().stream().map(DynamicFirstLevelValueDto::fromEntity).toList())
                    .build();
        }
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFirstLevelValueDto {
        private String name;
        private String code;
        private List<DynamicFilterSecondLevelDto> secondLevel;

        public DynamicFirstLevelValue toEntity() {
            return DynamicFirstLevelValue.builder()
                    .name(this.name)
                    .code(this.code)
                    .secondLevel(Objects.isNull(this.secondLevel) ? null : this.secondLevel.stream().map(DynamicFilterSecondLevelDto::toEntity).toList())
                    .build();
        }

        public static DynamicFirstLevelValueDto fromEntity(DynamicFirstLevelValue entity) {
            return DynamicFirstLevelValueDto.builder()
                    .name(entity.getName())
                    .code(entity.getCode())
                    .secondLevel(Objects.isNull(entity.getSecondLevel()) ? null : entity.getSecondLevel().stream().map(DynamicFilterSecondLevelDto::fromEntity).toList())
                    .build();
        }
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterSecondLevelDto {
        private String name;
        private String code;
        @JsonProperty("isActive")
        private Boolean isActive;
        private List<DynamicFilterSecondLevelValueDto> value;

        public DynamicFilterSecondLevel toEntity() {
            return DynamicFilterSecondLevel.builder()
                    .isActive(this.isActive)
                    .code(this.code)
                    .name(this.name)
                    .value(Objects.isNull(this.value) ? null : this.value.stream().map(DynamicFilterSecondLevelValueDto::toEntity).toList())
                    .build();
        }

        public static DynamicFilterSecondLevelDto fromEntity(DynamicFilterSecondLevel entity) {
            return DynamicFilterSecondLevelDto.builder()
                    .isActive(entity.getIsActive())
                    .code(entity.getCode())
                    .name(entity.getName())
                    .value(Objects.isNull(entity.getValue()) ? null : entity.getValue().stream().map(DynamicFilterSecondLevelValueDto::fromEntity).toList())
                    .build();
        }
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterSecondLevelValueDto {
        private String name;
        private String code;

        public DynamicFilterSecondLevelValue toEntity() {
            return DynamicFilterSecondLevelValue.builder()
                    .code(this.code)
                    .name(this.name)
                    .build();
        }

        public static DynamicFilterSecondLevelValueDto fromEntity(DynamicFilterSecondLevelValue entity) {
            return DynamicFilterSecondLevelValueDto
                    .builder()
                    .name(entity.getName())
                    .code(entity.getCode())
                    .build();
        }
    }
}