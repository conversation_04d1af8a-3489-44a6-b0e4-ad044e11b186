package com.vitadairy.gift.features.dynamicFilter.dto.response;

import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.main.response.EntityResponse;
import lombok.Builder;

public class CreateOrUpdateDynamicFilterResponse extends EntityResponse<DynamicFilterDto> {

    @Builder
    public CreateOrUpdateDynamicFilterResponse(DynamicFilterDto data) {
        super(data);
    }
}
