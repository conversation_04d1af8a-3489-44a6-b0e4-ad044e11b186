package com.vitadairy.gift.features.dynamicFilter.controllers;

import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.gift.features.dynamicFilter.dto.request.CreateOrUpdateDynamicFilterRequest;
import com.vitadairy.gift.features.dynamicFilter.dto.response.CreateOrUpdateDynamicFilterResponse;
import com.vitadairy.gift.features.dynamicFilter.dto.response.ListDynamicFilterResponse;
import com.vitadairy.gift.features.dynamicFilter.services.DynamicFilterService;
import com.vitadairy.gift.interfaces.GiftEnableLoggingAdminActionInterface;
import com.vitadairy.main.configs.ResponseFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@GiftEnableLoggingAdminActionInterface
@RequestMapping("v4/gs/dynamic-filter")
public class DynamicFilterController {
    private final DynamicFilterService dynamicFilterService;
    private final ResponseFactory responseFactory;

    public DynamicFilterController(DynamicFilterService dynamicFilterService, ResponseFactory responseFactory) {
        this.dynamicFilterService = dynamicFilterService;
        this.responseFactory = responseFactory;
    }

    // @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-bo-loc', T(java.util.List).of('full-access', 'create'))")
    @PostMapping
    public ResponseEntity<CreateOrUpdateDynamicFilterResponse> createOrUpdateDynamicFilter(@RequestBody CreateOrUpdateDynamicFilterRequest request) {
        DynamicFilterDto requestDto = request.toDto();
        DynamicFilterDto dynamicFilterDtoResult = dynamicFilterService.createOrUpdateDynamicFilter(requestDto);
        CreateOrUpdateDynamicFilterResponse response = new CreateOrUpdateDynamicFilterResponse(dynamicFilterDtoResult);
        return responseFactory.successDto(response);
    }

    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-bo-loc', T(java.util.List).of('full-access', 'update'))")
    @PutMapping
    public ResponseEntity<CreateOrUpdateDynamicFilterResponse> updateDynamicFilter(@RequestBody CreateOrUpdateDynamicFilterRequest request) {
        DynamicFilterDto requestDto = request.toDto();
        DynamicFilterDto dynamicFilterDtoResult = dynamicFilterService.createOrUpdateDynamicFilter(requestDto);
        CreateOrUpdateDynamicFilterResponse response = new CreateOrUpdateDynamicFilterResponse(dynamicFilterDtoResult);
        return responseFactory.successDto(response);
    }

    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-bo-loc', T(java.util.List).of('full-access', 'read'))")
    @GetMapping
    public ResponseEntity<ListDynamicFilterResponse> listDynamicFilter() {
        List<DynamicFilterDto> response = dynamicFilterService.listDynamicFilter();
        return responseFactory.successDto(ListDynamicFilterResponse.builder().data(response).build());
    }
}
