package com.vitadairy.gift.features.dynamicFilter.dto.response;

import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.main.response.EntityResponse;
import lombok.Builder;

import java.util.List;

public class ListDynamicFilterResponse extends EntityResponse<List<DynamicFilterDto>> {

    @Builder
    public ListDynamicFilterResponse(List<DynamicFilterDto> data) {
        super(data);
    }
}
