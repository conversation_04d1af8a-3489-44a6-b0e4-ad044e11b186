package com.vitadairy.gift.features.dynamicFilter.dto.request;

import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class CreateOrUpdateDynamicFilterRequest {
    @NotEmpty
    private String name;
    @NotEmpty
    private String code;
    @NotEmpty
    private int priority;
    @NotEmpty
    private Boolean isActive;
    @NotEmpty
    List<DynamicFilterValueRequest> value;

    public DynamicFilterDto toDto() {
        return DynamicFilterDto.builder()
                .name(this.name)
                .code(this.code)
                .isActive(this.isActive)
                .priority(this.priority)
                .value(this.value == null ? null : this.value.stream().map(DynamicFilterValueRequest::toDto).toList())
                .build();
    }
}

@Data
class DynamicFilterValueRequest {
    @NotEmpty
    private String name;
    @NotEmpty
    List<DynamicFilterFirstLevelRequest> firstLevel;

    public DynamicFilterValueDto toDto() {
        return DynamicFilterValueDto.builder()
                .name(this.name)
                .code(StringUtils.stripAccents(this.name).toUpperCase().replaceAll(StringUtils.SPACE, "_"))
                .firstLevel(this.firstLevel == null ? null : this.firstLevel.stream().map(DynamicFilterFirstLevelRequest::toDto).toList())
                .build();
    }
}

@Data
class DynamicFilterFirstLevelRequest {
    @NotEmpty
    private String name;
    @NotEmpty
    private Boolean isActive;
    @NotEmpty
    private List<DynamicFilterFirstLevelValueRequest> value;

    public DynamicFilterFirstLevelDto toDto() {
        return DynamicFilterFirstLevelDto.builder()
                .name(this.name)
                .code(StringUtils.stripAccents(this.name).toUpperCase().replaceAll(StringUtils.SPACE, "_"))
                .isActive(this.isActive)
                .value(this.value == null ? null : this.value.stream().map(DynamicFilterFirstLevelValueRequest::toDto).toList())
                .build();
    }
}

@Data
class DynamicFilterFirstLevelValueRequest {
    @NotEmpty
    private String name;
    private List<DynamicFilterSecondLevelRequest> secondLevel;

    public DynamicFirstLevelValueDto toDto() {
        return DynamicFirstLevelValueDto.builder()
                .name(this.name)
                .code(StringUtils.stripAccents(this.name).toUpperCase().replaceAll(StringUtils.SPACE, "_"))
                .secondLevel(this.secondLevel == null ? null : this.secondLevel.stream().map(DynamicFilterSecondLevelRequest::toDto).toList())
                .build();
    }
}

@Data
class DynamicFilterSecondLevelRequest {
    @NotEmpty
    private String name;
    private Boolean isActive;
    private List<DynamicFilterSecondLevelValueRequest> value;

    public DynamicFilterSecondLevelDto toDto() {
        return DynamicFilterSecondLevelDto.builder()
                .name(this.name)
                .code(StringUtils.stripAccents(this.name).toUpperCase().replaceAll(StringUtils.SPACE, "_"))
                .isActive(this.isActive)
                .value(this.value == null ? null : this.value.stream().map(DynamicFilterSecondLevelValueRequest::toDto).toList())
                .build();
    }
}

@Data
class DynamicFilterSecondLevelValueRequest {
    @NotEmpty
    private String name;

    public DynamicFilterSecondLevelValueDto toDto() {
        return DynamicFilterSecondLevelValueDto.builder()
                .name(this.name)
                .code(StringUtils.stripAccents(this.name).toUpperCase().replaceAll(StringUtils.SPACE, "_"))
                .build();
    }
}