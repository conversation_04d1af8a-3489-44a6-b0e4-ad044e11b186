package com.vitadairy.gift.features.userGift.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigInteger;

@Data
@Builder
public class GiftExportDto {
    private String id;
    private String name;
    private String category;
    private String type;
    private String sctNumber;
    private String tpmNumber;
    private String rewardAppGiftCode;
    private String badges;
    private Integer displayPoint;
    private BigInteger money;
    private Long total;
    private String status;
    private Integer qtyReward;
    private Integer qtyReservation;
}
