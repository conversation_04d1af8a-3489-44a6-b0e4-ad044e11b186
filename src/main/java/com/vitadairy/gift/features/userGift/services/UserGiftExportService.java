package com.vitadairy.gift.features.userGift.services;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.nio.channels.Channels;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.WriteChannel;
import com.vitadairy.gift.configs.GoogleStorageClient;
import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.constants.ExportType;
import com.vitadairy.gift.entities.ExportHistory;
import com.vitadairy.gift.features.userGift.dto.UserGiftExportDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.repositories.ExportHistoryRepository;
import com.vitadairy.gift.services.FetchExportGsUserGiftService;
import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.common.ExportResponse;
import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.main.exception.ExportException;
import com.vitadairy.main.configs.ApplicationProperties;
import com.vitadairy.main.utils.IdUtils;

@Service
@Slf4j
public class UserGiftExportService {
    private final ExportHistoryRepository exportHistoryRepository;
    private final ObjectMapper objectMapper;
    private final FetchExportGsUserGiftService fetchExportGsUserGiftService;
    private final GoogleStorageClient googleStorageClient;
    private final Integer exportTimeoutMinutes;
    private final ApplicationProperties applicationProperties;

    private final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
    private final static List<UserGiftExportMetadata> USER_GIFT_EXPORT_METADATA = List.of(
        UserGiftExportMetadata.builder().name("Customer Name").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getCustomerName).build(),
        UserGiftExportMetadata.builder().name("Customer ID").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getCustomerId).build(),
        UserGiftExportMetadata.builder().name("Customer Phone").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getCustomerPhone).build(),
        UserGiftExportMetadata.builder().name("Transaction External ID").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getTransactionExternalId).build(),
        UserGiftExportMetadata.builder().name("Transaction Date").dataType(DataType.DATE).cellDataSupplier(UserGiftExportDto::getTransactionDate).build(),
        UserGiftExportMetadata.builder().name("Type").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getType).build(),
        UserGiftExportMetadata.builder().name("Action Type").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getActionType).build(),
        UserGiftExportMetadata.builder().name("Gift Points").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getGiftPoint).build(),
        UserGiftExportMetadata.builder().name("Tier Point").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getTierPoint).build(),
        UserGiftExportMetadata.builder().name("User Gift ID").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getUserGiftId).build(),
        UserGiftExportMetadata.builder().name("Gift ID").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getGiftId).build(),
        UserGiftExportMetadata.builder().name("Voucher Ref ID").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getVoucherRefId).build(),
        UserGiftExportMetadata.builder().name("Voucher Code").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getVoucherCode).build(),
        UserGiftExportMetadata.builder().name("Voucher Link").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getVoucherLink).build(),
        UserGiftExportMetadata.builder().name("User Gift Status").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getUserGiftStatus).build(), 
        UserGiftExportMetadata.builder().name("Quantity").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getQuantity).build(),
        UserGiftExportMetadata.builder().name("Used Date").dataType(DataType.DATE).cellDataSupplier(UserGiftExportDto::getUsedDate).build(),
        UserGiftExportMetadata.builder().name("Expiry Date").dataType(DataType.DATE).cellDataSupplier(UserGiftExportDto::getExpiryDate).build(),
        UserGiftExportMetadata.builder().name("Is Reusing").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getIsReusing).build(),
        UserGiftExportMetadata.builder().name("Name").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getName).build(),
        UserGiftExportMetadata.builder().name("E Product ID").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getEProductId).build(),
        UserGiftExportMetadata.builder().name("E Product Name").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getEProductName).build(),
        UserGiftExportMetadata.builder().name("Price ID").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getPriceId).build(),
        UserGiftExportMetadata.builder().name("Money").dataType(DataType.NUMBER).cellDataSupplier(UserGiftExportDto::getMoney).build(),
        UserGiftExportMetadata.builder().name("Gift Code").dataType(DataType.STRING).cellDataSupplier(UserGiftExportDto::getGiftCode).build()
    );

    private final static String XLSX_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

    public UserGiftExportService(ExportHistoryRepository exportHistoryRepository,
                                ObjectMapper objectMapper,
                                FetchExportGsUserGiftService fetchExportGsUserGiftService,
                                GoogleStorageClient googleStorageClient,
                                @Value("${export.job.timeout.minutes:10}") Integer exportTimeoutMinutes,
                                ApplicationProperties applicationProperties) {
        this.exportHistoryRepository = exportHistoryRepository;
        this.objectMapper = objectMapper;
        this.fetchExportGsUserGiftService = fetchExportGsUserGiftService;
        this.googleStorageClient = googleStorageClient;
        this.exportTimeoutMinutes = exportTimeoutMinutes;
        this.applicationProperties = applicationProperties;
    }

    @Scheduled(fixedDelayString = "${export.job.timeout.ms:600000}")
    public void timeoutJob() {
        var timeoutStartedAt = Instant.now().minus(exportTimeoutMinutes, ChronoUnit.MINUTES);
        var expiredJobs = exportHistoryRepository.expireAllPendingJobsBeforeTime(timeoutStartedAt);
        log.info("Expired {} pending jobs because of timeout", expiredJobs);
    }

    @Scheduled(fixedDelayString = "${export.job.run.interval.ms:5000}")
    public void exportUserGift() {
        Optional<ExportHistory> jobOpt = exportHistoryRepository.findOnePendingJobLocked(ExportType.USER_GIFT.name());
        if (jobOpt.isEmpty()) {
            log.info("No pending job found");
            return;
        }

        ExportHistory job = jobOpt.get();
        log.info("Process pending job_id: {}", job.getId());
        var startedAt = Instant.now();
        job.setStatus(ExportStatus.PROCESSING);
        job.setStartedAt(startedAt);
        exportHistoryRepository.save(job);

        try {
            var filter = objectMapper.readValue(job.getFilter(), new TypeReference<UserGiftListRequest>() {});
            filter.setSize(applicationProperties.getExportConfig().getBatchSize());
            filter.setPage(0);
            FetchRequest<UserGiftListRequest> fetchRequest = new FetchRequest<>();
            fetchRequest.setRequest(filter);
            var timeoutAt = startedAt.plus(exportTimeoutMinutes, ChronoUnit.MINUTES);
            var resp = export(fetchRequest, timeoutAt);
            if (Objects.isNull(resp)) {
                job.setStatus(ExportStatus.FAILED);
                job.setErrorMessage("Export response is null");
            } else if (resp.getRc() == 1) {
                job.setStatus(ExportStatus.FAILED);
                job.setErrorMessage(resp.getRd());
            } else {
                log.info("Export success job_id {} to: {}", job.getId(), resp.getFilePath());
                job.setResultFilePath(resp.getFilePath());
                job.setStatus(ExportStatus.SUCCESS);
            }
        } catch (Exception e) {
            job.setStatus(ExportStatus.FAILED);
            job.setErrorMessage(e.getMessage());
        }

        exportHistoryRepository.save(job);
    }

    public ExportResponse export(FetchRequest<UserGiftListRequest> request, Instant timeoutAt) throws Exception {
        String filePath = String.format("export/%s.xlsx", IdUtils.generateId("USER_GIFTS_"));
        var gcsPath = googleStorageClient.getPath(filePath);

        return doExport(gcsPath, filePath, request, timeoutAt);
    }

    private ExportResponse doExport(String gcsFilePath, String filePath, FetchRequest<UserGiftListRequest> request, Instant timeoutAt) throws Exception {
        ExportResponse response = new ExportResponse();
        response.setFailed();
        try (SXSSFWorkbook wb = new SXSSFWorkbook(null, 100, false, true);
            WriteChannel writeChannel = googleStorageClient.getWriteChannel(filePath, XLSX_CONTENT_TYPE);
            OutputStream outputStream = Channels.newOutputStream(writeChannel)
        ) {
            SXSSFSheet sheet = wb.createSheet();

            // write header
            this.writeExportFileHeader(sheet);

            // setup data cell style
            var dataCellStyle = wb.createCellStyle();
            dataCellStyle.setBorderTop(BorderStyle.THIN);
            dataCellStyle.setBorderBottom(BorderStyle.THIN);
            dataCellStyle.setBorderLeft(BorderStyle.THIN);
            dataCellStyle.setBorderRight(BorderStyle.THIN);
            dataCellStyle.setAlignment(HorizontalAlignment.CENTER);

            if (Instant.now().isAfter(timeoutAt)) {
                throw new ExportException("Export timeout");
            }

            List<UserGiftExportDto> userGifts = fetchExportGsUserGiftService.fetch(request);
            while(userGifts.size() > 0) {
                for (UserGiftExportDto data : userGifts) {
                    Row row = sheet.createRow(sheet.getLastRowNum() + 1);
                    for (int i = 0; i < USER_GIFT_EXPORT_METADATA.size(); i++) {
                        var metadata = USER_GIFT_EXPORT_METADATA.get(i);
                        var cell = row.createCell(i);
                        Object cellData = metadata.getCellDataSupplier().apply(data);
                        this.writeCell(cell, dataCellStyle, cellData, metadata.getDataType());
                    }
                }

                request.getRequest().setPage(request.getRequest().getPage()+1);
                request.getRequest().setSize(request.getRequest().getSize());
                userGifts = fetchExportGsUserGiftService.fetch(request);
            }

            // write to google storage
            wb.write(outputStream);
            wb.dispose();

            response.setSuccess();
            response.setFilePath(filePath);
        } catch (Exception e) {
            var errorId = UUID.randomUUID().toString();
            log.error("Error exporting user gifts with error_id: {}", errorId, e);
            response.setFailed(String.format("Không thể xuất file do lỗi hệ thống\n(ID: %s)", errorId));
        }
        return response;
    }

    protected void writeExportFileHeader(Sheet sheet) {
        Row header = sheet.createRow(0);
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        Font font = sheet.getWorkbook().createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        for (int i = 0; i < USER_GIFT_EXPORT_METADATA.size(); i++) {
            var metadata = USER_GIFT_EXPORT_METADATA.get(i);
            Cell cell = header.createCell(i);
            cell.setCellValue(metadata.getName());
            cell.setCellStyle(cellStyle);
        }
    }

    private void writeCell(Cell cell, CellStyle cellStyle, Object data, DataType dataType) {
        cell.setCellStyle(cellStyle);
        if (Objects.isNull(data)) {
            cell.setCellValue("");
            return;
        }

        switch (dataType) {
            case DATE:
                if (data instanceof Date) {
                    cell.setCellValue(DATE_FORMAT.format((Date)data));
                } else if (data instanceof Instant) {
                        cell.setCellValue(DATE_FORMAT.format(Date.from((Instant)data)));
                } else if (data instanceof Long) {
                    cell.setCellValue(DATE_FORMAT.format(new Date((Long)data)));
                } else if (data instanceof Timestamp) {
                    cell.setCellValue(DATE_FORMAT.format(new Date(((Timestamp)data).getTime())));
                } else if (data instanceof String) {
                    cell.setCellValue(data.toString());
                } else {
                    cell.setCellValue("");
                }
                break;
            case NUMBER:
               cell.setCellValue(((Number)data).doubleValue());
               break;
            default:
                cell.setCellValue(data.toString());
                break;
         }
    }

    @Getter
    @Setter
    @Builder
    private static class UserGiftExportMetadata {
        private final String name;
        private final DataType dataType;
        private final Function<UserGiftExportDto, Object> cellDataSupplier;
    }
}
