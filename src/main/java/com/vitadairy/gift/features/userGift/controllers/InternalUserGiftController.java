package com.vitadairy.gift.features.userGift.controllers;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.gift.features.userGift.dto.response.ListUserGiftResponse;
import com.vitadairy.gift.features.userGift.dto.response.UserGiftResponse;
import com.vitadairy.gift.features.userGift.services.UserGiftService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.iresponse.BaseResponse;
import jakarta.validation.Valid;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("in/gs/user-gifts")
public class InternalUserGiftController {
    private final ResponseFactory responseFactory;
    private UserGiftService _userGiftService;

    public InternalUserGiftController(UserGiftService userGiftService, ResponseFactory responseFactory) {
        this._userGiftService = userGiftService;
        this.responseFactory = responseFactory;
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<BaseResponse> delete(@PathVariable Integer id) {
        try {
            return responseFactory.successDto(_userGiftService.delete(id));
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @GetMapping("")
    public ResponseEntity<ListUserGiftResponse> getUserGifts(@RequestParam List<Long> ids) {
        List<UserGift> userGifts = this._userGiftService.getUserGiftByIds(ids);
        ListUserGiftResponse response = new ListUserGiftResponse(userGifts.stream().map(UserGiftDto::fromEntity).toList(), HttpStatus.OK, "OK");
        return responseFactory.successDto(response);
    }

    @PutMapping("/{id}/status")
    public ResponseEntity<BaseResponse> updateStatus(@PathVariable Integer id,
                                                     @RequestBody @Valid String status) {
        if (status.startsWith("\"") && status.endsWith("\"")) {
            status = status.substring(1, status.length() - 1);
        }
        UserGift updated = this._userGiftService.updateStatus(id, status);
        UserGiftResponse response = new UserGiftResponse(UserGiftDto.fromEntity(updated), HttpStatus.OK,
                "update success");
        return responseFactory.successDto(response);
    }
}
