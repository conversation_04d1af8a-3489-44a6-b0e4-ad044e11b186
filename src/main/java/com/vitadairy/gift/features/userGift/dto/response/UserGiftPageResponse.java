package com.vitadairy.gift.features.userGift.dto.response;

import java.util.List;

import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.main.response.PageableResponse;

public class UserGiftPageResponse extends PageableResponse<UserGiftDto> {
    public UserGiftPageResponse(List<UserGiftDto> items, Long total, Integer page, Integer pageSize,
            HttpStatusCode statusCode, String msg) {
        super(items, total, 0L, page, pageSize, statusCode, msg);
    }
}
