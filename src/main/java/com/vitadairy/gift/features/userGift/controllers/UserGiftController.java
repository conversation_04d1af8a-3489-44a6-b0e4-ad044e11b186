package com.vitadairy.gift.features.userGift.controllers;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.dto.UserGiftPreOrderSendNotifyEnoughtPointDto;
import com.vitadairy.gift.features.gifts.dto.request.UpdateUserGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.UserGiftPreOrderSendNotifyEnoughtPointRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.UserGiftReuseRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.UserGiftPreOrderSendNotifyEnoughtPointResponse;
import com.vitadairy.gift.features.userGift.dto.PreOrderRequestDto;
import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.gift.features.userGift.dto.response.UserGiftPageResponse;
import com.vitadairy.gift.features.userGift.dto.response.UserGiftResponse;
import com.vitadairy.gift.features.userGift.services.UserGiftService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.iresponse.BaseResponse;
// import com.vitadairy.main.response.EntityResponse;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.zoo.services.EncryptDataService;
import jakarta.validation.Valid;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("v4/gs/user-gifts")
public class UserGiftController {
    private final ResponseFactory responseFactory;
    private final UserGiftService _userGiftService;
    private final EncryptDataService encryptDataService;

    public UserGiftController(UserGiftService userGiftService, ResponseFactory responseFactory, @Qualifier("zooEncryptDataService") EncryptDataService encryptDataService) {
        this._userGiftService = userGiftService;
        this.responseFactory = responseFactory;
        this.encryptDataService = encryptDataService;
    }

    @PreAuthorize("hasAuthority('USER')")
    @GetMapping()
    public ResponseEntity<UserGiftPageResponse> getUserGifts(
            @RequestParam(required = false) List<UserGift.USER_GIFT_STATUS> statuses,
            @RequestParam(required = false) List<UserGift.USER_GIFT_STATUS> preOrderStatus,
            @RequestParam(required = false) String giftTypes,
            @RequestParam(required = false) Long eventCode,
            @RequestParam(required = false) Boolean showPrice,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String authHeader) {
        var principal = AuthenticationHelper.getCurrentUser();
        String bearerToken = (authHeader != null && authHeader.startsWith("Bearer "))
                ? authHeader.substring(7) : null;

        Pageable pageable = PageRequest.of(page, size);
        Page<UserGift> userGifts = this._userGiftService.getUserGiftsByUserIdAndStatuses(
                principal.getUserId(), statuses, preOrderStatus, giftTypes, eventCode, pageable);
        List<UserGiftDto> result =
                userGifts.getContent().stream().map(UserGiftDto::fromEntity).toList();
        if (BooleanUtils.isFalse(showPrice)) {
            result.forEach(userGiftDto -> {
                if (Objects.nonNull(userGiftDto.getGift())) {
                    userGiftDto.getGift().setPrice(null);
                }
            });
        }

        // Encrypt data gotit
        result.forEach(userGiftDto -> {
            this.encryptGotitData(userGiftDto, bearerToken, Math.toIntExact(principal.getUserId()));
        });

        UserGiftPageResponse userGiftPageResponse = new UserGiftPageResponse(result,
                (long) userGifts.getTotalPages(), page, size, HttpStatus.OK, "success");
        return responseFactory.successDto(userGiftPageResponse);
    }

    @PreAuthorize("hasAuthority('USER')")
    @PostMapping("pre-order")
    public ResponseEntity<UserGiftResponse> createPreOrder(
            @RequestBody PreOrderRequestDto preOrderRequestDto,
            @RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String authHeader) {
        var principal = AuthenticationHelper.getCurrentUser();
        String bearerToken = (authHeader != null && authHeader.startsWith("Bearer "))
                ? authHeader.substring(7) : null;

        UserGift userGift =
                _userGiftService.creatPreOrderUserGift(principal.getUserId(), preOrderRequestDto);
        UserGiftDto userGiftDto = UserGiftDto.fromEntity(userGift);
        this.encryptGotitData(userGiftDto, bearerToken, Math.toIntExact(principal.getUserId()));
        UserGiftResponse response = new UserGiftResponse(userGiftDto,
                HttpStatus.OK, "update success");
        return responseFactory.successDto(response);
    }

    @GetMapping("/no-auth-token/{id}/gift-id")
    public Integer getGiftIdByUserGiftId(@PathVariable Integer id) {
        UserGift userGift = this._userGiftService.findById(id);
        Integer giftId = 0;
        if (Objects.nonNull(userGift)) {
            giftId = userGift.getGift().getId();
        }

        return giftId;
    }

    @PreAuthorize("hasAuthority('USER')")
    @PutMapping("pre-order/{id}")
    public ResponseEntity<UserGiftResponse> completed(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserGiftRequestDto updateUserGiftRequestDto,
            @RequestHeader(value = HttpHeaders.AUTHORIZATION, required = false) String authHeader
    ) {
        var principal = AuthenticationHelper.getCurrentUser();
        String bearerToken = (authHeader != null && authHeader.startsWith("Bearer "))
                ? authHeader.substring(7) : null;

        updateUserGiftRequestDto.setId(id);
        updateUserGiftRequestDto.setUserId(principal.getUserId());
        UserGift updated = this._userGiftService.update(updateUserGiftRequestDto);
        UserGiftDto userGiftDto = UserGiftDto.fromEntity(updated);
        this.encryptGotitData(userGiftDto, bearerToken, Math.toIntExact(principal.getUserId()));
        UserGiftResponse response = new UserGiftResponse(userGiftDto,
                HttpStatus.OK, "update success");
        return responseFactory.successDto(response);
    }

    @PostMapping("secret/cron")
    public ResponseEntity<BaseResponse> updateStatusByExpried() {
        // TODO: process POST request
        this._userGiftService.updateExpiredUserGifts();
        this._userGiftService.cancelOutOfHoldingUserGifts();
        this._userGiftService.remindLastHoldingDate();
        EntityResponse entityResponse = new EntityResponse<Object>(null, HttpStatus.OK, "ok");
        return responseFactory.successDto(entityResponse);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<BaseResponse> delete(@PathVariable Integer id) {
        try {
            return responseFactory.successDto(_userGiftService.delete(id));
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("/reuse")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<Boolean>> reuseUserGift(
            @Valid @RequestBody UserGiftReuseRequestDto body) {
        var principal = AuthenticationHelper.getCurrentUser();
        body.setUserId(principal == null ? null : principal.getUserId());
        var response = this._userGiftService.reuseUserGift(body);
        return responseFactory.successEntity(response);
    }

    @PostMapping("/no-auth-token/reuse")
    public ResponseEntity<EntityResponse<Boolean>> reuseUserGiftNoAuthenToken(
            @Valid @RequestBody UserGiftReuseRequestDto body) {
        // var principal = AuthenticationHelper.getCurrentUser();
        // body.setUserId(principal == null ? null : principal.getUserId());
        var response = this._userGiftService.reuseUserGift(body);
        return responseFactory.successEntity(response);
    }

    @PreAuthorize("hasAuthority('USER')")
    @GetMapping("/get-pre-order-enought-point-to-send-notify")
    public ResponseEntity<UserGiftPreOrderSendNotifyEnoughtPointResponse> getUserGiftPreOrderSendNotifyEnoughtPoint(
            @ModelAttribute UserGiftPreOrderSendNotifyEnoughtPointRequestDto dto) {
        var principal = AuthenticationHelper.getCurrentUser();
        dto.setUserId(principal.getUserId());
        List<UserGift> userGifts =
                this._userGiftService.getUserGiftPreOrderSendNotifyEnoughtPoint(dto);
        UserGiftPreOrderSendNotifyEnoughtPointResponse response =
                new UserGiftPreOrderSendNotifyEnoughtPointResponse(
                        UserGiftPreOrderSendNotifyEnoughtPointDto.fromEntity(userGifts),
                        HttpStatus.OK, "ok");
        return responseFactory.successDto(response);
    }

    private void encryptGotitData(UserGiftDto userGiftDto, String bearerToken, Integer userId) {
        if (
                Objects.isNull(userGiftDto) ||
                        bearerToken.isBlank() ||
                        Objects.isNull(userId) ||
                        userId <= 0
        ) {
            return;
        }

        UserGiftDynamicData userGiftDynamicData = userGiftDto.getDynamicData();
        if (Objects.isNull(userGiftDynamicData)) {
            return;
        }
        String voucherCode = userGiftDynamicData.getVoucherCode();
        String voucherLink = userGiftDynamicData.getVoucherLink();

        if (
                voucherCode != null &&
                        !voucherCode.isBlank() &&
                        voucherCode.length() < 20
        ) {
            String voucherCodeEncrypted = null;
            try {
                voucherCodeEncrypted = this.encryptDataService.encryptData(voucherCode, bearerToken, userId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            userGiftDynamicData.setVoucherCode(voucherCodeEncrypted);
        }

        if (
                voucherLink != null &&
                        !voucherLink.isBlank() &&
                        voucherLink.contains("gotit.vn")
        ) {
            String voucherLinkEncrypted = null;
            try {
                voucherLinkEncrypted = this.encryptDataService.encryptData(voucherLink, bearerToken, userId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            userGiftDynamicData.setVoucherLink(voucherLinkEncrypted);
        }

        userGiftDto.setDynamicData(userGiftDynamicData);
    }
}
