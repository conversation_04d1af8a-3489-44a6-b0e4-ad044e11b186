package com.vitadairy.gift.features.userGift.dto;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class GiftDto {
    private Integer id;
    private List<String> images;
    private GiftStatusEnum status;
    private String transportTypeCode;
    private String name;
    private GiftTypeEnum type;
    private BigInteger price;
    private List<String> hiddenTags;
    private String sctNumber;
    private String tpmNumber;
    private Long eventId;
    private String categoryCode;
    private Map<String,Object> dynamicData;

    public static GiftDto fromEntity(Gift gift) {
        var giftDto = GiftDto.builder()
                .id(gift.getId())
                .images(gift.getImages())
                .status(gift.getStatus())
                .transportTypeCode(gift.getTransportTypeCode())
                .name(gift.getName())
                .type(gift.getType())
                .price(gift.getPrice())
                .hiddenTags(gift.getHiddenTags())
                .sctNumber(gift.getSctNumber())
                .tpmNumber(gift.getTpmNumber())
                .eventId(gift.getEventId())
                .categoryCode(gift.getCategoryCode())
                .build();

        if (gift.getDynamicData() != null) {
            String sourceGift = "";
            if (gift.getDynamicData().getSourceGift() != null) {
                sourceGift = gift.getDynamicData().getSourceGift();
            }

            var dynamicData = Map.<String, Object>ofEntries(
                   Map.entry("sourceGift", sourceGift)
            );
            giftDto.setDynamicData(dynamicData);
        }

        return giftDto;
    }
}
