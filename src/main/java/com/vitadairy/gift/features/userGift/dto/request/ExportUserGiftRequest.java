package com.vitadairy.gift.features.userGift.dto.request;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.main.request.ExportRequest;

public class ExportUserGiftRequest extends ExportRequest<UserGiftListRequest, Integer> {
    @Override
    public void doValidate() throws Exception {
        super.doValidate();
    }

    @Override
    public FetchRequest<UserGiftListRequest> toFetchRequest() {
        return super.toFetchRequest();
    }
}
