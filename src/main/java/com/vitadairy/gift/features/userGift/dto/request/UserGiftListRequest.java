package com.vitadairy.gift.features.userGift.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vitadairy.gift.shared.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserGiftListRequest extends BaseDto.BaseFilter {
    private Long userId;
    private Long giftId;
    private String statuses;
    private String giftTypes;
    private String preOrderStatus;
    private String extendedSearch;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime fromDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private ZonedDateTime toDate;

    public void removeEmptyVariable() {
        if (StringUtils.isEmpty(this.statuses)) {
            this.setStatuses(null);
        }
        if (StringUtils.isEmpty(this.giftTypes)) {
            this.setGiftTypes(null);
        }
        if (StringUtils.isEmpty(this.extendedSearch)) {
            this.setExtendedSearch(null);
        }
    }

    public Instant getFromDate() {
        return Objects.nonNull(this.fromDate) ? this.fromDate.toInstant() : null;
    }

    public Instant getToDate() {
        return Objects.nonNull(this.toDate) ? this.toDate.toInstant() : null;
    }

    @JsonIgnore
    public List<String> getListStatuses() {
        return Objects.isNull(this.statuses) ? null
                : List.of(StringUtils.split(this.statuses, ","));
    }

    @JsonIgnore
    public List<String> getListGiftTypes() {
        return Objects.isNull(this.giftTypes) ? null
                : List.of(StringUtils.split(this.giftTypes, ","));
    }

    @JsonIgnore
    public List<String> getListPreOrderStatus() {
        return Objects.isNull(this.preOrderStatus) ? null
                : List.of(StringUtils.split(this.preOrderStatus, ","));
    }
}
