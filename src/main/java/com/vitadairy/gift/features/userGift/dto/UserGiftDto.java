package com.vitadairy.gift.features.userGift.dto;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
public class UserGiftDto {
    private Integer giftId;
    private Integer quantity;
    private Long id;
    private Long userId;
    private Timestamp holdingDate;
    private Integer usedQuantity;
    private Integer reservationPoint;
    private Float point;
    private Instant createdAt;
    private Instant updatedAt;
    private UserGiftDynamicData dynamicData;
    private Map<String, String> recipientSnapshot;
    private Map<String, String> giftSnapshot;
    private GiftDto gift;
    private USER_GIFT_STATUS status;
    private String telco;
    private USER_GIFT_STATUS preOrderStatus;
    private String transactionCode;

    public static UserGiftDto fromEntity(UserGift entity) {
        if(Objects.isNull(entity)) {
            return null;
        }
        GiftDto giftInfo = GiftDto.fromEntity(entity.getGift());
        if (Objects.nonNull(entity.getGiftSnapshot())) {
            giftInfo.setSctNumber(Objects.isNull(entity.getGiftSnapshot().getSctNumber()) ?
                    entity.getGiftSnapshot().getSctNumberJson() : entity.getGiftSnapshot().getSctNumber());
            giftInfo.setTpmNumber(entity.getGiftSnapshot().getTpmNumber());
        }
        UserGiftDynamicData dynamicData = entity.getDynamicData();
        return UserGiftDto.builder()
                .giftId(entity.getGift().getId())
                .quantity(entity.getQuantity())
                .id(entity.getId())
                .userId(entity.getUserId())
                .holdingDate(entity.getHoldingDate())
                .usedQuantity(entity.getUsedQuantity())
                .reservationPoint(entity.getReservationPoint())
                .point(entity.getPoint())
                .dynamicData(dynamicData)
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                // .giftSnapshot() TODO: init giftSnapShot
                .recipientSnapshot(entity.getRecipientSnapshot())
                .status(entity.getStatus())
                .gift(giftInfo)
                .telco(entity.getDynamicData() != null ? entity.getDynamicData().getTelco() : null)
                .preOrderStatus(entity.getPreOrderStatus())
                .transactionCode(entity.getTransactionCode())
                .build();
    }
}
