package com.vitadairy.gift.features.userGift.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigInteger;
import java.util.Date;

@Data
@Builder
public class UserGiftExportDto {
    private String customerName;
    private String customerId;
    private String customerPhone;
    private String transactionExternalId;
    private Date transactionDate;
    private String type;
    private String actionType;
    private Float giftPoint;
    private Float tierPoint;


    private Integer userGiftId;
    private Integer giftId;
    private String voucherRefId;
    private String voucherCode;
    private String voucherLink;
    private String userGiftStatus;
    private Integer quantity;
    private Date usedDate;
    private Date expiryDate;
    private Boolean isReusing;


    private String name;
    private String eProductId;
    private String eProductName;
    private String priceId;
    private BigInteger money;
    private String giftCode;
}
