package com.vitadairy.gift.features.userGift.services;

import java.sql.Timestamp;

import com.vitadairy.gift.common.MultiTransactionHandler;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.request.UpdateUserGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.UserGiftPreOrderSendNotifyEnoughtPointRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.UserGiftReuseRequestDto;
import com.vitadairy.gift.features.gifts.services.GiftExchangeService;
import com.vitadairy.gift.features.points.services.TransactionService;
import com.vitadairy.gift.features.userGift.dto.UserGiftRequestDto;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.repositories.GiftReservationRepository;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.gift.repositories.UserRepository;
import com.vitadairy.gift.services.RestApiWarehouseService;
import com.vitadairy.gift.utils.StringUtils;
import com.vitadairy.main.common.GiftFunctionDefs;
import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.dto.TransactionDto;
import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.iresponse.BaseResponse;

import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.common.CrmTransactionTypeCode;
import com.vitadairy.zoo.dto.DDXTrackingUserActionWithPointRequestDto;
import com.vitadairy.zoo.dto.UserGiftNotificationRequestDto;
import com.vitadairy.zoo.entities.HistoryPoint;

import com.vitadairy.zoo.enums.EnumHistoryPointActionType;
import com.vitadairy.zoo.enums.EnumHistoryPointType;
import com.vitadairy.zoo.enums.FeatureNoti;
import com.vitadairy.zoo.repositories.HistoryPointRepository;
import com.vitadairy.zoo.services.DDXService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.Instant;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class UserGiftService {
    private UserGiftRepository userGiftRepository;
    private GiftRepository giftRepository;
    private GiftReservationRepository _giftReservationRepository;
    private TransactionService _transactionService;
    private com.vitadairy.zoo.repositories.UserRepository _userRepository;
    private final HistoryPointRepository historyPointRepository;
    private UserRepository _gsUserRepository;
    private final WarehouseTransactionService whTransactionService;
    private GiftExchangeService giftExchangeService;
    private ApplicationEventPublisher _eventPublisher;
    private RestApiWarehouseService restApiWarehouseService;
    private PlatformTransactionManager giftTransactionManager;
    private PlatformTransactionManager zooTransactionManager;
    private MultiTransactionHandler multiTransactionHandler;
    private final DDXService ddxService;

    public UserGiftService(UserGiftRepository userGiftRepository, GiftRepository giftRepository,
                           GiftReservationRepository giftReservationRepository,
                           TransactionService transactionService,
                           @Qualifier("zooUserRepository") com.vitadairy.zoo.repositories.UserRepository userRepository,
                           @Qualifier("zooHistoryPointRepository") HistoryPointRepository historyPointRepository,
                           @Qualifier("giftUserRepository") UserRepository gsUserRepository,
                           @Qualifier("giftTransactionManager") PlatformTransactionManager transactionManager,
                           @Qualifier("zooTransactionManager") PlatformTransactionManager zooTransactionManager,
                           MultiTransactionHandler multiTransactionHandler,
                           WarehouseTransactionService whTransactionService,
                           GiftExchangeService giftExchangeService, ApplicationEventPublisher eventPublisher,
                           RestApiWarehouseService restApiWarehouseService,
                           @Qualifier("zooDDXService") DDXService ddxService) {
        this.userGiftRepository = userGiftRepository;
        this.giftRepository = giftRepository;
        this._giftReservationRepository = giftReservationRepository;
        this._transactionService = transactionService;
        this._userRepository = userRepository;
        this.historyPointRepository = historyPointRepository;
        this._gsUserRepository = gsUserRepository;
        this.giftTransactionManager = transactionManager;
        this.zooTransactionManager = zooTransactionManager;
        this.multiTransactionHandler = multiTransactionHandler;
        this.whTransactionService = whTransactionService;
        this.giftExchangeService = giftExchangeService;
        this._eventPublisher = eventPublisher;
        this.restApiWarehouseService = restApiWarehouseService;
        this.ddxService = ddxService;
    }

    private void _validatePreOrder(GiftReservation giftReservation,
            com.vitadairy.zoo.entities.User user) {
        if (user.getGiftPoint() < giftReservation.getCoinToMinusPoint()) {
            throw new ApplicationException("Not enough gift point", VtdStatusEnum.GF_POINT);
        }

        if (!this.checkUserIsNotProcessingByDDX(user)) {
            throw new ApplicationException("Account is processing reset point", VtdStatusEnum.GF_USER);
        }
    }

    public HistoryPoint handleTransactionPreOrder(Long userId, String actionType, Float point,
            String type, UserGift userGift, Float pointsChange, Float userPoint) {
        // Create HistoryPoint
        HistoryPoint historyPoint = this._transactionService.saveHistoryPoint(userId, actionType,
                point, 0, 0, null, type, userGift, null, null, 1);

        // Update User number scan
        this._transactionService.updateNumberPointUsedInDay(userId, pointsChange);

        // Update gift point in User
        this._userRepository.updateGiftPointById(userId, userPoint);
        return historyPoint;
    }

    public boolean checkPreOrder(Long userId, Gift gift, GiftReservation giftReservation) {
        if (gift.getIsAllowReservation() == false) {
            throw new ApplicationException("Không thể đặt trước", VtdStatusEnum.GP_ACTIVE);
        }
        UserGift userGift = this.userGiftRepository.findFirstByUserIdAndGiftIdAndStatus(userId,
                gift.getId(), USER_GIFT_STATUS.PRE_ORDER);
        long countExchangedStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.EXCHANGED, userId, gift.getId());
        long countCancelStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.CANCEL, userId, gift.getId());
        long countPreOrderStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.PRE_ORDER, userId, gift.getId());
        long total = countExchangedStatus + countCancelStatus + countPreOrderStatus;
        if (total >= giftReservation.getLimitReservationTime()) {
            return false;
        }

        if (userGift == null && giftReservation.getMaximumReservationQuantity() != 0) {
            return true;
        }

        if (userGift != null || gift.getQuantity() == 0
                || giftReservation.getMaximumReservationQuantity() == 0) {
            return false;
        }
        return true;
    }

    public boolean checkAfter(Gift gift, GiftReservation giftReservation) {
        DateTimeFormatter desiredFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

        LocalDate currentDate = LocalDate.now();
        LocalDate currentDatePlusExpriedDays =
                currentDate.plusDays(giftReservation.getReservationExpiredDays());
        String formattedPlusExpriedDays = currentDatePlusExpriedDays.format(desiredFormatter);
        LocalDateTime originalDateTime =
                LocalDateTime.parse(gift.getEndDate().toString(), originalFormatter);
        String formattedEndDate = originalDateTime.format(desiredFormatter);
        int compareAfter = formattedPlusExpriedDays.compareTo(formattedEndDate);
        if (compareAfter > 0)
            return false;
        return true;
    }

    public UserGift findById(Integer id) {
        return userGiftRepository.findById(id).orElse(null);
    }

//    @Transactional("giftTransactionManager")
    public UserGift creatPreOrderUserGift(Long userId, UserGiftRequestDto dto) {
        Gift gift = this.giftRepository.findById(dto.getGiftId())
                .orElseThrow(() -> new RuntimeException("Gift not found"));
        var user = this._userRepository.getUserById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found " + userId));

        GiftReservation giftReservation =
                this._giftReservationRepository.findByGiftId(gift.getId());
        float point = (float) gift.getPoint() - giftReservation.getCoinToMinusPoint()
                - giftReservation.getDiscountReservationPoint();

        // Validate before Pre-Order
        this._validatePreOrder(giftReservation, user);

        boolean checkPreOrder = checkPreOrder(userId, gift, giftReservation);
        boolean isAfter = this.checkAfter(gift, giftReservation);
        if (checkPreOrder == false || isAfter == false) {
            throw new ApplicationException("Not pass validate preorder", VtdStatusEnum.GP_ACTIVE);
        }
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime holdingDate =
                currentTime.plusDays(giftReservation.getReservationExpiredDays());
        float totalPointBefore = user.getGiftPoint();

        this.multiTransactionHandler.initTransaction(
                this.giftTransactionManager,
                "GiftTransaction"
        );
        this.multiTransactionHandler.initTransaction(
                this.zooTransactionManager,
                "ZooTransaction"
        );
        UserGift userGift = new UserGift();
        HistoryPoint historyPoint = null;
        float userPointRevert = user.getGiftPoint();
        Float pointsChange = (float) giftReservation.getReservationPoint();
        try {
            userGift.setGift(gift);
            userGift.setQuantity(1);
            userGift.setUserId(userId);
            userGift.setReservationPoint(giftReservation.getReservationPoint());
            userGift.setPoint(point);
            userGift.setGiftSnapshot(gift);
            userGift.setGiftReservationSnapshot(giftReservation);
            userGift.setHoldingDate(Timestamp.valueOf(holdingDate));
            userGift.setPreOrderStatus(USER_GIFT_STATUS.PRE_ORDER);
            userGift.setExtendedSearch(user.getPhoneNumber());
            userGift = userGiftRepository.save(userGift);

            // Increment quantity reservation
            int rowAffected = this.giftRepository.decrementQuantity(gift.getId());
            if (rowAffected == 0) {
                throw new ApplicationException("Cannot decrement Quantity");
            }

            this.giftRepository.incrementQuantityReservation(gift.getId());

            // Handle Transaction for PreOrder
            Float userPoint = user.getGiftPoint() - giftReservation.getCoinToMinusPoint();
            historyPoint =
                    this.handleTransactionPreOrder(userId, CrmTransactionTypeCode.PRE_ORDER_GIFT,
                            (float) giftReservation.getCoinToMinusPoint(),
                            Constant.HistoryPointType.SPEND_POINT, userGift, pointsChange, userPoint);

            userGift.setTransactionCode(historyPoint.getTransactionExternalId());

            // Increment coin in gs_user
            User gsUser = this._gsUserRepository.findById(userId).orElse(null);;
            if (Objects.isNull(gsUser)) {
                User newGsUser = new User();
                newGsUser.setCoin(giftReservation.getCoinToMinusPoint());
                newGsUser.setId(userId);
                newGsUser.setGiftIds(new Integer[0]);
                this._gsUserRepository.save(newGsUser);
            } else {
                Float coin = (Objects.isNull(gsUser.getCoin()) ? 0 : gsUser.getCoin())
                        + giftReservation.getCoinToMinusPoint();
                this._gsUserRepository.updateCointById(userId, coin);
            }

            this._giftReservationRepository
                    .decrementMaximumReservationQuantity(giftReservation.getId());

            userGift = userGiftRepository.save(userGift);
            var userUpdated = this._userRepository.getUserById(userId).orElse(null);

//        DDXTrackingUserActionWithPointRequestDto dto = this.ddxService.generateDataTracking(
//                user,
//                EnumHistoryPointType.ADD_POINT,
//                EnumHistoryPointActionType.RETURN_POINT,
//                totalPointBefore,
//                userGift.getPoint(),
//                historyReturnedPoint.getTransactionExternalId(),
//                userGift.getUpdatedAt()
//        );
//        this.ddxService.trackingUserActionWithPoint(dto);

//            if (List.of(137135L, 137136L, 137138L, 137141L).contains(user.getId())) {
//                throw new ApplicationException("Test exchange fail and lose data", HttpStatus.BAD_REQUEST);
//            }

            float totalPointAfter = userUpdated.getGiftPoint();
            DDXTrackingUserActionWithPointRequestDto ddxDto = this.ddxService.generateDataTracking(
                    user,
                    EnumHistoryPointType.SPEND_POINT,
                    EnumHistoryPointActionType.PRE_ORDER_GIFT,
                    totalPointBefore,
                    totalPointAfter - totalPointBefore,
                    totalPointAfter,
                    historyPoint.getTransactionExternalId(),
                    historyPoint.getTransactionDate()
            );
            this.ddxService.trackingUserActionWithPoint(ddxDto);

            this.multiTransactionHandler.commitAll();
        } catch (Exception e) {
            HistoryPoint finalHistoryPoint = historyPoint;
            this.multiTransactionHandler.setManualRollbackAction("ZooTransaction", () -> {
                try {
                    if (finalHistoryPoint != null) {
                        this.historyPointRepository.delete(finalHistoryPoint);
                        this._transactionService.revertNumberPointUsedInDay(userId, pointsChange);
                        this._userRepository.updateGiftPointById(userId, userPointRevert);
                    }
                } catch (Exception subEx) {
                    log.error("Rollback data by delete zoo data failed : ", subEx);
                }
            });
            this.multiTransactionHandler.rollbackAll();
            log.error("creatPreOrderUserGift error: ", e);

            throw e;
        }

        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(new Long[] {userId},
                        userGift.getStatus().toString(), FeatureNoti.NOTI_PRE_ORDER_GIFT);
        this.pushNotification(userGiftNotificationRequestDto);

        return userGift;
    }

//    @Transactional("giftTransactionManager")
    public UserGift update(UpdateUserGiftRequestDto dto) {
        Long userId = dto.getUserId();
        UserGift userGiftExists = this.userGiftRepository.findByIdAndUserIdAndStatusNot(dto.getId(),
                userId, USER_GIFT_STATUS.LOCK);
        if (userGiftExists == null || userGiftExists.getStatus() == USER_GIFT_STATUS.EXCHANGED
                || userGiftExists.getStatus() == USER_GIFT_STATUS.CANCEL) {
            throw new ApplicationException("Status not valid", VtdStatusEnum.GP_ACTIVE);
        }
        var user = this._userRepository.getUserById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found " + userId));

        if (!this.checkUserIsNotProcessingByDDX(user)) {
            throw new ApplicationException("Account is processing reset point", VtdStatusEnum.GF_USER);
        }

        GiftReservation giftReservation = userGiftExists.getGiftReservationSnapshot();
        Gift gift = userGiftExists.getGiftSnapshot();

        String type = Constant.HistoryPointType.SPEND_POINT;
        String actionType = CrmTransactionTypeCode.REWARD_GIFT;
        float point = (float) userGiftExists.getGiftSnapshot().getPoint()
                - giftReservation.getCoinToMinusPoint()
                - giftReservation.getDiscountReservationPoint();

        UserGiftDynamicData userGiftDynamicData = userGiftExists.getDynamicData();
        if (Objects.isNull(userGiftDynamicData)) {
            userGiftDynamicData = new UserGiftDynamicData();
        }
        if (dto.getStatus() == USER_GIFT_STATUS.EXCHANGED) {
            int result = Float.compare(point, user.getGiftPoint());
            if (result > 0) {
                throw new ApplicationException("Not enough gift point", VtdStatusEnum.GF_POINT);
            }
            userGiftDynamicData.setExchangeDate(Instant.now().toString());
        }

        // Set expired date to user gift
        if (gift.getExpireHour() != null) {
            Instant now = Instant.now();
            Instant expiredDate = now.plusSeconds(gift.getExpireHour() * 3600);
            userGiftDynamicData.setExpiryDate(expiredDate.toString());
            // log.info("[userGiftDynamicData] {} ", userGiftDynamicData);
            userGiftExists.setDynamicData(userGiftDynamicData);
        }

        this.multiTransactionHandler.initTransaction(
                this.giftTransactionManager,
                "GiftTransaction"
        );
        this.multiTransactionHandler.initTransaction(
                this.zooTransactionManager,
                "ZooTransaction"
        );

        UserGift userGiftSaved = null;
        float userPointRevert = user.getGiftPoint();
        float pointsChange = 0.0f;
        HistoryPoint historyPoint = null;
        Boolean allowSyncToSF = Boolean.FALSE;
        try {
            float totalPointBefore = user.getGiftPoint();
            Float userPoint = user.getGiftPoint() - point;
            Float userGiftPoint = point + giftReservation.getCoinToMinusPoint();
            userGiftExists.setPoint(userGiftPoint);
            userGiftExists.setStatus(dto.getStatus());
            userGiftExists.setPreOrderStatus(dto.getStatus());
            if (userGiftExists.getStatus() == USER_GIFT_STATUS.CANCEL) {
                this.giftRepository.incrementQuantity(userGiftExists.getGift().getId(), 1);
                int rowAffected = this.giftRepository
                        .decrementQuantityReservation(userGiftExists.getGift().getId());
                if (rowAffected == 0) {
                    throw new ApplicationException("Cannot decrement quantity reservation");
                }

                this._giftReservationRepository
                        .incrementMaximumReservationQuantity(giftReservation.getId());
                type = Constant.HistoryPointType.ADD_POINT;
                actionType = CrmTransactionTypeCode.RETURN_POINT_PRE_ORD;
                userPoint = user.getGiftPoint() + giftReservation.getCoinToMinusPoint();
                point = (float) giftReservation.getCoinToMinusPoint();
            }
            pointsChange = point;
            historyPoint = this.handleTransactionPreOrder(userId, actionType, point, type,
                    userGiftExists, pointsChange, userPoint);

            // Decrement coin in gs_user
            Optional<User> gsUser = this._gsUserRepository.findById(userId);
            Float coin = 0F;
            if (gsUser == null) {
                User newUser = new User();
                newUser.setId(userId);
                newUser.setCoin(coin);
                newUser.setGiftIds(new Integer[0]); // Empty array for fav_gift_ids
                this._gsUserRepository.save(newUser);
            } else {
                coin = gsUser.get().getCoin() - giftReservation.getCoinToMinusPoint();
                if (coin <= 0) {
                    coin = 0F;
                }
                this._gsUserRepository.updateCointById(userId, coin);
            }

//            userGiftExists = userGiftRepository.save(userGiftExists);
            // Get again to have fresh value of gift_point
            var userUpdated = this._userRepository.getUserById(userId).orElse(null);

            float totalPointAfter = userUpdated.getGiftPoint();
            USER_GIFT_STATUS status = dto.getStatus();
//            if (List.of(137135L, 137136L, 137138L, 137141L).contains(user.getId())) {
//                throw new ApplicationException("Test exchange fail and lose data", HttpStatus.BAD_REQUEST);
//            }
            DDXTrackingUserActionWithPointRequestDto ddxDto = this.ddxService.generateDataTracking(
                    user,
                    USER_GIFT_STATUS.EXCHANGED == status ? EnumHistoryPointType.SPEND_POINT : EnumHistoryPointType.ADD_POINT,
                    USER_GIFT_STATUS.EXCHANGED == status ? EnumHistoryPointActionType.REWARD_GIFT : EnumHistoryPointActionType.RETURN_POINT_PRE_ORD,
                    totalPointBefore,
                    totalPointAfter - totalPointBefore,
                    totalPointAfter,
                    historyPoint.getTransactionExternalId(),
                    historyPoint.getTransactionDate()
            );
            this.ddxService.trackingUserActionWithPoint(ddxDto);

            // Sync to SF
            if (userGiftExists.getStatus() == USER_GIFT_STATUS.EXCHANGED) {
                userGiftExists.setTransactionCode(historyPoint.getTransactionExternalId());
                allowSyncToSF = Boolean.TRUE;
            }
            // log.info("[User Gift] {} ", userGiftExists);
            userGiftSaved = userGiftRepository.save(userGiftExists);

            this.multiTransactionHandler.commitAll();
        } catch (Exception e) {
            HistoryPoint finalHistoryPoint = historyPoint;
            Float finalPointsChange = pointsChange;
            this.multiTransactionHandler.setManualRollbackAction("ZooTransaction", () -> {
                try {
                    if (finalHistoryPoint != null) {
                        this.historyPointRepository.delete(finalHistoryPoint);
                        this._transactionService.revertNumberPointUsedInDay(userId, finalPointsChange);
                        this._userRepository.updateGiftPointById(userId, userPointRevert);
                    }
                } catch (Exception subEx) {
                    log.error("Rollback data by delete zoo data failed : ", subEx);
                }
            });
            this.multiTransactionHandler.rollbackAll();
            log.error("updatePreOrderUserGift error: ", e);

            throw e;
        }
        
        if (allowSyncToSF.equals(Boolean.TRUE)) {
            String codeEndPointCrm =
                    this.giftExchangeService.generateCodeEndPointExchangeGiftCrm(gift);
            var payload = new HashMap<String, Object>();
            payload = this.giftExchangeService.generatePayloadDataExchangeGiftCrm(gift, user,
                    userGiftSaved, historyPoint, userGiftSaved.getPoint());
            var batchDto = TransactionBatchDto.builder()
                    .transactions(List
                            .of(new TransactionDto().setCode(codeEndPointCrm).setPayload(payload)))
                    .build();
            whTransactionService.send(batchDto);
        }
        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(new Long[] {userId}, dto.getStatus().toString(), FeatureNoti.NOTI_PRE_ORDER_GIFT);
        this.pushNotification(userGiftNotificationRequestDto);

        return userGiftSaved;
    }

    @Transactional("giftTransactionManager")
    public Page<UserGift> getUserGiftsByUserIdAndStatuses(Long userId,
            List<UserGift.USER_GIFT_STATUS> statuses,
            List<UserGift.USER_GIFT_STATUS> preOrderStatus, String giftTypes, Long eventCode,
            Pageable pageable) {

        return this.userGiftRepository.findByUserIdAndStatusIn(userId, statuses, preOrderStatus,
                StringUtils.toStrings(giftTypes), eventCode, pageable);
    }

    public List<UserGift> getUserGiftByIds(List<Long> ids) {
        return this.userGiftRepository.findByIdIn(ids);
    }

    @Transactional(value = "giftTransactionManager", rollbackFor = Exception.class)
    public BaseResponse delete(Integer id) {
        userGiftRepository.deleteById(id);
        return EntityResponse.entityBuilder().statusCode(HttpStatus.OK).build();
    }

    // @Scheduled(fixedRate = 12 * 3600000)
    @Transactional("giftTransactionManager")
    public void updateExpiredUserGifts() {
        List<UserGift> expiredUserGifts = userGiftRepository.getExpiredUserGifts();
        userGiftRepository.updateExpiredUserGifts();
        if(CollectionUtils.isNotEmpty(expiredUserGifts)){
            for (UserGift userGift : expiredUserGifts) {
                restApiWarehouseService.updateUserGiftHistoryEvent(userGift.getTransactionCode(), USER_GIFT_STATUS.EXPIRED);
            }
        }
    }

    public UserGift updateStatus(Integer id, String status) {
        Optional<UserGift> oUserGift = userGiftRepository.findById(id);
        if (oUserGift.isPresent()) {
            UserGift userGift = oUserGift.get();
            if(USER_GIFT_STATUS.fromValue(status) == userGift.getStatus() && userGift.getStatus() == USER_GIFT_STATUS.IN_PROCESS) {
                throw new ApplicationException("User Gift status already IN PROCESS", HttpStatus.BAD_REQUEST);
            }
            userGift.setStatus(USER_GIFT_STATUS.fromValue(status));
            restApiWarehouseService.updateUserGiftHistoryEvent(userGift.getTransactionCode(), userGift.getStatus());
            return userGiftRepository.save(userGift);
        }
        return null;
    }

    // @Scheduled(fixedRate = 10 * 1000)
    @Transactional("giftTransactionManager")
    public void cancelOutOfHoldingUserGifts() {
        long total = this.userGiftRepository.countByOutOfHoldingDate();
        // log.info("[START] CANCELED USER GIFTS THAT HAVE EXPIRED DATE - TOTAL: " + total);
        Set<Long> userIds = new HashSet<>();
        int batchSize = 500;
        int page = (int) Math.ceil((double) total / batchSize);
        for (int i = 0; i < page; i++) {
            List<UserGift> userGifts =
                    this.userGiftRepository.findAllByOutOfHoldingDate(batchSize, batchSize * i);
            // log.info("CANCELED USER GIFTS THAT HAVE EXPIRED DATE - BATCH: " + i);
            for (UserGift userGift : userGifts) {
                this.processingCancel(userGift);
                userIds.add(userGift.getUserId());
            }
        }

        Long[] uniqueUserIds = userIds.toArray(new Long[0]);
        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(uniqueUserIds, "SYSTEM_CANCEL", FeatureNoti.NOTI_CANCEL_GIFT);
        this.pushNotification(userGiftNotificationRequestDto);
        log.info("[SUCCESSFULLY] CANCELED USER GIFTS THAT HAVE EXPIRED DATE");
    }

    public Boolean reuseUserGift(UserGiftReuseRequestDto body) {
        com.vitadairy.zoo.entities.User user =
                this._userRepository.findById(body.getUserId()).orElse(null);
        UserGift userGift = this.userGiftRepository.findById(body.getUserGiftId()).orElse(null);

        this.validateReuseUserGiftRequest(user, userGift);

        HistoryPoint historyPoint = this.historyPointRepository
                .findFirstByGiftIdOrderByTransactionDateDesc(userGift.getId()).orElse(null);
        if (Objects.isNull(historyPoint)) {
            throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
        }

        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setIsReusing(Boolean.TRUE);
        userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepository.save(userGift);

        this.giftExchangeService.handleUseGift(user, userGift, historyPoint,
                GiftFunctionDefs.USING_GIFT, body.getTransactionDate());
        userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setIsReusing(Boolean.FALSE);
        userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepository.save(userGift);

        return true;
    }

    public List<UserGift> getUserGiftPreOrderSendNotifyEnoughtPoint(
            UserGiftPreOrderSendNotifyEnoughtPointRequestDto dto) {
        if (Objects.isNull(dto.getUserTotalPoint())) {
            throw new ApplicationException("Data invalid", HttpStatus.BAD_REQUEST);
        }

        List<UserGift> userGifts =
                this.userGiftRepository.getFirstPreOrderUserGiftNotPushNotification(dto.getUserId(),
                        dto.getUserTotalPoint());
        if (Objects.isNull(userGifts) || userGifts.isEmpty()) {
            // throw new ApplicationException("Not found", HttpStatus.NOT_FOUND);
            if (Objects.isNull(userGifts)) {
                userGifts = new ArrayList<UserGift>();
            }
            return userGifts;
        }
        // userGift = userGifts.get(0);
        userGifts = userGifts.stream().map(userGift -> {
            UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
            if (Objects.isNull(userGiftDynamicData)) {
                userGiftDynamicData = new UserGiftDynamicData();
            }
            userGiftDynamicData.setPushedNotifyEnoughtPointToFinishPreOrder(Boolean.TRUE);
            userGift.setDynamicData(userGiftDynamicData);

            return userGift;
        }).toList();
        this.userGiftRepository.saveAll(userGifts);

        return userGifts;
    }

    @Transactional("giftTransactionManager")
    private void processingCancel(UserGift userGift) {
        // log.info("[START] CANCELED - ID: " + userGift.getId());
        long userId = userGift.getUserId();
        GiftReservation giftReservation = userGift.getGiftReservationSnapshot();
        var user = this._userRepository.getUserById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found " + userId));
        userGift.setStatus(USER_GIFT_STATUS.CANCEL);
        userGift.setPreOrderStatus(USER_GIFT_STATUS.CANCEL);

        this._giftReservationRepository
                .incrementMaximumReservationQuantity(giftReservation.getId());
        String type = Constant.HistoryPointType.ADD_POINT;
        String actionType = CrmTransactionTypeCode.RETURN_POINT;
        Float userPoint = user.getGiftPoint() + giftReservation.getCoinToMinusPoint();
        Float point = (float) giftReservation.getCoinToMinusPoint();

        // HistoryPoint historyPoint =
        this.handleTransactionPreOrder(userId, actionType, point, type, userGift, point, userPoint);

        Optional<User> gsUser = this._gsUserRepository.findById(userId);
        Float coin = gsUser.get().getCoin() - giftReservation.getCoinToMinusPoint();
        if (coin <= 0) {
            coin = 0F;
        }
        this._gsUserRepository.updateCointById(userId, coin);
        userGiftRepository.save(userGift);

        int rowAffected = this.giftRepository
                .increaseQuantityAndDecreaseQuantityReservation(userGift.getGift().getId());
        if (rowAffected == 0) {
            Optional<Gift> giftOptional = this.giftRepository.findById(userGift.getGift().getId());
            if (giftOptional.isPresent()) {
                Gift gift = giftOptional.get();
                if (gift.getQuantityReservation() < 0) {
                    gift.setQuantityReservation(0);
                }
                this.giftRepository.save(gift);
            }
        }
    }

    public boolean pushNotification(UserGiftNotificationRequestDto dto) {
        this._eventPublisher.publishEvent(dto);
        return true;
    }

    private void validateReuseUserGiftRequest(com.vitadairy.zoo.entities.User user,
            UserGift userGift) {
        if (Objects.isNull(user) || Objects.isNull(userGift)) {
            throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
        }
        if (!UserGift.USER_GIFT_STATUS.NEED_REUSE.equals(userGift.getStatus())) {
            throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
        }
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        Boolean isReusing = userGiftDynamicData.getIsReusing();
        if (Objects.nonNull(isReusing) && Boolean.TRUE.equals(isReusing)) {
            throw new ApplicationException("User gift is processing", HttpStatus.BAD_REQUEST);
        }
        Gift gift = userGift.getGift();
        if (Objects.isNull(gift)) {
            throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
        }
        if (!GiftTypeEnum.E_VOUCHER.equals(gift.getType())
                && !GiftTypeEnum.E_VOUCHER_SHOP.equals(gift.getType())
                && !GiftTypeEnum.E_VOUCHER_SHOP_BKIDS.equals(gift.getType())) {
            throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
        }
        if (!user.getId().equals(userGift.getUserId())) {
            throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
        }
    }

    public void remindLastHoldingDate() {
        Set<Long> userIds = new HashSet<>();
        // Remind Holding Date
        List<UserGift> userGiftList = this.userGiftRepository.findUserGiftsByHoldingDateMinusOne();
        for (UserGift userGift : userGiftList) {
            userIds.add(userGift.getUserId());
        }

        Long[] uniqueUserIds = userIds.toArray(new Long[0]);
        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(uniqueUserIds, "LASTDAY_OUTDATE", FeatureNoti.NOTI_PRE_ORDER_GIFT);
        this.pushNotification(userGiftNotificationRequestDto);
    }

    private boolean checkUserIsNotProcessingByDDX(com.vitadairy.zoo.entities.User user) {
        if (Objects.isNull(user)) {
            return false;
        }

        return user.checkUserIsNotProcessingByDDX();
    }
}
