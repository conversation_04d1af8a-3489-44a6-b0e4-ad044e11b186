package com.vitadairy.gift.features.userGift.controllers;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.enums.AudienceEnum;
import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.gift.features.userGift.dto.request.ExportUserGiftRequest;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.features.userGift.dto.response.AdminFinishUserGiftEvoucherNeedReuseResponse;
import com.vitadairy.gift.features.userGift.dto.response.AdminProcessUserGiftEvoucherNeedReuseResponse;
import com.vitadairy.gift.features.userGift.dto.response.UserGiftPageResponse;
import com.vitadairy.gift.features.userGift.services.UserGiftAdminService;
import com.vitadairy.gift.schedulers.UserGiftNotification;
import com.vitadairy.main.configs.ApplicationProperties;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.iresponse.BaseResponse;
import com.vitadairy.main.response.EntityResponse;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("v4/gs/admin/user-gifts")
@AllArgsConstructor
public class UserGiftAdminController {
    private final ResponseFactory responseFactory;
    private UserGiftAdminService userGiftAdminService;

    private final ApplicationProperties applicationProperties;
    private UserGiftNotification userGiftNotification;

    @GetMapping("/job")
    public ResponseEntity<?> job() {
        userGiftNotification.run();
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-user-gift', T(java.util.List).of('full-access', 'read'))")
    @GetMapping()
    public ResponseEntity<UserGiftPageResponse> getUserGifts(
            @RequestParam(required = false) List<UserGift.USER_GIFT_STATUS> statuses,
            @RequestParam(required = false) List<UserGift.USER_GIFT_STATUS> preOrderStatus,
            @RequestParam(required = false) String giftTypes,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<UserGift> userGifts = this.userGiftAdminService.getUserGifts(statuses, preOrderStatus,
                giftTypes, pageable);
//        List<UserGiftDto> result =
//                userGifts.getContent().stream().map(UserGiftDto::fromEntity).toList();
        List<UserGiftDto> result =
                userGifts.getContent()
                        .stream()
                        .map(entity -> UserGiftDto.fromEntity(entity, AudienceEnum.ADMIN))
                        .toList();
        UserGiftPageResponse userGiftPageResponse = new UserGiftPageResponse(result,
                (long) userGifts.getTotalElements(), page, size, HttpStatus.OK, "success");
        return responseFactory.successDto(userGiftPageResponse);
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-user-gift', T(java.util.List).of('full-access', 'read'))")
    @GetMapping("/list")
    public ResponseEntity<UserGiftPageResponse> getListUserGifts(
            @ModelAttribute UserGiftListRequest userGiftListRequest) {
        Page<UserGift> userGifts = this.userGiftAdminService.getUserGifts(userGiftListRequest);
//        List<UserGiftDto> result =
//                userGifts.getContent().stream().map(UserGiftDto::fromEntity).toList();
        List<UserGiftDto> result =
                userGifts.getContent()
                        .stream()
                        .map(entity -> UserGiftDto.fromEntity(entity, AudienceEnum.ADMIN))
                        .toList();
        UserGiftPageResponse userGiftPageResponse = new UserGiftPageResponse(result,
                (long) userGifts.getTotalElements(), userGiftListRequest.getPage(),
                userGiftListRequest.getSize(), HttpStatus.OK, "success");
        return responseFactory.successDto(userGiftPageResponse);
    }

    @PostMapping("/export")
    public ResponseEntity<EntityResponse<Boolean>> exportUserGift(@Valid @RequestBody ExportUserGiftRequest request) {
        try {
            request.doValidate();
            var fetchRequest = request.toFetchRequest();
            fetchRequest.getRequest().setSize(applicationProperties.getExportConfig().getBatchSize());
            fetchRequest.setPageable(new com.vitadairy.libraries.importexport.dto.Page(0, applicationProperties.getExportConfig().getBatchSize()));
            var res = userGiftAdminService.createExportHistory(fetchRequest);
            return responseFactory.successDto(new EntityResponse<>(res, HttpStatus.OK, "success"));
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    @PostMapping("/need-reuse/reuse")
    public AdminProcessUserGiftEvoucherNeedReuseResponse processUserGiftEvoucherNeedReuse(@Valid @RequestBody List<Long> userGiftIds) {
        AdminProcessUserGiftEvoucherNeedReuseResponse response = this.userGiftAdminService.processUserGiftEvoucherNeedReuse(userGiftIds);

        return response;
    }

    @PostMapping("/need-reuse/finish")
    public AdminFinishUserGiftEvoucherNeedReuseResponse finishUserGiftEvoucherNeedReuse(@Valid @RequestBody List<Long> userGiftIds) {
        AdminFinishUserGiftEvoucherNeedReuseResponse response = this.userGiftAdminService.finishUserGiftEvoucherNeedReuse(userGiftIds);

        return response;
    }
}
