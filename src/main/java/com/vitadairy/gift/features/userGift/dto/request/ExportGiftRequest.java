package com.vitadairy.gift.features.userGift.dto.request;

import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.main.request.ExportRequest;

public class ExportGiftRequest extends ExportRequest<ListGiftRequestDto, Integer> {
    @Override
    public void doValidate() throws Exception {
        super.doValidate();
    }

    @Override
    public FetchRequest<ListGiftRequestDto> toFetchRequest() {
        return super.toFetchRequest();
    }
}
