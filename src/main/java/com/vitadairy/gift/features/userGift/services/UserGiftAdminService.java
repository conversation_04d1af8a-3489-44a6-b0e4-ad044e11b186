package com.vitadairy.gift.features.userGift.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.constants.ExportType;
import com.vitadairy.gift.entities.ExportHistory;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.request.UserGiftReuseRequestDto;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherService;
import com.vitadairy.gift.features.gifts.services.GiftExchangeService;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.features.userGift.dto.response.AdminFinishUserGiftEvoucherNeedReuseResponse;
import com.vitadairy.gift.features.userGift.dto.response.AdminProcessUserGiftEvoucherNeedReuseResponse;
import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.dto.TransactionDto;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.repositories.GotItTransactionLogRepository;
import com.vitadairy.zoo.repositories.HistoryPointRepository;
import com.vitadairy.zoo.repositories.UserRepository;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.repositories.ExportHistoryRepository;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.gift.utils.StringUtils;
import com.vitadairy.libraries.importexport.dto.FetchRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserGiftAdminService {
    private UserGiftRepository userGiftRepository;

    private UserRepository userRepo;

    private HistoryPointRepository historyPointRepo;

    private UserGiftService userGiftService;

    private GotItTransactionLogRepository gotItTransactionLogRepo;

    private GiftExchangeService giftExchangeService;

    private WarehouseTransactionService whTransactionService;

    private ObjectMapper objectMapper;

    private IEVoucherService eVoucherService;

    private TransactionTemplate giftTransactionTemplate;

    private ExportHistoryRepository exportHistoryRepository;

    public UserGiftAdminService(
            UserGiftRepository userGiftRepository,
            UserRepository userRepo,
            HistoryPointRepository historyPointRepo,
            UserGiftService userGiftService,
            GotItTransactionLogRepository gotItTransactionLogRepo,
            ObjectMapper objectMapper,
            GiftExchangeService giftExchangeService,
            WarehouseTransactionService whTransactionService,
            @Qualifier("eVoucherServiceImpl") IEVoucherService eVoucherService,
            @Qualifier("giftTransactionManager") PlatformTransactionManager giftTransactionManager,
            ExportHistoryRepository exportHistoryRepository
    ) {
        this.userGiftRepository = userGiftRepository;
        this.userRepo = userRepo;
        this.historyPointRepo = historyPointRepo;
        this.userGiftService = userGiftService;
        this.gotItTransactionLogRepo = gotItTransactionLogRepo;
        this.objectMapper = objectMapper;
        this.giftExchangeService = giftExchangeService;
        this.whTransactionService = whTransactionService;
        this.eVoucherService = eVoucherService;
        this.giftTransactionTemplate = new TransactionTemplate(giftTransactionManager);
        this.exportHistoryRepository = exportHistoryRepository;
    }

    @Transactional("giftTransactionManager")
    public Page<UserGift> getUserGifts(List<UserGift.USER_GIFT_STATUS> statuses,
            List<UserGift.USER_GIFT_STATUS> preOrderStatus, String giftTypes, Pageable pageable) {
        Page<UserGift> ug = this.userGiftRepository.findByStatusAndGiftType(statuses,
                preOrderStatus, StringUtils.toStrings(giftTypes), pageable);
        return ug;
    }

    public Page<UserGift> getUserGifts(UserGiftListRequest request) {
        request.removeEmptyVariable();
        List<String> preOrderStatuses = request.getListPreOrderStatus();
        if (CollectionUtils.isNotEmpty(preOrderStatuses)) {
            return this.userGiftRepository.getAdminListOnlyForPreOrderUserGift(request.getUserId(), request.getGiftId(),
                    request.getExtendedSearch(), request.getListStatuses(), request.getListGiftTypes(),
                    request.getListPreOrderStatus(), request.getFromDate(), request.getToDate(),
                    request.getPageable());
        } else {
            return this.userGiftRepository.getAdminList(request.getUserId(), request.getGiftId(),
                    request.getExtendedSearch(), request.getListStatuses(), request.getListGiftTypes(),
                    request.getListPreOrderStatus(), request.getFromDate(), request.getToDate(),
                    request.getPageable());
        }
    }

    public AdminProcessUserGiftEvoucherNeedReuseResponse processUserGiftEvoucherNeedReuse(
            List<Long> userGiftIds) {
        AdminProcessUserGiftEvoucherNeedReuseResponse response =
                new AdminProcessUserGiftEvoucherNeedReuseResponse();

        List<String> results = userGiftIds.stream().map(userGiftId -> {
            String status = "ok";
            String markedStr =
                    String.format("process evoucher need reuse user gift id: %d", userGiftId);
            log.info("{}. Begin process", markedStr);
            try {
                UserGift userGift =
                        this.userGiftRepository.findById(Math.toIntExact(userGiftId)).orElse(null);
                if (Objects.isNull(userGift)) {
                    throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                }
                UserGiftReuseRequestDto dto = new UserGiftReuseRequestDto();
                dto.setUserGiftId(Math.toIntExact(userGift.getId()));
                dto.setUserId(userGift.getUserId());
                this.userGiftService.reuseUserGift(dto);
            } catch (Exception e) {
                if (e instanceof ApplicationException) {
                    status = ((ApplicationException) e).getMsg();
                } else {
                    status = e.getMessage();
                }
                log.error("{}. ERROR: ", markedStr, e);
            }
            log.info("{}. End process", markedStr);
            return String.format("User gift id %d processed. Status: %s", userGiftId, status);
        }).collect(Collectors.toList());
        response.setResults(results);

        // throw new RestApiException(StatusCode.INVALID_PARAMS);

        return response;
    }

    public AdminFinishUserGiftEvoucherNeedReuseResponse finishUserGiftEvoucherNeedReuse(
            List<Long> userGiftIds) {
        AdminFinishUserGiftEvoucherNeedReuseResponse response =
                new AdminFinishUserGiftEvoucherNeedReuseResponse();

        List<String> results = userGiftIds.stream().map(userGiftId -> {
            String status = "ok";
            String markedStr =
                    String.format("finish evoucher need reuse user gift id: %d", userGiftId);
            log.info("{}. Begin process", markedStr);
            try {
                var isOk = this.giftTransactionTemplate.execute(dbTransStatus -> {
                    UserGift userGift =
                            this.userGiftRepository.findById(Math.toIntExact(userGiftId)).orElse(null);
                    if (Objects.isNull(userGift)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    if (!UserGift.USER_GIFT_STATUS.NEED_REUSE.equals(
                            userGift.getStatus())) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    Gift gift = userGift.getGift();
                    if (Objects.isNull(gift)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    if (!GiftTypeEnum.E_VOUCHER.equals(gift.getType())) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
                    String voucherRefId = userGiftDynamicData.getVoucherRefId();
                    if (Objects.isNull(voucherRefId)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    /*GotItTransactionLog gotItTransactionLog = this.gotItTransactionLogRepo.findByRefId(voucherRefId).orElse(null);
                    if (Objects.isNull(gotItTransactionLog)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }*/
                    User user = this.userRepo.findById(userGift.getUserId()).orElse(null);
                    if (Objects.isNull(user)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    List<HistoryPoint> historyPoints = this.historyPointRepo.findByGiftId(userGift.getId());
                    if (Objects.isNull(historyPoints) || historyPoints.isEmpty()) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    HistoryPoint historyPoint = historyPoints.get(0);
                    /*TopupResponseV1Dot4 topupResponse = this.objectMapper.convertValue(gotItTransactionLog.getResponse(), TopupResponseV1Dot4.class);
                    if (Objects.isNull(topupResponse)) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }
                    if (
                            !topupResponse.getStatus().equals(1) &&
                                    !topupResponse.getStatus().equals(8) &&
                                    !topupResponse.getStatus().equals(10)
                    ) {
                        throw new ApplicationException("User gift invalid", HttpStatus.BAD_REQUEST);
                    }*/
                    Object responseCheckTopup = this.eVoucherService.checkTopupTransaction(
                            user,
                            voucherRefId,
                            historyPoint
                    );
                    String activeService = this.eVoucherService.getActiveService();
                    switch (activeService) {
                        case Constant.EvoucherService.GOT_IT -> {
                            TopupResponseV1Dot4 topupResponse = (TopupResponseV1Dot4) this.eVoucherService.processTopupResponse(
                                    userGift,
                                    responseCheckTopup
                            );
                            if (Objects.isNull(topupResponse)) {
                                // Stop flow check in next time
                                throw new ApplicationException("Gotit topup status transaction invalid", HttpStatus.BAD_REQUEST);
                            }
                        }
                        default -> {
                            // Keep flow check in next time
                            throw new ApplicationException("Active topup service null");
                        }
                    }
                    /*switch (topupResponse.getStatus()) {
                        case 1 -> {
                            userGift.setStatus(UserGift.USER_GIFT_STATUS.TOPUP);
                        }
                        case 8, 10 -> {
                            userGift.setStatus(UserGift.USER_GIFT_STATUS.USED);
                            userGiftDynamicData.setVoucherCode(topupResponse.getCardInfo().getCardNumber());
                            userGiftDynamicData.setVoucherLink(topupResponse.getLink());
                        }
                    }*/
                    userGiftDynamicData.setIsReusing(Boolean.FALSE);
                    userGift.setDynamicData(userGiftDynamicData);
                    this.userGiftRepository.save(userGift);
                    var payload = new HashMap<String, Object>();
                    payload = this.giftExchangeService.generatePayloadDataUsingGiftCrm(
                            gift,
                            user,
                            userGift,
                            historyPoint,
                            null);
                    String codeEndPointCrm = this.giftExchangeService.generateCodeEndPointUsingGiftCrm(gift);
                    Boolean whIsOk = Boolean.FALSE;
                    if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                        var batchDto = TransactionBatchDto.builder().transactions(List.of(
                                new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                        whIsOk = whTransactionService.send(batchDto);
                    }

                    return Boolean.TRUE;
                });
            } catch (Exception e) {
                if (e instanceof ApplicationException) {
                    status = ((ApplicationException) e).getMsg();
                } else {
                    status = e.getMessage();
                }
                log.error("{}. ERROR: ", markedStr, e);
            }
            log.info("{}. End process", markedStr);
            return String.format("User gift id %d processed. Status: %s", userGiftId, status);
        }).collect(Collectors.toList());
        response.setResults(results);

        // throw new RestApiException(StatusCode.INVALID_PARAMS);

        return response;
    }

    public Boolean createExportHistory(UserGiftListRequest request) throws JsonProcessingException {
        var exportHistory = new ExportHistory();
        exportHistory.setExportType(ExportType.USER_GIFT);
        exportHistory.setStatus(ExportStatus.PENDING);
        exportHistory.setFilter(objectMapper.writeValueAsString(request));
        exportHistoryRepository.save(exportHistory);
        return true;
    }
}
