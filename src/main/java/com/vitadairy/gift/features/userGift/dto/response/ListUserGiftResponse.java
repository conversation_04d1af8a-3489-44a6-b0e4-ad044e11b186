package com.vitadairy.gift.features.userGift.dto.response;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.main.response.EntityResponse;

public class ListUserGiftResponse extends EntityResponse<List<UserGiftDto>> {
    public ListUserGiftResponse(List<UserGiftDto> data, HttpStatus httpStatus, String message) {
        super(data, HttpStatusCode.valueOf(httpStatus.value()), message);
    }
}
