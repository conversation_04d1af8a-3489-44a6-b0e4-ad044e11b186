package com.vitadairy.gift.features.importgift.controllers;

import com.vitadairy.gift.common.ImportTypeDefs;
import com.vitadairy.gift.features.importgift.services.ImportGiftService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.response.ImportResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("v4/gs/gifts/import")
@Slf4j
public class ImportGsGiftController {

    private final ResponseFactory responseFactory;
    private final ImportGiftService importGiftService;

    @PostMapping("create")
    public ResponseEntity<ImportResponse> importGift(@RequestParam("file") MultipartFile file,
                                                     RedirectAttributes redirectAttributes) {
        try {
            redirectAttributes.addFlashAttribute("message",
                    "You successfully uploaded " + file.getOriginalFilename() + "!");
            ImportResponse response = importGiftService.doImport(file, ImportTypeDefs.IMPORT_GIFT);
            return responseFactory.successDto(response);
        } catch (Exception ex) {
            log.error("Error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }
}
