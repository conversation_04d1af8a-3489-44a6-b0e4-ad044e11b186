package com.vitadairy.gift.features.importgift.services;

import com.vitadairy.gift.common.ImportTypeDefs;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.libraries.importexport.service.ProcessImportFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProcessImportGiftStrategy {
    private final ProcessImportFileService<Gift> giftProcessImportFileService;

    public ProcessImportFileService<?> getProcessImportFileService(String type) {
        if (type == null) {
            return null;
        }
        switch (type) {
            case ImportTypeDefs.IMPORT_GIFT:
                return giftProcessImportFileService;
            default:
                return null;
        }
    }

}
