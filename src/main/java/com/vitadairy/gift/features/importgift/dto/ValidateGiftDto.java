package com.vitadairy.gift.features.importgift.dto;

import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ValidateGiftDto {
    @NotBlank
    private GiftTypeEnum type;
    @NotBlank
    private String categoryCode;
    @NotBlank
    private String name;
    @NotNull
    private List<String> images;
    @NotNull
    private List<String> tierCodes;
    @NotNull
    private Integer point;
    @NotNull
    private BigInteger price;
    @NotNull
    private Timestamp startDate;
    @NotNull
    private Timestamp endDate;
    @NotNull
    private Timestamp expireDate;
    @NotBlank
    private String sctNumber;
    private List<String> hiddenTags;
    private DynamicDataDto dynamicData;

}

