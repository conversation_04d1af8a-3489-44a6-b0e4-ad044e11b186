package com.vitadairy.gift.features.constants.dto;

import java.time.Instant;
import java.util.Objects;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Constant.ConstantDynamicData;
import com.vitadairy.main.dto.BaseDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder.Default;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class BaseConstantDto extends BaseDto {
    @Builder(builderMethodName = "baseBuilder")
    public BaseConstantDto(Integer id, String code, Boolean isActive, Integer priority,
                           ConstantDynamicDataDto dynamicData, Instant createdAt, Instant updatedAt) {
        setId(id);
        setCode(code);
        setCreatedAt(createdAt);
        setDynamicData(dynamicData);
        setIsActive(isActive);
        setPriority(priority);
        setUpdatedAt(updatedAt);
    }

    private Integer id;
    private String code;
    @Default
    private Boolean isActive = false;
    private Integer priority;
    private ConstantDynamicDataDto dynamicData;

    public static BaseConstantDto fromEntity(Constant constant) {
        ConstantDynamicData dynamicData = constant.getDynamicData();
        return BaseConstantDto.baseBuilder()
                .code(constant.getValue())
                .isActive(constant.getIsActive())
                .priority(constant.getPriority())

                .updatedAt(constant.getUpdatedAt())
                .createdAt(constant.getCreatedAt())
                .dynamicData(ConstantDynamicDataDto
                        .builder()
                        .name(Objects.isNull(dynamicData) ? null : dynamicData.getName())
                        .description(Objects.isNull(dynamicData) ? null : dynamicData.getDescription())
                        .icon(Objects.isNull(dynamicData) ? null : dynamicData.getIcon())
                        .label(Objects.isNull(dynamicData) ? null : dynamicData.getLabel())
                        .labelCode(Objects.isNull(dynamicData) ? null : dynamicData.getLabelCode())
                        .color(Objects.isNull(dynamicData) ? null : dynamicData.getColor())
                        .updatedById(Objects.isNull(dynamicData) ? null : dynamicData.getUpdatedById())
                        .build())
                .build();
    }
}
