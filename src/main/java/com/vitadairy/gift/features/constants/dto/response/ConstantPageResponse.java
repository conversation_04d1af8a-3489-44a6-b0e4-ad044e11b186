package com.vitadairy.gift.features.constants.dto.response;

import java.util.List;

import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.constants.dto.BaseConstantDto;
import com.vitadairy.main.response.PageableResponse;

import lombok.Builder;

public class ConstantPageResponse extends PageableResponse<BaseConstantDto> {
    @Builder(builderMethodName = "constantListBuilder")
    public ConstantPageResponse(List<BaseConstantDto> items, Long total, Integer page, Integer pageSize,
            HttpStatusCode statusCode, String msg) {
        super(items, total, total, page, pageSize, statusCode, msg);
    }
}
