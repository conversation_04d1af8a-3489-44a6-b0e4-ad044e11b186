package com.vitadairy.gift.features.constants.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ConstantDynamicDataRequestDto {
    private String description;
    @NotNull
    private String name;
    private Integer updatedById;
    private String label;
    private String labelCode;
    private String color;
    private String icon;
}
