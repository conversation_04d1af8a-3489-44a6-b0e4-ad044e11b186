package com.vitadairy.gift.features.constants.dto.response;

import java.util.Map;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ConstantRequestDto {
    private String keyAlpha = "gift";

    @NotBlank
    private String keyBeta;

    private String keyGamma;

    private String keyDelta;

    @NotBlank
    private String value;

    // @Enumerated(EnumType.STRING)
    // private ConstantTypeEnum type = ConstantTypeEnum.CATEGORY;

    @NotNull
    private Boolean isActive = false;

    private Integer priority;

    private Map<String, Object> dynamicData;

}