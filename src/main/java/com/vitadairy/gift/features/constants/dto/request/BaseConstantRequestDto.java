package com.vitadairy.gift.features.constants.dto.request;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Constant.ConstantDynamicData;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder.Default;

import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class BaseConstantRequestDto {

    @NotNull
    private String code;
    @Default
    private Boolean isActive = false;
    private Integer priority;
    @NotNull
    private ConstantDynamicDataRequestDto dynamicData;

    public static Constant toEntity(BaseConstantRequestDto dto) {
        ConstantDynamicDataRequestDto dynamicData = dto.getDynamicData();
        return Constant.builder()
                .keyAlpha(ConstantFeatureEnum.GIFT.toString())
                .value(dto.getCode())
                .isActive(dto.getIsActive())
                .priority(dto.getPriority())
                .dynamicData(ConstantDynamicData.builder()
                        .name(Objects.isNull(dynamicData) ? null : dynamicData.getName())
                        .description(Objects.isNull(dynamicData) ? null : dynamicData.getDescription())
                        .updatedById(Objects.isNull(dynamicData) ? null : dynamicData.getUpdatedById())
                        .label(Objects.isNull(dynamicData) ? null : dynamicData.getLabel())
                        .labelCode(Objects.isNull(dynamicData) ? null : dynamicData.getLabelCode())
                        .icon(Objects.isNull(dynamicData) ? null : dynamicData.getIcon())
                        .color(Objects.isNull(dynamicData) ? null : dynamicData.getColor())
                        .build())
                .build();
    }

}
