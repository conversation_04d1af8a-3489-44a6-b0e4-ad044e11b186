package com.vitadairy.gift.features.constants.dto.response;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.constants.dto.BaseConstantDto;
import com.vitadairy.main.response.EntityResponse;

public class ConstantResponse extends EntityResponse<BaseConstantDto> {
    public ConstantResponse(BaseConstantDto data, HttpStatus status, String message) {
        super(data, HttpStatusCode.valueOf(status.value()), message);
    }
}
