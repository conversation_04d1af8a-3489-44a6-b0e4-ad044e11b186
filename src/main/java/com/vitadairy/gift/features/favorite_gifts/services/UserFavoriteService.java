package com.vitadairy.gift.features.favorite_gifts.services;

import com.vitadairy.gift.common.UserFavoriteConstant;
import com.vitadairy.gift.constants.ErrorCodeConstant;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.favorite_gifts.dto.request.ToggleFavoriteGiftRequestDto;
import com.vitadairy.gift.features.favorite_gifts.enums.ToggleEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.request.ListFavouriteGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.ListGiftResponse;
import com.vitadairy.gift.features.gifts.services.GiftCacheService;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.gift.repositories.UserRepository;
import com.vitadairy.main.exception.ResourceException;
import com.vitadairy.zoo.dto.UserDto;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("UserFavoriteService")
public class UserFavoriteService {
    private UserRepository userRepository;
    private GiftService giftService;
    private GiftCacheService giftCacheService;

    public UserFavoriteService(UserRepository userRepository,
            GiftService giftService,
                               GiftCacheService giftCacheService) {
        this.userRepository = userRepository;
        this.giftService = giftService;
        this.giftCacheService = giftCacheService;
    }

    public List<GiftDto> getFavoriteGifts(Long userId) {
        User userFavorite = getUserFavorite(userId);
        com.vitadairy.zoo.entities.User currentUser = giftService.getUserFromAuthToken();
        if (!Objects.isNull(userFavorite)) {

            var dto = new ListGiftRequestDto();
            dto.setIds(userFavorite.getGiftIds());
            dto.setSize(UserFavoriteConstant.MAX_GIFT);
            dto.setStatus(GiftStatusEnum.ENABLED);
            dto.setIsCustomer(true);
            dto.setTierCodes(currentUser.getTierCode());
            var data = giftCacheService.pageableSpecification(dto);

            List<GiftDto> sortedGift = new ArrayList<>();
            for (Integer giftId : userFavorite.getGiftIds()) {
                GiftDto found = data.getGifts()
                        .stream()
                        .filter(gift -> gift.getId().equals(giftId)).findAny()
                        .orElse(null);
                if (!Objects.isNull(found)) {
                    sortedGift.add(found);
                }
            }
            return sortedGift;
        }
        return new ArrayList<>();
    }

    public ListGiftResponse getCurrentUserFavorite(ListFavouriteGiftRequestDto request) {
        UserDto currentUser = giftService.getUserDtoFromAuthToken();
        if (!Objects.isNull(currentUser)) {
            User userFavorite = getUserFavorite(currentUser.getUserId());
            if(Objects.isNull(userFavorite)
                    || Objects.isNull(userFavorite.getGiftIds())
                    || userFavorite.getGiftIds().length == 0) {
                return new ListGiftResponse(new ArrayList<>(), 0, 0);
            }
            var dto = new ListGiftRequestDto();
            dto.setPage(request.getPage());
            dto.setSize(request.getSize());
            dto.setIds(userFavorite.getGiftIds());
            dto.setStatus(GiftStatusEnum.ENABLED);
            dto.setIsCustomer(true);
            dto.setTierCodes(currentUser.getTierCode());
            return giftCacheService.pageableSpecification(dto);
        }
        return null;
    }

    public User getUserFavorite(Long userId) {
        return userRepository.findById(userId).orElse(null);
    }

    public void create(Integer giftId, Long userId) {
        User foundUserFavorite = this.getUserFavorite(userId);
        if (null == foundUserFavorite) {
            userRepository.save(User
                    .builder()
                    .id(userId)
                    .giftIds(new Integer[] { giftId })
                    .coin(0F)
                    .build());
        } else {
            userRepository.addGift(giftId, userId);
        }
    }

    public void remove(Integer giftId, Long userId) {
        userRepository.removeGift(giftId, userId);
    }

    public void validateToggleGift(ToggleFavoriteGiftRequestDto dto, Long userId) {
        Integer totalGift = userRepository.totalGiftByUserId(userId);
        if (dto.getAction().equals(ToggleEnum.ADD) && null != totalGift
                && totalGift >= UserFavoriteConstant.MAX_GIFT) {
            throw new ResourceException(ErrorCodeConstant.REACT_MAX_GIFT, HttpStatus.BAD_REQUEST);
        }
    }

}
