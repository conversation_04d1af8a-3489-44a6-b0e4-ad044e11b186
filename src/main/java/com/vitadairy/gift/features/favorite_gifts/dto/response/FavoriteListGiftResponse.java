package com.vitadairy.gift.features.favorite_gifts.dto.response;

import java.util.List;

import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.main.response.EntityResponse;

public class FavoriteListGiftResponse extends EntityResponse<List<GiftDto>> {
    public FavoriteListGiftResponse(List<GiftDto> data, HttpStatusCode statusCode, String message) {
        super(data, statusCode, message);
    }
}
