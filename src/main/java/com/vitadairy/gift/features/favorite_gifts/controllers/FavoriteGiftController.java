package com.vitadairy.gift.features.favorite_gifts.controllers;

import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.features.favorite_gifts.dto.request.ToggleFavoriteGiftRequestDto;
import com.vitadairy.gift.features.favorite_gifts.dto.response.FavoriteGiftIdsResponse;
import com.vitadairy.gift.features.favorite_gifts.enums.ToggleEnum;
import com.vitadairy.gift.features.favorite_gifts.services.UserFavoriteService;
import com.vitadairy.gift.features.gifts.dto.request.ListFavouriteGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.GiftPageResponse;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.helper.AuthenticationHelper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping("v4/gs/favorite-gifts")
public class FavoriteGiftController {
    private UserFavoriteService favoriteGiftService;
    private final ResponseFactory responseFactory;

    public FavoriteGiftController(UserFavoriteService favoriteGiftService, ResponseFactory responseFactory) {
        this.favoriteGiftService = favoriteGiftService;
        this.responseFactory = responseFactory;
    }

    @PreAuthorize("hasAuthority('USER')")
    @GetMapping("gifts")
    public ResponseEntity<GiftPageResponse> listGifts(@ModelAttribute ListFavouriteGiftRequestDto request) {
        var gifts = favoriteGiftService.getCurrentUserFavorite(request);
        GiftPageResponse response = new GiftPageResponse(gifts.getGifts(), gifts.getTotalElements(), gifts.getTotalPages(),
                request.getPage(), request.getSize(), HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

    @PreAuthorize("hasAuthority('USER')")
    @PostMapping()
    public ResponseEntity<FavoriteGiftIdsResponse> toggle(@RequestBody ToggleFavoriteGiftRequestDto dto) {
        var principal = AuthenticationHelper.getCurrentUser();
        Integer giftId = dto.getGiftId();
        Long userId = principal.getUserId();
        this.favoriteGiftService.validateToggleGift(dto, userId);
        if (dto.getAction() == ToggleEnum.ADD) {
            favoriteGiftService.create(giftId, userId);
        } else {
            favoriteGiftService.remove(giftId, userId);
        }
        FavoriteGiftIdsResponse response = new FavoriteGiftIdsResponse(new Integer[]{giftId}, HttpStatus.OK, "OK");
        return responseFactory.success(response);
    }

    @PreAuthorize("hasAuthority('USER')")
    @GetMapping("gift-ids")
    public ResponseEntity<FavoriteGiftIdsResponse> favoriteGiftIds() {
        var principal = AuthenticationHelper.getCurrentUser();
        User userFavorite = favoriteGiftService.getUserFavorite(principal.getUserId());
        FavoriteGiftIdsResponse response = new FavoriteGiftIdsResponse(Objects.isNull(userFavorite) ? new Integer[]{} : userFavorite.getGiftIds(), HttpStatus.OK, "OK");
        return responseFactory.success(response);
    }
}
