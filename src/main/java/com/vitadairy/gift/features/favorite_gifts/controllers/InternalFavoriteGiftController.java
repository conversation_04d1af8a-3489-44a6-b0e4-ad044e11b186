package com.vitadairy.gift.features.favorite_gifts.controllers;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.features.favorite_gifts.dto.response.FavoriteGiftIdsResponse;
import com.vitadairy.gift.features.favorite_gifts.dto.response.FavoriteListGiftResponse;
import com.vitadairy.gift.features.favorite_gifts.services.UserFavoriteService;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.main.configs.ResponseFactory;

@RestController
@RequestMapping("in/gs/favorite-gifts")
public class InternalFavoriteGiftController {
    private UserFavoriteService favoriteGiftService;
    private final ResponseFactory responseFactory;

    public InternalFavoriteGiftController(UserFavoriteService favoriteGiftService, ResponseFactory responseFactory) {
        this.favoriteGiftService = favoriteGiftService;
        this.responseFactory = responseFactory;
    }

    @GetMapping("gift-ids")
    public ResponseEntity<FavoriteGiftIdsResponse> favoriteGiftIds(@RequestParam(name = "userId") Long userId) {
        User userFavorite = favoriteGiftService.getUserFavorite(userId);
        Integer[] giftIds = new Integer[] {};
        if (userFavorite != null && userFavorite.getGiftIds().length > 0) {
            giftIds = userFavorite.getGiftIds();
        }
        FavoriteGiftIdsResponse response = new FavoriteGiftIdsResponse(giftIds,
                HttpStatus.OK, "OK");
        return responseFactory.success(response);
    }

    @GetMapping("gifts")
    public ResponseEntity<FavoriteListGiftResponse> listGifts(@RequestParam(name = "userId") Long userId) {
        var data = favoriteGiftService.getFavoriteGifts(userId);
        FavoriteListGiftResponse response = new FavoriteListGiftResponse(
                data,
                HttpStatus.OK,
                "OK");
        return responseFactory.success(response);
    }

}
