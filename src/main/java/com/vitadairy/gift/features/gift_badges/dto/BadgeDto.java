package com.vitadairy.gift.features.gift_badges.dto;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.main.dto.BaseDto;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class BadgeDto extends BaseDto {
    private String code;
    private Boolean isActive;
    private Integer priority;
    private String name;
    private String color;
    private String icon;

    public static BadgeDto fromEntity(Constant constant) {
        BadgeDto category = BadgeDto.builder()
                .code(constant.getValue())
                .isActive(constant.getIsActive())
                .priority(constant.getPriority())
                .name(constant.getDynamicData().getName())
                .color(constant.getDynamicData().getColor())
                .icon(constant.getDynamicData().getIcon())
                .build();
        category.setUpdatedAt(constant.getUpdatedAt());
        category.setCreatedAt(constant.getCreatedAt());
        return category;
    }
}
