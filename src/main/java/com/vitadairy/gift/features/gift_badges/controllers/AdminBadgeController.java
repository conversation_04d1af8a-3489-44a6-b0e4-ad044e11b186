package com.vitadairy.gift.features.gift_badges.controllers;

import java.util.List;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.gift_badges.dto.BadgeDto;
import com.vitadairy.gift.features.gift_badges.dto.request.BadgeRequestDto;
import com.vitadairy.gift.features.gift_badges.dto.request.ListBadgeRequestDto;
import com.vitadairy.gift.features.gift_badges.dto.response.BadgeResponseDto;
import com.vitadairy.gift.features.gift_badges.dto.response.ListBadgeResponseDto;
import com.vitadairy.gift.features.gift_badges.services.BadgeService;
import com.vitadairy.main.configs.ResponseFactory;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;

@RestController
@RequestMapping("v4/gs/admin/gift-badges")
public class AdminBadgeController {
        private final BadgeService badgeService;
        private ResponseFactory responseFactory;

        public AdminBadgeController(BadgeService badgeService, ResponseFactory responseFactory) {
                this.badgeService = badgeService;
                this.responseFactory = responseFactory;
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'create'))")
        @PostMapping()
        public ResponseEntity<BadgeResponseDto> create(@Valid @RequestBody BadgeRequestDto body) {
                try {
                        Constant constant = this.badgeService.create(body);
                        BadgeResponseDto constantDetailResponseDto = new BadgeResponseDto(
                                        BadgeDto.fromEntity(constant));
                        return responseFactory.successDto(constantDetailResponseDto);
                } catch (DataIntegrityViolationException ex) {
                        return responseFactory.errorDto(body.getCode() + " đã tồn tại!");
                } catch (Exception ex) {
                        return responseFactory.errorDto(ex.getMessage());
                }

        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'update'))")
        @PatchMapping("{code}")
        public ResponseEntity<BadgeResponseDto> update(@PathVariable("code") String code,
                        @Valid @RequestBody BadgeRequestDto body) {
                try {
                        Constant constant = this.badgeService.update(code, body);
                        BadgeResponseDto constantDetailResponseDto = new BadgeResponseDto(
                                        BadgeDto.fromEntity(constant));
                        return responseFactory.successDto(constantDetailResponseDto);
                } catch (EntityNotFoundException ex) {
                        return responseFactory.errorDto(body.getCode() + " không tồn tại!");
                } catch (Exception ex) {
                        return responseFactory.errorDto(ex.getMessage());
                }
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
        @GetMapping()
        public ResponseEntity<ListBadgeResponseDto> list(@ModelAttribute ListBadgeRequestDto dto) {
                Sort sort = Sort.by(Sort.Direction.ASC, "createdAt");
                List<Constant> constants = this.badgeService.list(dto, sort);
                List<BadgeDto> constantBuilderDtos = constants.stream().map(BadgeDto::fromEntity)
                                .toList();
                ListBadgeResponseDto constantPageResponse = new ListBadgeResponseDto(constantBuilderDtos);
                return responseFactory.successDto(constantPageResponse);
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
        @GetMapping("{code}")
        public ResponseEntity<BadgeResponseDto> details(@PathVariable("code") String code) {
                Constant constant = this.badgeService.getByCode(code);
                BadgeResponseDto constantDetailResponseDto = new BadgeResponseDto(BadgeDto.fromEntity(constant));
                return responseFactory.successDto(constantDetailResponseDto);
        }
}
