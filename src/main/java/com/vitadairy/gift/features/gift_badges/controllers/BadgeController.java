package com.vitadairy.gift.features.gift_badges.controllers;

import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.gift_badges.dto.BadgeDto;
import com.vitadairy.gift.features.gift_badges.dto.request.ListBadgeRequestDto;
import com.vitadairy.gift.features.gift_badges.dto.response.BadgeResponseDto;
import com.vitadairy.gift.features.gift_badges.dto.response.ListBadgeResponseDto;
import com.vitadairy.gift.features.gift_badges.services.BadgeService;
import com.vitadairy.main.configs.ResponseFactory;

@RestController
@RequestMapping("v4/gs/gift-badges")
public class BadgeController {
    private final BadgeService badgeService;
    private ResponseFactory responseFactory;

    public BadgeController(BadgeService badgeService, ResponseFactory responseFactory) {
        this.badgeService = badgeService;
        this.responseFactory = responseFactory;
    }

    @GetMapping()
    public ResponseEntity<ListBadgeResponseDto> list(@ModelAttribute ListBadgeRequestDto dto) {
        Sort sort = Sort.by(Sort.Direction.ASC, "priority");
        // dto.setIsActive(true);
        List<Constant> constants = this.badgeService.list(dto, sort);
        List<BadgeDto> constantBuilderDtos = constants.stream().map(BadgeDto::fromEntity)
                .toList();
        ListBadgeResponseDto constantPageResponse = new ListBadgeResponseDto(constantBuilderDtos);
        return responseFactory.successDto(constantPageResponse);
    }

    @GetMapping("{code}")
    public ResponseEntity<BadgeResponseDto> details(@PathVariable("code") String code) {
        Constant constant = this.badgeService.getByCode(code);
        BadgeResponseDto constantDetailResponseDto = new BadgeResponseDto(BadgeDto.fromEntity(constant));
        return responseFactory.successDto(constantDetailResponseDto);
    }
}
