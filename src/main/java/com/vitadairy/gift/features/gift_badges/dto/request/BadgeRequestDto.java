package com.vitadairy.gift.features.gift_badges.dto.request;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Constant.ConstantDynamicData;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BadgeRequestDto {
    @NotNull
    private String code;
    private Boolean isActive = false;
    private Integer priority;
    @NotNull
    private String name;
    private String color;
    private String icon;

    public static Constant toEntity(BadgeRequestDto dto) {
        return Constant.builder().keyAlpha(ConstantFeatureEnum.GIFT.toString())
                .keyBeta(ConstantTypeEnum.BADGE.toString())
                .value(dto.getCode())
                .isActive(dto.getIsActive())
                .priority(dto.getPriority())
                .dynamicData(ConstantDynamicData.builder()
                        .name(dto.getName())
                        .color(dto.getColor())
                        .icon(dto.getIcon())
                        .build())
                .build();
    }
}
