package com.vitadairy.gift.features.gift_badges.services;

import java.util.List;
import java.util.Objects;

import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;
import com.vitadairy.gift.features.gift_badges.dto.request.BadgeRequestDto;
import com.vitadairy.gift.features.gift_badges.dto.request.ListBadgeRequestDto;
import com.vitadairy.gift.repositories.ConstantRepository;

import jakarta.persistence.EntityNotFoundException;

@Service("GiftBadgeService")
public class BadgeService {
    private final ConstantRepository constantRepository;

    public BadgeService(ConstantRepository constantRepository) {
        this.constantRepository = constantRepository;
    }

    public Constant create(BadgeRequestDto dto) {
        Constant constant = BadgeRequestDto.toEntity(dto);
        return this.constantRepository.save(constant);
    }

    public Constant getByCode(String code) {
        Constant constant = constantRepository
                .findFirstByKeyAlphaAndKeyBetaAndValue(ConstantFeatureEnum.GIFT.toString(),
                        ConstantTypeEnum.BADGE.toString(), code);
        if (Objects.isNull(constant)) {
            throw new EntityNotFoundException("Constant not found with code" + code);
        }
        return constant;
    }

    public Constant update(String code, BadgeRequestDto dto) {
        Constant constant = this.getByCode(code);
        Constant newConstant = BadgeRequestDto.toEntity(dto);
        newConstant.setId(constant.getId());
        return constantRepository.save(newConstant);
    }

    public Constant details(Integer id) {
        return this.constantRepository.findById(id).get();
    }

    public List<Constant> list(ListBadgeRequestDto dto, Sort sort) {

        return this.constantRepository.findByKeyAlphaAndKeyBetaAndIsActive(ConstantFeatureEnum.GIFT.toString(),
                ConstantTypeEnum.BADGE.toString(), dto.getIsActive(), sort);
    }
}
