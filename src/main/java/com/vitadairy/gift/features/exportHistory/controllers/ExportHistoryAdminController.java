package com.vitadairy.gift.features.exportHistory.controllers;

import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.constants.ExportType;
import com.vitadairy.gift.features.exportHistory.dto.ExportHistoryDto;
import com.vitadairy.gift.features.exportHistory.dto.request.ExportHistoryListFilter;
import com.vitadairy.gift.features.exportHistory.services.ExportHistoryAdminService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.main.response.PageableResponse;

@RestController
@RequestMapping("v4/gs/admin/export-history")
public class ExportHistoryAdminController {
    private final ResponseFactory responseFactory;
    private final ExportHistoryAdminService exportHistoryAdminService;

    public ExportHistoryAdminController(ResponseFactory responseFactory, ExportHistoryAdminService exportHistoryAdminService) {
        this.responseFactory = responseFactory;
        this.exportHistoryAdminService = exportHistoryAdminService;
    }

    @GetMapping
    public ResponseEntity<PageableResponse<ExportHistoryDto>> getExportHistories(@RequestParam("export_type") ExportType exportType,
                                                                                @RequestParam(defaultValue = "0") int page,
                                                                                @RequestParam(defaultValue = "10") int size) {
        var filter = new ExportHistoryListFilter();
        filter.setExportType(exportType);
        var pageable = PageRequest.of(page, size);
        var data = exportHistoryAdminService.getExportHistories(filter, pageable);
        return responseFactory.pageableSuccess(data.getContent(), data.getTotalElements(), page, size);
    }

    @GetMapping("download/{id}")
    public ResponseEntity<EntityResponse<String>> getExportHistoryFile(@PathVariable Long id) {
        var file = exportHistoryAdminService.getExportHistoryFile(id);
        return responseFactory.successEntity(file);
    }
}
