package com.vitadairy.gift.features.exportHistory.services;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import com.vitadairy.gift.configs.GoogleStorageClient;
import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.features.exportHistory.dto.ExportHistoryDto;
import com.vitadairy.gift.features.exportHistory.dto.request.ExportHistoryListFilter;
import com.vitadairy.gift.repositories.ExportHistoryRepository;
import com.vitadairy.main.exception.ApplicationException;

@Service
@Slf4j
public class ExportHistoryAdminService {
    private final ExportHistoryRepository exportHistoryRepository;
    private final GoogleStorageClient googleStorageClient;
    private final Integer exportHistoryFileTtlMinutes;

    public ExportHistoryAdminService(ExportHistoryRepository exportHistoryRepository, 
                                    GoogleStorageClient googleStorageClient,
                                    @Value("${export.history.file.ttl.minutes:10}") Integer exportHistoryFileTtlMinutes) {
        this.exportHistoryRepository = exportHistoryRepository;
        this.googleStorageClient = googleStorageClient;
        this.exportHistoryFileTtlMinutes = exportHistoryFileTtlMinutes;
    }

    public List<ExportHistoryDto> getExportHistories(ExportHistoryListFilter request, Pageable pageable) {
        return exportHistoryRepository.findByExportTypeOrderByCreatedAtDesc(request.getExportType(), pageable)
        .stream()
        .map(entity -> new ExportHistoryDto()
            .setId(entity.getId())
            .setExportType(entity.getExportType())
            .setStatus(entity.getStatus())
            .setFilter(entity.getFilter())
            .setErrorMessage(entity.getErrorMessage())
            .setCreatedAt(entity.getCreatedAt())
            .setUpdatedAt(entity.getUpdatedAt())
        ).toList();
    }

    public String getExportHistoryFile(Long id) {
        var exportHistory = exportHistoryRepository.findById(id).orElseThrow(() -> new ApplicationException("Export history not found"));
        if (exportHistory.getStatus() != ExportStatus.SUCCESS) {
            throw new ApplicationException("Export history not success");
        }
        var file = googleStorageClient.generateGetSignedUrl(exportHistory.getResultFilePath(), exportHistoryFileTtlMinutes);
        return file.toString();
    }
}
