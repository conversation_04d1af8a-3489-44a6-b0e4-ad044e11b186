package com.vitadairy.gift.features.exportHistory.services;


import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.configs.GoogleStorageClient;
import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.features.exportHistory.dto.ExportHistoryDto;
import com.vitadairy.gift.features.exportHistory.dto.request.ExportHistoryListFilter;
import com.vitadairy.gift.repositories.ExportHistoryRepository;
import com.vitadairy.main.exception.ApplicationException;

@Service
@Slf4j
public class ExportHistoryAdminService {
    private final ExportHistoryRepository exportHistoryRepository;
    private final GoogleStorageClient googleStorageClient;
    private final Integer exportHistoryFileTtlMinutes;
    private final ObjectMapper objectMapper;

    public ExportHistoryAdminService(ExportHistoryRepository exportHistoryRepository, 
                                    GoogleStorageClient googleStorageClient,
                                    @Value("${export.history.file.ttl.minutes:10}") Integer exportHistoryFileTtlMinutes,
                                    ObjectMapper objectMapper) {
        this.exportHistoryRepository = exportHistoryRepository;
        this.googleStorageClient = googleStorageClient;
        this.exportHistoryFileTtlMinutes = exportHistoryFileTtlMinutes;
        this.objectMapper = objectMapper;
    }

    public Page<ExportHistoryDto> getExportHistories(ExportHistoryListFilter request, Pageable pageable) {
        var result = exportHistoryRepository.findByExportTypeOrderByCreatedAtDesc(request.getExportType(), pageable);
        var data = result.getContent().stream()
            .map(entity -> {
                var dto = new ExportHistoryDto()
                .setId(entity.getId())
                .setExportType(entity.getExportType())
                .setStatus(entity.getStatus())
                .setErrorMessage(entity.getErrorMessage())
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt());
                try {
                    var obj = objectMapper.readValue(entity.getFilter(), new TypeReference<Map<String, Object>>() {});
                    dto.setFilter(obj);
                } catch (JsonProcessingException e) {
                    log.error("Error reading filter", e);
                    dto.setFilter(entity.getFilter());
                }
                return dto;
            }).toList();

        return new PageImpl<>(data, pageable, result.getTotalElements());
    }

    public String getExportHistoryFile(Long id) {
        var exportHistory = exportHistoryRepository.findById(id).orElseThrow(() -> new ApplicationException("Export history not found"));
        if (exportHistory.getStatus() != ExportStatus.SUCCESS) {
            throw new ApplicationException("Export history not success");
        }
        var file = googleStorageClient.generateGetSignedUrl(exportHistory.getResultFilePath(), exportHistoryFileTtlMinutes);
        return file.toString();
    }
}
