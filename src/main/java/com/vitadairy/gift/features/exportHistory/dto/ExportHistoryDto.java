package com.vitadairy.gift.features.exportHistory.dto;

import java.time.Instant;

import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.constants.ExportType;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class ExportHistoryDto {
    private Long id;
    private ExportType exportType;
    private ExportStatus status;
    private String filter;
    private String errorMessage;
    private Instant createdAt;
    private Instant updatedAt;
}
