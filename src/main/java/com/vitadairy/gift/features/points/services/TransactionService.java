package com.vitadairy.gift.features.points.services;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.common.CrmTransactionTypeCode;
import com.vitadairy.zoo.entities.CrmTransactionType;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.HistoryPointAttribute;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.entities.UserNumberScan;
import com.vitadairy.zoo.repositories.CrmTransactionTypeRepository;
import com.vitadairy.zoo.repositories.HistoryPointRepository;
import com.vitadairy.zoo.repositories.UserNumberScanRepository;
import com.vitadairy.zoo.repositories.UserRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class TransactionService {
    private HistoryPointRepository _historyPointRepository;
    private UserRepository _userRepository;
    private UserNumberScanRepository _userNumberScanRepository;
    private CrmTransactionTypeRepository _crmTransactionTypeRepo;

    public TransactionService(HistoryPointRepository historyPointRepository,
            @Qualifier("zooUserRepository") UserRepository userRepository,
            UserNumberScanRepository userNumberScanRepository, CrmTransactionTypeRepository crmTransactionTypeRepository) {
        this._historyPointRepository = historyPointRepository;
        this._userRepository = userRepository;
        this._userNumberScanRepository = userNumberScanRepository;
        this._crmTransactionTypeRepo = crmTransactionTypeRepository;
    }

    public HistoryPoint saveHistoryPoint(Long userId, String actionType, float giftPoint, float stockPoint, float tierPoint, Long money,
            String type, UserGift userGift, String brand, List<HistoryPointAttribute> attributes,
            int amount) {
        var user = this._userRepository.getUserById(userId)
                .orElseThrow(() -> new EntityNotFoundException("Not found user id " +
                        userId));
        if (attributes == null) {
            attributes = new ArrayList<>();
        }
        HistoryPoint historyPoint = new HistoryPoint();
        historyPoint.setCustomerId(user.getId());
        historyPoint.setCustomerName(user.getFirstName() + " " + user.getLastName());
        historyPoint.setCustomerPhone(user.getPhoneNumber());
        historyPoint.setGiftPoint(giftPoint);
        historyPoint.setStockPoint(stockPoint);
        historyPoint.setTierPoint(tierPoint);
        historyPoint.setStatus(Constant.TransactionStatus.SUCCESS);
        historyPoint.setTransactionDate(Instant.now());
        historyPoint.setType(type);
        historyPoint.setActionType(actionType);
        historyPoint.setMoney(money != null ? money : 0);
        historyPoint.setCdpSyncUp(false);
        historyPoint.setTransactionExternalId(IdUtils.generateIdWithActionType(actionType));
        if (userGift != null) {
            historyPoint.setGiftId(userGift.getId());
        }
        historyPoint.setBrand(brand);
        attributes.add(new HistoryPointAttribute(historyPoint, Constant.HistoryPointAttributeCode.AMOUNT,
                String.valueOf(amount)));
        if (!CollectionUtils.isEmpty(attributes)) {
            attributes.forEach(historyPointAttribute -> historyPointAttribute.setHistoryPoint(historyPoint));
            historyPoint.setAttributes(attributes);
        }
        return this._historyPointRepository.save(historyPoint);
    }

    public HistoryPoint saveHistoryReturnPoint(
            User user,
            UserGift userGift,
            String returnPointTxId,
            String spendPointTxId
    ) {
        CrmTransactionType crmTransactionType = this._crmTransactionTypeRepo
                .findFirstByCode(CrmTransactionTypeCode.RETURN_POINT).orElse(null);

        HistoryPoint historyPoint = new HistoryPoint();
        historyPoint.setCustomerId(user.getId());
        historyPoint.setCustomerName(user.getName());
        historyPoint.setCustomerPhone(user.getPhoneNumber());
        historyPoint.setGiftPoint(userGift.getPoint());
        historyPoint.setTierPoint(0F);
        historyPoint.setTransactionDate(Instant.now());
        historyPoint.setType(Constant.HistoryPointType.ADD_POINT);
        historyPoint.setStatus(Constant.TransactionStatus.RETURNED);
        if (Objects.nonNull(crmTransactionType)) {
            historyPoint.setActionType(crmTransactionType.getCode());
        }
        // TODO: gift missing money, using price instead
        historyPoint.setMoney(Long.parseLong(String.valueOf(userGift.getGift().getPrice())));
        historyPoint.setTransactionExternalId(returnPointTxId);
        historyPoint.setCdpSyncUp(false);

        HistoryPointAttribute attrReVoucherRefId = new HistoryPointAttribute(historyPoint,
                Constant.HistoryPointAttributeCode.RE_VOUCHER_REF_ID, returnPointTxId);
        HistoryPointAttribute attrSpendPointTxId = new HistoryPointAttribute(historyPoint,
                Constant.HistoryPointAttributeCode.SPEND_POINT_TX_ID, spendPointTxId);

        List<HistoryPointAttribute> attributes = new ArrayList<>();
        attributes.add(attrReVoucherRefId);
        attributes.add(attrSpendPointTxId);
        historyPoint.setAttributes(attributes);

        historyPoint = this._historyPointRepository.save(historyPoint);

        return historyPoint;
    }

    public void updateNumberPointUsedInDay(Long userId, Float pointsChange) {
        UserNumberScan userNumberScan = this._userNumberScanRepository.findById(userId).orElse(null);
        if (Objects.isNull(userNumberScan)) {
            userNumberScan = new UserNumberScan(userId);
            userNumberScan.setNumberScan(1);
            userNumberScan.setNumberScanInDay(1);
            userNumberScan.setNumberScanInMonth(1);
            this._userNumberScanRepository.save(userNumberScan);
        } else {
            userNumberScan.setNumberPointUsedInDay(userNumberScan.getNumberPointUsedInDay() + pointsChange);
            this._userNumberScanRepository.save(userNumberScan);
        }
    }

    public void revertNumberPointUsedInDay(Long userId, Float pointsRevert) {
        UserNumberScan userNumberScan = this._userNumberScanRepository.findById(userId).orElse(null);
        if (Objects.nonNull(userNumberScan)) {
            if (userNumberScan.getNumberPointUsedInDay().equals((float) 0)) {
                this._userNumberScanRepository.deleteById(userNumberScan.getUserId());
            } else {
                userNumberScan.setNumberPointUsedInDay(userNumberScan.getNumberPointUsedInDay() - pointsRevert);
                this._userNumberScanRepository.save(userNumberScan);
            }
        }
    }
}