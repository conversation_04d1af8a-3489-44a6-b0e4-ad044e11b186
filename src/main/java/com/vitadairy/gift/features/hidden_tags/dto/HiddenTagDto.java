package com.vitadairy.gift.features.hidden_tags.dto;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.main.dto.BaseDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class HiddenTagDto extends BaseDto {
    private String code;
    private Boolean isActive;
    private Integer priority;
    private String name;

    public static HiddenTagDto fromEntity(Constant constant) {
        HiddenTagDto category = HiddenTagDto.builder()
                .code(constant.getValue())
                .isActive(constant.getIsActive())
                .priority(constant.getPriority())
                .name(constant.getDynamicData().getName())
                .build();
        category.setUpdatedAt(constant.getUpdatedAt());
        category.setCreatedAt(constant.getCreatedAt());
        return category;
    }
}
