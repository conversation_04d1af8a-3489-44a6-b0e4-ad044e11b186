package com.vitadairy.gift.features.hidden_tags.services;

import java.util.List;
import java.util.Objects;

import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;
import com.vitadairy.gift.features.hidden_tags.dto.request.HiddenTagRequestDto;
import com.vitadairy.gift.features.hidden_tags.dto.request.ListHiddenTagRequestDto;
import com.vitadairy.gift.interfaces.GiftEnableLoggingAdminActionInterface;
import com.vitadairy.gift.repositories.ConstantRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GiftEnableLoggingAdminActionInterface
@Service("HiddenTagService")
public class HiddenTagService {
    private ConstantRepository constantRepository;

    public HiddenTagService(ConstantRepository constantRepository, 
                            ModelMapper mapper) {
        this.constantRepository = constantRepository;
    }

    public List<Constant> list(ListHiddenTagRequestDto dto, Sort sort) {

        return constantRepository.findByKeyAlphaAndKeyBetaAndIsActive(ConstantFeatureEnum.GIFT.toString(),
                ConstantTypeEnum.HIDDEN_TAG.toString(), dto.getIsActive(), sort);
    }

    public Constant create(HiddenTagRequestDto dto) {
        Constant constant = this.constantRepository.save(HiddenTagRequestDto.toEntity(dto));
        return constant;
    }

    public Constant getByCode(String code) {
        Constant constant = constantRepository
                .findFirstByKeyAlphaAndKeyBetaAndValue(ConstantFeatureEnum.GIFT.toString(),
                        ConstantTypeEnum.HIDDEN_TAG.toString(), code);
        if (Objects.isNull(constant)) {
            throw new EntityNotFoundException("Constant not found with code" + code);
        }
        return constant;
    }

    public Constant update(String code, HiddenTagRequestDto dto) {
        Constant constant = this.getByCode(code);
        Constant newConstant = HiddenTagRequestDto.toEntity(dto);
        newConstant.setId(constant.getId());

        Constant updatedConstant = constantRepository.save(newConstant);

        return updatedConstant;
    }
}
