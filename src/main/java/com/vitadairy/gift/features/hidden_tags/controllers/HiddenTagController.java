package com.vitadairy.gift.features.hidden_tags.controllers;

import java.util.List;
import java.util.stream.Collectors;

import com.vitadairy.main.configs.ResponseFactory;

import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Constant;

import com.vitadairy.gift.features.hidden_tags.dto.HiddenTagDto;
import com.vitadairy.gift.features.hidden_tags.dto.request.ListHiddenTagRequestDto;
import com.vitadairy.gift.features.hidden_tags.dto.response.HiddenTagResponseDto;
import com.vitadairy.gift.features.hidden_tags.dto.response.ListHiddenTagResponseDto;
import com.vitadairy.gift.features.hidden_tags.services.HiddenTagService;

@RestController
@RequestMapping("v4/gs/hidden-tags")
public class HiddenTagController {
    private final ResponseFactory responseFactory;
    private HiddenTagService hiddenTagService;

    public HiddenTagController(ResponseFactory responseFactory, HiddenTagService hiddenTagService) {
        this.responseFactory = responseFactory;
        this.hiddenTagService = hiddenTagService;
    }

    @GetMapping()
    public ResponseEntity<ListHiddenTagResponseDto> list(@ModelAttribute ListHiddenTagRequestDto dto) {
        Sort sort = Sort.by(Sort.Direction.ASC, "priority");
        // dto.setIsActive(true);
        List<Constant> constants = this.hiddenTagService.list(dto, sort);
        List<HiddenTagDto> result = constants.stream().map(HiddenTagDto::fromEntity).collect(Collectors.toList());
        ListHiddenTagResponseDto response = new ListHiddenTagResponseDto(result);
        return responseFactory.successDto(response);
    }

    @GetMapping("{code}")
    public ResponseEntity<HiddenTagResponseDto> details(@PathVariable("code") String code) {
        Constant constant = this.hiddenTagService.getByCode(code);
        HiddenTagResponseDto response = new HiddenTagResponseDto(HiddenTagDto.fromEntity(constant));
        return responseFactory.successDto(response);
    }
}
