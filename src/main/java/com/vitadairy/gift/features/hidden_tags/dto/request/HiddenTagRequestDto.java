package com.vitadairy.gift.features.hidden_tags.dto.request;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Constant.ConstantDynamicData;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HiddenTagRequestDto {
    @NotNull
    private String code;
    private Boolean isActive = false;
    private Integer priority;
    @NotNull
    private String name;

    public static Constant toEntity(HiddenTagRequestDto dto) {
        return Constant.builder().keyAlpha(ConstantFeatureEnum.GIFT.toString())
                .keyBeta(ConstantTypeEnum.HIDDEN_TAG.toString())
                .value(dto.getCode())
                .isActive(dto.getIsActive())
                .priority(dto.getPriority())
                .dynamicData(ConstantDynamicData.builder()
                        .name(dto.getName())
                        .build())
                .build();
    }
}
