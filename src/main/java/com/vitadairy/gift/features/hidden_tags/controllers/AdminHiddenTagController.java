package com.vitadairy.gift.features.hidden_tags.controllers;

import java.util.List;
import java.util.stream.Collectors;

import com.vitadairy.main.configs.ResponseFactory;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.hidden_tags.dto.HiddenTagDto;
import com.vitadairy.gift.features.hidden_tags.dto.request.HiddenTagRequestDto;
import com.vitadairy.gift.features.hidden_tags.dto.request.ListHiddenTagRequestDto;
import com.vitadairy.gift.features.hidden_tags.dto.response.HiddenTagResponseDto;
import com.vitadairy.gift.features.hidden_tags.dto.response.ListHiddenTagResponseDto;
import com.vitadairy.gift.features.hidden_tags.services.HiddenTagService;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;

@RestController
@RequestMapping("v4/gs/admin/hidden-tags")
public class AdminHiddenTagController {
    private final ResponseFactory responseFactory;
    private HiddenTagService hiddenTagService;

    public AdminHiddenTagController(ResponseFactory responseFactory, HiddenTagService hiddenTagService) {
        this.responseFactory = responseFactory;
        this.hiddenTagService = hiddenTagService;
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-hidden-user-tag', T(java.util.List).of('full-access', 'create'))")
    @PostMapping()
    public ResponseEntity<HiddenTagResponseDto> create(@Valid @RequestBody HiddenTagRequestDto body) {
        try {
            Constant constant = this.hiddenTagService.create(body);
            HiddenTagResponseDto response = new HiddenTagResponseDto(HiddenTagDto.fromEntity(constant));
            return responseFactory.successDto(response);
        } catch (DataIntegrityViolationException ex) {
            return responseFactory.errorDto(body.getCode() + " đã tồn tại!");
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-hidden-user-tag', T(java.util.List).of('full-access', 'update'))")
    @PatchMapping("{code}")
    public ResponseEntity<HiddenTagResponseDto> update(@PathVariable("code") String code,
            @Valid @RequestBody HiddenTagRequestDto body) {
        try {
            Constant constant = this.hiddenTagService.update(code, body);
            HiddenTagResponseDto response = new HiddenTagResponseDto(HiddenTagDto.fromEntity(constant));
            return responseFactory.successDto(response);
        } catch (EntityNotFoundException ex) {
            return responseFactory.errorDto(body.getCode() + " không tồn tại!");
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-hidden-user-tag', T(java.util.List).of('full-access', 'read'))")
    @GetMapping()
    public ResponseEntity<ListHiddenTagResponseDto> list(@ModelAttribute ListHiddenTagRequestDto dto) {
        Sort sort = Sort.by(Sort.Direction.ASC, "createdAt");
        List<Constant> constants = this.hiddenTagService.list(dto, sort);
        List<HiddenTagDto> result = constants.stream().map(HiddenTagDto::fromEntity).collect(Collectors.toList());
        ListHiddenTagResponseDto response = new ListHiddenTagResponseDto(result);
        return responseFactory.successDto(response);
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-hidden-user-tag', T(java.util.List).of('full-access', 'read'))")
    @GetMapping("{code}")
    public ResponseEntity<HiddenTagResponseDto> details(@PathVariable("code") String code) {
        Constant constant = this.hiddenTagService.getByCode(code);
        HiddenTagResponseDto response = new HiddenTagResponseDto(HiddenTagDto.fromEntity(constant));
        return responseFactory.successDto(response);
    }
}
