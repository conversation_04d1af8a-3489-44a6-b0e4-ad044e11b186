package com.vitadairy.gift.features.gift_category.controllers;

import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.gift_category.dto.GiftCategoryDto;
import com.vitadairy.gift.features.gift_category.dto.request.ListGiftCategoryRequestDto;
import com.vitadairy.gift.features.gift_category.dto.response.GiftCategoryResponseDto;
import com.vitadairy.gift.features.gift_category.dto.response.ListGiftCategoryResponseDto;
import com.vitadairy.gift.features.gift_category.services.GiftCategoryService;
import com.vitadairy.main.configs.ResponseFactory;

@RestController
@RequestMapping("v4/gs/gift-categories")
public class GiftCategoryController {
    private GiftCategoryService giftCategoryService;
    private ResponseFactory responseFactory;

    public GiftCategoryController(GiftCategoryService giftCategoryService, ResponseFactory responseFactory) {
        this.giftCategoryService = giftCategoryService;
        this.responseFactory = responseFactory;
    }

    @GetMapping()
    public ResponseEntity<ListGiftCategoryResponseDto> list(@ModelAttribute ListGiftCategoryRequestDto dto) {
        Sort sort = Sort.by(Sort.Direction.DESC, "priority");
        dto.setIsActive(true);
        List<Constant> constants = this.giftCategoryService.list(dto, sort);

        List<GiftCategoryDto> items = constants.stream().map(GiftCategoryDto::fromEntity).toList();
        ListGiftCategoryResponseDto reponse = new ListGiftCategoryResponseDto(items);
        return responseFactory.successDto(reponse);
    }
    
    @GetMapping("/list/belong-cls")
    public ResponseEntity<ListGiftCategoryResponseDto> listCategoryCLS() {
        List<Constant> constants = this.giftCategoryService.listCategoryBelongCLS();

        List<GiftCategoryDto> items = constants.stream().map(GiftCategoryDto::fromEntity).toList();
        ListGiftCategoryResponseDto response = new ListGiftCategoryResponseDto(items);
        return responseFactory.successDto(response);
    }

    @GetMapping("/list/active")
    public ResponseEntity<ListGiftCategoryResponseDto> activeList() {
        List<Constant> constants = this.giftCategoryService.activeList();
        List<GiftCategoryDto> items = constants.stream().map(GiftCategoryDto::fromEntity).toList();
        ListGiftCategoryResponseDto reponse = new ListGiftCategoryResponseDto(items);
        return responseFactory.successDto(reponse);
    }

    @GetMapping("{code}")
    public ResponseEntity<GiftCategoryResponseDto> details(@PathVariable("code") String code) {
        Constant constant = this.giftCategoryService.getByCode(code);
        GiftCategoryResponseDto constantDetailResponseDto = new GiftCategoryResponseDto(
                GiftCategoryDto.fromEntity(constant));
        return responseFactory.successDto(constantDetailResponseDto);
    }
}
