package com.vitadairy.gift.features.gift_category.services;

import java.util.*;
import java.util.stream.Collectors;

import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.zoo.dto.UserDto;
import com.vitadairy.zoo.repositories.EventAddCanRepository;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;
import com.vitadairy.gift.features.gift_category.dto.request.GiftCategoryRequestDto;
import com.vitadairy.gift.features.gift_category.dto.request.ListGiftCategoryRequestDto;
import com.vitadairy.gift.repositories.ConstantRepository;

import jakarta.persistence.EntityNotFoundException;
import org.springframework.web.server.ResponseStatusException;

@Service("GiftCategoryService")
@RequiredArgsConstructor
public class GiftCategoryService {
    private final ConstantRepository constantRepository;
    private final GiftService giftService;
    private final EventAddCanRepository eventAddCanRepository;

    public List<Constant> list(ListGiftCategoryRequestDto dto, Sort sort) {
        return constantRepository.findByKeyAlphaAndKeyBetaAndIsActive(ConstantFeatureEnum.GIFT.toString(),
                ConstantTypeEnum.CATEGORY.toString(), dto.getIsActive(), sort);
    }

    public List<Constant> activeList() {
        UserDto currentUser = giftService.getUserDtoFromAuthToken();
        return constantRepository.getActiveCategories(currentUser.getTierCode());
    }

    public Constant create(GiftCategoryRequestDto dto) {
        Constant constant = GiftCategoryRequestDto.toEntity(dto);
        return this.constantRepository.save(constant);
    }

    public Constant getByCode(String code) {
        Constant constant = constantRepository
                .findFirstByKeyAlphaAndKeyBetaAndValue(ConstantFeatureEnum.GIFT.toString(),
                        ConstantTypeEnum.CATEGORY.toString(), code);
        if (Objects.isNull(constant)) {
            throw new EntityNotFoundException("Constant not found with code" + code);
        }

        return constant;
    }

    public Constant update(String code, GiftCategoryRequestDto dto) {
        Constant constant = this.getByCode(code);
        Constant newConstant = GiftCategoryRequestDto.toEntity(dto);
        newConstant.setId(constant.getId());
        return constantRepository.save(newConstant);
    }

    private List<String> getCategoryCodesInListEventAddCan() {
        Optional<List<List<String>>> categoryCodesInEventAddCan = this.eventAddCanRepository.findGiftCategoryCodesInListEventAddCan();
        List<String> categoryCodes = categoryCodesInEventAddCan.orElse(List.of()).stream()
                .flatMap(List::stream)
                .toList();
        return categoryCodes;
    }

    public List<Constant> listNotInEventAddCan(ListGiftCategoryRequestDto dto, Sort sort) {
        List<String> categoryCodes = this.getCategoryCodesInListEventAddCan();
        return constantRepository.findByKeyAlphaAndKeyBetaAndIsActiveAndNotValue(
                ConstantFeatureEnum.GIFT.toString(), ConstantTypeEnum.CATEGORY.toString(), dto.getIsActive(), categoryCodes, sort);
    }

    //getGiftByCategoryCodes
    public List<Constant> getGiftByCategoryCodes(List<String> categoryCodes) {
        List<Constant> constants = constantRepository.findAllByKeyAlphaAndKeyBetaAndValueIn(
                ConstantFeatureEnum.GIFT.toString(),     // keyAlpha = "GIFT"
                ConstantTypeEnum.CATEGORY.toString(),    // keyBeta = "CATEGORY"
                categoryCodes
        );
        if (constants.isEmpty()) {
            throw new EntityNotFoundException("No gift categories found with the provided codes: " + categoryCodes);
        }
        return constants;
    }

    //duplicateGiftCategoryByCategoryCodes
    public List<Constant> duplicateGiftCategoryByCategoryCodes(Map<String, String> categorySuffixMap) {
        List<String> categoryCodes = new ArrayList<>(categorySuffixMap.keySet());

        List<Constant> originalCategories = constantRepository.findAllByKeyAlphaAndKeyBetaAndValueIn("GIFT", "CATEGORY", categoryCodes);
        if (originalCategories.size() != categoryCodes.size()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "CategoryCodes not found: " + categoryCodes);
        }

        long timestamp = System.currentTimeMillis()/1000;

        List<Constant> duplicatedCategories = originalCategories.stream().map(category -> {
            Constant newCategory = new Constant();

            String suffix = categorySuffixMap.getOrDefault(category.getValue(), "").trim();
            if (suffix.isEmpty()) {
                suffix = "_" + timestamp;
            }

            String newValue = category.getValue() + "_" + suffix;

            boolean exists = constantRepository.existsByKeyAlphaAndKeyBetaAndValue("GIFT", "CATEGORY", newValue);
            if (exists) {
                newValue += "_" + timestamp;
            }

            newCategory.setValue(newValue);
            newCategory.setKeyAlpha("GIFT");
            newCategory.setKeyBeta("CATEGORY");
            newCategory.setIsActive(category.getIsActive());
            newCategory.setDynamicData(category.getDynamicData());

            return newCategory;
        }).collect(Collectors.toList());
        return constantRepository.saveAll(duplicatedCategories);
    }
}
