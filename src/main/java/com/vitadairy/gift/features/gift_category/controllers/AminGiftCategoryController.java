package com.vitadairy.gift.features.gift_category.controllers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.vitadairy.gift.features.gifts.dto.request.CategoryDuplicateRequest;
import com.vitadairy.gift.interfaces.GiftEnableLoggingAdminActionInterface;
import org.hibernate.query.sqm.EntityTypeException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.features.gift_category.dto.GiftCategoryDto;
import com.vitadairy.gift.features.gift_category.dto.request.GiftCategoryRequestDto;
import com.vitadairy.gift.features.gift_category.dto.request.ListGiftCategoryRequestDto;
import com.vitadairy.gift.features.gift_category.dto.response.GiftCategoryResponseDto;
import com.vitadairy.gift.features.gift_category.dto.response.ListGiftCategoryResponseDto;
import com.vitadairy.gift.features.gift_category.services.GiftCategoryService;
import com.vitadairy.main.configs.ResponseFactory;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;

@RestController
@GiftEnableLoggingAdminActionInterface
@RequestMapping("v4/gs/admin/gift-categories")
public class AminGiftCategoryController {
        private GiftCategoryService giftCategoryService;
        private ResponseFactory responseFactory;

        public AminGiftCategoryController(GiftCategoryService giftCategoryService,
                        ResponseFactory responseFactory) {
                this.giftCategoryService = giftCategoryService;
                this.responseFactory = responseFactory;
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'create'))")
        @PostMapping()
        public ResponseEntity<GiftCategoryResponseDto> create(@Valid @RequestBody GiftCategoryRequestDto body) {
                try {
                        Constant constant = this.giftCategoryService.create(body);
                        GiftCategoryResponseDto response = new GiftCategoryResponseDto(
                                        GiftCategoryDto.fromEntity(constant));
                        return responseFactory.successDto(response);
                } catch (DataIntegrityViolationException ex) {
                        return responseFactory.errorDto(body.getCode() + " đã tồn tại!");
                } catch (Exception ex) {
                        return responseFactory.errorDto(ex.getMessage());
                }
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'update'))")
        @PatchMapping("{code}")
        public ResponseEntity<GiftCategoryResponseDto> update(@PathVariable("code") String code,
                        @Valid @RequestBody GiftCategoryRequestDto body) {
                try {
                        Constant constant = this.giftCategoryService.update(code, body);
                        GiftCategoryResponseDto response = new GiftCategoryResponseDto(
                                        GiftCategoryDto.fromEntity(constant));
                        return responseFactory.successDto(response);
                } catch (EntityNotFoundException ex) {
                        return responseFactory.errorDto(body.getCode() + " không tồn tại!");
                } catch (Exception ex) {
                        return responseFactory.errorDto(ex.getMessage());
                }
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
        @GetMapping()
        public ResponseEntity<ListGiftCategoryResponseDto> list(@ModelAttribute ListGiftCategoryRequestDto dto) {
                Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
                List<Constant> constants = this.giftCategoryService.list(dto, sort);
                List<GiftCategoryDto> categories = constants.stream().map(GiftCategoryDto::fromEntity)
                                .toList();
                ListGiftCategoryResponseDto constantPageResponse = new ListGiftCategoryResponseDto(categories);
                return responseFactory.successDto(constantPageResponse);
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
        @GetMapping("{code}")
        public ResponseEntity<GiftCategoryResponseDto> details(@PathVariable("code") String code) {
                Constant constant = this.giftCategoryService.getByCode(code);
                GiftCategoryResponseDto constantDetailResponseDto = new GiftCategoryResponseDto(
                                GiftCategoryDto.fromEntity(constant));
                return responseFactory.successDto(constantDetailResponseDto);
        }

//        @PreAuthorize("hasAuthority('ADMIN')")
        @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
        @GetMapping("not-in-event-add-can")
        public ResponseEntity<ListGiftCategoryResponseDto> listNotInEventAddCan(@ModelAttribute ListGiftCategoryRequestDto dto) {
                Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
                List<Constant> constants = this.giftCategoryService.listNotInEventAddCan(dto, sort);
                List<GiftCategoryDto> categories = constants.stream().map(GiftCategoryDto::fromEntity)
                                .toList();
                ListGiftCategoryResponseDto constantPageResponse = new ListGiftCategoryResponseDto(categories);
                return responseFactory.successDto(constantPageResponse);
        }


        //getGiftByCategoryCodes
        @PreAuthorize("hasAuthority('ADMIN')")
        @GetMapping("/multiple")
        public ResponseEntity<ListGiftCategoryResponseDto> getGiftByCategoryCodes(@RequestParam("giftCategoryCode") List<String> categoryCodes) {
                System.out.println("Category Codes: " + categoryCodes);
                try {
                        List<Constant> constants = this.giftCategoryService.getGiftByCategoryCodes(categoryCodes);
                        List<GiftCategoryDto> categories = constants.stream().map(GiftCategoryDto::fromEntity).toList();
                        ListGiftCategoryResponseDto response = new ListGiftCategoryResponseDto(categories);
                        return responseFactory.successDto(response);
                } catch (EntityNotFoundException ex) {
                        return responseFactory.errorDto("Not found: " + categoryCodes);
                } catch (Exception ex) {
                        return responseFactory.errorDto(ex.getMessage());
                }
        }

        //duplicateGiftCategoryByCategoryCodes
        @PostMapping("/duplicate")
        @PreAuthorize("hasAuthority('ADMIN')")
        public ResponseEntity<Map<String, Constant>> duplicateGiftCategories(@RequestBody List<CategoryDuplicateRequest> categoryRequests) {

                Map<String, String> categorySuffixMap = categoryRequests.stream()
                        .collect(Collectors.toMap(CategoryDuplicateRequest::getCategoryCode, req -> req.getSuffix() != null ? req.getSuffix() : ""));

                List<Constant> duplicatedCategories = giftCategoryService.duplicateGiftCategoryByCategoryCodes(categorySuffixMap);

                Map<String, Constant> response = new HashMap<>();
                for (CategoryDuplicateRequest request : categoryRequests) {
                        String originalCode = request.getCategoryCode();
                        Constant matchedDuplicate = duplicatedCategories.stream()
                                .filter(dup -> dup.getValue().startsWith(originalCode + "_"))
                                .findFirst()
                                .orElse(null);
                        if (matchedDuplicate != null) {
                                response.put(originalCode, matchedDuplicate);
                        }
                }
                return ResponseEntity.ok(response);
        }
}
