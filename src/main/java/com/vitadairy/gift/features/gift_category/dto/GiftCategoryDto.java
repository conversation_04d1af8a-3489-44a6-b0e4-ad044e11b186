package com.vitadairy.gift.features.gift_category.dto;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.main.dto.BaseDto;

import lombok.Builder;
import lombok.Data;

import java.util.Objects;

@Data
@Builder
public class GiftCategoryDto extends BaseDto {
    private String code;
    private Boolean isActive;
    private Integer priority;
    private String name;
    private String description;

    public static GiftCategoryDto fromEntity(Constant constant) {
        GiftCategoryDto category = GiftCategoryDto.builder()
                .code(constant.getValue())
                .isActive(constant.getIsActive())
                .priority(constant.getPriority())
                .name(Objects.isNull(constant.getDynamicData()) ? null : constant.getDynamicData().getName())
                .description(Objects.isNull(constant.getDynamicData()) ? null : constant.getDynamicData().getDescription())
                .build();
        category.setUpdatedAt(constant.getUpdatedAt());
        category.setCreatedAt(constant.getCreatedAt());
        return category;
    }
}
