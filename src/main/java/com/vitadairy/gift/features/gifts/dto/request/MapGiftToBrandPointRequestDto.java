package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.MapGiftToBrandPoint;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class MapGiftToBrandPointRequestDto {
    @NotNull()
    @Min(1)
    private Integer brandId;

    @NotNull()
    @Min(0)
    private Integer brandPoint;

    public static MapGiftToBrandPoint toEntity(MapGiftToBrandPointRequestDto dto) {
        MapGiftToBrandPoint mapGiftToBrandPoint = new MapGiftToBrandPoint();
        mapGiftToBrandPoint.setBrandId(dto.getBrandId());
        mapGiftToBrandPoint.setBrandPoint(dto.getBrandPoint());

        return mapGiftToBrandPoint;
    }
}
