package com.vitadairy.gift.features.gifts.interfaces;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.GotItTopupStatus;
import com.vitadairy.zoo.requests.GetVoucherRequest;
import com.vitadairy.zoo.requests.TopupV1Dot4Request;
import com.vitadairy.zoo.responses.GetVoucherResponse;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import com.vitadairy.zoo.responses.VoucherResponse;

public interface IGotItService {
    TopupResponseV1Dot4 topupV1Dot4(User user, String phone, String refId, Long amount, HistoryPoint historyPoint);

    TopupResponseV1Dot4 checkTopupTransaction(User user, String refId, HistoryPoint historyPoint);

    GotItTopupStatus mapStatusFromGotItToInternal(TopupResponseV1Dot4 response);

    boolean checkTopupFail(TopupResponseV1Dot4 topupResponse);

    TopupResponseV1Dot4 processTopupResponse(UserGift userGift, TopupResponseV1Dot4 topupResponse);

    VoucherResponse processEVoucherResponse(UserGift userGift, VoucherResponse evoucherResponse);

    GetVoucherResponse getGotItVoucher(User user, Gift gift, String refId, HistoryPoint historyPoint);
}
