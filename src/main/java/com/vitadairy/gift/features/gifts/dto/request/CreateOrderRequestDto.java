package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
public class CreateOrderRequestDto {
    private Integer userId;
    private Map<String, String> recipientSnapshot;
    private List<GiftDto> giftSnapshot;
    private List<UserGiftDto> userGiftSnapshot;
}
