package com.vitadairy.gift.features.gifts.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GiftExchangeRequestDto {
    @NotNull
    private Integer giftId;
    @NotNull
    private Integer quantity;

    @JsonIgnore
    private Long userId;
}
