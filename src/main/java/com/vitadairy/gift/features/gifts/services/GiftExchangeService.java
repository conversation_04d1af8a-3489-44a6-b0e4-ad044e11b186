package com.vitadairy.gift.features.gifts.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.common.MultiTransactionHandler;
import com.vitadairy.gift.entities.*;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GetQuantityGiftDto;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.GiftingGiftDto;
import com.vitadairy.gift.features.gifts.dto.request.*;
import com.vitadairy.gift.repositories.MapGiftToBrandPointRepository;
import com.vitadairy.main.dto.GiftReturnPointRequestDto;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherService;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherShopService;
import com.vitadairy.gift.features.points.services.TransactionService;
import com.vitadairy.gift.features.userGift.dto.UserGiftDto;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.repositories.MapOldGiftToNewGiftRepository;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.gift.services.RestApiOrderService;
import com.vitadairy.gift.services.RestApiWarehouseService;
import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.main.common.GiftFunctionDefs;
import com.vitadairy.main.dto.CustomNotificationRequestDto;
import com.vitadairy.main.dto.DeductPointResponseDto;
import com.vitadairy.main.dto.GiftReturnPointResponseDto;
import com.vitadairy.main.dto.NotificationData;
import com.vitadairy.main.dto.SfUserInfoResponseDto;
import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.dto.TransactionDto;
import com.vitadairy.main.enums.ReturnPointStatusEnum;
import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.services.NotificationService;
import com.vitadairy.main.services.WarehouseTransactionService;
import com.vitadairy.main.utils.IdUtils;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.common.CrmTransactionTypeCode;
import com.vitadairy.zoo.dto.DDXTrackingUserActionWithPointRequestDto;
import com.vitadairy.zoo.entities.*;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.*;
import com.vitadairy.zoo.repositories.*;
import com.vitadairy.zoo.requests.UpdateEventPointHistoryToWhRequest;
import com.vitadairy.zoo.responses.GetVoucherResponse;
import com.vitadairy.zoo.responses.RequestDetailCouponDataDetailCouponResponse;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import com.vitadairy.zoo.responses.VoucherResponse;
import com.vitadairy.zoo.services.DDXService;
import com.vitadairy.zoo.services.UserService;
import com.vitadairy.zoo.util.CommonUtil;
import com.vitadairy.zoo.util.DateTimeUtil;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.vitadairy.gift.constants.NotificationConstant.RETURN_POINT_CONTENT;
import static com.vitadairy.gift.constants.NotificationConstant.RETURN_POINT_DESCRIPTION;
import static com.vitadairy.gift.constants.NotificationConstant.RETURN_POINT_TITLE;

@Service
@Slf4j
public class GiftExchangeService {
    private final GiftRepository giftRepository;
    private final MapGiftToBrandPointRepository mapGiftToBrandPointRepo;
    private final UserGiftRepository userGiftRepository;
    private final UserRepository userRepository;
    private final CrmTransactionTypeRepository crmTransactionTypeRepository;
    private final VitaCodeRepository vitaCodeRepository;
    private final MapOldGiftToNewGiftRepository mapOldGiftToNewGiftRepository;
    private final HistoryPointRepository historyPointRepository;
    private final EventDetailRepository eventDetailRepository;
    private final EventCanMarkRepository eventCanMarkRepository;
    private final SyncEventPointHistoryToWhRepository syncEventPointHistoryToWhRepo;
    private final EventPointHistoryRepository eventPointHistoryRepo;
    private final TransactionService pointTransactionService;
    private final WarehouseTransactionService whTransactionService;
    private final TransactionTemplate transactionTemplate;

    private final PlatformTransactionManager giftTransactionManager;
    private final PlatformTransactionManager zooTransactionManager;
    private final MultiTransactionHandler multiTransactionHandler;
    private final CommonUtil commonUtil;
    private final IEVoucherService eVoucherService;
    private final IEVoucherShopService eVoucherShopService;
    private final UserService userService;
    private final ProvinceRepository provinceRepository;
    private final ProvinceNewRepository provinceNewRepo;
    private final WardNewRepository wardNewRepo;
    private final ObjectMapper objectMapper;
    private HttpServletRequest request;
    private final NotificationService notificationService;
    private RestApiWarehouseService restApiWarehouseService;
    private RestApiOrderService restApiOrderService;
    private final EventAddCanRepository eventAddCanRepository;
    private final EventAddCanTimeExchangeGiftDetailRepository eventAddCanTimeExchangeGiftRepository;
    private final UserTrackingNumberTimeExchangeGiftRepository userTrackingNumberTimeExchangeGiftRepo;
    private final MapUserToBrandPointRepository mapUserToBrandPointRepo;
    private final MapHistoryPointToBrandPointRepository mapHistoryPointToBrandPointRepo;
    private final DDXService ddxService;
    private final HistoryUserExchangeGiftEventRepository historyUserExchangeGiftEventRepository;
    private final HistoryUserExchangeGiftEventDetailRepository historyUserExchangeGiftEventDetailRepository;
    private final UserProvinceRepository userProvinceRepo;


    public GiftExchangeService(GiftRepository giftRepository,
                               MapGiftToBrandPointRepository mapGiftToBrandPointRepo, @Qualifier("zooUserRepository") UserRepository userRepository,
                               UserGiftRepository userGiftRepository,
                               CrmTransactionTypeRepository crmTransactionTypeRepository,
                               VitaCodeRepository vitaCodeRepository,
                               MapOldGiftToNewGiftRepository mapOldGiftToNewGiftRepository,
                               @Qualifier("zooHistoryPointRepository") HistoryPointRepository historyPointRepository,
                               @Qualifier("zooEventDetailRepository") EventDetailRepository eventDetailRepository,
                               @Qualifier("zooEventCanMarkRepository") EventCanMarkRepository eventCanMarkRepository,
                               @Qualifier("zooSyncEventPointHistoryToWhRepository") SyncEventPointHistoryToWhRepository syncEventPointHistoryToWhRepo,
                               @Qualifier("zooEventPointHistoryRepository") EventPointHistoryRepository eventPointHistoryRepo, TransactionService pointTransactionService,
                               WarehouseTransactionService whTransactionService,
                               @Qualifier("giftTransactionManager") PlatformTransactionManager transactionManager, @Qualifier("zooTransactionManager") PlatformTransactionManager zooTransactionManager,
                               MultiTransactionHandler multiTransactionHandler, CommonUtil commonUtil, @Qualifier("eVoucherServiceImpl") IEVoucherService eVoucherService,
                               @Qualifier("eVoucherShopServiceImpl") IEVoucherShopService eVoucherShopService,
                               ProvinceRepository provinceRepository,
                               @Qualifier("userService") UserService userService,
                               ProvinceNewRepository provinceNewRepo, WardNewRepository wardNewRepo, ObjectMapper objectMapper,
                               RestApiWarehouseService restApiWarehouseService,
                               RestApiOrderService restApiOrderService,
                               NotificationService notificationService,
                               EventAddCanRepository eventAddCanRepository,
                               EventAddCanTimeExchangeGiftDetailRepository eventAddCanTimeExchangeGiftRepository,
                               @Qualifier("zooUserTrackingNumberTimeExchangeGiftRepository") UserTrackingNumberTimeExchangeGiftRepository userTrackingNumberTimeExchangeGiftRepo,
                               @Qualifier("zooMapUserToBrandPointRepository") MapUserToBrandPointRepository mapUserToBrandPointRepo,
                               @Qualifier("zooMapHistoryPointToBrandPoint") MapHistoryPointToBrandPointRepository mapHistoryPointToBrandPointRepo, @Qualifier("zooDDXService") DDXService ddxService,
                               HistoryUserExchangeGiftEventRepository historyUserExchangeGiftEventRepository,
                               HistoryUserExchangeGiftEventDetailRepository historyUserExchangeGiftEventDetailRepository,
                               UserProvinceRepository userProvinceRepo) {
        this.giftRepository = giftRepository;
        this.mapGiftToBrandPointRepo = mapGiftToBrandPointRepo;
        this.userRepository = userRepository;
        this.userGiftRepository = userGiftRepository;
        this.crmTransactionTypeRepository = crmTransactionTypeRepository;
        this.vitaCodeRepository = vitaCodeRepository;
        this.mapOldGiftToNewGiftRepository = mapOldGiftToNewGiftRepository;
        this.historyPointRepository = historyPointRepository;
        this.eventDetailRepository = eventDetailRepository;
        this.eventCanMarkRepository = eventCanMarkRepository;
        this.syncEventPointHistoryToWhRepo = syncEventPointHistoryToWhRepo;
        this.eventPointHistoryRepo = eventPointHistoryRepo;
        this.pointTransactionService = pointTransactionService;
        this.whTransactionService = whTransactionService;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.giftTransactionManager = transactionManager;
        this.zooTransactionManager = zooTransactionManager;
        this.multiTransactionHandler = multiTransactionHandler;
        this.commonUtil = commonUtil;
        this.eVoucherService = eVoucherService;
        this.eVoucherShopService = eVoucherShopService;
        this.userService = userService;
        this.provinceRepository = provinceRepository;
        this.provinceNewRepo = provinceNewRepo;
        this.wardNewRepo = wardNewRepo;
        this.objectMapper = objectMapper;
        this.restApiWarehouseService = restApiWarehouseService;
        this.restApiOrderService = restApiOrderService;
        this.notificationService = notificationService;
        this.eventAddCanRepository = eventAddCanRepository;
        this.eventAddCanTimeExchangeGiftRepository = eventAddCanTimeExchangeGiftRepository;
        this.userTrackingNumberTimeExchangeGiftRepo = userTrackingNumberTimeExchangeGiftRepo;
        this.mapUserToBrandPointRepo = mapUserToBrandPointRepo;
        this.mapHistoryPointToBrandPointRepo = mapHistoryPointToBrandPointRepo;
        this.ddxService = ddxService;
        this.historyUserExchangeGiftEventRepository = historyUserExchangeGiftEventRepository;
        this.historyUserExchangeGiftEventDetailRepository = historyUserExchangeGiftEventDetailRepository;
        this.userProvinceRepo = userProvinceRepo;
    }

    private List<TransactionStatus> initTransaction() {
        DefaultTransactionDefinition giftTransactionDefinition = new DefaultTransactionDefinition();
        giftTransactionDefinition.setName("GiftTransaction");
        giftTransactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        DefaultTransactionDefinition zooTransactionDefinition = new DefaultTransactionDefinition();
        zooTransactionDefinition.setName("ZooTransaction");
        zooTransactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus giftTransactionStatus = giftTransactionManager.getTransaction(giftTransactionDefinition);
        TransactionStatus zooTransactionStatus = zooTransactionManager.getTransaction(zooTransactionDefinition);
        return List.of(giftTransactionStatus, zooTransactionStatus);
    }

    public boolean checkUserPointSf(GiftExchangeRequestDto body){
        Gift gift = this.giftRepository.findById(body.getGiftId())
                .orElseThrow(() -> new EntityNotFoundException("Not found gift id " +
                        body.getGiftId()));
        SfUserInfoResponseDto sfUserInfo = restApiWarehouseService.getUserInfo(body.getUserId());
        var totalPoint = body.getQuantity() * gift.getPoint();
        if(Objects.nonNull(sfUserInfo) && Objects.nonNull(sfUserInfo.getData())
                && Objects.nonNull(sfUserInfo.getData().getRedeemPoint())
                && Double.parseDouble(sfUserInfo.getData().getRedeemPoint()) >= totalPoint){
            return true;
        }
        throw new ApplicationException("Not enough gift point", VtdStatusEnum.GF_POINT);
    }

    public Boolean exchangeGift(PhysicalGiftExchangeRequestDto body, boolean createOrder) {
        // Check feature active
        this.commonUtil.checkFeatureByCode(Constant.FeatureCode.REDEEM_GIFT);
        // Exchange
        Gift gift = this.giftRepository.findById(body.getGiftId())
                .orElseThrow(() -> new EntityNotFoundException("Not found gift id " +
                        body.getGiftId()));

        User user = this.userRepository.getUserById(body.getUserId())
                .orElseThrow(() -> new EntityNotFoundException("Not found user id " +
                        body.getUserId()));

        // validate gift
        this.validateBeforeExchange(gift, user, body);

        MapGiftToBrandPoint mapGiftToBrandPoint = gift.getBrandPoint();
        EventAddCan eventAddCan = this.eventAddCanRepository.findFistByCategoryCode(
                gift.getCategoryCode()
        ).orElse(null);
        MapUserToBrandPoint mapUserToBrandPoint = null;
        Boolean isExchangingBrandGift = this.checkIsExchangingBrandGift(mapGiftToBrandPoint);

        if (isExchangingBrandGift.equals(Boolean.TRUE)) {
            if (Objects.isNull(eventAddCan)) {
                throw new ApplicationException("Invalid request.",
                        HttpStatus.BAD_REQUEST);
            }
            // validate event expired
            if (eventAddCan.checkEventExpired().equals(Boolean.TRUE)) {
                throw new ApplicationException("Xu đã hết hạn.",
                        HttpStatus.BAD_REQUEST);
            }
            mapUserToBrandPoint = this.mapUserToBrandPointRepo.findFistByUserIdAndBrandIdAndEventAddCanId(
                    user.getId(),
                    mapGiftToBrandPoint.getBrandId(),
                    eventAddCan.getId()
            ).orElse(null);
            // validate exchange brand gift
            this.validateExchangeBrandGift(
                    mapGiftToBrandPoint,
                    mapUserToBrandPoint
            );
        }
        Integer giftPoint = gift.getPoint();
        if (isExchangingBrandGift.equals(Boolean.TRUE)) {
            giftPoint = mapGiftToBrandPoint.getBrandPoint();
        }

        GiftStatusEnum giftStatus = gift.getStatus();
        if (GiftStatusEnum.DISABLED.equals(giftStatus)) {
            throw new EntityNotFoundException("Not found gift id " +
                    body.getGiftId());
        }
        var totalPoint = body.getQuantity() * giftPoint;

        // Validate exchange event gift has setup limit time can exchange
//        EventAddCan eventAddCan = (EventAddCan) rsValidateExchangeGiftEvent.get("event_add_can");
        HashMap<String, Object> rsValidateExchangeGiftEventSetupLimitTimes = null;
        if (
                Objects.nonNull(eventAddCan) &&
                        eventAddCan.getEnableLimitTimeCanExchangeGift().equals(Boolean.TRUE)
        ) {
            rsValidateExchangeGiftEventSetupLimitTimes = this.validateExchangeGiftMatchLitmitTimesCanExchangeSetupAndReturnSetupData(
                    eventAddCan,
                    user
            );
            Boolean resultCheckExceedNumberTimesEarned = (Boolean) rsValidateExchangeGiftEventSetupLimitTimes.get("result_check_exceed_number_times_earned");
            Boolean resultCheckExceedTotalNumberTimes = (Boolean) rsValidateExchangeGiftEventSetupLimitTimes.get("result_check_exceed_total_number_times");
            if (resultCheckExceedTotalNumberTimes.equals(Boolean.TRUE)) {
                throw new ApplicationException("Bạn đã hết lượt đổi quà của toàn sự kiện đổi quà.",
                        HttpStatus.BAD_REQUEST);
            }
            if (resultCheckExceedNumberTimesEarned.equals(Boolean.TRUE)) {
                throw new ApplicationException("Bạn đã hết lượt đổi quà của sự kiện đổi quà. Hãy tích thêm lượt đổi nhé.",
                        HttpStatus.BAD_REQUEST);
            }
        }

        // Validate exchange event gift has setup time exchange
        HashMap<String, Object> rsValidateExchangeGiftEvent = this.validateExchangeGiftMatchTimeExchangeGiftSetupAndReturnSetupData(
                gift,
                user,
                body.getQuantity(),
                eventAddCan
        );
        Boolean resultCheck = (Boolean) rsValidateExchangeGiftEvent.get("result_check");
        if (resultCheck.equals(Boolean.FALSE)) {
            throw new ApplicationException("Bạn đã đổi vượt quá số lượng quà sự kiện đổi quà. Hãy đổi lại vào thời gian tới.",
                    HttpStatus.BAD_REQUEST);
        }

        String codeEndPointCrm = this.generateCodeEndPointExchangeGiftCrm(gift);
        List<TransactionStatus> transactionStatus = this.initTransaction();
        // TODO: can not using external class to init transaction
        // TODO: issue transaction commit and rollback
        // TODO: research to know why
        // TODO: issue call userService.deductPoints with FOR UPDATE NOWAIT already blocked
        // TODO: ERROR: could not obtain lock on row in relation "users"
//        this.multiTransactionHandler.initTransaction(
//                this.giftTransactionManager,
//                "GiftTransaction"
//        );
//        this.multiTransactionHandler.initTransaction(
//                this.zooTransactionManager,
//                "ZooTransaction"
//        );
//        this.multiTransactionHandler.registerTransaction(
//                this.zooTransactionManager,
//                "ZooTransaction"
//        );
//        this.multiTransactionHandler.registerTransaction(
//                this.giftTransactionManager,
//                "GiftTransaction"
//        );
        HashMap<String, Object> sfPayload;
        HistoryPoint historyPoint = null;
        MapHistoryPointToBrandPoint mapHistoryPointToBrandPoint = null;
        float totalPointBefore = user.getGiftPoint();
        var decreaseGiftPoint = DeductPointResponseDto.builder().success(false).build();
        EventAddCanTimeExchangeGiftDetail detailSetup = null;
        HistoryUserExchangeGiftEvent userExchangeGiftEvent = null;
        HistoryUserExchangeGiftEventDetail historyUserExchangeGiftEventDetail = null;
        Boolean insertingHistoryUserExchangeGiftEvent = null;
        UserTrackingNumberTimeExchangeGift userTrackingNumberTimeExchangeGift = null;
        var quantityUsed = 0;
        try {
//            this.multiTransactionHandler.beginTransaction("ZooTransaction");
            // Trừ xu gift_point của user tương ứng
            if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                int affected = this.mapUserToBrandPointRepo.decreaseBrandPoint(
                        mapUserToBrandPoint.getId(),
                        mapUserToBrandPoint.getBrandPoint(),
                        totalPoint
                );
                if (affected != 1) {
                    throw new ApplicationException("Đổi quà không thành công.",
                            HttpStatus.BAD_REQUEST);
                }
            } else {
                decreaseGiftPoint = userService.deductPoints(user.getId(), totalPoint);
                if (BooleanUtils.isNotTrue(decreaseGiftPoint.isSuccess())) {
                    throw new ApplicationException("Not enough gift point", VtdStatusEnum.GF_POINT);
                }
            }

//            this.multiTransactionHandler.beginTransaction("GiftTransaction");
            // Lưu quà vào Kho quà của user (user_gift)
            var userGift = new UserGift();
            userGift.setGift(gift);
            userGift.setQuantity(body.getQuantity());
            userGift.setUserId(user.getId());
            userGift.setStatus(createOrder ? UserGift.USER_GIFT_STATUS.IN_PROCESS : UserGift.USER_GIFT_STATUS.PENDING);
            userGift.setReservationPoint(0);
            userGift.setGiftSnapshot(gift);
            this.saveUserGiftDynamicDataWhenExchangeGift(gift, userGift);
            if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                userGift.setPoint((float) totalPoint);
                userGift.getDynamicData().setEventAddCanLoyaltyProgramSfId(eventAddCan.getLoyaltyProgramSfId());
                if(Objects.nonNull(mapUserToBrandPoint)){
                    userGift.getDynamicData().setMapUserToBrandPointId(mapUserToBrandPoint.getId());
                    userGift.getDynamicData().setMapUserToBrandPointIdSendSf(mapUserToBrandPoint.getIdSendSf());
                }
            } else {
                userGift.setPoint((float)decreaseGiftPoint.getGiftPoint());
                userGift.setStockPoint((float)decreaseGiftPoint.getStockPoint());
            }
            userGift = this.userGiftRepository.save(userGift);

            // Lưu Transaction Đổi quà của user (history_point)
            historyPoint = this.saveHistoryPoint(gift, user, userGift, body);
            userGift.setTransactionCode(historyPoint.getTransactionExternalId());
            userGift = this.userGiftRepository.save(userGift);
            if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                historyPoint.setGiftPoint(0F);
                historyPoint.setStockPoint(0F);
                this.historyPointRepository.save(historyPoint);

                mapHistoryPointToBrandPoint = new MapHistoryPointToBrandPoint();
                mapHistoryPointToBrandPoint.setHistoryPointId(Math.toIntExact(historyPoint.getId()));
                mapHistoryPointToBrandPoint.setBrandId(mapGiftToBrandPoint.getBrandId());
                mapHistoryPointToBrandPoint.setEventAddCanId(eventAddCan.getId());
                mapHistoryPointToBrandPoint.setBrandPoint((float) totalPoint);
                this.mapHistoryPointToBrandPointRepo.save(mapHistoryPointToBrandPoint);
            }

            quantityUsed = body.getQuantity();
            this.giftRepository.incrementQuantityReward(gift.getId(), quantityUsed);
            int rowAffected = this.giftRepository.decrementQuantity(gift.getId(), quantityUsed);
            if(rowAffected == 0) {
                throw new ApplicationException("Cannot decrement quantity", HttpStatus.BAD_REQUEST);
            }

            // Check update history exchange event gift of user
            detailSetup = (EventAddCanTimeExchangeGiftDetail) rsValidateExchangeGiftEvent.get("detail_setup");
            userExchangeGiftEvent = (HistoryUserExchangeGiftEvent) rsValidateExchangeGiftEvent.get("history_user_exchange_gift_event");
            // Time exchange event gift is happening
            if (Objects.nonNull(detailSetup)) {
                // Insert history
                if (Objects.isNull(userExchangeGiftEvent)) {
                    userExchangeGiftEvent = new HistoryUserExchangeGiftEvent();
                    userExchangeGiftEvent.setUserId(user.getId());
                    userExchangeGiftEvent.setEventAddCanTimeExchangeGiftId(detailSetup.getEventAddCanTimeExchangeGiftId());
                    userExchangeGiftEvent.setEventAddCanTimeExchangeGiftDetailId(detailSetup.getId());
                    userExchangeGiftEvent.setTotal(quantityUsed);

                    userExchangeGiftEvent = this.historyUserExchangeGiftEventRepository.save(userExchangeGiftEvent);
                    insertingHistoryUserExchangeGiftEvent = Boolean.TRUE;
                } else {
                    // Update history
                    int historyRowAffected = this.historyUserExchangeGiftEventRepository.increaseTotalExchangeEventGiftOfUser(
                            user.getId(),
                            detailSetup.getId(),
                            quantityUsed
                    );
                    if(historyRowAffected == 0) {
                        throw new ApplicationException("Cannot update history exchange event gift of user", HttpStatus.BAD_REQUEST);
                    }
                    insertingHistoryUserExchangeGiftEvent = Boolean.FALSE;
                }
                Integer historyExchangeGiftEventId = userExchangeGiftEvent.getId();

                historyUserExchangeGiftEventDetail = new HistoryUserExchangeGiftEventDetail();
                historyUserExchangeGiftEventDetail.setHistoryUserExchangeGiftEventId(historyExchangeGiftEventId);
                historyUserExchangeGiftEventDetail.setGiftId(gift.getId());
                historyUserExchangeGiftEventDetail.setTotal(quantityUsed);

                this.historyUserExchangeGiftEventDetailRepository.save(
                        historyUserExchangeGiftEventDetail
                );
            }

            // update tracking user exchange gift
            if (Objects.nonNull(rsValidateExchangeGiftEventSetupLimitTimes)) {
                userTrackingNumberTimeExchangeGift = (UserTrackingNumberTimeExchangeGift) rsValidateExchangeGiftEventSetupLimitTimes.get("user_tracking_number_time_exchange_gift");
                if (Objects.isNull(userTrackingNumberTimeExchangeGift)) {
                    throw new ApplicationException("Cannot update tracking exchange event gift of user", HttpStatus.BAD_REQUEST);
                }
                int historyRowAffected = this.userTrackingNumberTimeExchangeGiftRepo.increaseNumberOfTimesUsed(
                        userTrackingNumberTimeExchangeGift.getId(),
                        1
                );
                if(historyRowAffected == 0) {
                    throw new ApplicationException("Cannot update tracking exchange event gift of user", HttpStatus.BAD_REQUEST);
                }
            }


            // TODO: process checkAndUpdateUserNumberScan
            // TODO: process addNumberOfCanUsed

            sfPayload = this.generatePayloadDataExchangeGiftCrm(gift, user, userGift, historyPoint, (float) totalPoint);
            sfPayload.put("user_gift", userGift);
            sfPayload.put("history_point", historyPoint);

            if(createOrder){
                Map<String, String> recipientSnapshot = new HashMap<>();
//                if(Objects.nonNull(body.getWardCode())){
//                    Optional<Province> ward = provinceRepository.findByIdAndType(body.getWardCode().longValue(), Constant.ProvinceType.WARD);
//                    ward.ifPresent(province -> recipientSnapshot.put("wardName", province.getName()));
//                    recipientSnapshot.put("wardCode", body.getWardCode().toString());
//                }
//                if(Objects.nonNull(body.getDistrictCode())){
//                    Optional<Province> district = provinceRepository.findByIdAndType(body.getDistrictCode().longValue(), Constant.ProvinceType.DISTRICT);
//                    district.ifPresent(province -> recipientSnapshot.put("districtName", province.getName()));
//                    recipientSnapshot.put("districtCode", body.getDistrictCode().toString());
//                }
//                if(Objects.nonNull(body.getProvinceCode())){
//                    Optional<Province> province = provinceRepository.findByIdAndType(body.getProvinceCode().longValue(), Constant.ProvinceType.PROVINCE);
//                    province.ifPresent(province1 -> recipientSnapshot.put("provinceName", province1.getName()));
//                    recipientSnapshot.put("provinceCode", body.getProvinceCode().toString());
//                }
                ProvinceNew provinceNew = null;
                if(Objects.nonNull(body.getNewProvinceCode())){
                    provinceNew = provinceNewRepo.findById(body.getNewProvinceCode()).orElse(null);
                    if (Objects.nonNull(provinceNew)) {
                        recipientSnapshot.put("newProvinceName", provinceNew.getName());
                    }
                    recipientSnapshot.put("newProvinceCode", body.getNewProvinceCode().toString());
                }
                if(Objects.nonNull(body.getNewWardCode()) && Objects.nonNull(provinceNew)){
                    Optional<WardNew> ward = wardNewRepo.findByIdAndNewProvince(body.getNewWardCode(), provinceNew);
                    ward.ifPresent(province -> recipientSnapshot.put("newWardName", province.getName()));
                    recipientSnapshot.put("newWardCode", body.getNewWardCode().toString());
                }
                if(StringUtils.isNotEmpty(body.getRecipientName())){
                    recipientSnapshot.put("recipientName", body.getRecipientName());
                }
                if(StringUtils.isNotEmpty(body.getRecipientPhone())){
                    recipientSnapshot.put("recipientPhone", body.getRecipientPhone());
                }
                if(StringUtils.isNotEmpty(body.getStreetName())){
                    recipientSnapshot.put("streetName", body.getStreetName());
                }

                CreateOrderRequestDto createOrderRequest = CreateOrderRequestDto.builder()
                        .userId(user.getId().intValue())
                        .recipientSnapshot(recipientSnapshot)
                        .giftSnapshot(List.of(GiftDto.fromEntity(gift)))
                        .userGiftSnapshot(List.of(UserGiftDto.fromEntity(userGift)))
                        .build();

                var createOrderResponse = restApiOrderService.createOrder(this.getAuthBearerToken(), createOrderRequest);
                if(Objects.nonNull(createOrderResponse) && Objects.nonNull(createOrderResponse.getResponse().getCode()) &&
                        createOrderResponse.getResponse().getCode() != 200){
                    throw new ApplicationException("Error when create order", HttpStatus.BAD_REQUEST);
                }
            }

//            if (List.of(137135L, 137136L, 137138L, 137141L, 137142L).contains(user.getId())) {
//                throw new ApplicationException("Test exchange fail and lose data", HttpStatus.BAD_REQUEST);
//            }

            if (isExchangingBrandGift.equals(Boolean.FALSE)) {
                User userUpdated = this.userRepository.getUserById(user.getId()).orElse(null);
                float totalPointAfter = userUpdated.getGiftPoint();
                DDXTrackingUserActionWithPointRequestDto dto = this.ddxService.generateDataTracking(
                        user,
                        EnumHistoryPointType.SPEND_POINT,
                        EnumHistoryPointActionType.REWARD_GIFT,
                        totalPointBefore,
                        totalPointAfter - totalPointBefore,
                        totalPointAfter,
                        historyPoint.getTransactionExternalId(),
                        historyPoint.getTransactionDate()
                );
                this.ddxService.trackingUserActionWithPoint(dto);
            }

            zooTransactionManager.commit(transactionStatus.get(1));
            giftTransactionManager.commit(transactionStatus.get(0));
            // TODO: can not using external class to block and commit
            // TODO: research to know why
            // TODO: issue call userService.deductPoints with FOR UPDATE NOWAIT already blocked
//            this.multiTransactionHandler.commitAll();
//            this.multiTransactionHandler.lazyCommitAll();
        } catch (Exception ex) {
            if (!transactionStatus.get(1).isCompleted()) {
                zooTransactionManager.rollback(transactionStatus.get(1));
                log.error("rollback zooTransactionStatus");
            } else {
                try {
                    if (mapHistoryPointToBrandPoint != null) {
                        this.mapHistoryPointToBrandPointRepo.delete(mapHistoryPointToBrandPoint);
                    }
                    if (historyPoint != null) {
                        this.historyPointRepository.delete(historyPoint);
                    }
                    if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                        this.mapUserToBrandPointRepo.increaseBrandPoint(
                                mapUserToBrandPoint.getId(),
                                mapUserToBrandPoint.getBrandPoint() - totalPoint,
                                totalPoint
                        );
                    } else {
                        if (BooleanUtils.isTrue(decreaseGiftPoint.isSuccess())) {
                            userService.returnPoint(user.getId(), decreaseGiftPoint.getStockPoint(), decreaseGiftPoint.getGiftPoint(), 0);
                        }
                    }
                    if (Objects.nonNull(detailSetup)) {
                        if (historyUserExchangeGiftEventDetail != null && Objects.nonNull(historyUserExchangeGiftEventDetail.getId())) {
                            this.historyUserExchangeGiftEventDetailRepository.delete(historyUserExchangeGiftEventDetail);
                        }
                        if (Objects.nonNull(insertingHistoryUserExchangeGiftEvent)) {
                            if (insertingHistoryUserExchangeGiftEvent.equals(Boolean.TRUE)) {
                                this.historyUserExchangeGiftEventRepository.delete(userExchangeGiftEvent);
                            } else {
                                this.historyUserExchangeGiftEventRepository.decreaseTotalExchangeEventGiftOfUser(
                                        user.getId(),
                                        detailSetup.getId(),
                                        quantityUsed
                                );
                            }
                        }
                    }
                    if (Objects.nonNull(userTrackingNumberTimeExchangeGift)) {
                        this.userTrackingNumberTimeExchangeGiftRepo.decreaseNumberOfTimesUsed(
                                userTrackingNumberTimeExchangeGift.getId(),
                                1
                        );
                    }
                } catch (Exception subEx) {
                    log.error("Rollback data by delete zoo data failed : ", subEx);
                }
            }
            if (!transactionStatus.get(0).isCompleted()) {
                giftTransactionManager.rollback(transactionStatus.get(0));
                log.error("rollback giftTransactionStatus");
            }
            // TODO: can not using external class to rollback transaction
            // TODO: research to know why
            // TODO: ERROR: current transaction is aborted, commands ignored until end of transaction block
            // TODO: issue Error creating bean with name 'cacheManager': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
            // TODO: above issue happen when zooTransaction completed and need manual rollback
//            HistoryPoint finalHistoryPoint = historyPoint;
//            DeductPointResponseDto finalDecreaseGiftPoint = decreaseGiftPoint;
//            EventAddCanTimeExchangeGiftDetail finalDetailSetup = detailSetup;
//            HistoryUserExchangeGiftEventDetail finalHistoryUserExchangeGiftEventDetail = historyUserExchangeGiftEventDetail;
//            Boolean finalInsertingHistoryUserExchangeGiftEvent = insertingHistoryUserExchangeGiftEvent;
//            HistoryUserExchangeGiftEvent finalUserExchangeGiftEvent = userExchangeGiftEvent;
//            int finalQuantityUsed = quantityUsed;
//            this.multiTransactionHandler.setLazyManualRollbackAction("ZooTransaction", () -> {
//                try {
//                    if (finalHistoryPoint != null) {
//                        this.historyPointRepository.delete(finalHistoryPoint);
//                    }
//                    if (BooleanUtils.isTrue(finalDecreaseGiftPoint.isSuccess())) {
//                        userService.returnPoint(user.getId(), finalDecreaseGiftPoint.getStockPoint(), finalDecreaseGiftPoint.getGiftPoint(), 0);
//                    }
//                    if (Objects.nonNull(finalDetailSetup)) {
//                        if (finalHistoryUserExchangeGiftEventDetail != null && Objects.nonNull(finalHistoryUserExchangeGiftEventDetail.getId())) {
//                            this.historyUserExchangeGiftEventDetailRepository.delete(finalHistoryUserExchangeGiftEventDetail);
//                        }
//                        if (Objects.nonNull(finalInsertingHistoryUserExchangeGiftEvent)) {
//                            if (finalInsertingHistoryUserExchangeGiftEvent.equals(Boolean.TRUE)) {
//                                this.historyUserExchangeGiftEventRepository.delete(finalUserExchangeGiftEvent);
//                            } else {
//                                this.historyUserExchangeGiftEventRepository.decreaseTotalExchangeEventGiftOfUser(
//                                        user.getId(),
//                                        finalDetailSetup.getId(),
//                                        finalQuantityUsed
//                                );
//                            }
//                        }
//                    }
//                } catch (Exception subEx) {
//                    log.error("Rollback data by delete zoo data failed : ", subEx);
//                }
//            });
//            this.multiTransactionHandler.lazyRollbackAll();
            log.error("Exchange gift error: ", ex);
            throw ex;
        }

        if (!CollectionUtils.isEmpty(sfPayload)) {
            UserGift userGift = (UserGift) sfPayload.get("user_gift");
            historyPoint = (HistoryPoint) sfPayload.get("history_point");
            sfPayload.remove("user_gift");
            sfPayload.remove("history_point");

            if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                String loyaltyMemberC = String.format("%s-%s", 
                    userGift.getDynamicData().getMapUserToBrandPointIdSendSf(), 
                    userGift.getDynamicData().getMapUserToBrandPointId()
                );
                sfPayload.put("Loyalty_Member__c", loyaltyMemberC != null ? loyaltyMemberC : "");

                String programNameC = userGift.getDynamicData().getEventAddCanLoyaltyProgramSfId();
                sfPayload.put("Program_Name__c", programNameC != null ? programNameC : "");
            }

            if (!codeEndPointCrm.isEmpty()) {
                var batchDto = TransactionBatchDto.builder().transactions(List.of(
                        new TransactionDto().setCode(codeEndPointCrm).setPayload(sfPayload))).build();
                whTransactionService.send(batchDto);
            }
            
            this.handleUseGift(user, userGift, historyPoint, GiftFunctionDefs.USING_GIFT, null);
        } else {
            // TODO: do something when sfPayload is null
        }

        return true;
    }

    public Boolean checkRemindExchangeGift(Long userId) {
        return userGiftRepository.countPendingUserGiftsByUserIdAndCreatedAtBefore(userId, 3, 7) > 0;
    }

    public GiftingGiftDto giftingGift(GiftGiftingRequestDto requestDto) {
        GiftingGiftDto response = new GiftingGiftDto();
        Integer oldGiftId = requestDto.getOldGiftId();
        Integer giftId = requestDto.getGiftId();
        Integer crmTransactionTypeId = requestDto.getCrmTransactionTypeId();
        Long userId = requestDto.getUserId();
        float point = requestDto.getPoint();
        Integer quantity = requestDto.getQuantity();
        String brand = requestDto.getBrand();
        Integer eventId = requestDto.getEventId();
        if (
                (Objects.isNull(oldGiftId) && Objects.isNull(giftId)) ||
                        Objects.isNull(crmTransactionTypeId) ||
                        Objects.isNull(userId) ||
                        point < 0 ||
                        quantity <= 0
        ) {
            throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
        }
        Gift gift = null;
        if (Objects.nonNull(oldGiftId)) {
            MapOldGiftToNewGift mapOldGiftToNewGift = this.mapOldGiftToNewGiftRepository.findFirstByOldGiftId(Long.valueOf(oldGiftId)).orElse(null);
            if (Objects.nonNull(mapOldGiftToNewGift)) {
                gift = mapOldGiftToNewGift.getNewGift();
            }
        } else {
            gift = this.giftRepository.findById(giftId).orElse(null);
        }

        CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository.findById(Long.valueOf(crmTransactionTypeId)).orElse(null);
        User user = this.userRepository.findById(userId).orElse(null);
        if (
                Objects.isNull(gift) ||
                        Objects.isNull(crmTransactionType) ||
                        Objects.isNull(user)
        ) {
            throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
        }
        GiftDynamicData giftDynamicData = gift.getDynamicData();
        String transactionExternalId = IdUtils.generateId(ConstantFieldDefs.GIFTING_GIFT_PREFIX);
        String transactionDate = requestDto.getTransactionDate();
        String sourceGift = giftDynamicData.getSourceGift();
        Instant createDate = Instant.now();
        if (Objects.nonNull(transactionDate)) {
            createDate = DateTimeUtil.convertStringToInstant(
                    transactionDate,
                    Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS,
                    Constant.TIMEZONE.UTC_ZONE
            );
        }

        Instant finalCreateDate = Objects.nonNull(createDate) ? createDate : Instant.now();
        Instant finalExpiredDate = Instant.now().plus(Duration.ofDays(90));
        Gift finalGift = gift;

        MapGiftToBrandPoint mapGiftToBrandPoint = gift.getBrandPoint();
        EventAddCan eventAddCan = this.eventAddCanRepository.findFistByCategoryCode(gift.getCategoryCode()).orElse(null);
        MapUserToBrandPoint mapUserToBrandPoint = null;
        Boolean isExchangingBrandGift = this.checkIsExchangingBrandGift(mapGiftToBrandPoint);

        log.info("isExchangingBrandGift: {}", isExchangingBrandGift);
        if (isExchangingBrandGift.equals(Boolean.TRUE)) {
            log.info("eventAddCan: {}", eventAddCan);

            if (Objects.isNull(eventAddCan)) {
                throw new ApplicationException("Invalid brand gift, not found event add can",
                        HttpStatus.BAD_REQUEST);
            }
            mapUserToBrandPoint = this.mapUserToBrandPointRepo
                .findFistByUserIdAndBrandIdAndEventAddCanId(user.getId(), mapGiftToBrandPoint.getBrandId(), eventAddCan.getId())
                .orElse(null);

            log.info("mapUserToBrandPoint: {}", mapUserToBrandPoint);
        }

        List<TransactionStatus> transactionStatus = this.initTransaction();
        UserGift userGift = new UserGift();
        try {
            
            userGift.setGift(finalGift);
            userGift.setQuantity(quantity);
            userGift.setUserId(user.getId());
            userGift.setStatus(UserGift.USER_GIFT_STATUS.PENDING);
            userGift.setPoint(point);
            userGift.setReservationPoint(0);
            userGift.setGiftSnapshot(finalGift);
            UserGiftDynamicData userGiftDynamicData = new UserGiftDynamicData();
            userGiftDynamicData.setActionType(crmTransactionType.getCode());

            if (isExchangingBrandGift.equals(Boolean.TRUE)) {
                userGiftDynamicData.setEventAddCanLoyaltyProgramSfId(eventAddCan.getLoyaltyProgramSfId());
                if (Objects.nonNull(mapUserToBrandPoint)) {
                    userGiftDynamicData.setMapUserToBrandPointId(mapUserToBrandPoint.getId());
                    userGiftDynamicData.setMapUserToBrandPointIdSendSf(mapUserToBrandPoint.getIdSendSf());
                }

                log.info("userGiftDynamicData.eventAddCanLoyaltyProgramSfId: {}", userGiftDynamicData.getEventAddCanLoyaltyProgramSfId());
                log.info("userGiftDynamicData.mapUserToBrandPointId: {}", userGiftDynamicData.getMapUserToBrandPointId());
                log.info("userGiftDynamicData.mapUserToBrandPointIdSendSf: {}", userGiftDynamicData.getMapUserToBrandPointIdSendSf());
            } 

            VitaCode vitaCode = null;
            switch (finalGift.getType()) {
                case EV_VITA_CODE -> {
                    if (eventId != null) {
                        String eventSource = Constant.EventTypePrefix.EVENT_PRIZE_WINNING + eventId.toString();
                        vitaCode = this.vitaCodeRepository.findFirstByEventSourceAndSmsStatusForUpdateSkipLocked(eventSource, false).orElse(null);
                    } else {
                        UserProvince userProvince = this.userProvinceRepo.findByUserId(user.getId()).orElse(null);

                        if (Objects.isNull(userProvince) || Objects.isNull(userProvince.getNewProvince())) {
                            throw new ApplicationException("User province not found", HttpStatus.BAD_REQUEST);
                        }

                        vitaCode = this.vitaCodeRepository.findFirstByNewProvinceIdAndSmsStatus(
                                userProvince.getNewProvince().getId(),
                                false
                        ).orElse(null);
                    }
                    if (Objects.nonNull(vitaCode)) {
                        userGift.setStatus(UserGift.USER_GIFT_STATUS.USED);
                        userGiftDynamicData.setVoucherCode(vitaCode.getCode());
//                        userGiftDynamicData.setExpiryDate(finalExpiredDate.toString());
                        userGiftDynamicData.setExpiryDate(vitaCode.getExpiryDate().toString());
                    } else {
                        throw new ApplicationException("Vita code gifting not found", HttpStatus.BAD_REQUEST);
                    }
                }
                case GIFT,
                        E_VOUCHER,
                        E_VOUCHER_SHOP,
                        E_VOUCHER_SHOP_BKIDS -> {
                    Integer expireHour = finalGift.getExpireHour();
                    Timestamp expireDate = finalGift.getExpireDate();
                    Instant expiryInstant = null;
                    if (Objects.nonNull(expireHour)) {
                        expiryInstant = finalCreateDate.plusSeconds(expireHour * 3600L);
                    } else if (Objects.nonNull(expireDate)) {
                        // Convert to LocalDate and get end of the day (23:59:59)
                        LocalDate localDate = expireDate.toLocalDateTime().toLocalDate();
                        LocalDateTime endOfDay = localDate.atTime(23, 59, 59);
                        expiryInstant = endOfDay.toInstant(ZoneOffset.of("+07:00")); // Or your desired zone
                    }
                    String userGiftExpireDate = expiryInstant != null
                            ? DateTimeFormatter.ISO_INSTANT.format(expiryInstant)
                            : null;
                    userGiftDynamicData.setExpiryDate(userGiftExpireDate);

                    if (
                            finalGift.getType() == GiftTypeEnum.E_VOUCHER ||
                                    finalGift.getType() == GiftTypeEnum.E_VOUCHER_SHOP ||
                                    finalGift.getType() == GiftTypeEnum.E_VOUCHER_SHOP_BKIDS
                    ) {
                        userGift.setStatus(UserGift.USER_GIFT_STATUS.NEED_REUSE);
                    }
                }
            }

            userGift.setDynamicData(userGiftDynamicData);
            userGift.setTransactionCode(transactionExternalId);
            userGift = this.userGiftRepository.save(userGift);

            Boolean decreaseGift = requestDto.getDecreaseGift();
            if (Objects.nonNull(decreaseGift) && decreaseGift.equals(Boolean.TRUE)) {
                var quantityUsed = quantity;
                this.giftRepository.incrementQuantityReward(finalGift.getId(), quantityUsed);
                int rowAffected = this.giftRepository.decrementQuantity(finalGift.getId(), quantityUsed);
                if(rowAffected == 0) {
                    throw new ApplicationException("Cannot decrement quantity", HttpStatus.BAD_REQUEST);
                }
            }

            // Update vita code
            if (Objects.nonNull(vitaCode)) {
                vitaCode.setSmsStatus(true);
//                vitaCode.setCreatedDate(finalCreateDate);
//                vitaCode.setExpiryDate(finalExpiredDate);
                this.vitaCodeRepository.save(vitaCode);
            }

            /*var historyPoint = new HistoryPoint();
            historyPoint.setCustomerId(user.getId());
            historyPoint.setCustomerName(user.getFirstName() + " " + user.getLastName());
            historyPoint.setCustomerPhone(user.getPhoneNumber());
            historyPoint.setGiftPoint(point);
            historyPoint.setStatus(Constant.TransactionStatus.SUCCESS);
            historyPoint.setTransactionDate(Instant.now());
            historyPoint.setType(Constant.HistoryPointType.GIFT);
            historyPoint.setActionType(crmTransactionType.getCode());
            historyPoint.setTransactionExternalId(transactionExternalId);
            historyPoint.setBrand(brand);
            historyPoint.setGiftId(userGift.getId());
            historyPoint = this.historyPointRepository.save(historyPoint);*/

            zooTransactionManager.commit(transactionStatus.get(1));
            giftTransactionManager.commit(transactionStatus.get(0));
        } catch (Exception ex) {
            if (!transactionStatus.get(1).isCompleted()) {
                zooTransactionManager.rollback(transactionStatus.get(1));
                log.error("rollback zooTransactionStatus");
            }
            if (!transactionStatus.get(0).isCompleted()) {
                giftTransactionManager.rollback(transactionStatus.get(0));
                log.error("rollback giftTransactionStatus");
            }
            log.error("Gifting gift error: ", ex);

            throw ex;
        }

        //UserGift userGift = (UserGift) dataGifting.get("user_gift");
        userGift.setCreatedAt(finalCreateDate);
        this.userGiftRepository.save(userGift);
        //HistoryPoint historyPoint = (HistoryPoint) dataGifting.get("history_point");
        response.setUserGiftId(userGift.getId());
        //response.setHistoryPointId(historyPoint.getId());
        response.setTransactionExternalId(transactionExternalId);
        response.setUserGiftStatus(userGift.getStatus().toString());
        response.setSourceGift(sourceGift);
        response.setGiftType(String.valueOf(gift.getType()));
        if (gift.getType() == GiftTypeEnum.EV_VITA_CODE) {
            response.setVitaCode(userGift.getDynamicData().getVoucherCode());
        }

        return response;
    }

    public String removeUserGiftGifting(GiftRemoveUserGiftGiftingRequestDto requestDto) {
        Long userGiftId = requestDto.getUserGiftId();
        Boolean increaseGift = requestDto.getIncreaseGift();
        Integer quantity = requestDto.getQuantity();
        Integer giftId = requestDto.getGiftId();
        Long vitaCodeId = requestDto.getVitaCodeId();

        Gift gift = this.giftRepository.findById(giftId).orElse(null);
        UserGift userGift = this.userGiftRepository.findById(userGiftId.intValue()).orElse(null);
        if (Objects.isNull(gift) || Objects.isNull(userGift)) {
            throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
        }

        List<TransactionStatus> transactionStatus = this.initTransaction();
        try {
            this.userGiftRepository.deleteById(requestDto.getUserGiftId().intValue());

            if (Objects.nonNull(increaseGift) && increaseGift.equals(Boolean.TRUE)) {
                var quantityUsed = quantity;
                this.giftRepository.incrementQuantity(gift.getId(), quantityUsed);
                int rowAffected = this.giftRepository.decrementQuantityReward(gift.getId(), quantityUsed);
                if(rowAffected == 0) {
                    throw new ApplicationException("Cannot decrement quantity reward", HttpStatus.BAD_REQUEST);
                }
            }

//            if (Objects.nonNull(vitaCodeId)) {
//                VitaCode vitaCode = this.vitaCodeRepository.findById(vitaCodeId).orElse(null);
//                if (Objects.nonNull(vitaCode)) {
//                    vitaCode.setSmsStatus(false);
//                    this.vitaCodeRepository.save(vitaCode);
//                }
//            }

//            zooTransactionManager.commit(transactionStatus.get(1));
            giftTransactionManager.commit(transactionStatus.get(0));
        } catch (Exception ex) {
//            if (!transactionStatus.get(1).isCompleted()) {
//                zooTransactionManager.rollback(transactionStatus.get(1));
//                log.error("rollback zooTransactionStatus");
//            }
            if (!transactionStatus.get(0).isCompleted()) {
                giftTransactionManager.rollback(transactionStatus.get(0));
                log.error("rollback giftTransactionStatus");
            }
            log.error("Remove gifting gift error: ", ex);

            throw ex;
        }

        return "ok";
    }

    public void handleUseGift(User user, UserGift userGift, HistoryPoint historyPoint, String giftFunction, String transactionDate) {
        String returnPointTxId = "";
        switch (giftFunction) {
            case GiftFunctionDefs.EXCHANGE_GIFT -> {
                returnPointTxId = IdUtils.generateId(ConstantFieldDefs.EXCHANGE_GIFT);
            }
            case GiftFunctionDefs.GIFTING_GIFT -> {
                returnPointTxId = IdUtils.generateId(ConstantFieldDefs.GIFTING_GIFT_PREFIX);
            }
            case GiftFunctionDefs.USING_GIFT -> {
                returnPointTxId = this.commonUtil.generateUniqueVoucherRefId();
            }
            default -> {
                returnPointTxId = IdUtils.generateId(ConstantFieldDefs.TRANSACTION_CODE_PREFIX);
            }
        }
        Gift gift = userGift.getGift();
        try {
            switch (gift.getType()) {
                case E_VOUCHER -> {
                    Long amount = Long.valueOf(String.valueOf(gift.getPrice()));
                    this.useTopup(user, userGift, historyPoint, returnPointTxId, amount, transactionDate);
                }
                case E_VOUCHER_SHOP -> {
                    this.useEvoucherShop(user, userGift, historyPoint, returnPointTxId);
                }
                case E_VOUCHER_SHOP_BKIDS -> {
                    this.useEvoucherBkids(user, userGift, historyPoint, returnPointTxId);
                }
                case EV_VITA_CODE -> {
                    this.useEvoucherVitaCode(user, userGift, historyPoint, returnPointTxId);
                }
            }
        } catch (Exception e) {
            if (
                    gift.getType() != GiftTypeEnum.EV_VITA_CODE &&
                            this.checkGiftBelongToEvent(gift)
            ) {
                this.handleUserGiftNeedReuse(
                        user,
                        userGift
                );
            } else {
                this.handleReturnPoint(
                        user,
                        userGift,
                        historyPoint,
                        returnPointTxId);
            }
        }
    }

    public void sendUpdateGiftSf(List<String> returnPointTxIds){
        if(!CollectionUtils.isEmpty(returnPointTxIds)){
            returnPointTxIds.forEach(txId -> {
                try{
                    UserGift userGifts = this.userGiftRepository.findFirstByTransactionCode(txId).orElse(null);
                    if(Objects.nonNull(userGifts)){
                        String sfCode = this.generateCodeEndPointExchangeGiftCrm(userGifts.getGift());
                        User user = this.userRepository.getUserById(userGifts.getUserId())
                                .orElseThrow(() -> new EntityNotFoundException("Not found user id " +
                                        userGifts.getUserId()));
                        var payload = new HashMap<String, Object>();
                        if(userGifts.getGift().getType() == GiftTypeEnum.GIFT){
                            payload.put("ref_id", userGifts.getTransactionCode());
                            payload.put("user_gift_id", userGifts.getId());
                            payload.put("phoneNumber", user.getPhoneNumber());
                            payload.put("user_id", String.valueOf(user.getId()));
                            payload.put("status_user_gift", "Returned");
                        }
                        else if(userGifts.getGift().getType() == GiftTypeEnum.E_VOUCHER ||
                                userGifts.getGift().getType() == GiftTypeEnum.E_VOUCHER_SHOP ||
                                userGifts.getGift().getType() == GiftTypeEnum.E_VOUCHER_SHOP_BKIDS ||
                                userGifts.getGift().getType() == GiftTypeEnum.EV_VITA_CODE){
                            payload.put("ref_id", userGifts.getTransactionCode());
                            payload.put("user_id", userGifts.getUserId());
                            payload.put("phoneNumber", user.getPhoneNumber());
                            payload.put("status", "Returned");
                            payload.put("User_Gift_Id__c", userGifts.getId());
                        }

                        var batchDto = new TransactionBatchDto();
                        batchDto.setTransactions(List.of(
                                new TransactionDto().setCode(sfCode).setPayload(payload)));
                        whTransactionService.send(batchDto);
                    }
                }
                catch (Exception ex){
                    log.error("Error when update gift for txId: " + txId, ex);
                }
            });
        }
    }

    public List<GiftReturnPointResponseDto> doReturnPoint(List<GiftReturnPointRequestDto> dto) {
        String codeEndPointCrm = "RETURN_POINT_TLIST";
        List<GiftReturnPointResponseDto> responseResult = new ArrayList<>();
        Map<Long, List<GiftReturnPointRequestDto>> requestByUserId = dto.stream().collect(Collectors.groupingBy(GiftReturnPointRequestDto::getUserId));
        requestByUserId.forEach((userId, requestList) -> {
            User user = this.userRepository.getUserById(userId)
                    .orElseThrow(() -> new EntityNotFoundException("Not found user id " +
                            userId));
            List<Map<String, Object>> sfPayload = new ArrayList<>();
            requestList.forEach(request -> {
                GiftReturnPointResponseDto rs = GiftReturnPointResponseDto.builder()
                        .session(request.getSession())
                        .line(request.getLine())
                        .status(ReturnPointStatusEnum.SUCCESS)
                        .build();
                UserGift userGift;
                try{
                    userGift = this.userGiftRepository.findById(request.getUserGiftId().intValue())
                            .orElseThrow(() -> new EntityNotFoundException("Not found user gift id " +
                                    request.getUserGiftId()));
                    String spendPointTxId = userGift.getTransactionCode();
                    String returnPointTxId = IdUtils.generateId(ConstantFieldDefs.RETURN_POINT);;
                    HistoryPoint historyPoint = this.handleReturnPointInternal(user, userGift, spendPointTxId, returnPointTxId);
                    if(Objects.nonNull(historyPoint)) {
                        Map<String, Object> sfPayloadData = this.generatePayloadDataReturnGiftCrm(
                                user,
                                userGift,
                                historyPoint,
                                returnPointTxId,
                                spendPointTxId,
                                request.getReasonForSf());
                        /*if(Objects.nonNull(request.getOrderCode())){
                            sfPayloadData.put("order_id", request.getOrderCode());
                        }--remove on ticket 3455*/
                        if(!CollectionUtils.isEmpty(sfPayloadData)){
                            sfPayload.add(sfPayloadData);
                        }
                    }
                    pushNotificationReturnPoint(userId, request.getReasonForNotification(), Objects.nonNull(userGift) && Objects.nonNull(userGift.getGift()) ? userGift.getGift().getName() : "");

//                    if (List.of(137135L, 137136L, 137138L, 137141L).contains(user.getId())) {
//                        throw new ApplicationException("Test exchange fail and lose data", HttpStatus.BAD_REQUEST);
//                    }
                }
                catch (Exception ex){
                    log.error("Error when return point for userId: " + userId, ex);
                    rs.setStatus(ReturnPointStatusEnum.FAILED);
                    rs.setMessage(ex.getMessage());
                }
                responseResult.add(rs);
            });
            if(!CollectionUtils.isEmpty(sfPayload)) {
                var request = TransactionBatchDto.builder().transactions(List.of(
                        new TransactionDto().setCode(codeEndPointCrm).setTListPayload(sfPayload)
                )).build();
                whTransactionService.send(request);
            }
        });
        return responseResult;
    }

    private void pushNotificationReturnPoint(Long userId, String reason, String giftName) {
        String description = String.format(RETURN_POINT_DESCRIPTION, giftName, reason);
        CustomNotificationRequestDto customNotificationRequestDto = CustomNotificationRequestDto.builder()
                .userIds(new Long[]{userId})
                .title(RETURN_POINT_TITLE)
                .content(RETURN_POINT_CONTENT)
                .description(description)
                .data(NotificationData.builder().deeplink("").description(description).build())
                .featureNoti(FeatureNoti.NOTI_RETURN_POINT)
                .build();
        notificationService.push(customNotificationRequestDto);
    }

    public HistoryPoint handleReturnPointInternal(
            User user,
            UserGift userGift,
            String spendPointTxId,
            String returnPointTxId) {
        Gift gift = userGift.getGift();
        var transactionStatus = this.initTransaction();
        HistoryPoint historyPoint = null;
        var oldGiftQuantity = userGift.getQuantity();
        var decreaseGiftPoint = DeductPointResponseDto.builder().success(false).build();
        float totalPointBefore = user.getGiftPoint();
        try {
            if(Objects.nonNull(userGift.getDynamicData())){
                UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
                userGiftDynamicData.setExpiryDate(null);
                userGiftDynamicData.setUsedDate(null);
                userGiftDynamicData.setVoucherLink(null);
                userGiftDynamicData.setVoucherCode(null);
                userGiftDynamicData.setVoucherRefId(null);
                userGiftDynamicData.setIsUsed(null);
                userGiftDynamicData.setRecipientPhone(null);
                userGift.setDynamicData(userGiftDynamicData);
            }
            userGift.setStatus(UserGift.USER_GIFT_STATUS.FAILED);
            if (Objects.isNull(userGift.getTransactionCode())) {
                userGift.setTransactionCode(returnPointTxId);
            }
            this.userGiftRepository.save(userGift);

            // Update gift
            int rowAffected = this.giftRepository.decrementQuantityReward(gift.getId(), oldGiftQuantity);
            if(rowAffected == 0) {
                throw new ApplicationException("Cannot decrement quantity reward", HttpStatus.BAD_REQUEST);
            }
            this.giftRepository.incrementQuantity(gift.getId(), oldGiftQuantity);

            // Update user
            decreaseGiftPoint = userService.returnPoint(user.getId(), 0, userGift.getPoint(), 0);
            if(BooleanUtils.isNotTrue(decreaseGiftPoint.isSuccess())){
                throw new ApplicationException("return gift point error", VtdStatusEnum.GF_POINT);
            }

            // Save history point
            historyPoint = this.pointTransactionService.saveHistoryReturnPoint(
                    user,
                    userGift,
                    returnPointTxId,
                    spendPointTxId);

            // Get again to have fresh value of gift_point
            User userUpdated = this.userRepository.getUserById(user.getId()).orElse(null);
            float totalPointAfter = userUpdated.getGiftPoint();
            DDXTrackingUserActionWithPointRequestDto dto = this.ddxService.generateDataTracking(
                    user,
                    EnumHistoryPointType.ADD_POINT,
                    EnumHistoryPointActionType.RETURN_POINT,
                    totalPointBefore,
                    totalPointAfter - totalPointBefore,
                    totalPointAfter,
                    historyPoint.getTransactionExternalId(),
                    historyPoint.getTransactionDate()
            );
            this.ddxService.trackingUserActionWithPoint(dto);

            zooTransactionManager.commit(transactionStatus.get(1));
            giftTransactionManager.commit(transactionStatus.get(0));
        } catch (Exception ex) {
            if (!transactionStatus.get(1).isCompleted()) {
                zooTransactionManager.rollback(transactionStatus.get(1));
                log.error("rollback zooTransactionStatus");
            } else {
                try {
                    if (historyPoint != null) {
                        this.historyPointRepository.delete(historyPoint);
                        historyPoint = null;
                    }
                    if (BooleanUtils.isTrue(decreaseGiftPoint.isSuccess())) {
                        userService.returnPoint(user.getId(), 0, -1 *userGift.getPoint(), 0);
                    }
                } catch (Exception subEx) {
                    log.error("Rollback data by delete zoo data failed : ", subEx);
                }
            }
            if (!transactionStatus.get(0).isCompleted()) {
                giftTransactionManager.rollback(transactionStatus.get(0));
                log.error("rollback giftTransactionStatus");
            }
            log.error("handleReturnPointInternal error: ", ex);

            throw ex;
        }
        return historyPoint;
    }

    public void handleReturnPoint(
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            String returnPointTxId) {
        String spendPointTxId = Objects.nonNull(historyPoint) ? historyPoint.getTransactionExternalId() : null;
        Gift gift = userGift.getGift();
        var transactionStatues = this.initTransaction();
        boolean isOk = false;
        HistoryPoint historyReturnedPoint = null;
        var decreaseGiftPoint = DeductPointResponseDto.builder().success(false).build();
        float totalPointBefore = user.getGiftPoint();
        try {
            try{
                // Update user gift
                UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
                userGiftDynamicData.setExpiryDate(null);
                userGiftDynamicData.setUsedDate(null);
                userGiftDynamicData.setVoucherLink(null);
                userGiftDynamicData.setVoucherCode(null);
                userGiftDynamicData.setVoucherRefId(null);
                userGiftDynamicData.setIsUsed(null);
                userGiftDynamicData.setRecipientPhone(null);
                userGift.setDynamicData(userGiftDynamicData);
                userGift.setStatus(UserGift.USER_GIFT_STATUS.FAILED);
                this.userGiftRepository.save(userGift);

                // TODO: process decreaseNumberOfCanUsed

                // Update gift
                var oldGiftQuantity = userGift.getQuantity();
                //gift.setQuantity(oldGiftQuantity + userGift.getQuantity());
                //this.giftRepository.save(gift);
                int rowAffected = this.giftRepository.decrementQuantityReward(gift.getId(), oldGiftQuantity);
                if(rowAffected == 0) {
                    throw new ApplicationException("Cannot decrement quantity reward", HttpStatus.BAD_REQUEST);
                }
                this.giftRepository.incrementQuantity(gift.getId(), oldGiftQuantity);

                // Update user
                decreaseGiftPoint = userService.returnPoint(user.getId(), userGift.getStockPoint(), userGift.getPoint(), 0);
                if(BooleanUtils.isNotTrue(decreaseGiftPoint.isSuccess())){
                    throw new ApplicationException("return gift point error", VtdStatusEnum.GF_POINT);
                }

                // Save history point
                historyReturnedPoint = this.pointTransactionService.saveHistoryReturnPoint(
                        user,
                        userGift,
                        returnPointTxId,
                        spendPointTxId);

                // Get again to have fresh value of gift_point
                User userUpdated = this.userRepository.getUserById(user.getId()).orElse(null);
                float totalPointAfter = userUpdated.getGiftPoint();
                // TODO: gift point of user before updated (totalPointBefore) not right, need research more
                // TODO: temporary solution: get point of user gift and calculate
                totalPointBefore = totalPointAfter - userGift.getPoint();
                DDXTrackingUserActionWithPointRequestDto dto = this.ddxService.generateDataTracking(
                        user,
                        EnumHistoryPointType.ADD_POINT,
                        EnumHistoryPointActionType.RETURN_POINT,
                        totalPointBefore,
                        totalPointAfter - totalPointBefore,
                        totalPointAfter,
                        historyReturnedPoint.getTransactionExternalId(),
                        historyReturnedPoint.getTransactionDate()
                );
                this.ddxService.trackingUserActionWithPoint(dto);

                zooTransactionManager.commit(transactionStatues.get(1));
                giftTransactionManager.commit(transactionStatues.get(0));
                isOk = true;
            }
            catch (Exception ex){
                log.error("Error when return point for userGiftId: " + userGift.getId(), ex);
                if (!transactionStatues.get(1).isCompleted()) {
                    zooTransactionManager.rollback(transactionStatues.get(1));
                    log.error("rollback zooTransactionStatus");
                } else {
                    try {
                        if (historyReturnedPoint != null) {
                            this.historyPointRepository.delete(historyReturnedPoint);
                        }
                        if (BooleanUtils.isTrue(decreaseGiftPoint.isSuccess())) {
                            userService.returnPoint(user.getId(), -1 * userGift.getStockPoint(), -1 * userGift.getPoint(), 0);
                        }
                    } catch (Exception subEx) {
                        log.error("Rollback data by delete zoo data failed : ", subEx);
                    }
                }
                if (!transactionStatues.get(0).isCompleted()) {
                    giftTransactionManager.rollback(transactionStatues.get(0));
                    log.error("rollback giftTransactionStatus");
                }
                log.error("handleReturnPoint error: ", ex);
            }

            if (isOk) {
                this.syncUpdateEventPointHistoryToWh(
                        userGift,
                        historyPoint
                );
                // Send return point
                var payload = new HashMap<String, Object>();
                payload = this.generatePayloadDataReturnGiftCrm(
                        user,
                        userGift,
                        historyPoint,
                        returnPointTxId,
                        spendPointTxId,
                        null);
                String codeEndPointCrm = this.generateCodeEndPointReturnGiftCrm();
                if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                    var batchDto = TransactionBatchDto.builder().transactions(List.of(
                            new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                    whTransactionService.send(batchDto);
                }
                // Send returned point
                payload = this.generatePayloadDataReturnedGiftCrm(
                        user,
                        spendPointTxId);
                codeEndPointCrm = this.generateCodeEndPointReturnedGiftCrm();
                if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                    var batchDto = TransactionBatchDto.builder().transactions(List.of(
                            new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                    whTransactionService.send(batchDto);
                }
                // TODO: pushNotificationReturnPoint
            }
        } catch (Exception e) {
            this.handeReturnPointFail(e);
        }
    }

    public void handleUserGiftNeedReuse(
            User user,
            UserGift userGift) {
        //UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        //userGiftDynamicData.setIsReusing(Boolean.FALSE);
        userGift.setStatus(UserGift.USER_GIFT_STATUS.NEED_REUSE);
        //userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepository.save(userGift);
    }

    public GetQuantityGiftDto getNewGiftQuantityFromOldGift(GiftGetQuantityRequestDto requestDto) {
        GetQuantityGiftDto response = new GetQuantityGiftDto();
        response.setQuantity(0L);
        Integer oldGiftId = requestDto.getOldGiftId();
        if (Objects.isNull(oldGiftId)) {
            return response;
        }
        MapOldGiftToNewGift mapOldGiftToNewGift = this.mapOldGiftToNewGiftRepository.findFirstByOldGiftId(Long.valueOf(oldGiftId)).orElse(null);
        if (Objects.isNull(mapOldGiftToNewGift)) {
            return response;
        }
        Gift gift = mapOldGiftToNewGift.getNewGift();
        if (Objects.isNull(gift)) {
            return response;
        }
        response.setQuantity(gift.getQuantity());

        return response;
    }

    public boolean checkGiftBelongToEvent(Gift gift) {
        if (Objects.isNull(gift)) {
            return false;
        }
//        MapOldGiftToNewGift item = this.mapOldGiftToNewGiftRepository.findFirstByNewGiftId(
//                gift.getId()
//        ).orElse(null);
//        if (Objects.isNull(item)) {
//            return false;
//        }
//        Long oldGiftIid = item.getOldGiftId();
//        if (Objects.isNull(oldGiftIid)) {
//            return false;
//        }
        EventDetail eventDetail = this.eventDetailRepository.findFirstByGsGiftId(Long.valueOf(gift.getId())).orElse(null);
        EventCanMark eventCanMark = this.eventCanMarkRepository.findFirstByGsGiftId(Long.valueOf(gift.getId())).orElse(null);
        if (Objects.nonNull(eventDetail) || Objects.nonNull(eventCanMark)) {
            return true;
        }

        return false;
    }

    public void sendWhUsingGiftCrm(
            UserGift userGift,
            User user,
            Gift gift,
            HistoryPoint historyPoint
    ) {
        var payload = new HashMap<String, Object>();
        payload = this.generatePayloadDataUsingGiftCrm(
                gift,
                user,
                userGift,
                historyPoint,
                null);
        String codeEndPointCrm = this.generateCodeEndPointUsingGiftCrm(gift);

        if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
            var batchDto = TransactionBatchDto.builder().transactions(List.of(
                    new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
            whTransactionService.send(batchDto);
        }

    }

    private void handeReturnPointFail(Exception e) {
        log.error("Error while handle return point", e);
    }

    private void useTopup(User user, UserGift userGift, HistoryPoint historyPoint, String returnPointTxId,
                          Long amount, String transactionDate) {
        Gift gift = userGift.getGift();
        // Force update user gift voucher ref id -> use to debug and report
        // Should do this because avoid miss data if the transaction not done
        this.updateUserGiftVoucherRefId(userGift, returnPointTxId);
        this.userGiftRepository.save(userGift);
        Object topupRs = transactionTemplate.execute(status -> {
            // Begin using evoucher
            this.updateUserGiftByTopup(userGift, user, transactionDate);
            String topupServiceActive = this.eVoucherService.getActiveService();
            Object topupResponse = this.eVoucherService.topup(
                    user,
                    user.getPhoneNumber(),
                    amount,
                    returnPointTxId,
                    historyPoint);
            switch (topupServiceActive) {
                case Constant.EvoucherService.GOT_IT -> {
                    TopupResponseV1Dot4 gotitTopupResponse = (TopupResponseV1Dot4) topupResponse;
                    String topupResponseRefId = gotitTopupResponse.getRefId();
                    if (Objects.isNull(topupResponseRefId) || topupResponseRefId.isEmpty()) {
                        topupResponseRefId = returnPointTxId;
                        gotitTopupResponse.setRefId(returnPointTxId);
                    }
                    // this.updateUserGiftVoucherRefId(userGift, topupResponseRefId);
                    gotitTopupResponse = (TopupResponseV1Dot4) this.eVoucherService.processTopupResponse(
                            userGift,
                            gotitTopupResponse);
                    if (Objects.isNull(gotitTopupResponse)) {
                        throw new ApplicationException("Gotit topup error", HttpStatus.INTERNAL_SERVER_ERROR);
                    }
                }
            }
            // this.userGiftRepository.save(userGift);
            return topupResponse;
        });
        if (Objects.nonNull(topupRs)) {
            this.syncUpdateEventPointHistoryToWh(
                    userGift,
                    historyPoint
            );
            if (!userGift.getStatus().equals(UserGift.USER_GIFT_STATUS.TOPUP_IS_PROCESSING)) {
                this.sendWhUsingGiftCrm(
                        userGift,
                        user,
                        gift,
                        historyPoint
                );
            }
        }
    }

    private void useEvoucherShop(
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            String returnPointTxId) {
        String phoneNumber = user.getPhoneNumber();
        /*if (phoneNumber.equals("0564023184") ||
                phoneNumber.equals("0564023185")) {
            throw new ApplicationException("Gotit get evoucher error", HttpStatus.INTERNAL_SERVER_ERROR);
        }*/
        Gift gift = userGift.getGift();
        Object evoucherShopRs = transactionTemplate.execute(status -> {
            String service = Constant.EvoucherShopService.GOT_IT;
            Object evoucherShopResponse = this.eVoucherShopService.getVoucher(
                    user,
                    gift,
                    returnPointTxId,
                    service,
                    historyPoint);
            if (Objects.isNull(evoucherShopResponse)) {
                throw new ApplicationException("Gotit get evoucher error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            GetVoucherResponse getVoucherResponse = (GetVoucherResponse) evoucherShopResponse;
            List<VoucherResponse> vouchers = getVoucherResponse.getVouchers();
            if (CollectionUtils.isEmpty(vouchers)) {
                throw new ApplicationException("Gotit get evoucher error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            VoucherResponse voucher = vouchers.get(0);
            voucher = (VoucherResponse) this.eVoucherShopService.processEVoucherResponse(
                    userGift,
                    voucher,
                    service);

            return voucher;
        });
        if (Objects.nonNull(evoucherShopRs)) {
            this.syncUpdateEventPointHistoryToWh(
                    userGift,
                    historyPoint
            );
            var payload = new HashMap<String, Object>();
            payload = this.generatePayloadDataUsingGiftCrm(
                    gift,
                    user,
                    userGift,
                    historyPoint,
                    evoucherShopRs);
            String codeEndPointCrm = this.generateCodeEndPointUsingGiftCrm(gift);

            if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                var batchDto = TransactionBatchDto.builder().transactions(List.of(
                        new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                whTransactionService.send(batchDto);
            }     
        }
    }

    private void useEvoucherBkids(
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            String returnPointTxId) {
        String phoneNumber = user.getPhoneNumber();
        /*if (phoneNumber.equals("0564023184") ||
                phoneNumber.equals("0564023185")) {
            throw new ApplicationException("Bkids get evoucher error", HttpStatus.INTERNAL_SERVER_ERROR);
        }*/
        Gift gift = userGift.getGift();
        Object couponBkidsRs = transactionTemplate.execute(status -> {
            String service = Constant.EvoucherShopService.BKIDS;
            Object evoucherBkidResponse = this.eVoucherShopService.getVoucher(
                    user,
                    gift,
                    returnPointTxId,
                    service,
                    historyPoint);
            if (Objects.isNull(evoucherBkidResponse)) {
                throw new ApplicationException("Bkids get coupon error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            RequestDetailCouponDataDetailCouponResponse detail = (RequestDetailCouponDataDetailCouponResponse) evoucherBkidResponse;
            if (Objects.isNull(detail)) {
                throw new ApplicationException("Bkids get evoucher error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            detail = (RequestDetailCouponDataDetailCouponResponse) this.eVoucherShopService.processEVoucherResponse(
                    userGift,
                    detail,
                    service);

            return detail;
        });
        if (Objects.nonNull(couponBkidsRs)) {
            this.syncUpdateEventPointHistoryToWh(
                    userGift,
                    historyPoint
            );
            var payload = new HashMap<String, Object>();
            payload = this.generatePayloadDataUsingGiftCrm(
                    gift,
                    user,
                    userGift,
                    historyPoint,
                    null);
            String codeEndPointCrm = this.generateCodeEndPointUsingGiftCrm(gift);

            if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                var batchDto = TransactionBatchDto.builder().transactions(List.of(
                        new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                whTransactionService.send(batchDto);
            }
        }
    }

    private void useEvoucherVitaCode(
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            String returnPointTxId) {
        String phoneNumber = user.getPhoneNumber();
        /*if (phoneNumber.equals("0564023184") ||
                phoneNumber.equals("0564023185")) {
            throw new ApplicationException("Vita get code error", HttpStatus.INTERNAL_SERVER_ERROR);
        }*/
        Gift gift = userGift.getGift();
        Object vitaCodeRs = transactionTemplate.execute(status -> {
            String service = Constant.EvoucherShopService.VITA_CODE;
            Object evoucherVitaCodeResponse = this.eVoucherShopService.getVoucher(
                    user,
                    gift,
                    returnPointTxId,
                    service,
                    historyPoint);
            if (Objects.isNull(evoucherVitaCodeResponse)) {
                throw new ApplicationException("Vita get code error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            String code = (String) evoucherVitaCodeResponse;
            if (code.isEmpty()) {
                throw new ApplicationException("Vita get code error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            code = (String) this.eVoucherShopService.processEVoucherResponse(
                    userGift,
                    code,
                    service);

            return code;
        });
        if (Objects.nonNull(vitaCodeRs)) {
            this.syncUpdateEventPointHistoryToWh(
                    userGift,
                    historyPoint
            );
            var payload = new HashMap<String, Object>();
            payload = this.generatePayloadDataUsingGiftCrm(
                    gift,
                    user,
                    userGift,
                    historyPoint,
                    null);
            String codeEndPointCrm = this.generateCodeEndPointUsingGiftCrm(gift);

            if (!codeEndPointCrm.isEmpty() && Objects.nonNull(payload)) {
                var batchDto = TransactionBatchDto.builder().transactions(List.of(
                        new TransactionDto().setCode(codeEndPointCrm).setPayload(payload))).build();
                whTransactionService.send(batchDto);
            }
        }
    }

    private void updateUserGiftByTopup(UserGift userGift, User user, String transactionDate) {
        Instant useDate = Instant.now();
        if (Objects.nonNull(transactionDate)) {
            useDate = DateTimeUtil.convertStringToInstant(
                    transactionDate,
                    Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS,
                    Constant.TIMEZONE.UTC_ZONE
            );
        }
        Instant finalUsedDate = Objects.nonNull(useDate) ? useDate : Instant.now();
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setRecipientPhone(user.getPhoneNumber());
        if (Objects.isNull(userGiftDynamicData.getUsedDate())) {
            userGiftDynamicData.setUsedDate(finalUsedDate);
        }
        userGiftDynamicData.setIsUsed(true);
        userGift.setDynamicData(userGiftDynamicData);
    }

    private void updateUserGiftVoucherRefId(UserGift userGift, String voucherRefId) {
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setVoucherRefId(voucherRefId);
        userGift.setDynamicData(userGiftDynamicData);
    }

    private void validateBeforeExchange(Gift gift, User user, PhysicalGiftExchangeRequestDto body) {
        var current = Timestamp.from(Instant.now());
        GiftTypeEnum giftType = gift.getType();

        if (Constant.AccountStatus.NON_ACTIVE.equalsIgnoreCase(user.getAccountStatus())) {
            throw new ApplicationException("Account inactive", VtdStatusEnum.GF_USER);
        }

        if (!this.checkUserCanExchangeGift(user, gift)) {
            throw new ApplicationException("Invalid user tier", VtdStatusEnum.GF_TIER);
        }

        if (!this.checkUserIsNotProcessingByDDX(user)) {
            throw new ApplicationException("Account is processing reset point", VtdStatusEnum.GF_USER);
        }

        if (body.getQuantity() > 1 &&
                (giftType.equals(GiftTypeEnum.E_VOUCHER) ||
                        giftType.equals(GiftTypeEnum.E_VOUCHER_SHOP) ||
                        giftType.equals(GiftTypeEnum.EV_VITA_CODE))) {
            throw new ApplicationException("Sorry, this gift currently only supports exchanging with quantity equal 1.",
                    HttpStatus.BAD_REQUEST);
        }

        if (Objects.isNull(gift.getStartDate()) || Objects.isNull(gift.getEndDate())) {
            throw new ApplicationException("Invalid gift time", VtdStatusEnum.GF_TIME);
        }

        if (gift.getStartDate().after(current) || gift.getEndDate().before(current)) {
            throw new ApplicationException("Invalid gift time", VtdStatusEnum.GF_TIME);
        }

        /*
         * if (CollectionUtils.isEmpty(gift.getTierCodes()) ||
         * !gift.getTierCodes().contains(user.getTierCode())) {
         * throw new ApplicationException("Invalid user tier", HttpStatus.BAD_REQUEST);
         * }
         */

        if (gift.getQuantity() < body.getQuantity()) {
            throw new ApplicationException("Invalid gift quantity", VtdStatusEnum.GF_QUANTITY);
        }

        var totalPoint = body.getQuantity() * gift.getPoint();
        if (user.getGiftPoint() + user.getStockPoint() < totalPoint) {
            throw new ApplicationException("Not enough gift point", VtdStatusEnum.GF_POINT);
        }

        if (gift.getType() == GiftTypeEnum.GIFT && gift.getInventory() == 0) {
            throw new ApplicationException("Not enough gift inventory", VtdStatusEnum.GF_STORAGE);
        }
    }

    private void validateExchangeBrandGift(
            MapGiftToBrandPoint mapGiftToBrandPoint,
            MapUserToBrandPoint mapUserToBrandPoint
    ) {
        if (Objects.isNull(mapUserToBrandPoint)) {
            throw new ApplicationException("Bạn chưa tích xu brand.",
                    HttpStatus.BAD_REQUEST);
        }
        if (mapUserToBrandPoint.getBrandPoint().equals(0F)) {
            throw new ApplicationException("Bạn đã sử dụng hết xu brand.",
                    HttpStatus.BAD_REQUEST);
        }
        if (mapUserToBrandPoint.getBrandPoint() < mapGiftToBrandPoint.getBrandPoint()) {
            throw new ApplicationException("Bạn không đủ xu brand để đổi.",
                    HttpStatus.BAD_REQUEST);
        }
    }

    private void saveUserGiftDynamicDataWhenExchangeGift(Gift gift, UserGift userGift) {
        UserGiftDynamicData userGiftDynamicData = new UserGiftDynamicData();
        CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository
                .findFirstByCode(CrmTransactionTypeCode.REWARD_GIFT).orElse(null);

        switch (gift.getType()) {
            case E_VOUCHER, E_VOUCHER_SHOP, EV_VITA_CODE -> {
                // TODO: get setup base on REWARD_GIFT or crm transaction type on table event
                // detail
                crmTransactionType = this.crmTransactionTypeRepository
                        .findFirstByCode(CrmTransactionTypeCode.REWARD_GIFT).orElse(null);
            }
        }

        if (Objects.nonNull(crmTransactionType)) {
            userGiftDynamicData.setActionType(crmTransactionType.getCode());
        }

        // Set expired date to user gift
        if (gift.getExpireHour() != null) {
            Instant expiredDate = Instant.now().plusSeconds(gift.getExpireHour() * 3600);
            userGiftDynamicData.setExpiryDate(expiredDate.toString());
        }

        if(gift.getType() == GiftTypeEnum.GIFT){
            Instant expiredDate = null;
            if(Objects.nonNull(gift.getExpireDate())){
                expiredDate = gift.getExpireDate().toInstant().plus(1, ChronoUnit.DAYS);
            }
            else if(Objects.nonNull(gift.getExpireHour())){
                expiredDate = Instant.now().plusSeconds(gift.getExpireHour() * 3600);
            }
            if(Objects.nonNull(expiredDate)){
                userGiftDynamicData.setExpiryDate(expiredDate.toString());
            }
        }

        userGift.setDynamicData(userGiftDynamicData);
    }

    private HistoryPoint saveHistoryPoint(Gift gift, User user, UserGift userGift, PhysicalGiftExchangeRequestDto body) {
        String brand = null;
        GiftDynamicData giftDynamicData = gift.getDynamicData();
        if (Objects.nonNull(giftDynamicData) && Objects.nonNull(giftDynamicData.getBrandId())) {
            brand = giftDynamicData.getBrandId().toString();
        }

        return this.pointTransactionService.saveHistoryPoint(
                user.getId(), CrmTransactionTypeCode.REWARD_GIFT,
                userGift.getPoint(), userGift.getStockPoint(), 0, 0L, Constant.HistoryPointType.SPEND_POINT, userGift,
                brand, null, body.getQuantity());
    }

    private boolean checkUserCanExchangeGift(User user, Gift gift) {
        if (Objects.isNull(user) || Objects.isNull(gift)) {
            return false;
        }

        return !CollectionUtils.isEmpty(gift.getTierCodes()) && Objects.nonNull(user.getTierCode())
                && gift.getTierCodes().contains(user.getTierCode());
    }

    private boolean checkUserIsNotProcessingByDDX(User user) {
        if (Objects.isNull(user)) {
            return false;
        }

        return user.checkUserIsNotProcessingByDDX();
    }

    public HashMap<String, Object> generatePayloadDataExchangeGiftCrm(
            Gift gift,
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            Float totalPoint) {
        var payload = new HashMap<String, Object>();
        GiftDynamicData giftDynamicData = gift.getDynamicData();
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();

        switch (gift.getType()) {
            case GIFT -> {
                payload.put("ref_id", historyPoint.getTransactionExternalId());
                payload.put("phoneNumber", user.getPhoneNumber());
                payload.put("gift_id", Objects.nonNull(gift.getDynamicData()) ? gift.getDynamicData().getRewardAppGiftCode() : "");
                payload.put("status_user_gift", userGift.getStatus());
                payload.put("user_id", String.valueOf(user.getId()));
                payload.put("user_gift_id", String.valueOf(userGift.getId()));
                payload.put("gift_name", gift.getName());
                payload.put("quantity", String.valueOf(userGift.getQuantity()));
                payload.put("recipientName", "");
                payload.put("recipientPhone", "");
                payload.put("recipientAddress", "");
                payload.put("action_type", CrmTransactionTypeCode.REWARD_GIFT);
                payload.put("Description__c", "");
                payload.put("point", totalPoint);
                payload.put("Rule_Name__c", "Đổi điểm nhận quà");
                payload.put("Campaign__c", "");
                payload.put("Transaction_Type__c", "Gift Exchange");
                payload.put("Type__c", "Spending");
                payload.put("Expiry_date__c", "");
                payload.put("Transaction_Date__c", LocalDateTime.now());
                payload.put("Gift_Price__c", gift.getPrice());
                payload.put("Gifts_Source__c", giftDynamicData.getSourceGift());
            }
            case E_VOUCHER, E_VOUCHER_SHOP, EV_VITA_CODE -> {
                // TODO: get setup base on REWARD_GIFT or crm transaction type on table event
                // detail
                CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository
                        .findFirstByCode(userGift.getDynamicData().getActionType()).orElse(null);
                payload.put("ref_id", historyPoint.getTransactionExternalId());
                payload.put("user_id", String.valueOf(user.getId()));
                payload.put("phoneNumber", user.getPhoneNumber());
                payload.put("productId", String.valueOf(giftDynamicData.getEProductId()));
                payload.put("productName", giftDynamicData.getEProductName());
                payload.put("priceValue", String.valueOf(gift.getPrice()));
                payload.put("productLink", "");
                payload.put("quantity", String.valueOf(userGift.getQuantity()));
                payload.put("voucherCode", "");
                payload.put("createdDate", "");
                payload.put("status", "NEW");
                payload.put("evoucherId", giftDynamicData.getRewardAppGiftCode());
                payload.put("productPriceId", String.valueOf(giftDynamicData.getPriceId()));
                payload.put("id", String.valueOf(userGift.getId()));
                payload.put("Redeem_Points__c", userGift.getPoint());
                if (Objects.nonNull(crmTransactionType)) {
                    payload.put("Rule_Name__c", crmTransactionType.getDescription());
                    payload.put("Transaction_Type__c", crmTransactionType.getName());
                    payload.put("Campaign__c", crmTransactionType.getCampaignName());
                }
                // TODO: get setup base on REWARD_GIFT or crm transaction type on table event
                // detail
                payload.put("Type__c", Constant.CrmPointType.SPEND);
                payload.put("Manufacturing_Date__c", "");
                payload.put(
                        "expiryDate",
                        Objects.nonNull(userGiftDynamicData.getExpiryDate()) ?
                                userGiftDynamicData.getExpiryDate() :
                                ""
                );
                payload.put("Transaction_Date__c", LocalDateTime.now());
                payload.put("Voucher_RefId__c", "");
                payload.put("User_Gift_Id__c", userGift.getId());
                payload.put("EV_Source__c", gift.getType());
            }
            case E_VOUCHER_SHOP_BKIDS -> {
                CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository
                        .findFirstByCode(CrmTransactionTypeCode.REWARD_GIFT).orElse(null);
                payload.put("ref_id", historyPoint.getTransactionExternalId());
                payload.put("user_id", String.valueOf(user.getId()));
                payload.put("phoneNumber", user.getPhoneNumber());
                payload.put("productId", String.valueOf(giftDynamicData.getBkidsCourseId()));
                payload.put("productName", giftDynamicData.getBkidsCourseName());
                payload.put("productLink", "");
                payload.put("productPriceId", "");
                payload.put("priceValue", String.valueOf(giftDynamicData.getBkidsCoursePrice()));
                payload.put("quantity", String.valueOf(userGift.getQuantity()));
                payload.put("voucherCode", "");
                payload.put("expiryDate", "");
                payload.put("createdDate", LocalDateTime.now());
                payload.put("status", "NEW");
                payload.put("evoucherId", giftDynamicData.getRewardAppGiftCode());
                payload.put("id", String.valueOf(userGift.getId()));
                payload.put("Redeem_Points__c", userGift.getPoint());
                payload.put("Campaign__c", "");
                if (Objects.nonNull(crmTransactionType)) {
                    payload.put("Rule_Name__c", crmTransactionType.getDescription());
                    payload.put("Transaction_Type__c", crmTransactionType.getName());
                }
                payload.put("Type__c", Constant.CrmPointType.SPEND);
                payload.put("Manufacturing_Date__c", "");
                payload.put("Transaction_Date__c", DateTimeUtil.formatInstant(userGift.getCreatedAt(),
                        Constant.DateTimeFormat.YYYY_MM_DD_T_HH_MM_SS));
                payload.put("Voucher_RefId__c", "");
                payload.put("User_Gift_Id__c", userGift.getId());
                payload.put("EV_Source__c", gift.getType());
            }
            default -> {
                return null;
            }
        }

        return payload;
    }

    public HashMap<String, Object> generatePayloadDataUsingGiftCrm(
            Gift gift,
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            Object voucher) {
        var payload = new HashMap<String, Object>();
        switch (gift.getType()) {
            case E_VOUCHER,
                    E_VOUCHER_SHOP,
                    E_VOUCHER_SHOP_BKIDS,
                    EV_VITA_CODE -> {
                GiftTypeEnum giftType = gift.getType();
                CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository
                        .findFirstByCode(historyPoint.getActionType()).orElse(null);
                UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
                GiftDynamicData giftDynamicData = gift.getDynamicData();
                Instant useDate = userGiftDynamicData.getUsedDate();
                if (Objects.isNull(useDate)) {
                    useDate = Instant.now();
                }
                Instant expiryDate = null;
                if (GiftTypeEnum.E_VOUCHER.equals(giftType)) {
                    expiryDate = useDate;
                } else if (
                        !GiftTypeEnum.E_VOUCHER_SHOP_BKIDS.equals(giftType) &&
                                Objects.nonNull(userGiftDynamicData.getExpiryDate())
                ) {
                    String expiryDateStr = userGiftDynamicData.getExpiryDate();
                    try {
                        expiryDate = Instant.parse(expiryDateStr);
                    } catch (Exception ignored) {}
                    if (Objects.isNull(expiryDate)) {
                        try {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constant.DateTimeFormat.YYYY_MM_DD);
                            LocalDate localDate = LocalDate.parse(expiryDateStr, formatter);
                            LocalDateTime localDateTime = localDate.atStartOfDay();
                            expiryDate = localDateTime.toInstant(ZoneOffset.UTC);
                        } catch (Exception ignored) {}
                    }
                }
                String createdDateString = DateTimeUtil.formatInstant(useDate,
                        Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS_SS_Z);
                String expiryDateString = Objects.nonNull(expiryDate) ?
                        DateTimeUtil.formatInstant(expiryDate,
                        Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS_SS_Z) :
                        null;
                payload.put("ref_id", historyPoint.getTransactionExternalId());
                payload.put("user_id", String.valueOf(user.getId()));
                payload.put("phoneNumber", user.getPhoneNumber());
                if (GiftTypeEnum.E_VOUCHER_SHOP_BKIDS.equals(giftType)) {
                    payload.put("productId", String.valueOf(giftDynamicData.getBkidsCourseId()));
                    payload.put("productName", giftDynamicData.getBkidsCourseName());
                    payload.put("priceValue", String.valueOf(giftDynamicData.getBkidsCoursePrice()));
                    payload.put("productPriceId", "");
                } else {
                    payload.put("productId", String.valueOf(giftDynamicData.getEProductId()));
                    payload.put("productName", giftDynamicData.getEProductName());
                    payload.put("priceValue", String.valueOf(gift.getPrice()));
                    payload.put("productPriceId", String.valueOf(giftDynamicData.getPriceId()));
                }
                payload.put("evoucherId", giftDynamicData.getRewardAppGiftCode());
                payload.put("productLink", "");
                if (
                        GiftTypeEnum.E_VOUCHER_SHOP.equals(giftType) &&
                                Objects.nonNull(voucher)
                ) {
                    VoucherResponse voucherShop = (VoucherResponse) voucher;
                    payload.put("productLink", voucherShop.getProduct().getLink());
                }
                payload.put("quantity", String.valueOf(userGift.getQuantity()));
                String voucherCode = userGiftDynamicData.getVoucherCode();
                payload.put(
                        "voucherCode", ""
                );
                // Hotfix ticket 3194
                if (!GiftTypeEnum.E_VOUCHER.equals(giftType)) {
                    payload.put(
                            "voucherCode",
                            Objects.nonNull(voucherCode) && !voucherCode.isEmpty() ?
                                    voucherCode : ""
                    );
                }
                payload.put("createdDate", createdDateString);
                payload.put("expiryDate", expiryDateString);
                payload.put("status", "NEW");
                // payload.put("id", userGift.getId());
                payload.put("Redeem_Points__c", userGift.getPoint());
                if (Objects.nonNull(crmTransactionType)) {
                    payload.put("Rule_Name__c", crmTransactionType.getDescription());
                    payload.put("Transaction_Type__c", crmTransactionType.getName());
                    payload.put("Campaign__c", crmTransactionType.getCampaignName());
                }
                switch (historyPoint.getType()) {
                    case Constant.HistoryPointType.GIFT -> {
                        payload.put("Type__c", Constant.CrmPointType.GIFT);
                    }
                    default -> {
                        payload.put("Type__c", Constant.CrmPointType.SPEND);
                    }
                }
                payload.put("Manufacturing_Date__c", "");
                payload.put("Transaction_Date__c", DateTimeUtil.formatInstant(userGift.getCreatedAt(),
                        Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));
                payload.put("Voucher_RefId__c", userGiftDynamicData.getVoucherRefId());
                payload.put("User_Gift_Id__c", userGift.getId());
                payload.put("EV_Source__c", gift.getType());

                if (gift.getBrandPoint().checkIsExchangeBrandGift()) {
                    String loyaltyMemberC = String.format("%s-%s", 
                        userGift.getDynamicData().getMapUserToBrandPointIdSendSf(), 
                        userGift.getDynamicData().getMapUserToBrandPointId()
                    );
                    payload.put("Loyalty_Member__c", loyaltyMemberC != null ? loyaltyMemberC : "");

                    String programNameC = userGift.getDynamicData().getEventAddCanLoyaltyProgramSfId();
                    payload.put("Program_Name__c", programNameC != null ? programNameC : "");
                }
            }
            default -> {
                payload = null;
            }
        }

        return payload;
    }

    private HashMap<String, Object> generatePayloadDataReturnGiftCrm(
            User user,
            UserGift userGift,
            HistoryPoint historyPoint,
            String returnPointTxId,
            String spendPointTxId,
            String reason) {
        CrmTransactionType crmTransactionType = this.crmTransactionTypeRepository
                .findFirstByCode(CrmTransactionTypeCode.RETURN_POINT).orElse(null);

        var payload = new HashMap<String, Object>();
        payload.put("ref_id", returnPointTxId);
        payload.put("user_id", user.getId());
        payload.put("phone_number_user", user.getPhoneNumber());
        payload.put("returned_date",
                DateTimeUtil.formatInstant(Instant.now(), Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));
        payload.put("returned_redeem_point", userGift.getPoint());
        payload.put("Type__c", Constant.CrmPointType.ADD);
        if (Objects.nonNull(crmTransactionType)) {
            payload.put("Transaction_Type__c", crmTransactionType.getName());
            payload.put("Rule_Name__c", Objects.nonNull(reason) ? reason : crmTransactionType.getDescription());
        }
        /*payload.put("Transaction_Date__c", DateTimeUtil.formatInstant(historyPoint.getTransactionDate(),
                Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));*/
        payload.put("Transaction_Date__c", DateTimeUtil.formatInstant(Instant.now(),
                Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));
        payload.put("Transaction_Trigger_External_ID__c", spendPointTxId);

        return payload;
    }

    private HashMap<String, Object> generatePayloadDataReturnedGiftCrm(
            User user,
            String spendPointTxId) {
        var payload = new HashMap<String, Object>();
        payload.put("ref_id", spendPointTxId);
        payload.put("user_id", user.getId());
        payload.put("phoneNumber", user.getPhoneNumber());
        payload.put("status", "Returned");

        return payload;
    }

    public String generateCodeEndPointExchangeGiftCrm(Gift gift) {
        switch (gift.getType()) {
            case GIFT -> {
                return "EXCHANGE_GIFT";
            }
            case E_VOUCHER, E_VOUCHER_SHOP, EV_VITA_CODE, E_VOUCHER_SHOP_BKIDS -> {
                return "EXCHANGE_VOUCHER";
            }
            default -> {
                return "";
            }
        }
    }

    public String generateCodeEndPointUsingGiftCrm(Gift gift) {
        switch (gift.getType()) {
            case GIFT -> {
                return "EXCHANGE_GIFT";
            }
            case E_VOUCHER, E_VOUCHER_SHOP, EV_VITA_CODE, E_VOUCHER_SHOP_BKIDS -> {
                return "EXCHANGE_VOUCHER";
            }
            default -> {
                return "";
            }
        }
    }

    public void syncUpdateEventPointHistoryToWh(UserGift userGift, HistoryPoint historyPoint) {
        if (
                Objects.isNull(userGift) ||
                        Objects.isNull(historyPoint)
        ) {
            return;
        }

        //log.debug("History point {}", historyPoint.getId());
        EventPointHistory eventPointHistory = this.eventPointHistoryRepo.findFirstByHistoryPointId(historyPoint.getId()).orElse(null);
        //log.debug("Event point history {}", eventPointHistory);
        if (Objects.isNull(eventPointHistory)) {
            return;
        }

        UpdateEventPointHistoryToWhRequest request = new UpdateEventPointHistoryToWhRequest();
        request.setTransactionCode(historyPoint.getTransactionExternalId());
        request.setStatus(userGift.getStatus().toString());

        SyncEventPointHistoryToWh record = new SyncEventPointHistoryToWh();
        record.setEventPointHistoryId(eventPointHistory.getId());
        record.setTransactionCode(historyPoint.getTransactionExternalId());
        String bearerToken = this.getAuthBearerToken();
        if (Objects.isNull(bearerToken)) {
            bearerToken = "No auth token";
        }
        record.setUserAccessToken(bearerToken);
        record.setMethod(SyncEventPointHistoryToWhMethod.UPDATE);
        try {
            String requestStr = this.objectMapper.writeValueAsString(request);
            record.setRequest(requestStr);
        } catch (Exception ignore) {}

        this.syncEventPointHistoryToWhRepo.save(record);
    }

    private String generateCodeEndPointReturnGiftCrm() {
        return "RETURN_POINT";
    }

    private String generateCodeEndPointReturnedGiftCrm() {
        return "EXCHANGE_VOUCHER";
    }

    private String getAuthBearerToken() {
        try {
            this.request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String bearerToken = this.request.getHeader(Constant.Header.AUTHORIZATION);
            if (!StringUtils.isNotEmpty(bearerToken) || !bearerToken.startsWith("Bearer ")) {
                return null;
            }

            return "Bearer ".concat(bearerToken.substring(7));
        } catch (Exception e) {
            return null;
        }
    }

    private HashMap<String, Object> validateExchangeGiftMatchTimeExchangeGiftSetupAndReturnSetupData(
            Gift gift,
            User user,
            Integer quantity,
            EventAddCan eventAddCan
    ) {
        HashMap<String, Object> sfPayload = new HashMap<>();
        sfPayload.put("result_check", Boolean.TRUE);
        sfPayload.put("event_add_can", null);
        sfPayload.put("detail_setup", null);
        sfPayload.put("history_user_exchange_gift_event", null);

        if (
                Objects.isNull(gift) ||
                        Objects.isNull(user) ||
                        Objects.isNull(eventAddCan)
        ) {
            return sfPayload;
        }

//        EventAddCan eventAddCan = this.eventAddCanRepository.findFistByCategoryCode(
//                gift.getCategoryCode()
//        ).orElse(null);
//        if (Objects.isNull(eventAddCan)) {
//            return sfPayload;
//        }
        sfPayload.put("event_add_can", eventAddCan);
        if (eventAddCan.getEnableTimeExchangeGift().equals(Boolean.FALSE)) {
            return sfPayload;
        }

        EventAddCanTimeExchangeGiftDetail detailSetup = this.eventAddCanTimeExchangeGiftRepository.getDetailSetupGiftOfEventAddCanEffectivity(
                eventAddCan.getId(),
                gift.getId()
        ).orElse(null);
        if (Objects.isNull(detailSetup)) {
            return sfPayload;
        }
        sfPayload.put("detail_setup", detailSetup);
        if (0 == detailSetup.getTotal()) {
            sfPayload.put("result_check", Boolean.FALSE);
            return sfPayload;
        }
        if (detailSetup.getTotal() < quantity) {
            sfPayload.put("result_check", Boolean.FALSE);
            return sfPayload;
        }

        HistoryUserExchangeGiftEvent userExchangeGiftEvent = this.historyUserExchangeGiftEventRepository.findFirstByUserIdAndEventAddCanTimeExchangeGiftDetailId(
                user.getId(),
                detailSetup.getId()
        ).orElse(null);
        if (Objects.isNull(userExchangeGiftEvent)) {
            return sfPayload;
        }
        sfPayload.put("history_user_exchange_gift_event", userExchangeGiftEvent);
        if (detailSetup.getTotal() < userExchangeGiftEvent.getTotal() + quantity) {
            sfPayload.put("result_check", Boolean.FALSE);
            return sfPayload;
        }

        return sfPayload;
    }

    private HashMap<String, Object> validateExchangeGiftMatchLitmitTimesCanExchangeSetupAndReturnSetupData(
            EventAddCan eventAddCan,
            User user
    ) {
        HashMap<String, Object> sfPayload = new HashMap<>();
        sfPayload.put("result_check_exceed_number_times_earned", Boolean.FALSE);
        sfPayload.put("result_check_exceed_total_number_times", Boolean.FALSE);
        sfPayload.put("user_tracking_number_time_exchange_gift", null);

        if (Objects.isNull(eventAddCan) || Objects.isNull(user)) {
            return sfPayload;
        }

        sfPayload.put("result_check_exceed_number_times_earned", Boolean.TRUE);
        sfPayload.put("result_check_exceed_total_number_times", Boolean.TRUE);
        UserTrackingNumberTimeExchangeGift userTrackingNumberTimeExchangeGift = this.userTrackingNumberTimeExchangeGiftRepo.findFistByEventAddCanIdAndUserId(
                eventAddCan.getId(),
                user.getId()
        ).orElse(null);
        if (Objects.isNull(userTrackingNumberTimeExchangeGift)) {
            return sfPayload;
        }
        sfPayload.put("user_tracking_number_time_exchange_gift", userTrackingNumberTimeExchangeGift);
        sfPayload.put("result_check_exceed_number_times_earned", userTrackingNumberTimeExchangeGift.checkUserExchangeGiftExceedNumberTimesEarned());
        sfPayload.put("result_check_exceed_total_number_times", userTrackingNumberTimeExchangeGift.checkUserExchangeGiftExceedTotalNumberTimes());

        return sfPayload;
    }

    private Boolean checkIsExchangingBrandGift(
            MapGiftToBrandPoint mapGiftToBrandPoint
    ) {
        return Objects.nonNull(mapGiftToBrandPoint) &&
                mapGiftToBrandPoint.checkIsExchangeBrandGift() ? Boolean.TRUE : Boolean.FALSE;
    }
}
