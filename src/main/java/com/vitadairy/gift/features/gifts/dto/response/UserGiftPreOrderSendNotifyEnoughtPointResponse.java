package com.vitadairy.gift.features.gifts.dto.response;

import com.vitadairy.gift.features.gifts.dto.UserGiftPreOrderSendNotifyEnoughtPointDto;
import com.vitadairy.main.response.EntityResponse;
import org.springframework.http.HttpStatusCode;

public class UserGiftPreOrderSendNotifyEnoughtPointResponse extends EntityResponse<UserGiftPreOrderSendNotifyEnoughtPointDto> {
    public UserGiftPreOrderSendNotifyEnoughtPointResponse(UserGiftPreOrderSendNotifyEnoughtPointDto data, HttpStatusCode statusCode, String message) {
        super(data, statusCode, message);
    }
}
