package com.vitadairy.gift.features.gifts.controllers;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.constants.Constants;
import com.vitadairy.gift.features.gifts.constants.ExchangeGiftFilterType;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GetQuantityGiftDto;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.GiftingGiftDto;
import com.vitadairy.gift.features.gifts.dto.request.*;
import com.vitadairy.gift.features.gifts.dto.response.GiftPageResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftsResponse;
import com.vitadairy.gift.features.gifts.dto.response.ListExchangeGiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.ListGiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.TopExchangeGiftsResponse;
import com.vitadairy.gift.features.gifts.services.GiftExchangeService;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.zoo.dto.UserDto;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("v4/gs/gifts")
@AllArgsConstructor
public class GiftController {
    private final GiftService giftService;
    private final GiftExchangeService giftExchangeService;
    private final ResponseFactory responseFactory;

    @GetMapping()
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<GiftPageResponse> getGifts(@ModelAttribute ListGiftRequestDto dto) {
        dto.setSort("priority", Sort.Direction.ASC);
        dto.setSort("id", Sort.Direction.DESC);
        dto.setStatus(GiftStatusEnum.ENABLED);
        dto.setIsCustomer(true);
        UserDto currentUser = giftService.getUserDtoFromAuthToken();
        ListGiftResponse gifts = giftService.getGiftsByCurrentUser(dto, currentUser);
        GiftPageResponse response = new GiftPageResponse(gifts.getGifts(), gifts.getTotalElements(),
                gifts.getTotalPages(),
                dto.getPage(), dto.getSize(), HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

    @GetMapping("/dynamic-filter")
    public ResponseEntity<GiftPageResponse> getGifts(
            @ModelAttribute GiftDynamicFilterRequestDto dto) {
        Page<Gift> data = giftService.getGiftsByDynamicFilter(dto);
        var principal = AuthenticationHelper.getCurrentUser();
        GiftPageResponse response = new GiftPageResponse(
                giftService.setupGiftsCanRedeemV2(data.getContent().stream().map(GiftDto::fromEntity).toList(),
                        principal.getUserId()),
                data.getTotalElements(), (long) data.getTotalPages(), dto.getPage(), dto.getSize(), HttpStatus.OK,
                "OK");

        return responseFactory.successDto(response);
    }

    @GetMapping("/by-user-point")
    public ResponseEntity<GiftsResponse> getGiftsByUserPoint(@RequestParam Double point) {
        List<GiftDto> gifts = giftService.getGiftsByUserPoint(point);
        return responseFactory.successDto(new GiftsResponse(gifts, HttpStatus.OK, "OK"));
    }

    @GetMapping("/top-exchange")
    public ResponseEntity<TopExchangeGiftsResponse> getGiftsByUserPoint(
            @RequestParam ExchangeGiftFilterType type) {
        ListExchangeGiftResponse gifts = giftService.getTopExchangeGift(type);
        return responseFactory.successDto(new TopExchangeGiftsResponse(gifts.getData(), HttpStatus.OK, "OK"));
    }

    @GetMapping("sections")
    public ResponseEntity<GiftPageResponse> sectionGifts(
            @ModelAttribute SessionGiftRequestDto dto) {
        UserDto currentUser = giftService.getUserDtoFromAuthToken();
        var gifts = giftService.sectionGifts(dto, currentUser);
        GiftPageResponse response = new GiftPageResponse(gifts.getGifts(),
                gifts.getTotalElements(), gifts.getTotalPages(), 0, (int) gifts.getTotalElements(), HttpStatus.OK, "OK");
        return responseFactory.successDto(response);
    }

    @GetMapping("/no-auth-token/{id}")
    public ResponseEntity<GiftResponse> getDetailsByIdNoAuthToken(@PathVariable Integer id) {
        Gift gift = this.giftService.details(id);
        GiftDto giftDto = this.giftService.setupGiftCanRedeem(gift);
        giftDto.setIsHiddenPreOrderBt(true);
        giftDto.setIsDisabledPreOrderBt(true);
        GiftResponse giftResponse = new GiftResponse(giftDto, HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    @PreAuthorize("hasAnyAuthority('USER','ADMIN')")
    @GetMapping("{id}")
    public ResponseEntity<GiftResponse> getDetailsById(@PathVariable Integer id) {
        var principal = AuthenticationHelper.getCurrentUser();
        Gift gift = this.giftService.details(id);
        GiftDto giftDto = this.giftService.setupGiftCanRedeem(gift);
        Boolean isHiddenPreOrder = this.giftService.isHiddenPreOrder(gift, principal.getUserId());
        giftDto.setIsHiddenPreOrderBt(isHiddenPreOrder);
        Boolean isDisabledPreOrderBt = this.giftService.isDisabledPreOrderBt(gift, principal.getUserId());
        giftDto.setIsDisabledPreOrderBt(isDisabledPreOrderBt);
        Integer quantityLimitForBooking = this.giftService.getQuantityLimitForBooking(gift, principal.getUserId());
        giftDto.setQuantityLimitForBooking(quantityLimitForBooking);
        GiftResponse giftResponse = new GiftResponse(giftDto, HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    // @PutMapping("{id}")
    @PatchMapping("{id}")
    public ResponseEntity<GiftResponse> updateGift(@PathVariable("id") Integer id,
            @Valid @RequestBody GiftRequestDto body) {
        // Issue-3718: đối với quà có type = GIFT thì mặc định expiry time là 168 giờ, admin không đc sửa
        if (body.getType() == GiftTypeEnum.GIFT) {
            body.setExpireHour(Constants.GIFT_EXPIRE_HOUR);
        }
        Gift gift = this.giftService.update(id, body);
        GiftResponse giftResponse = new GiftResponse(GiftDto.fromEntity(gift), HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    @PatchMapping("/update-status/{id}")
    public ResponseEntity<GiftResponse> updateGiftStatus(@PathVariable("id") Integer id,
            @Valid @RequestBody GiftStatusRequestDto body) {
        GiftResponse gift = this.giftService.updateGiftStatus(id, body.getStatus());
        return responseFactory.successDto(gift);
    }

    @PatchMapping("/update-stock/{id}")
    public ResponseEntity<GiftResponse> updateGiftStock(@PathVariable("id") Integer id,
            @Valid @RequestBody GiftStockRequestDto body) {
        GiftResponse gift = this.giftService.updateGiftStock(id, body);
        return responseFactory.successDto(gift);
    }

    @PostMapping("/gift-exchange")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<Boolean>> exchangeGift(
            @Valid @RequestBody GiftExchangeRequestDto body) {
        var principal = AuthenticationHelper.getCurrentUser();
        PhysicalGiftExchangeRequestDto physicalGiftExchangeRequestDto =
                PhysicalGiftExchangeRequestDto.builder().giftId(body.getGiftId())
                        .quantity(body.getQuantity())
                        .userId(principal == null ? null : principal.getUserId()).build();
        var response = this.giftExchangeService.exchangeGift(physicalGiftExchangeRequestDto, false);
        return responseFactory.successEntity(response, VtdStatusEnum.GF_SUCCESS);
    }

    @GetMapping("/gift-exchange/remind-check")
    public ResponseEntity<EntityResponse<Boolean>> checkRemindExchangeGift() {
        var principal = AuthenticationHelper.getCurrentUser();
        var response = this.giftExchangeService.checkRemindExchangeGift(principal.getUserId());
        return responseFactory.successEntity(response);
    }

    @PostMapping("/physical-gift-exchange")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<Boolean>> physicalExchangeGift(
            @Valid @RequestBody PhysicalGiftExchangeRequestDto body) {
        var principal = AuthenticationHelper.getCurrentUser();
        body.setUserId(principal == null ? null : principal.getUserId());
        var response = this.giftExchangeService.exchangeGift(body, true);
        return responseFactory.successEntity(response, VtdStatusEnum.GF_SUCCESS);
    }

    @PostMapping("/check-user-point")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<Boolean>> checkUserPoint(
            @Valid @RequestBody GiftExchangeRequestDto body) {
        var principal = AuthenticationHelper.getCurrentUser();
        body.setUserId(principal == null ? null : principal.getUserId());
        var response = this.giftExchangeService.checkUserPointSf(body);
        return responseFactory.successEntity(response);
    }

    @PostMapping("/gift-gifting")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<GiftingGiftDto>> giftingGift(
            @Valid @RequestBody GiftGiftingRequestDto body) {
        var principal = AuthenticationHelper.getCurrentUser();
        body.setUserId(principal == null ? null : principal.getUserId());
        var response = this.giftExchangeService.giftingGift(body);
        return responseFactory.successEntity(response);
    }

    @PostMapping("/no-auth-token/gift-gifting")
    public ResponseEntity<EntityResponse<GiftingGiftDto>> giftingGiftNoAuthenToken(
            @Valid @RequestBody GiftGiftingRequestDto body) {
        // var principal = AuthenticationHelper.getCurrentUser();
        // body.setUserId(principal == null ? null : principal.getUserId());
        var response = this.giftExchangeService.giftingGift(body);
        return responseFactory.successEntity(response);
    }

    @PostMapping("/no-auth-token/gift-remove-user-gift-gifting")
    public String removeUserGiftGifting(
            @Valid @RequestBody GiftRemoveUserGiftGiftingRequestDto body) {
        // var principal = AuthenticationHelper.getCurrentUser();
        // body.setUserId(principal == null ? null : principal.getUserId());
        return this.giftExchangeService.removeUserGiftGifting(body);
    }

    @GetMapping("/gift-get-quantity")
    @PreAuthorize("hasAnyAuthority({'USER'})")
    public ResponseEntity<EntityResponse<GetQuantityGiftDto>> getGifts(
            @ModelAttribute GiftGetQuantityRequestDto dto) {
        var response = this.giftExchangeService.getNewGiftQuantityFromOldGift(dto);
        return responseFactory.successEntity(response);
    }

    //getGiftByGiftIds
    @GetMapping("/multiple")
    public ResponseEntity<GiftsResponse> getGiftByGiftIds(@RequestParam List<Integer> giftIds) {
        List<GiftDto> giftDtos = giftService.getGiftByGiftIds(giftIds);
        return responseFactory.successDto(new GiftsResponse(giftDtos, HttpStatus.OK, "OK"));
    }
}
