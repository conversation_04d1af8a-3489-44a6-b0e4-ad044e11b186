package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.GiftDynamicFilter;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class GiftDynamicDataDto {
    private String description;
    private String link;
    private BigDecimal weight;
    private BigDecimal height;
    private BigDecimal width;
    private BigDecimal length;
    private String rewardAppGiftCode;
    private String smsGiftId;
    private Boolean activeSmsNotification;
    private String sourceGift;
    private String selectGift;
    private GiftDynamicFilter dynamicFilter;
    private Long brandId;
    private Long eProductId;
    private String eProductName;
    private Long priceId;
    private String bkidsCourseType;
    private String bkidsCourseId;
    private String bkidsCourseName;
    private List<String> bkidsCourseClass;
    private Long bkidsCoursePrice;
    private Long giftCategoryId;
    private String code;

    public GiftDynamicDataDto() {
    }

    public GiftDynamicDataDto(String description, String link, BigDecimal weight, BigDecimal height, BigDecimal width, BigDecimal length, String rewardAppGiftCode, String smsGiftId, Boolean activeSmsNotification, String sourceGift, String selectGift, GiftDynamicFilter dynamicFilter, Long brandId, Long eProductId, String eProductName, Long priceId, String bkidsCourseType, String bkidsCourseId, String bkidsCourseName, List<String> bkidsCourseClass, Long bkidsCoursePrice, Long giftCategoryId, String code) {
        this.description = description;
        this.link = link;
        this.weight = weight;
        this.height = height;
        this.width = width;
        this.length = length;
        this.rewardAppGiftCode = rewardAppGiftCode;
        this.smsGiftId = smsGiftId;
        this.activeSmsNotification = activeSmsNotification;
        this.sourceGift = sourceGift;
        this.selectGift = selectGift;
        this.dynamicFilter = dynamicFilter;
        this.brandId = brandId;
        this.eProductId = eProductId;
        this.eProductName = eProductName;
        this.priceId = priceId;
        this.bkidsCourseType = bkidsCourseType;
        this.bkidsCourseId = bkidsCourseId;
        this.bkidsCourseName = bkidsCourseName;
        this.bkidsCourseClass = bkidsCourseClass;
        this.bkidsCoursePrice = bkidsCoursePrice;
        this.giftCategoryId = giftCategoryId;
        this.code = code;
    }

    public static GiftDynamicDataDto fromEntity(GiftDynamicData entity){
        return GiftDynamicDataDto.builder()
                .description(entity.getDescription())
                .link(entity.getLink())
                .weight(entity.getWeight())
                .height(entity.getHeight())
                .width(entity.getWidth())
                .length(entity.getLength())
                .rewardAppGiftCode(entity.getRewardAppGiftCode())
                .smsGiftId(entity.getSmsGiftId())
                .activeSmsNotification(entity.getActiveSmsNotification())
                .sourceGift(entity.getSourceGift())
                .selectGift(entity.getSelectGift())
                .dynamicFilter(entity.getDynamicFilter())
                .brandId(entity.getBrandId())
                .eProductId(entity.getEProductId())
                .eProductName(entity.getEProductName())
                .priceId(entity.getPriceId())
                .bkidsCourseType(entity.getBkidsCourseType())
                .bkidsCourseId(entity.getBkidsCourseId())
                .bkidsCourseName(entity.getBkidsCourseName())
                .bkidsCourseClass(StringUtils.isNotEmpty(entity.getBkidsCourseClass()) ? List.of(entity.getBkidsCourseClass().split(",")) : null)
                .bkidsCoursePrice(entity.getBkidsCoursePrice())
                .giftCategoryId(entity.getGiftCategoryId())
                .code(entity.getCode())
                .build();
    }
}