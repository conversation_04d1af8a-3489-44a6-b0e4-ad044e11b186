package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.main.dto.BaseDto;
import lombok.*;
import lombok.Builder.Default;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GiftInUserGiftPreOrderSendNotifyEnoughtPointDto extends BaseDto {
    private List<String> name;
    public static GiftInUserGiftPreOrderSendNotifyEnoughtPointDto fromEntity(List<Gift> gifts) {
        return GiftInUserGiftPreOrderSendNotifyEnoughtPointDto.builder()
                .name(gifts.stream().map(Gift::getName).toList())
                .build();
    }
}
