package com.vitadairy.gift.features.gifts.dto.response;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Data
@Getter
@Setter
@Builder
public class ExchangeGiftResponse {
    private Integer rowNumber;
    private String[] images;
    private String name;
    private BigDecimal price;
    private Integer point;
    private Long totalExchange;
    private Long giftId;

    public ExchangeGiftResponse(Integer rowNumber, String[] images, String name, BigDecimal price, Integer point, Long totalExchange, Long giftId) {
        this.rowNumber = rowNumber;
        this.images = images;
        this.name = name;
        this.price = price;
        this.point = point;
        this.totalExchange = totalExchange;
        this.giftId = giftId;
    }

    public ExchangeGiftResponse() {
    }
}
