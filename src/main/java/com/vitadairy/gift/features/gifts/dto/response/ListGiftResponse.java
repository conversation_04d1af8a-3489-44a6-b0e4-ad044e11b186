package com.vitadairy.gift.features.gifts.dto.response;

import com.vitadairy.gift.features.gifts.dto.GiftDto;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.List;

@Data
@Getter
@Setter
@Builder
public class ListGiftResponse implements java.io.Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private List<GiftDto> gifts;
    private long totalElements;
    private long totalPages;

    public ListGiftResponse(List<GiftDto> gifts, long totalElements, long totalPages) {
        this.gifts = gifts;
        this.totalElements = totalElements;
        this.totalPages = totalPages;
    }

    public ListGiftResponse(){}
}
