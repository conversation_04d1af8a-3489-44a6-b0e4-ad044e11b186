package com.vitadairy.gift.features.gifts.services;

import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.ListGiftResponse;
import com.vitadairy.gift.repositories.ConstantRepository;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.utils.StringUtils;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
public class GiftCacheService {
    private final GiftRepository giftRepository;
    private final ConstantRepository constantRepository;

    @Cacheable(value = "listGifts", sync = true)
    public ListGiftResponse pageableSpecification(ListGiftRequestDto dto) {
        Sort sort = Sort.by(dto.getSortBy(), dto.getOrderBy());
        Pageable pageable = dto.getPageable(sort);
        Page<Gift> pageGiPage = this.giftRepository.findAll((root, query, builder) -> {
            List<Predicate> predicates = this._buildPredicates(dto, builder, root);
            return builder.and(predicates.toArray(new Predicate[0]));
        }, pageable);
        return ListGiftResponse.builder()
                .gifts(pageGiPage.getContent().stream().map(GiftDto::fromEntity).toList())
                .totalElements(pageGiPage.getTotalElements())
                .totalPages(pageGiPage.getTotalPages())
                .build();
    }

    private List<Predicate> _buildPredicates(ListGiftRequestDto dto, CriteriaBuilder builder,
                                             Root<Gift> root) {
        List<Predicate> predicates = new ArrayList<>();
        if (dto.getMaxPoint() != null) {
            predicates.add(builder.lessThanOrEqualTo(root.get("point"), dto.getMaxPoint()));
        }

        if (dto.getStatus() != null) {
            predicates.add(builder.equal(root.get("status"), dto.getStatus()));
        }

        if (dto.getIsAllowReservation() != null) {
            predicates.add(
                    builder.equal(root.get("isAllowReservation"), dto.getIsAllowReservation()));
        }
        if (dto.getCategoryCode() != null && !dto.getCategoryCode().isEmpty()) {
            predicates.add(builder.equal(root.get("categoryCode"), dto.getCategoryCode()));
        }
        if (dto.getName() != null && !dto.getName().isEmpty()) {
            String searchTerm = dto.getName().toLowerCase();
            String pattern = "%" + searchTerm.toLowerCase() + "%";
            Expression<String> lowerNameExpression = builder.lower(root.get("name"));
            Predicate namePredicate = builder.like(lowerNameExpression, pattern);
            predicates.add(namePredicate);
        }
        if (dto.getType() != null) {
            predicates.add(builder.equal(root.get("type"), dto.getType()));
        }
        String vtierCodes = dto.getTierCodes();
        List<Predicate> tierCodePredicates = new ArrayList<>();
        if (vtierCodes != null) {
            for (String tierCode : vtierCodes.split(",")) {
                Expression<String> tierCodesExpression = root.get("tierCodes").as(String.class);
                Predicate tierCodePredicate =
                        builder.like(tierCodesExpression, "%" + tierCode + "%");
                tierCodePredicates.add(tierCodePredicate);
            }
        }
        if (!tierCodePredicates.isEmpty()) {
            predicates.add(builder.and(tierCodePredicates.toArray(new Predicate[0])));
        }

        if (dto.getId() != null && !dto.getId().isEmpty()) {
            Integer[] ids = StringUtils.toNumbers(dto.getId());

            CriteriaBuilder.In<Integer> inClause = builder.in(root.get("id"));
            for (Integer id : ids) {
                inClause.value(id);
            }
            predicates.add(inClause);
        }

        if (dto.getIds() != null && dto.getIds().length > 0) {
            Integer[] ids = dto.getIds();
            CriteriaBuilder.In<Integer> inClause = builder.in(root.get("id"));
            for (Integer id : ids) {
                inClause.value(id);
            }
            predicates.add(inClause);
        }

        if (Objects.nonNull(dto.getIsCustomer()) && dto.getIsCustomer()) {
            Instant now = Instant.now();
            predicates.add(builder.greaterThan(root.get("endDate"), Timestamp.from(now)));
            predicates.add(builder.lessThanOrEqualTo(root.get("startDate"), Timestamp.from(now)));
            if (Objects.isNull(dto.getCategoryCode())) {
                List<Constant> giftCategories = this.constantRepository
                        .findByKeyAlphaAndKeyBetaAndIsActive(ConstantFeatureEnum.GIFT.toString(),
                                ConstantTypeEnum.CATEGORY.toString(), true, Sort.unsorted());
                CriteriaBuilder.In<String> inClause = builder.in(root.get("categoryCode"));
                giftCategories.forEach(giftCategory -> {
                    inClause.value(giftCategory.getValue());
                });
                predicates.add(inClause);
            }
        }
        return predicates;
    }
}
