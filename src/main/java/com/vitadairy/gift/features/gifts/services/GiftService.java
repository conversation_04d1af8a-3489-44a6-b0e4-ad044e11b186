package com.vitadairy.gift.features.gifts.services;

import com.vitadairy.auth.dto.AuthorizationResponse;
import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.entities.MapOldGiftToNewGift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.constants.enums.ConstantFeatureEnum;
import com.vitadairy.gift.features.constants.enums.ConstantTypeEnum;
import com.vitadairy.gift.features.gifts.constants.ExchangeGiftFilterType;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.GiftDynamicDataDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftDynamicFilterRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftReservationRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftStockRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.SessionGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.ExchangeGiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.ListExchangeGiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.ListGiftResponse;
import com.vitadairy.gift.repositories.ConstantRepository;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.repositories.GiftReservationRepository;
import com.vitadairy.gift.repositories.MapOldGiftToNewGiftRepository;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.gift.shared.BaseResponse;
import com.vitadairy.gift.utils.StringUtils;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.zoo.dto.UserDto;
import com.vitadairy.zoo.entities.EventAddCan;
import com.vitadairy.zoo.entities.EventCanMark;
import com.vitadairy.zoo.entities.EventNumberUserCan;
import com.vitadairy.zoo.entities.GiftCategory;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.entities.UserTagAgg;
import com.vitadairy.zoo.repositories.EventAddCanRepository;
import com.vitadairy.zoo.repositories.EventCanMarkRepository;
import com.vitadairy.zoo.repositories.EventNumberUserCanRepository;
import com.vitadairy.zoo.repositories.GiftCategoryRepository;
import com.vitadairy.zoo.repositories.UserRepository;
import com.vitadairy.zoo.repositories.UserTagAggRepository;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service("GiftService")
public class GiftService {
    @AllArgsConstructor
    @Getter
    enum ResponseCode {
        SUCCESS("SUCCESS", 200), INTERNAL_SERVER_ERROR("INTERNAL_SERVER_ERROR",
                500), BAD_REQUEST("BAD_REQUEST", 400), INVALID_PARAMS("INVALID_PARAMS", 400),

        HOMESTAY_NOT_FOUND("HOMESTAY_NOT_FOUND", 404), HOMESTAY_NOT_ACTIVE("HOMESTAY_NOT_ACTIVE",
                404),

        GUESTS_INVALID("GUESTS_INVALID", 400), CHECKIN_DATE_INVALID("CHECKIN_DATE_INVALID",
                400), NIGHTS_INVALID("NIGHTS_INVALID", 400), HOMESTAY_BUSY("HOMESTAY_BUSY", 400);

        private final String type;
        private final Integer code;
    }

    private GiftRepository giftRepository;
    private GiftReservationRepository giftReservationRepository;
    private UserGiftRepository userGiftRepository;
    private ConstantRepository constantRepository;
    private UserRepository userRepository;
    private EventAddCanRepository eventAddCanRepo;
    private EventCanMarkRepository eventCanMarkRepo;
    private GiftCategoryRepository giftCategoryRepo;
    private EventNumberUserCanRepository eventNumberUserCanRepo;
    private MapOldGiftToNewGiftRepository mapOldGiftToNewGiftRepository;
    private GiftCacheService giftCacheService;
    private UserTagAggRepository userTagAggRepository;

    public GiftService(GiftRepository giftRepository,
                       GiftReservationRepository giftReservationRepository,
                       UserGiftRepository userGiftRepository, ConstantRepository constantRepository,
                       UserRepository userRepository,
                       EventAddCanRepository eventAddCanRepo,
                       @Qualifier("zooEventCanMarkRepository") EventCanMarkRepository eventCanMarkRepo,
                       GiftCategoryRepository giftCategoryRepo,
                       EventNumberUserCanRepository eventNumberUserCanRepo,
                       MapOldGiftToNewGiftRepository mapOldGiftToNewGiftRepository,
                       GiftCacheService giftCacheService,
                       UserTagAggRepository userTagAggRepository) {
        this.giftRepository = giftRepository;
        this.giftReservationRepository = giftReservationRepository;
        this.userGiftRepository = userGiftRepository;
        this.constantRepository = constantRepository;
        this.userRepository = userRepository;
        this.eventAddCanRepo = eventAddCanRepo;
        this.eventCanMarkRepo = eventCanMarkRepo;
        this.giftCategoryRepo = giftCategoryRepo;
        this.eventNumberUserCanRepo = eventNumberUserCanRepo;
        this.mapOldGiftToNewGiftRepository = mapOldGiftToNewGiftRepository;
        this.giftCacheService = giftCacheService;
        this.userTagAggRepository = userTagAggRepository;
    }

    @Transactional("giftTransactionManager")
    public Gift createGift(GiftRequestDto giftDto) {
        if (giftDto.getType() == GiftTypeEnum.GIFT) {
            var dynamicData = giftDto.getDynamicData();
            if (dynamicData != null && dynamicData.getSourceGift() == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,"Source gift is required");
            }
        }
        // Validate create gift brand
        this.validateUpdateGiftBrand(giftDto);

        GiftReservationRequestDto giftReservationDto = giftDto.getGiftReservation();
        // Gift
        Gift gift = GiftRequestDto.toEntity(giftDto);
        gift.setQuantityReward(0);
        gift.setQuantityReservation(0);
        Gift createGift = this.giftRepository.save(gift);

        // GiftReservation
        GiftReservation giftReservation = GiftReservationRequestDto.toEntity(giftReservationDto);
        giftReservation.setGift(createGift);
        GiftReservation newGiftReservation = this.giftReservationRepository.save(giftReservation);
        createGift.setGiftReservation(newGiftReservation);
        return createGift;
    }

    public GiftResponse updateGiftStatus(Integer giftId, GiftStatusEnum status) {
        this.giftRepository.findById(giftId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Gift with id " + giftId + " not found"));
        int rowAffected = this.giftRepository.updateGiftStatusById(giftId, status.toString());
        if (rowAffected == 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Cannot update gift");
        }
        Gift updatedGift = this.giftRepository.findById(giftId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Gift with id " + giftId + " not found"));
        return new GiftResponse(GiftDto.fromEntity(updatedGift), HttpStatus.OK, "OK");
    }

    public GiftResponse updateGiftStock(Integer giftId, GiftStockRequestDto giftStockRequestDto) {
        Gift gift = this.giftRepository.findById(giftId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Gift with id " + giftId + " not found"));
        if(gift.getStatus() != GiftStatusEnum.DISABLED) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Gift with id " + giftId + " is not DISABLED");
        }
        int rowAffected = this.giftRepository.updateGiftStockById(giftId, giftStockRequestDto.getQuantity(), giftStockRequestDto.getInventory(), giftStockRequestDto.getQuantityLimitForBooking());
        if (rowAffected == 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Cannot update gift");
        }
        Gift updatedGift = this.giftRepository.findById(giftId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Gift with id " + giftId + " not found"));
        return new GiftResponse(GiftDto.fromEntity(updatedGift), HttpStatus.OK, "OK");
    }

    public Gift update(Integer giftId, GiftRequestDto giftDto) {
        if (giftDto.getType() == GiftTypeEnum.GIFT) {
            var dynamicData = giftDto.getDynamicData();
            if (dynamicData != null && dynamicData.getSourceGift() == null) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,"Source gift is required");
            }
        }
        
        Gift oldGift = this.giftRepository.findById(giftId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Gift with id " + giftId + " not found"));

        // Validate update gift brand
        this.validateUpdateGiftBrand(giftDto, oldGift);

        Gift updatedGift = GiftRequestDto.toEntity(giftDto, oldGift.getBrandPoint());
        updatedGift.setId(giftId);
        if (Objects.isNull(giftDto.getStartDate())) {
            updatedGift.setStartDate(oldGift.getStartDate());
        }
        if (Objects.isNull(giftDto.getEndDate())) {
            updatedGift.setEndDate(oldGift.getEndDate());
        }

        // Gift Reservation
        GiftReservationRequestDto giftReservationDto = giftDto.getGiftReservation();
        if (!Objects.isNull(giftReservationDto)) {
            GiftReservation giftReservation =
                    this.giftReservationRepository.findByGiftId(oldGift.getId());
            GiftReservation updatedGiftReservation =
                    GiftReservationRequestDto.toEntity(giftReservationDto);
            if (!Objects.isNull(giftReservation)) {
                updatedGiftReservation.setId(giftReservation.getId());
            }
            updatedGiftReservation.setGift(oldGift);
            GiftReservation newGiftReservation =
                    this.giftReservationRepository.save(updatedGiftReservation);
            updatedGift.setGiftReservation(newGiftReservation);
        }
        // TODO: hard code to ignore update some fields in dynamic data
        // TODO: cong-tesosoft
        GiftDynamicData oldGiftDynamicData = oldGift.getDynamicData();
        GiftDynamicData newGiftDynamicData = updatedGift.getDynamicData();
        if (Objects.isNull(newGiftDynamicData.getPriceId())) {
            newGiftDynamicData.setPriceId(oldGiftDynamicData.getPriceId());
        }
        if (Objects.isNull(newGiftDynamicData.getEProductId())) {
            newGiftDynamicData.setEProductId(oldGiftDynamicData.getEProductId());
        }
        if (Objects.isNull(newGiftDynamicData.getGiftCategoryId())) {
            newGiftDynamicData.setGiftCategoryId(oldGiftDynamicData.getGiftCategoryId());
        }
        if (Objects.isNull(newGiftDynamicData.getBkidsCourseClass())) {
            newGiftDynamicData.setBkidsCourseClass(oldGiftDynamicData.getBkidsCourseClass());
        }
        if (Objects.isNull(newGiftDynamicData.getBkidsCourseId())) {
            newGiftDynamicData.setBkidsCourseId(oldGiftDynamicData.getBkidsCourseId());
        }
        if (Objects.isNull(newGiftDynamicData.getBkidsCourseName())) {
            newGiftDynamicData.setBkidsCourseName(oldGiftDynamicData.getBkidsCourseName());
        }
        if (Objects.isNull(newGiftDynamicData.getBkidsCourseType())) {
            newGiftDynamicData.setBkidsCourseType(oldGiftDynamicData.getBkidsCourseType());
        }
        if (Objects.isNull(newGiftDynamicData.getBkidsCoursePrice())) {
            newGiftDynamicData.setBkidsCoursePrice(oldGiftDynamicData.getBkidsCoursePrice());
        }
        updatedGift.setDynamicData(newGiftDynamicData);
        updatedGift.setEventId(oldGift.getEventId());

        return this.giftRepository.save(updatedGift);
    }

    public Gift details(Integer id) {
        Gift gift = this.giftRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Not found parent id " + id));
        GiftReservation giftReservation = this.giftReservationRepository.findByGiftId(gift.getId());
        gift.setGiftReservation(giftReservation);
        return gift;
    }

    public BaseResponse<Gift> pageableExampleMatcher(ListGiftRequestDto dto) {
        var sort = Sort.by("id").descending();
        var pageable = dto.getPageable(sort);

        var immutablePair = this._buildExampleMatcher(dto);
        var example = Example.of(immutablePair.right, immutablePair.left);
        var findAndCount = this.giftRepository.findAll(example, pageable);

        return new BaseResponse<>(findAndCount.toList(), findAndCount.getTotalElements());
    }

    public Page<Gift> getGiftsByDynamicFilter(GiftDynamicFilterRequestDto dto) {
        User currentUser = this.getUserFromAuthToken();
        if (Objects.nonNull(currentUser)) {
            dto.setTierCode(currentUser.getTierCode());
        }
        return this.giftRepository.filterByDynamicFilter(
                GiftDynamicFilterRequestDto.removeEmptyString(dto), dto.getPageable());
    }

    public List<GiftDto> getGiftsByUserPoint(Double point) {
        List<Gift> gifts = this.giftRepository
                .findAllByPointGreaterThanEqualAndStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqualOrderByPointDesc(
                        (int) Math.ceil(point), GiftStatusEnum.ENABLED,
                        Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
                        PageRequest.of(0, 4));
        return this.setupGiftsCanRedeem(gifts);
        /*
         * return this.giftRepository
         * .findAllByPointGreaterThanEqualAndStatusAndStartDateLessThanEqualAndEndDateGreaterThanEqualOrderByPointDesc(
         * (int) Math.ceil(point), GiftStatusEnum.ENABLED, Timestamp.from(Instant.now()),
         * Timestamp.from(Instant.now()), PageRequest.of(0, 4)) .stream()
         * .map(GiftDto::fromEntity).toList();
         */
    }

    @Cacheable(value = "top-exchange-gifts", sync = true)
    public ListExchangeGiftResponse getTopExchangeGift(ExchangeGiftFilterType type) {
        var topExchanges = switch (type) {
            case WEEK -> this.userGiftRepository
                    .getTopExchangeGiftByCurrentWeek(PageRequest.of(0, 10));
            case MONTH -> this.userGiftRepository
                    .getTopExchangeGiftByCurrentMonth(PageRequest.of(0, 10));
            case YEAR -> this.userGiftRepository
                    .getTopExchangeGiftByCurrentYear(PageRequest.of(0, 10));
        };

        return ListExchangeGiftResponse.builder()
                .data(
                        topExchanges.stream().map(exchange -> ExchangeGiftResponse.builder()
                                .rowNumber(exchange.getRowNumber())
                                .images(exchange.getImages())
                                .name(exchange.getName())
                                .price(exchange.getPrice())
                                .point(exchange.getPoint())
                                .totalExchange(exchange.getTotalExchange())
                                .giftId(exchange.getGiftId())
                                .build()).toList())
                .build();
    }

    @Cacheable(value = "gifts", sync = true)
    public ListGiftResponse getGiftsByCurrentUser(ListGiftRequestDto dto, UserDto currentUser) {
        if (Objects.nonNull(currentUser)) {
            dto.setTierCodes(currentUser.getTierCode());
        }
        ListGiftResponse gifts = giftCacheService.pageableSpecification(dto);
        List<GiftDto> giftDtos = setupGiftsCanRedeemV2(gifts.getGifts(), currentUser.getUserId());
        giftDtos = setupGiftsCategoryName(giftDtos);
        return ListGiftResponse.builder()
                .gifts(giftDtos)
                .totalPages(gifts.getTotalPages())
                .totalElements(gifts.getTotalElements())
                .build();
    }

    public Page<Gift> pageableSpecification(ListGiftRequestDto dto) {

        Specification<Gift> spec = (root, query, builder) -> {
            List<Predicate> predicates = this._buildPredicates(dto, builder, root);
            return builder.and(predicates.toArray(new Predicate[0]));
        };
        Sort sort = Sort.by(dto.getSortBy(), dto.getOrderBy());
        Pageable pageable = dto.getPageable(sort);
        if (pageable.isPaged()) {
            return this.giftRepository.findAll(spec, pageable);
        } else {
            List<Gift> gifts = this.giftRepository.findAll(spec, sort);
            return new PageImpl<>(gifts, Pageable.unpaged(), gifts.size());
        }
    }

    @Cacheable(value = "sectionGifts", sync = true)
    public ListGiftResponse sectionGifts(SessionGiftRequestDto dto,UserDto user) {
        List<UserTagAgg> userTagAggs = userTagAggRepository.findAllByUserIdOrderByPointDescBrandAsc(user.getUserId());
        String[] userBrandTag = CollectionUtils.isNotEmpty(userTagAggs) ? new String[]{userTagAggs.get(0).getBrand()} : null;
        String[] hiddenTags = CollectionUtils.isNotEmpty(dto.getHiddenTag()) ? dto.getHiddenTag().toArray(new String[0]) : null;

        List<GiftDto> listSectionUserBrandTagGifts = Objects.nonNull(userBrandTag) ? giftRepository.sectionFindByHiddenTags(user.getTierCode(), userBrandTag, (double) user.getGiftPoint()).stream().map(GiftDto::fromEntity).toList() : new ArrayList<>();
        List<GiftDto> listSectionHiddenTagGifts = Objects.nonNull(hiddenTags) ? giftRepository.sectionFindByHiddenTags(user.getTierCode(), hiddenTags, (double) user.getGiftPoint()).stream().map(GiftDto::fromEntity).toList() : new ArrayList<>();
        List<GiftDto> listSectionGifts = giftRepository.sectionFindByHiddenTags(user.getTierCode(), null, (double) user.getGiftPoint()).stream().map(GiftDto::fromEntity).toList();
        Set<GiftDto> allItems = new HashSet<>();

        allItems.addAll(listSectionUserBrandTagGifts);
        allItems.addAll(listSectionHiddenTagGifts);
        allItems.addAll(listSectionGifts);

        Set<GiftDto> commonItems = new HashSet<>(listSectionGifts);
        if(Objects.nonNull(hiddenTags)){
            commonItems.retainAll(listSectionHiddenTagGifts);
        }
        if(Objects.nonNull(userBrandTag)){
            commonItems.retainAll(listSectionUserBrandTagGifts);
        }


        Set<GiftDto> twoListItems = new HashSet<>(listSectionGifts);
        if(Objects.nonNull(hiddenTags)){
            twoListItems.retainAll(listSectionHiddenTagGifts);
            twoListItems.addAll(intersection(listSectionHiddenTagGifts, listSectionUserBrandTagGifts));
        }
        if(Objects.nonNull(userBrandTag)){
            twoListItems.addAll(intersection(listSectionGifts, listSectionUserBrandTagGifts));
        }
        twoListItems.removeAll(commonItems);

        Set<GiftDto> oneListItems = new HashSet<>(allItems);
        oneListItems.removeAll(commonItems);
        oneListItems.removeAll(twoListItems);

        List<GiftDto> result = new ArrayList<>(commonItems.stream().sorted(Comparator.comparing(GiftDto::getPoint).reversed().thenComparing(GiftDto::getName)).toList());
        result.addAll(twoListItems.stream().sorted(Comparator.comparing(GiftDto::getPoint).reversed().thenComparing(GiftDto::getName)).toList());
        result.addAll(oneListItems.stream().sorted(Comparator.comparing(GiftDto::getPoint).reversed().thenComparing(GiftDto::getName)).toList());

        List<GiftDto> giftDtos = setupGiftsCanRedeemV2(result, user.getUserId());

        return ListGiftResponse.builder()
                .gifts(giftDtos)
                .totalElements(giftDtos.size())
                .totalPages(1)
                .build();
    }

    public Set<GiftDto> intersection(List<GiftDto> list1, List<GiftDto> list2) {
        Set<GiftDto> result = new HashSet<>(list1);
        result.retainAll(list2);
        return result;
    }

    public List<GiftDto> setupGiftsCanRedeem(List<Gift> gifts) {
        List<GiftDto> result = new ArrayList<GiftDto>();
        User user = this.getUserFromAuthToken();
        gifts.stream().forEach(item -> {
            GiftDto giftDto = GiftDto.fromEntity(item);
            this.setupGiftCanRedeem(giftDto, user);
            result.add(giftDto);
        });

        return result;
    }

    public List<GiftDto> setupGiftsCanRedeemV2(List<GiftDto> gifts, Long userId) {
        List<GiftDto> result = new ArrayList<GiftDto>();
        List<Long> giftCategoryIds = new ArrayList<>();
        List<Long> giftIds = new ArrayList<>();
        gifts.stream().forEach(item -> {
            GiftDynamicDataDto giftDynamicData = item.getDynamicData();
            //GiftDto giftDto = GiftDto.fromEntity(item);
            //this.setupGiftCanRedeem(giftDto, user);
            //result.add(giftDto);
            giftCategoryIds.add(giftDynamicData.getGiftCategoryId());
            giftIds.add(Long.valueOf(item.getId()));
        });
        List<Long> finalGiftCategoryIds = new ArrayList<>(
                new LinkedHashSet<>(giftCategoryIds));
        List<MapOldGiftToNewGift> mapOldGiftToNewGifts = this.mapOldGiftToNewGiftRepository.findByNewGiftIdIn(giftIds);
        List<GiftCategory> giftCategories = this.giftCategoryRepo.findAllById(finalGiftCategoryIds);
        gifts.stream().forEach(item -> {
            GiftDynamicDataDto giftDynamicData = item.getDynamicData();
            Long giftCategoryId = giftDynamicData.getGiftCategoryId();
            String giftCategoryCode = item.getCategoryCode();
            GiftCategory giftCategory = null;
            Long oldGiftIid = null;
            if (Objects.nonNull(giftCategoryId)) {
                for (GiftCategory giftCategoryItem : giftCategories) {
                    if (giftCategoryItem.getId().equals(giftCategoryId)) {
                        giftCategory = giftCategoryItem;
                        break;
                    }
                }
            }
            for (MapOldGiftToNewGift mapOldGiftToNewGift : mapOldGiftToNewGifts) {
                if (mapOldGiftToNewGift.getNewGift().getId().equals(item.getId())) {
                    oldGiftIid = mapOldGiftToNewGift.getOldGiftId();
                    break;
                }
            }
            if (Objects.nonNull(giftCategory)) {
                EventAddCan eventAddCan = giftCategory.getEventAddCan();
                if (this.checkEventAddCanTypeCanSetupGiftCanRedeem(eventAddCan)) {
                    EventNumberUserCan eventNumberUserCan = this.eventNumberUserCanRepo
                            .findByEventAddCanAndUser(eventAddCan.getId(), userId);
                    Long giftId = (long) item.getId();
                    this.setupGiftCanRedeem(item, eventAddCan, eventNumberUserCan, oldGiftIid, giftId);
                }
            // Check for new gift in Improvement Event phase 1
            } else {
                EventAddCan eventAddCan = this.eventAddCanRepo.findFistByCategoryCode(giftCategoryCode).orElse(null);
                if (Objects.nonNull(eventAddCan)) {
                    if (this.checkEventAddCanTypeCanSetupGiftCanRedeem(eventAddCan)) {
                        EventNumberUserCan eventNumberUserCan = this.eventNumberUserCanRepo
                                .findByEventAddCanAndUser(eventAddCan.getId(), userId);

                        Long giftId = (long) item.getId();
                        this.setupGiftCanRedeem(item, eventAddCan, eventNumberUserCan, oldGiftIid, giftId);
                    }
                }
            }
            result.add(item);
        });

        return result;
    }

    public GiftDto setupGiftCanRedeem(Gift gift) {
        User user = this.getUserFromAuthToken();
        GiftDto giftDto = GiftDto.fromEntity(gift);
        this.setupGiftCanRedeem(giftDto, user);

        return giftDto;
    }

    public List<GiftDto> setupGiftsCategoryName(List<GiftDto> giftDtos) {
        List<GiftDto> result = new ArrayList<GiftDto>();
        List<String> categoryCodes = new ArrayList<>();
        giftDtos.stream().forEach(giftDto -> {
            //giftDto = this.setupGiftCategoryName(giftDto);
            //result.add(giftDto);
            String categoryCode = giftDto.getCategoryCode();
            if (Objects.nonNull(categoryCode)) {
                categoryCodes.add(giftDto.getCategoryCode());
            }
        });
        List<String> finalCategoryCodes = new ArrayList<>(
                new LinkedHashSet<>(categoryCodes));
        List<Constant> constants = this.constantRepository.findAllByKeyAlphaAndKeyBetaAndValueIn(
                ConstantFeatureEnum.GIFT.toString(),
                ConstantTypeEnum.CATEGORY.toString(),
                finalCategoryCodes
        );
        giftDtos.stream().forEach(giftDto -> {
            giftDto.setCategoryName("");
            String categoryCode = giftDto.getCategoryCode();
            Constant category = null;
            for (Constant constantItem : constants) {
                if (constantItem.getValue().equals(categoryCode)) {
                    category = constantItem;
                    break;
                }
            }
            if (Objects.nonNull(category)) {
                Constant.ConstantDynamicData dynamicData = category.getDynamicData();
                giftDto.setCategoryName(dynamicData.getName());
            }
            result.add(giftDto);
        });

        return result;
    }

    public GiftDto setupGiftCategoryName(GiftDto giftDto) {
        giftDto.setCategoryName("");
        String categoryCode = giftDto.getCategoryCode();
        Constant category = this.constantRepository.findFirstByKeyAlphaAndKeyBetaAndValue(
                ConstantFeatureEnum.GIFT.toString(), ConstantTypeEnum.CATEGORY.toString(),
                categoryCode);
        if (Objects.nonNull(category)) {
            Constant.ConstantDynamicData dynamicData = category.getDynamicData();
            giftDto.setCategoryName(dynamicData.getName());
        }

        return giftDto;
    }

    private ImmutablePair<ExampleMatcher, Gift> _buildExampleMatcher(ListGiftRequestDto dto) {
        Gift exampleGift = new Gift();
        exampleGift.setStatus(dto.getStatus());
        exampleGift.setIsAllowReservation(dto.getIsAllowReservation());
        exampleGift.setCategoryCode(dto.getCategoryCode());
        exampleGift.setName(dto.getName());
        exampleGift.setType(dto.getType());
        ExampleMatcher matcher =
                ExampleMatcher.matching().withMatcher("status", match -> match.exact())
                        .withMatcher("isAllowReservation", match -> match.exact())
                        .withMatcher("categoryCode", match -> match.exact())
                        .withMatcher("name", match -> match.contains().ignoreCase())
                        .withMatcher("type", match -> match.exact());

        return new ImmutablePair<ExampleMatcher, Gift>(matcher, exampleGift);
    }

    private List<Predicate> _buildPredicates(ListGiftRequestDto dto, CriteriaBuilder builder,
                                             Root<Gift> root) {
        List<Predicate> predicates = new ArrayList<>();
        if (dto.getMaxPoint() != null) {
            predicates.add(builder.lessThanOrEqualTo(root.get("point"), dto.getMaxPoint()));
        }

        if (dto.getStatus() != null) {
            predicates.add(builder.equal(root.get("status"), dto.getStatus()));
        }

        if (dto.getIsAllowReservation() != null) {
            predicates.add(
                    builder.equal(root.get("isAllowReservation"), dto.getIsAllowReservation()));
        }
        if (dto.getCategoryCode() != null && !dto.getCategoryCode().isEmpty()) {
            predicates.add(builder.equal(root.get("categoryCode"), dto.getCategoryCode()));
        }
        if (dto.getName() != null && !dto.getName().isEmpty()) {
            String searchTerm = dto.getName().toLowerCase();
            String pattern = "%" + searchTerm.toLowerCase() + "%";
            Expression<String> lowerNameExpression = builder.lower(root.get("name"));
            Predicate namePredicate = builder.like(lowerNameExpression, pattern);
            predicates.add(namePredicate);
        }
        if (dto.getType() != null) {
            predicates.add(builder.equal(root.get("type"), dto.getType()));
        }
        String vtierCodes = dto.getTierCodes();
        List<Predicate> tierCodePredicates = new ArrayList<>();
        if (vtierCodes != null) {
            for (String tierCode : vtierCodes.split(",")) {
                Expression<String> tierCodesExpression = root.get("tierCodes").as(String.class);
                Predicate tierCodePredicate =
                        builder.like(tierCodesExpression, "%" + tierCode + "%");
                tierCodePredicates.add(tierCodePredicate);
            }
        }
        if (!tierCodePredicates.isEmpty()) {
            predicates.add(builder.and(tierCodePredicates.toArray(new Predicate[0])));
        }

        if (dto.getId() != null && !dto.getId().isEmpty()) {
            Integer[] ids = StringUtils.toNumbers(dto.getId());

            CriteriaBuilder.In<Integer> inClause = builder.in(root.get("id"));
            for (Integer id : ids) {
                inClause.value(id);
            }
            predicates.add(inClause);
        }

        if (dto.getIds() != null && dto.getIds().length > 0) {
            Integer[] ids = dto.getIds();
            CriteriaBuilder.In<Integer> inClause = builder.in(root.get("id"));
            for (Integer id : ids) {
                inClause.value(id);
            }
            predicates.add(inClause);
        }

        if (Objects.nonNull(dto.getIsCustomer()) && dto.getIsCustomer()) {
            Instant now = Instant.now();
            predicates.add(builder.greaterThan(root.get("endDate"), Timestamp.from(now)));
            predicates.add(builder.lessThanOrEqualTo(root.get("startDate"), Timestamp.from(now)));
            if (Objects.isNull(dto.getCategoryCode())) {
                List<Constant> giftCategories = this.constantRepository
                        .findByKeyAlphaAndKeyBetaAndIsActive(ConstantFeatureEnum.GIFT.toString(),
                                ConstantTypeEnum.CATEGORY.toString(), true, Sort.unsorted());
                CriteriaBuilder.In<String> inClause = builder.in(root.get("categoryCode"));
                giftCategories.forEach(giftCategory -> {
                    inClause.value(giftCategory.getValue());
                });
                predicates.add(inClause);
            }
        }
        return predicates;
    }

    public Boolean isHiddenPreOrder(Gift gift, Long userId) {
        Boolean isEligible = false;
        // FIXME: Below code return 2 UserGift in the develop ENV at 26/07/2024 13:50.
        // Please check/validate in the insert/update function
        if (gift.getIsAllowReservation() != null) {
            if (gift.getIsAllowReservation() == false) {
                return true;
            }
        }

        UserGift userGift = this.userGiftRepository.findFirstByUserIdAndGiftIdAndStatus(userId,
                gift.getId(), UserGift.USER_GIFT_STATUS.PRE_ORDER);
        GiftReservation giftReservation = this.giftReservationRepository.findByGiftId(gift.getId());
        if (Objects.isNull(giftReservation) || giftReservation.getLimitReservationTime() == null) {
            return true;
        }
        long countExchangedStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.EXCHANGED, userId, gift.getId());
        long countCancelStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.CANCEL, userId, gift.getId());
        long countPreOrderStatus = this.userGiftRepository
                .countByStatusAndUserId(USER_GIFT_STATUS.PRE_ORDER, userId, gift.getId());
        long total = countExchangedStatus + countCancelStatus + countPreOrderStatus;

        if (total >= giftReservation.getLimitReservationTime()) {
            return true;
        }
        if (userGift == null && giftReservation.getMaximumReservationQuantity() != 0) {
            return false;
        }

        // log.info("[USER GIFT] {} ", userGift.getId());
        // log.info("[GIFT QUANTITY] {} ", gift.getQuantity());
        // log.info("[TOTAL] {} ", total);
        // log.info("[LIMITRESER] {} ", giftReservation.getLimitReservationTime());
        if (userGift != null || gift.getQuantity() == 0) {
            isEligible = true;
        }
        // log.info("[isHiddenPreOrder] {} " + isEligible);
        return isEligible;
    }

    public Boolean isDisabledPreOrderBt(Gift gift, Long userId) {
        Boolean isAfter = false;
        Optional<User> user = userRepository.getUserById(userId);
        GiftReservation giftReservation = this.giftReservationRepository.findByGiftId(gift.getId());
        if (Objects.isNull(giftReservation) || giftReservation.getLimitReservationTime() == null
                || user.get().getGiftPoint() < giftReservation.getReservationPoint()) {
            return true;
        }
        DateTimeFormatter desiredFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

        LocalDate currentDate = LocalDate.now();
        LocalDate currentDatePlusExpriedDays =
                currentDate.plusDays(giftReservation.getReservationExpiredDays());
        String formattedPlusExpriedDays = currentDatePlusExpriedDays.format(desiredFormatter);
        LocalDateTime endDateTime =
                LocalDateTime.parse(gift.getEndDate().toString(), originalFormatter);
        String formattedEndDate = endDateTime.format(desiredFormatter);
        int afterDate = formattedPlusExpriedDays.compareTo(formattedEndDate);
        // log.info("[PLUS DATE] {} ", formattedPlusExpriedDays);
        // log.info("[END DATE] {} ", formattedEndDate);
        // log.info("[AFTER DATE] {} ", afterDate);
        // log.info("[MAXIMUM] {} ", giftReservation.getMaximumReservationQuantity());
        if (afterDate > 0 || giftReservation.getMaximumReservationQuantity() == 0) {
            isAfter = true;
        }
        return isAfter;
    }

    public User getUserFromAuthToken() {
        User currentUser = null;
        AuthorizationResponse authRes = AuthenticationHelper.getCurrentUser();
        if (Objects.nonNull(authRes) && Objects.nonNull(authRes.getUserId())) {
            Optional<User> user = userRepository.getUserById(authRes.getUserId());
            if (user.isPresent()) {
                currentUser = user.get();
            }
        }

        return currentUser;
    }

    public UserDto getUserDtoFromAuthToken() {
        User currentUser = this.getUserFromAuthToken();
        return UserDto.fromEntity(currentUser);
    }

    private void setupGiftCanRedeem(GiftDto gift, User user) {
        if (Objects.isNull(user)) {
            return;
        }
        GiftDynamicDataDto giftDynamicData = gift.getDynamicData();
        Long giftCategoryId = giftDynamicData.getGiftCategoryId();
        MapOldGiftToNewGift mapOldGiftToNewGift =
                this.mapOldGiftToNewGiftRepository.findFirstByNewGiftId(gift.getId()).orElse(null);
        Long oldGiftIid = null;
        Long giftId = (long) gift.getId();

        EventAddCan eventAddCan = null;
        if (Objects.nonNull(giftCategoryId) && Objects.nonNull(mapOldGiftToNewGift)) {
            oldGiftIid = mapOldGiftToNewGift.getOldGiftId();
            GiftCategory giftCategory = this.giftCategoryRepo.findById(giftCategoryId).orElse(null);
             eventAddCan = giftCategory.getEventAddCan();
            if (this.checkEventAddCanTypeCanSetupGiftCanRedeem(eventAddCan)) {
                EventNumberUserCan eventNumberUserCan = this.eventNumberUserCanRepo
                        .findByEventAddCanAndUser(eventAddCan.getId(), user.getId());
                this.setupGiftCanRedeem(gift, eventAddCan, eventNumberUserCan, oldGiftIid, giftId);
            }
        // Check for new gift in Improvement Event phase 1
        } else {
             eventAddCan = this.eventAddCanRepo.findFistByCategoryCode(gift.getCategoryCode()).orElse(null);
            if (Objects.nonNull(eventAddCan)) {
                if (this.checkEventAddCanTypeCanSetupGiftCanRedeem(eventAddCan)) {
                    EventNumberUserCan eventNumberUserCan = this.eventNumberUserCanRepo
                            .findByEventAddCanAndUser(eventAddCan.getId(), user.getId());
                    this.setupGiftCanRedeem(gift, eventAddCan, eventNumberUserCan, oldGiftIid, giftId);
                }
            }
        }
    }

    private void setupGiftCanRedeem(GiftDto gift, EventAddCan eventAddCan,
                                    EventNumberUserCan eventNumberUserCan, Long oldGiftIid, Long giftId) {
        /*
         * if (!this.checkEventAddCanTypeCanSetupGiftCanRedeem(eventAddCan)) { return; }
         */
        
        Optional<EventCanMark> optionalEventCanMark = null;
        if (Objects.nonNull(oldGiftIid)) {
            optionalEventCanMark = this.eventCanMarkRepo.findByEventAddCanIdAndGiftId(eventAddCan.getId(), oldGiftIid);
        } else {
            optionalEventCanMark = this.eventCanMarkRepo.findByEventAddCanIdAndGsGiftId(eventAddCan.getId(), giftId);
        }
        if (optionalEventCanMark.isPresent()) {
            EventCanMark eventCanMark = optionalEventCanMark.get();

            gift.setPreferential_point(eventCanMark.getPreferentialPoint());
            if (Objects.isNull(eventNumberUserCan)
                    || eventNumberUserCan.getNumberOfCan() < eventCanMark.getNumberScan()) {
                gift.setCanRedeem(false);
            }
        }
    }

    private Boolean checkEventAddCanTypeCanSetupGiftCanRedeem(EventAddCan eventAddCan) {
        if (Objects.isNull(eventAddCan)) {
            return false;
        }
        
        return true;
    }

    private void validateUpdateGiftBrand(GiftRequestDto giftDto) {
        if (
                giftDto.getPoint() > 0 &&
                        Objects.nonNull(giftDto.getBrandPoint())
        ) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Gift point and brand point can not set on one gift");
        }
    }

    private void validateUpdateGiftBrand(GiftRequestDto giftDto, Gift oldGift) {
        // Validate general data
        this.validateUpdateGiftBrand(giftDto);
        // Validate update existing gift
        if (
                Objects.nonNull(giftDto.getBrandPoint()) &&
                        oldGift.checkIsGiftBrand().equals(Boolean.FALSE)
        ) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Can not update gift to brand point");
        }
        if (
                Objects.isNull(giftDto.getBrandPoint()) &&
                        oldGift.checkIsGiftBrand().equals(Boolean.TRUE)
        ) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Can not update gift to gift point");
        }
    }


    //getGiftByGiftIds
    public List<GiftDto> getGiftByGiftIds(List<Integer> giftIds) {
        List<Gift> gifts = this.giftRepository.findAllById(giftIds);
        return gifts.stream()
                .map(GiftDto::fromEntity)
                .collect(Collectors.toList());
    }

    //duplicateGiftsByGiftIds
    @Transactional("giftTransactionManager")
    public List<Gift> duplicateGifts(Map<Integer, String> giftSuffixMap) {
        List<Integer> giftIds = new ArrayList<>(giftSuffixMap.keySet());

        List<Gift> originalGifts = giftRepository.findAllById(giftIds);
        if (originalGifts.size() != giftIds.size()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "GiftIds not found: " + giftIds);
        }
        long timestamp = System.currentTimeMillis()/1000;

        List<Gift> duplicatedGifts = originalGifts.stream().map(gift -> {
            Gift newGift = new Gift();

            String suffix = giftSuffixMap.getOrDefault(gift.getId(), "").trim();
            if (suffix.isEmpty()) {
                suffix = "_" + timestamp;
            }

            newGift.setName(gift.getName() + "_" + suffix);
            newGift.setCategoryCode(gift.getCategoryCode());
            newGift.setPrice(gift.getPrice());

            newGift.setImages(gift.getImages() != null ? new ArrayList<>(gift.getImages()) : new ArrayList<>());
            newGift.setTransportTypeCode(gift.getTransportTypeCode() != null ? gift.getTransportTypeCode() : "");
            newGift.setStatus(gift.getStatus() != null ? gift.getStatus() : GiftStatusEnum.ENABLED);
            newGift.setBadgeCodes(gift.getBadgeCodes() != null ? new ArrayList<>(gift.getBadgeCodes()) : new ArrayList<>());
            newGift.setTierCodes(gift.getTierCodes() != null ? new ArrayList<>(gift.getTierCodes()) : new ArrayList<>());
            newGift.setType(gift.getType() != null ? gift.getType() : GiftTypeEnum.GIFT);
            newGift.setPoint(gift.getPoint() != null ? gift.getPoint() : 0);
            newGift.setHiddenTags(gift.getHiddenTags() != null ? new ArrayList<>(gift.getHiddenTags()) : new ArrayList<>());
            newGift.setSfNumber(gift.getSfNumber() != null ? gift.getSfNumber() : 0L);
            newGift.setStartDate(gift.getStartDate() != null ? gift.getStartDate() : new Timestamp(System.currentTimeMillis()));
            newGift.setEndDate(gift.getEndDate());
            newGift.setExpireDate(gift.getExpireDate());
            newGift.setExpireHour(gift.getExpireHour() != null ? gift.getExpireHour() : 0);
            newGift.setSctNumber(gift.getSctNumber() != null ? gift.getSctNumber() : "");
            newGift.setQuantity(gift.getQuantity() != null ? gift.getQuantity() : 0L);
            newGift.setInventory(gift.getInventory() != null ? gift.getInventory() : 0);
            newGift.setQuantityLimitForBooking(gift.getQuantityLimitForBooking() != null ? gift.getQuantityLimitForBooking() : 0);
            newGift.setPurchaseOption(gift.getPurchaseOption() != null ? gift.getPurchaseOption() : "");
            newGift.setEventId(gift.getEventId() != null ? gift.getEventId() : 0L);
            newGift.setQuantityReward(gift.getQuantityReward() != null ? gift.getQuantityReward() : 0);
            newGift.setQuantityReservation(gift.getQuantityReservation() != null ? gift.getQuantityReservation() : 0);
            newGift.setIsAllowReservation(gift.getIsAllowReservation() != null ? gift.getIsAllowReservation() : false);
            newGift.setPriority(gift.getPriority() != null ? gift.getPriority() : 0);
            newGift.setDynamicData(gift.getDynamicData() != null ? gift.getDynamicData() : new GiftDynamicData());

            return newGift;
        }).collect(Collectors.toList());

        return giftRepository.saveAll(duplicatedGifts);
    }
}
