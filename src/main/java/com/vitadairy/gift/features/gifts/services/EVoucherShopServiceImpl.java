package com.vitadairy.gift.features.gifts.services;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.features.gifts.interfaces.IBkidsService;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherShopService;
import com.vitadairy.gift.features.gifts.interfaces.IGotItService;
import com.vitadairy.gift.features.gifts.interfaces.IVitaCodeService;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.responses.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("eVoucherShopServiceImpl")
public class EVoucherShopServiceImpl implements IEVoucherShopService {
    //@Value("${evoucher-shop.service}")
    //private String activeService = Constant.EvoucherShopService.GOT_IT;

    private final IGotItService gotItService;

    private final IBkidsService bkidsService;

    private final IVitaCodeService vitaCodeService;

    public EVoucherShopServiceImpl(
            @Qualifier("eVoucherServiceGotitImpl") IGotItService gotItService,
            @Qualifier("eVoucherServiceBkidsImpl") IBkidsService bkidsService,
            @Qualifier("eVoucherServiceVitaCodeImpl") IVitaCodeService vitaCodeService
    ) {
        this.gotItService = gotItService;
        this.bkidsService = bkidsService;
        this.vitaCodeService = vitaCodeService;
    }

    public Object getVoucher(User user, Gift gift, String refId, String service, HistoryPoint historyPoint) {
        switch (service) {
            case Constant.EvoucherShopService.GOT_IT -> {
                return this.gotItService.getGotItVoucher(
                        user,
                        gift,
                        refId,
                        historyPoint
                );
            }
            case Constant.EvoucherShopService.BKIDS -> {
                RequestCouponResponse requestCouponResponse = this.bkidsService.requestCoupon(
                        user,
                        gift
                );
                if (
                        Objects.isNull(requestCouponResponse) ||
                                !requestCouponResponse.isValid()
                ) {
                    return null;
                }
                List<RequestCouponDataCouponResponse> list = requestCouponResponse.getData();
                RequestCouponDataCouponResponse item = list.get(0);
                if (!item.isValid()) {
                    return null;
                }
                RequestDetailCouponResponse requestDetailCouponResponse =
                        this.bkidsService.requestDetailCoupon(user, item.get_id());
                if (
                        Objects.isNull((requestDetailCouponResponse)) ||
                                !requestDetailCouponResponse.isValid()
                ) {
                    return null;
                }
                RequestDetailCouponDataDetailCouponResponse detail = requestDetailCouponResponse.getData();
                if (!detail.isValid()) {
                    return null;
                }

                return detail;
            }
            case Constant.EvoucherShopService.VITA_CODE -> {
                return this.vitaCodeService.getCodeEvoucherVacxinV1(
                        gift
                );
            }
            default -> {
                return null;
            }
        }
    }

    @Override
    public Object processEVoucherResponse(UserGift userGift, Object evoucherResponse, String service) {
        switch (service) {
            case Constant.EvoucherShopService.GOT_IT -> {
                return this.gotItService.processEVoucherResponse(
                        userGift,
                        (VoucherResponse) evoucherResponse
                );
            }
            case Constant.EvoucherShopService.BKIDS -> {
                return this.bkidsService.processCouponResponse(
                        userGift,
                        (RequestDetailCouponDataDetailCouponResponse) evoucherResponse
                );
            }
            case Constant.EvoucherShopService.VITA_CODE -> {
                return this.vitaCodeService.processCodeEvoucherVacxinV1Response(
                        userGift,
                        (String) evoucherResponse
                );
            }
            default -> {
                return null;
            }
        }
    }
}
