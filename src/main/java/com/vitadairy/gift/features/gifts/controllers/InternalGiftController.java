package com.vitadairy.gift.features.gifts.controllers;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftRequestDto;
import com.vitadairy.main.dto.GiftReturnPointRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.SessionGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.GiftPageResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftResponse;
import com.vitadairy.gift.features.gifts.services.GiftExchangeService;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.dto.GiftReturnPointResponseDto;
import com.vitadairy.main.dto.ReturnPointResponseDto;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.zoo.dto.UserDto;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("in/gs/gifts")
public class InternalGiftController {
    private final GiftService giftService;
    private final GiftExchangeService giftExchangeService;
    private final ResponseFactory responseFactory;

    public InternalGiftController(GiftService giftService, ResponseFactory responseFactory, GiftExchangeService giftExchangeService) {
        this.giftService = giftService;
        this.responseFactory = responseFactory;
        this.giftExchangeService = giftExchangeService;
    }

    @GetMapping()
    public ResponseEntity<GiftPageResponse> getGifts(
            @ModelAttribute ListGiftRequestDto dto) {
        Page<Gift> data = giftService.pageableSpecification(dto);
        GiftPageResponse response = new GiftPageResponse(
                data.getContent().stream().map((el) -> GiftDto.fromEntity(el)).toList(),
                (long) data.getTotalElements(), (long) data.getTotalPages(), dto.getPage(), dto.getSize(), HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

    @GetMapping("{id}")
    public ResponseEntity<GiftResponse> getDetailsById(@PathVariable Integer id) {
        Gift gift = this.giftService.details(id);
        GiftResponse giftResponse = new GiftResponse(GiftDto.fromEntity(gift), HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    @GetMapping("sections")
    public ResponseEntity<GiftPageResponse> sectionGifts(@ModelAttribute SessionGiftRequestDto dto) {
        UserDto currentUser = giftService.getUserDtoFromAuthToken();
        var gifts = giftService.sectionGifts(dto, currentUser);
        GiftPageResponse response = new GiftPageResponse(gifts.getGifts(),
                gifts.getTotalElements(), 1L, 0, (int) gifts.getTotalElements(), HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

    @PatchMapping("{id}")
    public ResponseEntity<GiftResponse> updateGift(@PathVariable("id") Integer id,
                                                   @Valid @RequestBody GiftRequestDto body) {
        Gift gift = this.giftService.update(id, body);
        GiftResponse giftResponse = new GiftResponse(GiftDto.fromEntity(gift), HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    @PostMapping("/return-point")
    public ResponseEntity<ReturnPointResponseDto> returnPoint(@RequestBody List<GiftReturnPointRequestDto> dto) {
        List<GiftReturnPointResponseDto> response = giftExchangeService.doReturnPoint(dto);
        return responseFactory.successDto(new ReturnPointResponseDto(response, HttpStatus.OK, "OK"));
    }

    @PostMapping("/update-sf-gift")
    public ResponseEntity<?> updateSfGift(@RequestBody List<String> returnPointTxId) {
        giftExchangeService.sendUpdateGiftSf(returnPointTxId);
        return responseFactory.success(EntityResponse.<Boolean>entityBuilder()
                .statusCode(HttpStatus.OK)
                .msg("ok")
                .build());
    }
}
