package com.vitadairy.gift.features.gifts.dto.response;

import com.vitadairy.main.response.EntityResponse;
import org.springframework.http.HttpStatusCode;

import java.util.List;

public class TopExchangeGiftsResponse extends EntityResponse<List<ExchangeGiftResponse>> {

    public TopExchangeGiftsResponse(List<ExchangeGiftResponse> data, HttpStatusCode statusCode, String message) {
        super(data, statusCode, message);
    }
}
