package com.vitadairy.gift.features.gifts.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.zoo.entities.GotItTransactionLog;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.GotItTopupStatus;
import com.vitadairy.zoo.enums.GotItTransactionType;
import com.vitadairy.gift.features.gifts.interfaces.IGotItService;
import com.vitadairy.zoo.repositories.GotItTransactionLogRepository;
import com.vitadairy.zoo.repositories.UserRepository;
import com.vitadairy.zoo.requests.CheckTopUpTransactionDebug99Request;
import com.vitadairy.zoo.requests.CheckTopUpTransactionRequest;
import com.vitadairy.zoo.requests.GetVoucherRequest;
import com.vitadairy.zoo.requests.TopupV1Dot4Request;
import com.vitadairy.zoo.responses.GetVoucherResponse;
import com.vitadairy.zoo.responses.GotitErrorResponse;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import com.vitadairy.zoo.responses.VoucherResponse;
import com.vitadairy.zoo.common.Constant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

@Service("eVoucherServiceGotitImpl")
@Slf4j
public class GotItServiceImpl implements IGotItService {
    @Value("${got-it.base-url}")
    private String baseUrl;

    @Value("${got-it.content-type}")
    private String contentType;

    @Value("${got-it.accept-language}")
    private String acceptLanguage;

    @Value("${got-it.auth.header}")
    private String authHeader;

    @Value("${got-it.auth.token}")
    private String authToken;

    @Value("${got-it.auth.expiry-time-seconds:86400}")
    private long expiryTimeSeconds = 86400l;

    @Value("${got-it.api.category}")
    private String categoryApi;

    @Value("${got-it.api.list-product}")
    private String listProductApi;

    @Value("${got-it.api.product-detail}")
    private String productDetailApi;

    @Value("${got-it.api.transaction}")
    private String transactionApi;

    @Value("${got-it.api.brand}")
    private String brandApi;

    //@Value("${got-it.api.brand-detail}")
    //private String brandDetailApi;

    @Value("${got-it.api-v1.4.topup}")
    private String topupApi;

    @Value(("${got-it.api.get-evoucher}"))
    private String getEvoucherApi;

    @Value("${got-it.api.check-status-evoucher}")
    private String checkStatusEvoucherApi;

    @Value("${got-it.api-v1.4.check-topup}")
    private String checkTopupApi;

    @Value("${got-it.connection-timeout-seconds:10}")
    private int connectionTimeOutSeconds = 10;

    @Value("${got-it.read-timeout-seconds:20}")
    private int readTimeOutSeconds = 20;

    //@Value("${got-it.delay-time-check-status-transaction:12}")
    private int delayTimeCheckStatusTransaction = 12;

    @Value("${got-it.phone-debug-status-ninety-nine:}")
    private List<String> phoneDebugStatusNinetyNine = new ArrayList<>();

    private final GotItTransactionLogRepository gotItTransactionLogRepo;

    private final ObjectMapper objectMapper;

    private final RestTemplate restTemplate;

    private final UserGiftRepository userGiftRepo;

    private final UserRepository userRepository;

    private HttpEntity<?> entity;

    private final long eVoucherShopExpiryTimeSeconds = 31536000L;

    public GotItServiceImpl(GotItTransactionLogRepository gotItTransactionLogRepo, ObjectMapper objectMapper, RestTemplate restTemplate, UserGiftRepository userGiftRepo, UserRepository userRepository) {
        this.gotItTransactionLogRepo = gotItTransactionLogRepo;
        this.objectMapper = objectMapper;
        this.restTemplate = restTemplate;
        this.userGiftRepo = userGiftRepo;
        this.userRepository = userRepository;
    }

    @Override
    public TopupResponseV1Dot4 topupV1Dot4(User user, String phone, String refId, Long amount, HistoryPoint historyPoint) {
        TopupV1Dot4Request payload = new TopupV1Dot4Request(
                phone,
                amount,
                2,
                refId
        );
        RestTemplateBuilder restTemplateBuilder = new RestTemplateBuilder();
        RestTemplate template = restTemplateBuilder
                .setConnectTimeout(Duration.ofSeconds(this.connectionTimeOutSeconds))
                .setReadTimeout(Duration.ofSeconds(this.connectionTimeOutSeconds))
                .build();

        TopupResponseV1Dot4 response = null;
        String errorMsg = "";
        try {
            response = this.postV2(
                    this.topupApi,
                    payload,
                    TopupResponseV1Dot4.class,
                    template
            );
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
            if (Objects.nonNull(errorMsg) && errorMsg.contains("timed out")) {
                response = new TopupResponseV1Dot4(); // Initialize if null
                response.setStatus(8888); //  gotit status code timeout (self define)
                response.setRefId(refId);
            }
        }
        this.loggingCallApi(
                user,
                historyPoint,
                refId,
                payload,
                response,
                errorMsg,
                GotItTransactionType.TOPUP,
                this.mapStatusFromGotItToInternal(response)
        );

        if (Objects.isNull(response)) {
            response = new TopupResponseV1Dot4(); // Initialize if null
            response.setStatus(2); //  the same logic with status 2 on topup response so I set it to 2
            response.setRefId(refId);
        }

        return response;
    }

    @Override
    public TopupResponseV1Dot4 checkTopupTransaction(User user, String refId, HistoryPoint historyPoint) {
        TopupResponseV1Dot4 response = null;
        String errorMsg = "";
        String phoneNumber = user.getPhoneNumber();
        Object request = null;
        if (this.phoneDebugStatusNinetyNine.contains(phoneNumber)) {
            request = new CheckTopUpTransactionDebug99Request(refId, Boolean.TRUE);
        } else {
            request = new CheckTopUpTransactionRequest(refId);
        }

        try {
            if (Objects.isNull(refId) || refId.isEmpty()) {
                throw new ApplicationException("Ref id null", HttpStatus.BAD_REQUEST);
            }
            response = this.postV2(
                    this.checkTopupApi,
                    request,
                    TopupResponseV1Dot4.class
            );
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
        }
        this.loggingCallApi(
                user,
                historyPoint,
                refId,
                request,
                response,
                errorMsg,
                GotItTransactionType.CHECK_TRANSACTION,
                this.mapStatusFromGotItToInternal(response)
        );

        if (Objects.isNull(response)) {
            response = new TopupResponseV1Dot4(); // Initialize if null
            response.setStatus(2); //  the same logic with status 2 on topup response so I set it to 2
            response.setRefId(refId);
        }

        return response;
    }

    @Override
    public GotItTopupStatus mapStatusFromGotItToInternal(TopupResponseV1Dot4 response) {
        if (Objects.isNull(response)) {
            return GotItTopupStatus.STATUS_FAILED;
        }
        switch (response.getStatus()) {
            case 1:
                return GotItTopupStatus.STATUS_SUCCESS;

            case 2:
            case 5:
            case 6:
            case 7:
            case 9:
            case 99:
                return GotItTopupStatus.STATUS_PROCESSING;

            case 2006:
            case 5002:
                //case 2008:
            case 9999:
            case 2018:
            case 19:
                return GotItTopupStatus.STATUS_ERROR;

            case 8:
            case 10:
                return GotItTopupStatus.STATUS_RETURN_CODE_AS_TOPUP_FAILED;

            case 8888:
                return GotItTopupStatus.STATUS_TIMEOUT;

            case 2008:
                return GotItTopupStatus.STATUS_VOUCHER_REF_ID_PROCESSED;

            default:
                //LOGGER.error("GotItTopupStatus is unimplemented. Received={}", response.getStatus());
                return GotItTopupStatus.STATUS_FAILED;
        }
    }

    @Override
    public boolean checkTopupFail(TopupResponseV1Dot4 response) {
        return false;
    }

    @Override
    public TopupResponseV1Dot4 processTopupResponse(UserGift userGift, TopupResponseV1Dot4 topupResponse) {
        if (Objects.isNull(userGift) || Objects.isNull(topupResponse)) {
            return null;
        }
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setVoucherRefId(topupResponse.getRefId());
        Long userId = userGift.getUserId();
        User user = this.userRepository.findById(userId).orElse(null);
        if (Objects.nonNull(user)) {
            userGiftDynamicData.setTelco(
                    this.getTelcoFromTopupResponse(
                            topupResponse,
                            user.getPhoneNumber()
                            )
            );
        }
        userGift.setDynamicData(userGiftDynamicData);

        switch (topupResponse.getStatus()) {
            case 1 -> {
                userGift.setStatus(UserGift.USER_GIFT_STATUS.TOPUP);
                this.userGiftRepo.save(userGift);
                return topupResponse;
            }
            case 2, 2008, 8888, 99 -> {
                Instant now = Instant.now();
                Instant gotitTransactionLogTimeCheck = now.plus(Duration.ofHours(this.delayTimeCheckStatusTransaction));
                userGiftDynamicData.setTopupTransactionStatusTimeCheck(gotitTransactionLogTimeCheck.toString());
                //userGiftDynamicData.setTopupTransactionStatusNumberCheck(0);
                userGift.setDynamicData(userGiftDynamicData);
                userGift.setStatus(UserGift.USER_GIFT_STATUS.TOPUP_IS_PROCESSING);
                this.userGiftRepo.save(userGift);
                return topupResponse;
            }
            case 8, 10 -> {
                //UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
                userGift.setStatus(UserGift.USER_GIFT_STATUS.USED);
                userGiftDynamicData.setVoucherCode(topupResponse.getCardInfo().getCardNumber());
                userGiftDynamicData.setVoucherLink(topupResponse.getLink());
                userGift.setDynamicData(userGiftDynamicData);
                this.userGiftRepo.save(userGift);
                return topupResponse;
            }
            default -> {
                return null;
            }
        }
    }

    @Override
    public VoucherResponse processEVoucherResponse(UserGift userGift, VoucherResponse evoucherResponse) {
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setExpiryDate(evoucherResponse.getExpiryDate());
        userGiftDynamicData.setUsedDate(Instant.now());
        userGift.setUpdatedAt(Instant.now());
        userGiftDynamicData.setVoucherRefId(evoucherResponse.getVoucherRefId());
        userGiftDynamicData.setVoucherLink(evoucherResponse.getVoucherLink());
        userGiftDynamicData.setVoucherCode(evoucherResponse.getVoucherCode());
        userGift.setStatus(UserGift.USER_GIFT_STATUS.UNUSED);
        userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepo.save(userGift);

        return evoucherResponse;
    }

    @Override
    public GetVoucherResponse getGotItVoucher(User user, Gift gift, String refId, HistoryPoint historyPoint) {
        GiftDynamicData giftDynamicData = gift.getDynamicData();
        Long eProductId = giftDynamicData.getEProductId();
        Long priceId = giftDynamicData.getPriceId();
        GotItTopupStatus status;
        List<GetVoucherResponse> responses = null;
        GetVoucherResponse response = null;
        String errorMsg = "";
        Instant expiryDate = Instant.now().plusSeconds(this.eVoucherShopExpiryTimeSeconds);
        GetVoucherRequest request = new GetVoucherRequest(eProductId, priceId, expiryDate, user.getName(), user.getPhoneNumber(), refId);

        try {
            if (request.isInvalid()) {
                log.error("[GotItServiceImpl][getGotItVoucher] Gift not exited: {}", request);
                throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
            }
            responses = this.postV2(
                    this.getEvoucherApi,
                    request,
                    List.class
            );
            if (CollectionUtils.isEmpty(responses)) {
                throw new ApplicationException("Response empty", HttpStatus.INTERNAL_SERVER_ERROR);
            }

            response = this.objectMapper.convertValue(responses.get(0), GetVoucherResponse.class);
            status = GotItTopupStatus.STATUS_SUCCESS;
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
            status = GotItTopupStatus.STATUS_FAILED;
        }

        this.loggingCallApi(
                user,
                historyPoint,
                refId,
                request,
                responses,
                errorMsg,
                GotItTransactionType.GET_EVOUCHER,
                status
        );

        return response;
    }

    private String mapPhoneNumberToTelco(String phoneNumber) {
        String prefix = phoneNumber.substring(0, 3);

        switch (prefix) {
            case "086":
            case "096":
            case "097":
            case "098":
            case "032":
            case "033":
            case "034":
            case "035":
            case "036":
            case "037":
            case "038":
            case "039":
                return Constant.PhoneNumberTelco.VIET_TEL;

            case "088":
            case "091":
            case "094":
            case "083":
            case "084":
            case "085":
            case "081":
            case "082":
                return Constant.PhoneNumberTelco.VINA_PHONE;

            case "089":
            case "090":
            case "093":
            case "070":
            case "079":
            case "077":
            case "076":
            case "078":
                return Constant.PhoneNumberTelco.MOBI_PHONE;

            case "092":
            case "056":
            case "058":
                return Constant.PhoneNumberTelco.VIETNAM_MOBILE;

            case "099":
            case "059":
                return Constant.PhoneNumberTelco.G_MOBILE;

            default:
                log.error("PhoneNumber prefix is not valid. Received={}", phoneNumber);
                return Constant.PhoneNumberTelco.VIET_TEL;
        }
    }

    private String getTelcoFromTopupResponse(TopupResponseV1Dot4 topupResponse, String phoneNumber) {
        String telco;
        if(Objects.nonNull(topupResponse) && Objects.nonNull(topupResponse.getCardInfo()) && Objects.nonNull(topupResponse.getCardInfo().getTelco())) {
            telco = topupResponse.getCardInfo().getTelco();
        } else {
            telco = this.mapPhoneNumberToTelco(phoneNumber);
        }
        return telco;
    }

    private HttpEntity configEntity() {
        return new HttpEntity<>(configHeaders());
    }

    private <T> HttpEntity configEntity(T body) {
        return new HttpEntity<>(body, configHeaders());
    }

    private HttpHeaders configHeaders() {
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.valueOf(this.contentType));
        headers.setAcceptLanguage(Collections.singletonList(new Locale.LanguageRange(this.acceptLanguage)));
        headers.set(this.authHeader, this.authToken);

        return headers;
    }

    private String generateFullApiUrl(String api) {
        return this.baseUrl.concat(api);
    }

    private <T, U> T postV2(String api, U body, Class<T> responseType, RestTemplate restTemplateParams) {
        ResponseEntity<Object> response;
        try {
            response = restTemplateParams.exchange(this.generateFullApiUrl(api), HttpMethod.POST, this.configEntity(body), Object.class);
        } catch (Exception e) {
            log.error("[GotItServiceImpl][errorHandler] got it error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        try {
            T res = objectMapper.convertValue(response.getBody(), responseType);
            return res;
        } catch (Exception ignored) {}

        return null;
    }

    private <T, U> T postV2(String api, U body, Class<T> responseType) {
        ResponseEntity<Object> response;
        try {
            response = this.restTemplate.exchange(this.generateFullApiUrl(api), HttpMethod.POST, this.configEntity(body), Object.class);
        } catch (Exception e) {
            log.error("[GotItServiceImpl][errorHandler] got it error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        try {
            T res = objectMapper.convertValue(response.getBody(), responseType);
            return res;
        } catch (Exception ignored) {}

        return null;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void loggingCallApi(User user, HistoryPoint historyPoint, String refId, Object request, Object response, String errorMsg, GotItTransactionType type, GotItTopupStatus status) {
        try {
            String requestStr = this.objectMapper.writeValueAsString(request);
            String responseStr = this.objectMapper.writeValueAsString(response);

            GotItTransactionLog gotItTransLog = new GotItTransactionLog(user, requestStr, user.getId(), refId, type);
            gotItTransLog.setTransactionExternalId(historyPoint.getTransactionExternalId());
            gotItTransLog.setStatus(status);
            if (Objects.nonNull(errorMsg) && !errorMsg.isEmpty()) {
                GotitErrorResponse gotitErrorResponse = new GotitErrorResponse();
                gotitErrorResponse.setError(errorMsg);
                gotItTransLog.setResponse(this.objectMapper.writeValueAsString(gotitErrorResponse));
            } else {
                gotItTransLog.setResponse(responseStr);
            }

            this.gotItTransactionLogRepo.save(gotItTransLog);
        } catch (Exception e) {
            log.error("[GotItServiceImpl][errorHandler] got it error : ", e);
        }
    }
}
