package com.vitadairy.gift.features.gifts.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.interfaces.IVitaCodeService;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.EventAddCan;
import com.vitadairy.zoo.entities.GiftCategory;
import com.vitadairy.zoo.entities.OutBoxMessage;
import com.vitadairy.zoo.entities.VitaCode;
import com.vitadairy.zoo.enums.*;
import com.vitadairy.zoo.repositories.EventAddCanRepository;
import com.vitadairy.zoo.repositories.GiftCategoryRepository;
import com.vitadairy.zoo.repositories.OutboxMsgRepository;
import com.vitadairy.zoo.repositories.VitaCodeRepository;
import com.vitadairy.zoo.requests.VitaLoyaltyV3GetEvoucherVacxinV1Request;
import com.vitadairy.zoo.responses.VitaLoyaltyV3Response;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.core5.http.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;

@Service("eVoucherServiceVitaCodeImpl")
@Slf4j
public class VitaCodeServiceImpl implements IVitaCodeService {
    @Value("${rest.url.vita-loyalty-v3:unknown}")
    private String baseUrl;

    //@Value("${vita-loyalty-v3.api.get-code-evoucher-vacxin-v1:unknown}")
    private String getCodeEvoucherVacxinV1Api = Constant.LoyaltyV3Service.GET_CODE_EVOUCHER_VACXIN;

    private final ObjectMapper objectMapper;

    private final OutboxMsgRepository outboxMsgRepository;

    private final GiftCategoryRepository giftCategoryRepository;

    private final VitaCodeRepository vitaCodeRepository;

    private final RestTemplate restTemplate;

    private final UserGiftRepository userGiftRepo;

    private EventAddCanRepository eventAddCanRepo;

    private HttpServletRequest request;

    public VitaCodeServiceImpl(
            ObjectMapper objectMapper,
            OutboxMsgRepository outboxMsgRepository,
            GiftCategoryRepository giftCategoryRepository,
            VitaCodeRepository vitaCodeRepository,
            RestTemplate restTemplate,
            UserGiftRepository userGiftRepo,
            EventAddCanRepository eventAddCanRepo
            ) {
        this.objectMapper = objectMapper;
        this.outboxMsgRepository = outboxMsgRepository;
        this.giftCategoryRepository = giftCategoryRepository;
        this.vitaCodeRepository = vitaCodeRepository;
        this.restTemplate = restTemplate;
        this.userGiftRepo = userGiftRepo;
        this.eventAddCanRepo = eventAddCanRepo;
    }

    @Override
    public String getCodeEvoucherVacxinV1(Gift gift) {
        if (Objects.isNull(gift)) {
            return null;
        }

        GiftDynamicData giftDynamicData = gift.getDynamicData();
        if (Objects.isNull(giftDynamicData)) {
            return null;
        }

        EventAddCan eventAddCan = null;
        Long giftCategoryId = giftDynamicData.getGiftCategoryId();
        if (Objects.nonNull(giftCategoryId)) {
            GiftCategory giftCategory = this.giftCategoryRepository.findById(giftCategoryId).orElse(null);
            if (Objects.isNull(giftCategory)) {
                return null;
            }
            eventAddCan = giftCategory.getEventAddCan();
        } else {
            eventAddCan = this.eventAddCanRepo.findFistByCategoryCode(gift.getCategoryCode()).orElse(null);
        }

        if (Objects.isNull(eventAddCan)) {
            return null;
        }

        OutboxMessageStatus status;
        String eventSource = Constant.EventTypePrefix.EVENT_246 + eventAddCan.getId().toString();
        VitaLoyaltyV3GetEvoucherVacxinV1Request req = new VitaLoyaltyV3GetEvoucherVacxinV1Request();
        req.setEventSource(eventSource);
        VitaLoyaltyV3Response response = null;
        String errorMsg = "";
        String code = null;
        try {
            response = this.post(
                    this.getCodeEvoucherVacxinV1Api,
                    req,
                    VitaLoyaltyV3Response.class
            );
            if (Objects.isNull(response)) {
                throw new ApplicationException("Response null", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            code = response.getResponse();
            if (null == code || code.isEmpty()) {
                throw new ApplicationException("Code null", HttpStatus.INTERNAL_SERVER_ERROR);
            }
            status = OutboxMessageStatus.SUCCESS;
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
            status = OutboxMessageStatus.FAILED;
        }
        this.loggingCallApi(
                request,
                response,
                errorMsg,
                status
        );

        return code;
    }

    @Override
    public String processCodeEvoucherVacxinV1Response(UserGift userGift, String code) {
        if (Objects.isNull(userGift)) {
            return code;
        }
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setVoucherCode(code);
        userGift.setStatus(UserGift.USER_GIFT_STATUS.USED);
        VitaCode vitaCode = this.vitaCodeRepository.findByCode(code);
        if (
                Objects.nonNull(vitaCode) &&
                        Objects.nonNull(vitaCode.getExpiryDate())
        ) {
            userGiftDynamicData.setExpiryDate(vitaCode.getExpiryDate().toString());
        }
        userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepo.save(userGift);

        return code;
    }

    private HttpEntity configEntity() {
        return new HttpEntity<>(configHeaders());
    }

    private <T> HttpEntity configEntity(T body) {
        return new HttpEntity<>(body, configHeaders());
    }

    private HttpHeaders configHeaders() {
        String accessToken = this.getAuthBearerToken();
        HttpHeaders headers = new HttpHeaders();
        String token = "Bearer " + accessToken;
        headers.set(Constant.Header.AUTHORIZATION, token);
        headers.set(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());

        return headers;
    }

    private String generateFullApiUrl(String api) {
        return this.baseUrl.concat(api);
    }

    private String getAuthBearerToken() {
        this.request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String bearerToken = this.request.getHeader(Constant.Header.AUTHORIZATION);
        if (!StringUtils.hasText(bearerToken) || !bearerToken.startsWith("Bearer ")) {
            return null;
        }

        return bearerToken.substring(7);
    }

    private <T, U> T post(String api, U body, Class<T> responseType) {
        ResponseEntity<Object> response;
        try {
            response = this.restTemplate.exchange(this.generateFullApiUrl(api), HttpMethod.POST, this.configEntity(body), Object.class);
        } catch (Exception e) {
            log.error("[VitaCodeServiceImpl][errorHandler] vitacode error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        try {
            T res = objectMapper.convertValue(response.getBody(), responseType);
            return res;
        } catch (Exception ignored) {}

        return null;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void loggingCallApi(Object request, Object response, String errorMsg, OutboxMessageStatus status) {
        try {
            OutBoxMessage outBoxMessage = new OutBoxMessage();
            String requestStr = this.objectMapper.writeValueAsString(request);
            String responseStr = this.objectMapper.writeValueAsString(response);

            outBoxMessage.setProvider(SyncProvider.VITA_LOYALTY_V3);
            outBoxMessage.setRequest(requestStr);
            outBoxMessage.setCallType(CallType.SYNC);
            outBoxMessage.setStatus(status);
            // Set retry number is 99 mean:
            //  - This record is result of calling API from Java reward
            //  - Ignore recall from code NestJS loyalty
            outBoxMessage.setRetryNumber(Constant.OutboxMessageRetryNumber.CALL_FROM_JAVA_REWARD);
            outBoxMessage.setSyncType(SyncType.IMMEDIATE);
            if (Objects.nonNull(errorMsg) && !errorMsg.isEmpty()) {
                outBoxMessage.setResponse(errorMsg);
            } else {
                outBoxMessage.setResponse(responseStr);
            }

            this.outboxMsgRepository.save(outBoxMessage);
        } catch (Exception e) {
            log.error("[VitaCodeServiceImpl][errorHandler] vita code error : ", e);
        }
    }
}
