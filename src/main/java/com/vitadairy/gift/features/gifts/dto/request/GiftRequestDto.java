package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.MapGiftToBrandPoint;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDynamicFilterDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.FutureOrPresent;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

@Data
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GiftRequestDto {
    // @NotNull
    // private Integer id;

    @NotEmpty
    private List<String> images;

    // @NotBlank
    private String transportTypeCode;

    @NotNull
    private GiftStatusEnum status;

    @NotBlank
//    @Size(max = 100)
    @Size(max = 100, message = "Tên quà không vượt quá 100 ký tự")
    private String name;

    private List<String> badgeCodes;

    @NotBlank
    private String categoryCode;

    @NotEmpty
    private List<String> tierCodes;

    @NotNull
    private GiftTypeEnum type;

    @NotNull
    @Min(0)
    private Integer point;

    @NotNull
    @Min(0)
    private BigInteger price;

    // @NotEmpty
    private List<String> hiddenTags;

    @Min(0)
    private long sfNumber;

    // @FutureOrPresent
    private Timestamp startDate;

    @FutureOrPresent
    private Timestamp endDate;

    // @FutureOrPresent
    private Timestamp expireDate;

    @Min(0)
    private Integer expireHour;

    private String sctNumber;

    @NotBlank
    private String tpmNumber;

    @Min(0)
    private long quantity;

    @Min(0)
    private Integer inventory;

    @Min(0)
    private Integer quantityLimitForBooking;

    private String purchaseOption;

    @Min(0)
    private Integer quantityReward;

    @Min(0)
    private Integer quantityReservation;

    private Boolean isAllowReservation;

    @Min(0)
    private Integer priority;

    @NotNull
    private GiftDynamicDataRequestDto dynamicData;

    private GiftReservationRequestDto giftReservation;

    private DynamicFilterRequestDto dynamicFilter;

    @Valid
    private MapGiftToBrandPointRequestDto brandPoint;

    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SectionGiftDto {
        private String hiddenTag;
        private String tierCode;
    }

    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterRequestDto {
        private String code;
        private String valueCode;
        private String name;
        private String firstLevelCode;
        private String firstLevelValueCode;
        private String secondLevelCode;
        private String secondLevelValueCode;

        public GiftDynamicFilterDto toDynamicFilterDto() {
            return GiftDynamicFilterDto.builder().code(this.code).valueCode(this.valueCode)
                    .name(this.name).firstLevelCode(this.firstLevelCode)
                    .firstLevelValueCode(this.firstLevelValueCode)
                    .secondLevelCode(this.secondLevelCode)
                    .secondLevelValueCode(this.secondLevelValueCode).build();
        }
    }

    public static Gift toEntity(GiftRequestDto dto) {
        GiftDynamicData dynamicData = null;
        if (dto.getDynamicData() != null) {
            dynamicData = GiftDynamicDataRequestDto.toEntity(dto.getDynamicData());
            dynamicData.setDynamicFilter(dto.getDynamicFilter() != null
                    ? dto.getDynamicFilter().toDynamicFilterDto().toEntity()
                    : null);
        }
        Gift gift = Gift.builder().images(dto.getImages()).transportTypeCode(dto.getTransportTypeCode())
                .status(dto.getStatus() != null ? dto.getStatus() : GiftStatusEnum.ENABLED)
                .name(dto.getName()).badgeCodes(dto.getBadgeCodes())
                .categoryCode(dto.getCategoryCode()).tierCodes(dto.getTierCodes())
                .type(dto.getType()).point(dto.getPoint()).price(dto.getPrice())
                .hiddenTags(dto.getHiddenTags()).sfNumber(dto.getSfNumber())
                .startDate(dto.getStartDate()).endDate(dto.getEndDate())
                .expireDate(dto.getExpireDate()).expireHour(dto.getExpireHour())
                .sctNumber(dto.getSctNumber()).tpmNumber(dto.getTpmNumber()).quantity(dto.getQuantity())
                .quantityReward(dto.getQuantityReward())
                .quantityReservation(dto.getQuantityReservation())
                .inventory(dto.getInventory() != null ? dto.getInventory() : 0)
                .quantityLimitForBooking(
                        dto.getQuantityLimitForBooking() != null ? dto.getQuantityLimitForBooking()
                                : 0)
                .purchaseOption(dto.getPurchaseOption())
                .isAllowReservation(dto.getIsAllowReservation()).priority(dto.getPriority())
                .dynamicData(dynamicData).build();

        if (Objects.nonNull(dto.getBrandPoint())) {
            gift.setBrandPoint(MapGiftToBrandPointRequestDto.toEntity(dto.getBrandPoint()));
        }

        return gift;
    }

    public static Gift toEntity(GiftRequestDto dto, MapGiftToBrandPoint oldMapGiftToBrandPoint) {
        Gift gift = toEntity(dto);
        MapGiftToBrandPoint newMapGiftToBrandPoint = gift.getBrandPoint();
        if (
                Objects.nonNull(newMapGiftToBrandPoint) &&
                        Objects.nonNull(oldMapGiftToBrandPoint)
        ) {
            newMapGiftToBrandPoint.setId(oldMapGiftToBrandPoint.getId());
            gift.setBrandPoint(newMapGiftToBrandPoint);
        }

        return gift;
    }
}
