package com.vitadairy.gift.features.gifts.interfaces;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.entities.EventAddCan;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.responses.RequestCouponResponse;
import com.vitadairy.zoo.responses.RequestDetailCouponDataDetailCouponResponse;
import com.vitadairy.zoo.responses.RequestDetailCouponResponse;

public interface IVitaCodeService {
    String getCodeEvoucherVacxinV1(Gift gift);

    String processCodeEvoucherVacxinV1Response(UserGift userGift, String code);
}
