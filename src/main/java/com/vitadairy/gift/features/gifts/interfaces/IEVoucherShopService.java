package com.vitadairy.gift.features.gifts.interfaces;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;

public interface IEVoucherShopService {
    Object getVoucher(User user, Gift gift, String refId, String service, HistoryPoint historyPoint);

    Object processEVoucherResponse(UserGift userGift, Object evoucherResponse, String service);
}
