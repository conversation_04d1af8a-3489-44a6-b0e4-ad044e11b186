package com.vitadairy.gift.features.gifts.dto;

import java.time.OffsetDateTime;

import com.vitadairy.gift.entities.GiftTimeExchangeGift;
import com.vitadairy.gift.enums.GiftTimeExchangeGiftPeriodTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GiftTimeExchangeGiftDto {
    private Long id;
    private GiftTimeExchangeGiftPeriodTypeEnum periodType;
    private OffsetDateTime startDate;
    private OffsetDateTime endDate;
    private Integer limitExchangeGift;

    public static GiftTimeExchangeGiftDto fromEntity(GiftTimeExchangeGift entity) {
        return GiftTimeExchangeGiftDto.builder()
                .id(entity.getId())
                .periodType(entity.getPeriodType())
                .startDate(entity.getStartDate())
                .endDate(entity.getEndDate())
                .limitExchangeGift(entity.getLimitExchangeGift())
                .build();
    }
}
