package com.vitadairy.gift.features.gifts.services;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherService;
import com.vitadairy.gift.features.gifts.interfaces.IGotItService;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("eVoucherServiceImpl")
public class EVoucherServiceImpl implements IEVoucherService {
    @Value("${evoucher.service}")
    private String activeService = Constant.EvoucherService.GOT_IT;

    private final IGotItService gotItService;

    public EVoucherServiceImpl(@Qualifier("eVoucherServiceGotitImpl") IGotItService gotItService) {
        this.gotItService = gotItService;
    }

    @Override
    public Object topup(User user, String phone, Long amount, String refId, HistoryPoint historyPoint) {
        switch (this.activeService) {
            case Constant.EvoucherService.GOT_IT -> {
                return this.gotItService.topupV1Dot4(
                        user,
                        phone,
                        refId,
                        amount,
                        historyPoint
                );
            }
            default -> {
                return null;
            }
        }
    }

    @Override
    public Object checkTopupTransaction(User user, String refId, HistoryPoint historyPoint) {
        switch (this.activeService) {
            case Constant.EvoucherService.GOT_IT -> {
                return this.gotItService.checkTopupTransaction(
                        user,
                        refId,
                        historyPoint
                );
            }
            default -> {
                return null;
            }
        }
    }

    @Override
    public Object processTopupResponse(UserGift userGift, Object topupResponse) {
        switch (this.activeService) {
            case Constant.EvoucherService.GOT_IT -> {
                return this.gotItService.processTopupResponse(
                        userGift,
                        (TopupResponseV1Dot4) topupResponse
                );
            }
            default -> {
                return null;
            }
        }
    }

    @Override
    public String getActiveService() {
        return this.activeService;
    }
}
