package com.vitadairy.gift.features.gifts.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftGiftingRequestDto {
    //@NotNull
    private Integer oldGiftId;
    @NotNull
    private Integer crmTransactionTypeId;
    @NotNull
    private Integer quantity;
    @NotNull
    private float point;
    @NotNull
    private String brand;
    @NotNull
    private Boolean decreaseGift;
    private Long userId;
    private String transactionDate;
    private Integer giftId;
    private Integer eventId;
}
