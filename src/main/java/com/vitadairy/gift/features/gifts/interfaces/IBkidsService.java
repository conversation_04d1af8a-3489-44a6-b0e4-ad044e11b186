package com.vitadairy.gift.features.gifts.interfaces;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.GotItTopupStatus;
import com.vitadairy.zoo.requests.RequestCouponRequest;
import com.vitadairy.zoo.responses.*;

public interface IBkidsService {
    RequestCouponResponse requestCoupon(User user, Gift gift);

    RequestDetailCouponResponse requestDetailCoupon(User user, String id);

    RequestDetailCouponDataDetailCouponResponse processCouponResponse(UserGift userGift, RequestDetailCouponDataDetailCouponResponse evoucherResponse);
}
