package com.vitadairy.gift.features.gifts.interfaces;

import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;

public interface IEVoucherService {
    Object topup(User user, String phone, Long amount, String refId, HistoryPoint historyPoint);

    Object checkTopupTransaction(User user, String refId, HistoryPoint historyPoint);

    Object processTopupResponse(UserGift userGift, Object topupResponse);

    String getActiveService();
}
