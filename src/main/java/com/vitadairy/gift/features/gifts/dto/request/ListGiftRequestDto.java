package com.vitadairy.gift.features.gifts.dto.request;

import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.stream.Collectors;

import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.shared.BaseDto;
import com.vitadairy.gift.utils.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import lombok.Setter;
import org.springframework.data.domain.Sort;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ListGiftRequestDto extends BaseDto.BaseFilter{
    private String id;
    private String ids;
    private GiftStatusEnum status;
    private GiftTypeEnum type;
    private Boolean isAllowReservation;
    private String categoryCode;
    private String name;
    private Double maxPoint;
    private String orderBy = "id";
    private Sort.Direction sortBy = Sort.Direction.DESC;
    private String tierCodes; // List tier code split by ','
    private Boolean isCustomer;

    public void setIds(Integer[] ids) {
        this.ids = Arrays.stream(ids)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public Integer[] getIds() {
        return StringUtils.toNumbers(this.ids);
    }

    public void setSort(String orderBy, Sort.Direction sortBy) {
        this.orderBy = orderBy;
        this.sortBy = sortBy;
    }

    @Override
    public String toString() {
        return "ListGiftRequestDto{" +
                "id='" + id + '\'' +
                ", ids='" + ids + '\'' +
                ", status=" + status +
                ", type=" + type +
                ", isAllowReservation=" + isAllowReservation +
                ", categoryCode='" + categoryCode + '\'' +
                ", name='" + name + '\'' +
                ", maxPoint=" + maxPoint +
                ", orderBy='" + orderBy + '\'' +
                ", sortBy=" + sortBy +
                ", tierCodes='" + tierCodes + '\'' +
                ", isCustomer=" + isCustomer +
                ", page=" + getPage() +
                ", size=" + getSize() +
                '}';
    }
}