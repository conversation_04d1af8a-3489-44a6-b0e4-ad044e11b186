package com.vitadairy.gift.features.gifts.controllers;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.GiftPageResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftResponse;
import com.vitadairy.gift.features.gifts.services.GiftCacheService;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.main.configs.ResponseFactory;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("v4/gs/no-auth-token/gifts")
@AllArgsConstructor
public class PublicGiftController {
    private final GiftService giftService;
    private final GiftCacheService giftCacheService;
    private final ResponseFactory responseFactory;

    @GetMapping()
    public ResponseEntity<GiftPageResponse> getNoAuthenGifts(@ModelAttribute ListGiftRequestDto dto) {
        dto.setSort("priority", Sort.Direction.ASC);
        dto.setStatus(GiftStatusEnum.ENABLED);
        dto.setIsCustomer(false);
        var data = giftCacheService.pageableSpecification(dto);
        List<GiftDto> giftDtos = data.getGifts();
        giftDtos = giftService.setupGiftsCategoryName(giftDtos);
        GiftPageResponse response = new GiftPageResponse(giftDtos, data.getTotalPages(), data.getTotalPages(),
                dto.getPage(), dto.getSize(), HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

    @GetMapping("{id}")
    public ResponseEntity<GiftResponse> getDetailsById(@PathVariable Integer id) {
        Gift gift = this.giftService.details(id);
        GiftDto giftDto = this.giftService.setupGiftCanRedeem(gift);
        GiftResponse giftResponse = new GiftResponse(giftDto, HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }
}
