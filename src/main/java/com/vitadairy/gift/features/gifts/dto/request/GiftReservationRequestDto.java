package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftReservation;

import jakarta.persistence.Transient;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Data
public class GiftReservationRequestDto {
    @Min(0)
    private Integer reservationPoint;

    @Min(0)
    private Integer reservationPercent;

    @Min(0)
    private Integer discountReservationPoint;

    @Min(0)
    private Integer discountReservationPercent;

    @Min(0)
    private Integer minCoinsForReservation;

    @Min(0)
    private Integer reservationExpiredDays;

    @Min(0)
    private Integer maximumReservationQuantity;

    @Min(0)
    private Integer limitReservationTime;

    @Min(0)
    private Float coinToMinusPoint;

    @Min(0)
    private Integer coinToMinusPercent;

    @Transient
    private Gift gift;

    public static GiftReservation toEntity(GiftReservationRequestDto dto) {
        return GiftReservation.builder().reservationPoint(dto.getReservationPoint())
                .reservationPercent(dto.getReservationPercent())
                .limitReservationTime(dto.getLimitReservationTime())
                .discountReservationPoint(dto.getDiscountReservationPoint())
                .discountReservationPercent(dto.getDiscountReservationPercent())
                .reservationExpiredDays(dto.getReservationExpiredDays())
                .minCoinsForReservation(dto.getMinCoinsForReservation())
                .maximumReservationQuantity(dto.getMaximumReservationQuantity())
                .coinToMinusPoint(dto.getCoinToMinusPoint())
                .coinToMinusPercent(dto.getCoinToMinusPercent()).build();
    }
}
