package com.vitadairy.gift.features.gifts.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftRemoveUserGiftGiftingRequestDto {
    @NotNull
    private Long userGiftId;
    @NotNull
    private Integer quantity;
    @NotNull
    private Boolean increaseGift;
//    @NotNull
    private Long vitaCodeId;
    @NotNull
    private Integer giftId;
}
