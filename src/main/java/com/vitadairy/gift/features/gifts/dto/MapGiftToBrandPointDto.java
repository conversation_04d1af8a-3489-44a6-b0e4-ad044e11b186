package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.MapGiftToBrandPoint;
import lombok.*;

import java.util.Objects;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MapGiftToBrandPointDto {
    private Integer brandId;

    private Integer brandPoint;

    public static MapGiftToBrandPointDto fromEntity(MapGiftToBrandPoint entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        return MapGiftToBrandPointDto
                .builder()
                .brandId(entity.getBrandId())
                .brandPoint(entity.getBrandPoint())
                .build();
    }
}
