package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.validator.ValidUserGiftStatus;

import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class UpdateUserGiftRequestDto {
    @Nullable
    private Long id;

    @Nullable
    private Long userId;

    @ValidUserGiftStatus
    private USER_GIFT_STATUS status;
}