package com.vitadairy.gift.features.gifts.services;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.interfaces.IBkidsService;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.BkidsTransactionHistory;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.BkidsTransactionHistoryStatus;
import com.vitadairy.zoo.enums.BkidsTransactionHistoryType;
import com.vitadairy.zoo.repositories.BkidsTransactionHistoryRepository;
import com.vitadairy.zoo.requests.RequestCouponRequest;
import com.vitadairy.zoo.responses.*;
import com.vitadairy.zoo.util.Checksum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Base64;
import java.util.Objects;

@Service("eVoucherServiceBkidsImpl")
@Slf4j
public class BkidsServiceImpl implements IBkidsService {
    @Value("${bkids.base-url}")
    private String baseUrl;

    @Value("${bkids.key-id}")
    private String keyId;

    @Value("${bkids.key-secret}")
    private String keySecret;

    @Value("${bkids.api-path.request-coupon}")
    private String requestCouponApiPath;

    @Value("${bkids.api-path.detail-coupon}")
    private String detailCouponApiPath;

    @Value("${bkids.auth.header}")
    private String authHeader;

    private final ObjectMapper objectMapper;

    private final BkidsTransactionHistoryRepository bkidsTransactionHistoryRepo;

    private final RestTemplate restTemplate;

    private final UserGiftRepository userGiftRepo;

    public BkidsServiceImpl(
            ObjectMapper objectMapper,
            BkidsTransactionHistoryRepository bkidsTransactionHistoryRepo,
            RestTemplate restTemplate, UserGiftRepository userGiftRepo) {
        this.objectMapper = objectMapper;
        this.bkidsTransactionHistoryRepo = bkidsTransactionHistoryRepo;
        this.restTemplate = restTemplate;
        this.userGiftRepo = userGiftRepo;
    }

    @Override
    public RequestCouponResponse requestCoupon(User user, Gift gift) {
        if (Objects.isNull(gift)) {
            throw new ApplicationException("Gift empty", HttpStatus.BAD_REQUEST);
        }
        BkidsTransactionHistoryStatus status;
        RequestCouponResponse response = null;
        String errorMsg = "";
        RequestCouponRequest request = null;

        try {
            GiftDynamicData giftDynamicData = gift.getDynamicData();
            String bkidsRequestCouponObjectType = this.convertGiftCourseTypeToRequestCouponObjectType(giftDynamicData.getBkidsCourseType());
            request = new RequestCouponRequest(giftDynamicData.getBkidsCourseName(), bkidsRequestCouponObjectType, giftDynamicData.getBkidsCourseId());
            if (request.isInvalid()) {
                log.error("[BkidsServiceImpl][requestCoupon] Request data invalid: {}", request);
                throw new ApplicationException("Request invalid", HttpStatus.BAD_REQUEST);
            }

            String couponRequestJsonData = this.objectMapper.writeValueAsString(request);
            String signnature = this.signatureData(couponRequestJsonData);
            String apiUrl = this.requestCouponApiPath + "?signature=" + signnature;

            response = this.post(apiUrl, request, RequestCouponResponse.class);
            status = BkidsTransactionHistoryStatus.SUCCESS;
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
            log.error("[BkidsServiceImpl][requestCoupon] error : ", e);
            status = BkidsTransactionHistoryStatus.FAILED;
        }

        this.loggingCallApi(
                user,
                request,
                response,
                errorMsg,
                BkidsTransactionHistoryType.REQUEST_KEY,
                status
        );

        return response;
    }

    @Override
    public RequestDetailCouponResponse requestDetailCoupon(User user, String id) {
        BkidsTransactionHistoryStatus status;
        RequestDetailCouponResponse response = null;
        String errorMsg = "";

        try {
            if (Objects.isNull(id)) {
                throw new ApplicationException("Coupon id empty", HttpStatus.BAD_REQUEST);
            }
            String apiUrl = this.detailCouponApiPath + "/" + id;

            response = this.get(apiUrl, RequestDetailCouponResponse.class);
            status = BkidsTransactionHistoryStatus.SUCCESS;
        } catch (Exception e) {
            if (e instanceof  ApplicationException) {
                errorMsg = ((ApplicationException) e).getMsg();
            } else {
                errorMsg = e.getMessage();
            }
            log.error("[BkidsServiceImpl][requestDetailCoupon] error : ", e);
            status = BkidsTransactionHistoryStatus.FAILED;
        }

        this.loggingCallApi(
                user,
                id,
                response,
                errorMsg,
                BkidsTransactionHistoryType.REQUEST_DETAIL_KEY,
                status
        );

        return response;
    }

    @Override
    public RequestDetailCouponDataDetailCouponResponse processCouponResponse(UserGift userGift, RequestDetailCouponDataDetailCouponResponse couponResponse) {
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        userGiftDynamicData.setExpiryDate(String.valueOf(couponResponse.getExpireDate()));
        userGift.setUpdatedAt(Instant.now());
        userGiftDynamicData.setUsedDate(couponResponse.getCreatedAt());
        userGiftDynamicData.setVoucherRefId(couponResponse.get_id());
        userGiftDynamicData.setVoucherCode(couponResponse.getKey());
        userGift.setStatus(UserGift.USER_GIFT_STATUS.USED);
        userGift.setDynamicData(userGiftDynamicData);
        this.userGiftRepo.save(userGift);

        return couponResponse;
    }

    private HttpEntity configEntity() {
        return new HttpEntity<>(configHeaders());
    }

    private <T> HttpEntity configEntity(T body) {
        return new HttpEntity<>(body, configHeaders());
    }

    private HttpHeaders configHeaders() {
        HttpHeaders headers = new HttpHeaders();
        String authToken = this.base64Encode("apikey:" + this.keyId);

        headers.setContentType(MediaType.valueOf("application/json"));
        headers.set(this.authHeader, authToken);

        return headers;
    }

    private String generateFullApiUrl(String api) {
        return this.baseUrl.concat(api);
    }

    private <T> T get(String api, Class<T> responseType) {
        ResponseEntity<Object> response;
        try {
            response = restTemplate.exchange(this.generateFullApiUrl(api), HttpMethod.GET, this.configEntity(), Object.class);
        } catch (Exception e) {
            log.error("[BkidsServiceImpl][errorHandler] bkids error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        errorHandler(response);

        //noinspection unchecked
        try {
            T ret = objectMapper.convertValue(response.getBody(), responseType);
            return ret;
        } catch (Exception ignored) {}

        return null;
    }

    private <T> T post(String api, Class<T> responseType) {
        ResponseEntity<Object> response;
        try {
            response = this.restTemplate.exchange(this.generateFullApiUrl(api), HttpMethod.POST, this.configEntity(), Object.class);
        } catch (Exception e) {
            log.error("[BkidsServiceImpl][errorHandler] bkids error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        try {
            T res = objectMapper.convertValue(response.getBody(), responseType);
            return res;
        } catch (Exception ignored) {}

        return null;
    }

    private <T, U> T post(String api, U body, Class<T> responseType) {
        ResponseEntity<Object> response;
        try {
            response = this.restTemplate.exchange(this.generateFullApiUrl(api), HttpMethod.POST, this.configEntity(body), Object.class);
        } catch (Exception e) {
            log.error("[BkidsServiceImpl][errorHandler] bkids error : ", e);
            throw new ApplicationException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        try {
            T res = objectMapper.convertValue(response.getBody(), responseType);
            return res;
        } catch (Exception ignored) {}

        return null;
    }

    private void errorHandler(ResponseEntity<Object> response) {
        HttpStatus status = (HttpStatus) response.getStatusCode();
        if (!HttpStatus.OK.equals(status)) {
            log.error("[BkidsServiceImpl][errorHandler] Http Status : {}", status);
            throw new ApplicationException("Bkids fail", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        ErrorResponse error = null;
        try {
            error = objectMapper.convertValue(response.getBody(), new TypeReference<>() {
            });
        } catch (Exception ignored) {
        }
        if (Objects.nonNull(error) && !StringUtils.isEmpty(error.getCode())) {
            log.error("[BkidsServiceImpl][errorHandler] {}" ,error.getMsg());
            throw new ApplicationException(error.getMsg(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String signatureData(String jsonData) {
        return Checksum.signSHA256(jsonData, this.keySecret);
    }

    private String base64Encode(String data) {
        return Base64.getEncoder().encodeToString(data.getBytes());
    }

    private void loggingCallApi(
            User user,
            String refId,
            Object response,
            String errorMsg,
            BkidsTransactionHistoryType type,
            BkidsTransactionHistoryStatus status
    ) {
        this.loggingCallApi(
                user,
                refId,
                null,
                response,
                errorMsg,
                type,
                status
        );
    }

    private void loggingCallApi(
            User user,
            Object request,
            Object response,
            String errorMsg,
            BkidsTransactionHistoryType type,
            BkidsTransactionHistoryStatus status
    ) {
        this.loggingCallApi(
                user,
                null,
                request,
                response,
                errorMsg,
                type,
                status
        );
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void loggingCallApi(
            User user,
            String refId,
            Object request,
            Object response,
            String errorMsg,
            BkidsTransactionHistoryType type,
            BkidsTransactionHistoryStatus status
    ) {
        try {
            String requestStr = this.objectMapper.writeValueAsString(request);
            String responseStr = this.objectMapper.writeValueAsString(response);

            BkidsTransactionHistory bkidsTransactionHistory = new BkidsTransactionHistory(
                    user,
                    type
            );
            if (Objects.nonNull(refId)) {
                bkidsTransactionHistory.setRequest(refId);
            } else {
                bkidsTransactionHistory.setRequest(requestStr);
            }
            bkidsTransactionHistory.setStatus(status);
            if (Objects.nonNull(errorMsg) && !errorMsg.isEmpty()) {
                bkidsTransactionHistory.setResponse(errorMsg);
            } else {
                bkidsTransactionHistory.setResponse(responseStr);
            }

            this.bkidsTransactionHistoryRepo.save(bkidsTransactionHistory);
        } catch (Exception e) {
            log.error("[BkidsServiceImpl][errorHandler] bkids error : ", e);
        }
    }

    private String convertGiftCourseTypeToRequestCouponObjectType(String giftCourseType) {
        switch (giftCourseType) {
            case Constant.GiftBkidsCourseType.COURSE:
                return Constant.BkidsRequestCouponObjectType.BKIDS_COURSE;
            case Constant.GiftBkidsCourseType.TRAINING_PATH:
                return Constant.BkidsRequestCouponObjectType.TRAINING_PATH;
            default:
                return "";
        }
    }
}
