package com.vitadairy.gift.features.gifts.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class ListExchangeGiftResponse {
    private List<ExchangeGiftResponse> data;

    public ListExchangeGiftResponse(List<ExchangeGiftResponse> data) {
        this.data = data;
    }

    public ListExchangeGiftResponse(){}
}
