package com.vitadairy.gift.features.gifts.services;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.interfaces.IEVoucherService;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.HistoryPoint;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.repositories.HistoryPointRepository;
import com.vitadairy.zoo.repositories.UserRepository;
import com.vitadairy.zoo.responses.TopupResponseV1Dot4;
import com.vitadairy.zoo.services.DDXService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Service("topupCheckTransactionSchedulerService")
@Slf4j
public class TopupCheckTransactionSchedulerService implements ScheduledJobRunner {
    private final UserGiftRepository userGiftRepository;

    private final UserRepository userRepository;

    private final HistoryPointRepository historyPointRepository;

    private final IEVoucherService eVoucherService;

    private final GiftExchangeService giftExchangeService;

    private Integer MAX_TIME_RUN_TOPUP_CHECK = 6;

    //@Value("${got-it.delay-time-check-status-transaction:12}")
    private int delayTimeCheckStatusTransaction = 12;

    private final DDXService ddxService;

    public TopupCheckTransactionSchedulerService(
            UserGiftRepository userGiftRepository,
            UserRepository userRepository,
            HistoryPointRepository historyPointRepository,
            @Qualifier("eVoucherServiceImpl") IEVoucherService eVoucherService,
            GiftExchangeService giftExchangeService,
            @Qualifier("zooDDXService") DDXService ddxService) {
        this.userGiftRepository = userGiftRepository;
        this.userRepository = userRepository;
        this.historyPointRepository = historyPointRepository;
        this.eVoucherService = eVoucherService;
        this.giftExchangeService = giftExchangeService;
        this.ddxService = ddxService;
    }

    @Override
    public void run() {
        log.info("GotitCheckTransactionSchedulerService begin");


        List<UserGift> userGiftNeedCheckTransaction = this.userGiftRepository.getListGotitNeedCheckTransaction(10, 0);
        //log.info("User gifts need process: {}", userGiftNeedCheckTransaction);
        for (UserGift userGift : userGiftNeedCheckTransaction) {
            String markedStr = String.format(
                    "User gift %d voucher ref id %s time check %d",
                    userGift.getId(),
                    userGift.getDynamicData().getVoucherRefId(),
                    userGift.getDynamicData().getTopupTransactionStatusNumberCheck()
            );
            log.info("{} begin", markedStr);

            boolean stopProcessUserGiftInFlow = false;
            // TODO: maybe using relation to load user in query
            // TODO: increase performance instead of load user by user id
            Long userId = userGift.getUserId();
            User user = this.userRepository.findById(userId).orElse(null);
            HistoryPoint historyPoint = this.historyPointRepository.findFirstByGiftIdOrderByTransactionDateDesc(userGift.getId()).orElse(null);
            
            TopupResponseV1Dot4 response = null;
            try {
                if (Objects.isNull(user)) {
                    // Stop flow check in next time
                    throw new ApplicationException("User null", HttpStatus.BAD_REQUEST);
                }
                if (Objects.isNull(historyPoint)) {
                    throw new ApplicationException("History point null", HttpStatus.BAD_REQUEST);
                }
                // Validate user gift
                this.validateUserGiftNeedCheckTopupTransaction(
                        userGift
                );
                Object responseCheckTopup = this.checkTopupTransaction(userGift, user, historyPoint);
                if (Objects.isNull(responseCheckTopup)) {
                    // Keep flow check in next time
                    throw new Exception("Response check topup null");
                }
                String activeService = this.eVoucherService.getActiveService();
                switch (activeService) {
                    case Constant.EvoucherService.GOT_IT -> {
                        response = (TopupResponseV1Dot4) responseCheckTopup;
                        response = (TopupResponseV1Dot4) this.eVoucherService.processTopupResponse(
                                userGift,
                                response
                        );
                        if (Objects.isNull(response)) {
                            // Stop flow check in next time
                            throw new ApplicationException("Gotit topup status transaction invalid", HttpStatus.BAD_REQUEST);
                        }
                    }
                    default -> {
                        // Keep flow check in next time
                        throw new Exception("Active topup service null");
                    }
                }
                log.info("{} status: ok", markedStr);
            } catch (Exception e) {
                String errorMsg = "";
                if (e instanceof ApplicationException) {
                    stopProcessUserGiftInFlow = true;
                    errorMsg = ((ApplicationException) e).getMsg();
                } else {
                    errorMsg = e.getMessage();
                }
                log.error("{} check topup transaction error: {}", markedStr, errorMsg, e);
            }

            try {
                this.updateUserGift(
                        userGift,
                        user,
                        historyPoint,
                        stopProcessUserGiftInFlow,
                        response
                );
                log.info("{} updated", markedStr);
            } catch (Exception e) {
                log.error("{} update error: {}", markedStr, e.getMessage(), e);
            }

            log.info("{} end", markedStr);
        }

        log.info("GotitCheckTransactionSchedulerService end");
    }

    public Object checkTopupTransaction(UserGift userGift, User user, HistoryPoint historyPoint) {
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        String voucherRefId = userGiftDynamicData.getVoucherRefId();

        return this.eVoucherService.checkTopupTransaction(
                user,
                voucherRefId,
                historyPoint
        );
    }

    private void validateUserGiftNeedCheckTopupTransaction(
            UserGift userGift
    ) {
        // Dynamic data null
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        if (Objects.isNull(userGiftDynamicData)) {
            // Stop flow check in next time
            throw new ApplicationException("Dynamic data null", HttpStatus.BAD_REQUEST);
        }
        // Voucher ref id null
        String voucherRefId = userGiftDynamicData.getVoucherRefId();
        if (Objects.isNull(voucherRefId) || voucherRefId.isEmpty()) {
            // Stop flow check in next time
            throw new ApplicationException("Voucher ref id null", HttpStatus.BAD_REQUEST);
        }
        // Voucher ref id already used by other user gift
        List<UserGift> userGifts = this.userGiftRepository.getListByVoucherRefIdAndIdNot(
                voucherRefId,
                userGift.getId(),
                1,
                0
        );
        if (Objects.nonNull(userGifts) && !userGifts.isEmpty()) {
            // Stop flow check in next time
            throw new ApplicationException("Voucher ref id already used", HttpStatus.BAD_REQUEST);
        }

        // OK
    }

    @Transactional("giftTransactionManager")
    private void updateUserGift(UserGift userGift, User user, HistoryPoint historyPoint, boolean stopProcessUserGiftInFlow, TopupResponseV1Dot4 response) {
        // HistoryPoint historyPoint = this.historyPointRepository.findFirstByGiftIdOrderByTransactionDateDesc(userGift.getId()).orElse(null);
        if (
                Objects.isNull(historyPoint) ||
                        Objects.isNull(user)
        ) {
            throw new ApplicationException("History point or user null", HttpStatus.BAD_REQUEST);
        }
        this.giftExchangeService.syncUpdateEventPointHistoryToWh(
                userGift,
                historyPoint
        );
        if (
                !stopProcessUserGiftInFlow &&
                        !userGift.getStatus().equals(UserGift.USER_GIFT_STATUS.TOPUP_IS_PROCESSING)
        ) {
            this.giftExchangeService.sendWhUsingGiftCrm(
                    userGift,
                    user,
                    userGift.getGift(),
                    historyPoint
            );
        }
        UserGiftDynamicData userGiftDynamicData = userGift.getDynamicData();
        Integer numberCheck = userGiftDynamicData.getTopupTransactionStatusNumberCheck();
        if (Objects.isNull(numberCheck)) {
            numberCheck = 0;
        }
        numberCheck += 1;
        userGiftDynamicData.setTopupTransactionStatusNumberCheck(
                numberCheck
        );
        userGift.setDynamicData(userGiftDynamicData);
        if (!stopProcessUserGiftInFlow && 
            (numberCheck < this.MAX_TIME_RUN_TOPUP_CHECK || (response != null && response.getStatus() == 10))) {
            Instant now = Instant.now();
            Instant gotitTransactionLogTimeCheck = now.plus(Duration.ofHours(this.delayTimeCheckStatusTransaction));
            userGiftDynamicData.setTopupTransactionStatusTimeCheck(gotitTransactionLogTimeCheck.toString());
            userGift.setDynamicData(userGiftDynamicData);
            this.userGiftRepository.save(userGift);
        } else {
            Gift gift = userGift.getGift();
            Boolean validateGiftBelongToEvent = this.giftExchangeService.checkGiftBelongToEvent(gift);
            if (
                    gift.getType() != GiftTypeEnum.EV_VITA_CODE &&
                            validateGiftBelongToEvent.equals(Boolean.TRUE)
            ) {
                this.giftExchangeService.handleUserGiftNeedReuse(
                        user,
                        userGift
                );
            } else {
                this.giftExchangeService.handleReturnPoint(
                        user,
                        userGift,
                        historyPoint,
                        userGiftDynamicData.getVoucherRefId()
                );
            }
        }
    }
}
