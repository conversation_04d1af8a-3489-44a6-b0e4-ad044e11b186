package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.shared.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftDynamicFilterRequestDto extends BaseDto.BaseFilter {
    private String code;
    private String valueCode;
    private String firstLevelCode;
    private String firstLevelValueCode;
    private String secondLevelCode;
    private String secondLevelValueCode;
    private Double minPoint;
    private Double maxPoint;
    private String keyword;
    private String tierCode;

    public static GiftDynamicFilterRequestDto removeEmptyString(GiftDynamicFilterRequestDto input){
        if (StringUtils.isEmpty(input.getCode())) {
            input.setCode(null);
        }
        if (StringUtils.isEmpty(input.getValueCode())) {
            input.setValueCode(null);
        }
        if (StringUtils.isEmpty(input.getFirstLevelCode())) {
            input.setFirstLevelCode(null);
        }
        if (StringUtils.isEmpty(input.getFirstLevelValueCode())) {
            input.setFirstLevelValueCode(null);
        }
        if (StringUtils.isEmpty(input.getSecondLevelCode())) {
            input.setSecondLevelCode(null);
        }
        if (StringUtils.isEmpty(input.getSecondLevelValueCode())) {
            input.setSecondLevelValueCode(null);
        }
        if(StringUtils.isEmpty(input.getKeyword())){
            input.setKeyword(null);
        }
        return input;
    }
}