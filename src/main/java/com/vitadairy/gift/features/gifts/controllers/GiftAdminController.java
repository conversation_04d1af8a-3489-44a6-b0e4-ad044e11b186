package com.vitadairy.gift.features.gifts.controllers;

import com.vitadairy.gift.common.ExportTypeDefs;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.features.constants.Constants;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftDuplicateRequest;
import com.vitadairy.gift.features.gifts.dto.request.GiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.dto.response.GiftPageResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftResponse;
import com.vitadairy.gift.features.gifts.dto.response.GiftsResponse;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.gift.features.userGift.dto.request.ExportGiftRequest;
import com.vitadairy.gift.interfaces.GiftEnableLoggingAdminActionInterface;
import com.vitadairy.gift.services.ExportService;
import com.vitadairy.main.configs.ResponseFactory;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
@GiftEnableLoggingAdminActionInterface
@RequestMapping("v4/gs/admin/gifts")
@AllArgsConstructor
public class GiftAdminController {
    private final GiftService giftService;
    private final ResponseFactory responseFactory;
    private ExportService exportService;

//    @PreAuthorize("hasAuthority('ADMIN')")

    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
    @GetMapping("{id}")
    public ResponseEntity<GiftResponse> getDetailsById(@PathVariable Integer id) {
        Gift gift = this.giftService.details(id);
        GiftDto giftDto = GiftDto.fromEntity(gift);
        GiftResponse giftResponse = new GiftResponse(giftDto, HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'read'))")
    @GetMapping()
    public ResponseEntity<GiftPageResponse> getGifts(
            @ModelAttribute ListGiftRequestDto dto) {
        dto.setSort("id", Sort.Direction.DESC);
        dto.setIsCustomer(false);
        Page<Gift> data = giftService.pageableSpecification(dto);
        GiftPageResponse response = new GiftPageResponse(
                data.getContent().stream().map(GiftDto::fromEntity).toList(),
                (long) data.getTotalElements(), (long) data.getTotalPages(), dto.getPage(), 0, HttpStatus.OK, "OK");

        return responseFactory.successDto(response);
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'create'))")
    @PostMapping()
    public ResponseEntity<GiftResponse> postMethodName(@Valid @RequestBody GiftRequestDto body) {
        try {
            // Issue-3718: đối với quà có type = GIFT thì mặc định expiry time là 168 giờ, admin không đc sửa
            if (body.getType() == GiftTypeEnum.GIFT) {
                body.setExpireHour(Constants.GIFT_EXPIRE_HOUR);
            }
            Gift gift = this.giftService.createGift(body);
            GiftResponse giftResponse = new GiftResponse(GiftDto.fromEntity(gift), HttpStatus.OK, "OK");
            return responseFactory.successDto(giftResponse);
        } catch (Exception ex) {
            log.error("[GiftAdminController][errorHandler] create gift error : ", ex);
            return responseFactory.errorDto(ex.getMessage());
        }
    }

//    @PreAuthorize("hasAuthority('ADMIN')")
    @PreAuthorize("@adminAuthorizeAclGuard.canAccess(authentication, 'quan-ly-qua', T(java.util.List).of('full-access', 'update'))")
    @PatchMapping("{id}")
    public ResponseEntity<GiftResponse> updateGift(@PathVariable("id") Integer id,
                                                   @Valid @RequestBody GiftRequestDto body) {
        // Issue-3718: đối với quà có type = GIFT thì mặc định expiry time là 168 giờ, admin không đc sửa
        if (body.getType() == GiftTypeEnum.GIFT) {
            body.setExpireHour(Constants.GIFT_EXPIRE_HOUR);
        }
        Gift gift = this.giftService.update(id, body);
        GiftResponse giftResponse = new GiftResponse(GiftDto.fromEntity(gift), HttpStatus.OK, "OK");
        return responseFactory.successDto(giftResponse);
    }

    @PostMapping("/export")
    public ResponseEntity<?> exportUserGift(@Valid @RequestBody ExportGiftRequest request) {
        try {
            request.doValidate();
            return exportService.export(ExportTypeDefs.GIFTS, request.toFetchRequest());
        } catch (Exception ex) {
            return responseFactory.errorDto(ex.getMessage());
        }
    }

    //getGiftByGiftIds
    @PreAuthorize("hasAuthority('ADMIN')")
    @GetMapping("/multiple")
    public ResponseEntity<GiftsResponse> getGiftByGiftIds(@RequestParam List<Integer> giftIds) {
        List<GiftDto> giftDtos = giftService.getGiftByGiftIds(giftIds);
        return responseFactory.successDto(new GiftsResponse(giftDtos, HttpStatus.OK, "OK"));
    }

    @PreAuthorize("hasAuthority('ADMIN')")
    @PostMapping("/duplicate")
    public ResponseEntity<Map<Integer, Gift>> duplicateGifts(@RequestBody List<GiftDuplicateRequest> giftRequests) {

        Map<Integer, String> giftSuffixMap = giftRequests.stream()
                .collect(Collectors.toMap(GiftDuplicateRequest::getGiftId, req -> req.getSuffix() != null ? req.getSuffix() : ""));

        List<Gift> duplicatedGifts = giftService.duplicateGifts(giftSuffixMap);

        Map<Integer, Gift> response = new HashMap<>();
        int index = 0;
        for (Gift gift : duplicatedGifts) {
            response.put(giftRequests.get(index).getGiftId(), gift);
            index++;
        }
        return ResponseEntity.ok(response);
    }
}
