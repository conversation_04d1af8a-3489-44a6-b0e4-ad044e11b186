package com.vitadairy.gift.features.gifts.dto.response;

import java.util.List;

import org.springframework.http.HttpStatusCode;

import com.vitadairy.gift.features.gifts.dto.GiftDto;
import com.vitadairy.main.response.PageableResponse;

public class GiftPageResponse extends PageableResponse<GiftDto> {
    public GiftPageResponse(List<GiftDto> items, Long total, Long totalPage, Integer page, Integer pageSize,
            HttpStatusCode statusCode, String msg) {
        super(items, total, totalPage, page, pageSize, statusCode, msg);
    }
}
