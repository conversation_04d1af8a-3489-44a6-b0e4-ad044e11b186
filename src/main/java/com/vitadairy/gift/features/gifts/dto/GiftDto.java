package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.main.dto.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

import org.hibernate.Hibernate;

@EqualsAndHashCode(callSuper = false)
@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GiftDto extends BaseDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;

    private List<String> images;

    private String transportTypeCode;

    @Default
    private GiftStatusEnum status = GiftStatusEnum.ENABLED;

    private String name;

    private List<String> badgeCodes;

    private String categoryCode;

    private String categoryName;

    private List<String> tierCodes;

    private GiftTypeEnum type;

    private Integer point;

    private BigInteger price;

    private List<String> hiddenTags;

    private Long sfNumber;

    private Timestamp startDate;

    private Timestamp endDate;

    private Timestamp expireDate;

    private Integer expireHour;

    private String sctNumber;

    private String tpmNumber;

    @Default
    private Long quantity = (long) 0;

    @Default
    private Integer inventory = 0;

    @Default
    private Integer quantityLimitForBooking = 0;

    private String purchaseOption;

    @Default
    private Integer quantityReward = 0;

    @Default
    private Integer quantityReservation = 0;

    @Default
    private boolean canRedeem = true;

    @Default
    private Integer preferential_point = null;

    private Boolean isAllowReservation;

    private Integer priority;

    private GiftDynamicDataDto dynamicData;

    private GiftReservation giftReservation;

    private Boolean isEligible;
    private Boolean isHiddenPreOrderBt;

    private Boolean isDisabledPreOrderBt; // use for after date

    private MapGiftToBrandPointDto brandPoint;

    private Boolean enableTimeExchangeGift;

    private List<GiftTimeExchangeGiftDto> giftTimeExchangeGifts;

    public static GiftDto fromEntity(Gift entity) {
        return GiftDto.builder()
                .id(entity.getId())
                .images(entity.getImages())
                .transportTypeCode(entity.getTransportTypeCode())
                .status(entity.getStatus())
                .name(entity.getName())
                .badgeCodes(entity.getBadgeCodes())
                .categoryCode(entity.getCategoryCode())
                .tierCodes(entity.getTierCodes())
                .type(entity.getType())
                .point(entity.getPoint())
                .price(entity.getPrice())
                .hiddenTags(entity.getHiddenTags())
                .sfNumber(entity.getSfNumber())
                .startDate(entity.getStartDate())
                .endDate(entity.getEndDate())
                .expireDate(entity.getExpireDate())
                .expireHour(entity.getExpireHour())
                .sctNumber(entity.getSctNumber())
                .tpmNumber(entity.getTpmNumber())
                .quantity(entity.getQuantity())
                .inventory(entity.getInventory())
                .quantityLimitForBooking(entity.getQuantityLimitForBooking())
                .purchaseOption(entity.getPurchaseOption())
                .quantityReward(entity.getQuantityReward() != null ? entity.getQuantityReward() : 0)
                .quantityReservation(entity.getQuantityReservation() != null ? entity.getQuantityReservation() : 0)
                .isAllowReservation(entity.getIsAllowReservation())
                .priority(entity.getPriority())
                .dynamicData(GiftDynamicDataDto.fromEntity(entity.getDynamicData()))
                .giftReservation(entity.getGiftReservation())
                .brandPoint(MapGiftToBrandPointDto.fromEntity(entity.getBrandPoint()))
                .enableTimeExchangeGift(entity.getEnableTimeExchangeGift())
                .giftTimeExchangeGifts(entity.getGiftTimeExchangeGifts() != null ? 
                                        entity.getGiftTimeExchangeGifts().stream()
                                        .map(GiftTimeExchangeGiftDto::fromEntity).toList() : null)
                .build();
    }
}
