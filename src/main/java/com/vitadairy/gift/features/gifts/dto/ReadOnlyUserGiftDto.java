package com.vitadairy.gift.features.gifts.dto;

import java.math.BigDecimal;
import java.math.BigInteger;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
public class ReadOnlyUserGiftDto {
    private Integer id;
    private Integer userId;
    private Integer giftId;
    private String status;
    private Integer quantity;
    private String dynamicData;
    private String transactionCode;

    // gift data
    private String giftName;
    private BigDecimal giftPrice;
    private String giftDynamicData;
}
