package com.vitadairy.gift.features.gifts.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhysicalGiftExchangeRequestDto {
    @NotNull
    private Integer giftId;
    @NotNull
    private Integer quantity;

    private Integer districtCode;
    private Integer provinceCode;
    private Integer wardCode;

    private Integer newProvinceCode;
    private Long newWardCode;

    private String recipientName;
    private String recipientPhone;
    private String streetName;

    @JsonIgnore
    private Long userId;
}
