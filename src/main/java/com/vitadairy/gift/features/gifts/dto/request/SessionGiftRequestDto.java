package com.vitadairy.gift.features.gifts.dto.request;

import com.vitadairy.gift.shared.BaseDto;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Getter
@Setter
@Builder
public class SessionGiftRequestDto {
    private List<String> hiddenTag;
    private String tierCode;
    private Double userPoint;

    public void validation(){
        hiddenTag = CollectionUtils.isEmpty(hiddenTag) ? null : hiddenTag;
        tierCode = StringUtils.isEmpty(tierCode) ? null : tierCode;
    }

    @Override
    public String toString() {
        return "SessionGiftRequestDto{" +
                "hiddenTag='" + hiddenTag + '\'' +
                ", tierCode='" + tierCode + '\'' +
                ", userPoint=" + userPoint +
                '}';
    }
}
