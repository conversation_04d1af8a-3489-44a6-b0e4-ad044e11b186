package com.vitadairy.gift.features.gifts.dto.request;

import java.time.OffsetDateTime;

import com.vitadairy.gift.entities.GiftTimeExchangeGift;
import com.vitadairy.gift.enums.GiftTimeExchangeGiftPeriodTypeEnum;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Data
public class GiftTimeExchangeGiftRequestDto {
    private Long id;

    @NotNull(message = "<PERSON><PERSON><PERSON> mốc thời gian không được để trống.")
    private GiftTimeExchangeGiftPeriodTypeEnum periodType;

    @NotNull(message = "<PERSON>ày bắt đầu không được để trống.")
    private OffsetDateTime startDate;

    @NotNull(message = "<PERSON><PERSON><PERSON> kết thúc không được để trống.")
    private OffsetDateTime endDate;

    @NotNull(message = "Số lượng giới hạn không được để trống.")
    private Integer limitExchangeGift;

    public static GiftTimeExchangeGift toEntity(GiftTimeExchangeGiftRequestDto dto) {
        return GiftTimeExchangeGift.builder()
                .id(dto.getId())
                .periodType(dto.getPeriodType())
                .startDate(dto.getStartDate())
                .endDate(dto.getEndDate())
                .limitExchangeGift(dto.getLimitExchangeGift())
                .build();
    }
}
