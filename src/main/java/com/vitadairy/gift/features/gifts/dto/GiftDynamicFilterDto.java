package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.GiftDynamicFilter;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class GiftDynamicFilterDto {
    private String code;
    private String valueCode;
    private String name;
    private String firstLevelCode;
    private String firstLevelValueCode;
    private String secondLevelCode;
    private String secondLevelValueCode;

    public GiftDynamicFilter toEntity() {
        return GiftDynamicFilter.builder()
                .code(this.code)
                .name(this.name)
                .valueCode(this.valueCode)
                .firstLevelCode(this.firstLevelCode)
                .firstLevelValueCode(this.firstLevelValueCode)
                .secondLevelCode(this.secondLevelCode)
                .secondLevelValueCode(this.secondLevelValueCode)
                .build();

    }

    public static GiftDynamicFilterDto fromEntity(GiftDynamicFilter entity) {
        return GiftDynamicFilterDto.builder()
                .code(entity.getCode())
                .valueCode(entity.getValueCode())
                .name(entity.getName())
                .firstLevelCode(entity.getFirstLevelCode())
                .firstLevelValueCode(entity.getFirstLevelValueCode())
                .secondLevelCode(entity.getSecondLevelCode())
                .secondLevelCode(entity.getSecondLevelCode())
                .build();
    }
}
