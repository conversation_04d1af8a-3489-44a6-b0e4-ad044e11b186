package com.vitadairy.gift.features.gifts.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GiftStockRequestDto {
    private long quantity;
    private int inventory;
    private int quantityLimitForBooking;
}
