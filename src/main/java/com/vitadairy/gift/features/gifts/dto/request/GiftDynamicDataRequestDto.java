package com.vitadairy.gift.features.gifts.dto.request;

import java.math.BigDecimal;
import java.util.List;

import com.vitadairy.gift.entities.GiftDynamicData;

import jakarta.validation.constraints.DecimalMin;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GiftDynamicDataRequestDto {
    private String description;

    private String link;

    @DecimalMin("0.0")
    private BigDecimal weight;

    @DecimalMin("0.0")
    private BigDecimal height;

    @DecimalMin("0.0")
    private BigDecimal width;

    @DecimalMin("0.0")
    private BigDecimal length;

    private String rewardAppGiftCode;

    private String smsGiftId;

    private Boolean activeSmsNotification;

    private String sourceGift;

    private String selectGift;

    private Long eProductId;
    private String eProductName;
    private Long priceId;
    private String bkidsCourseType;
    private String bkidsCourseId;
    private String bkidsCourseName;
    private List<String> bkidsCourseClass;
    private Long bkidsCoursePrice;
    private Long giftCategoryId;
    private String code;

    public static GiftDynamicData toEntity(GiftDynamicDataRequestDto dto) {
        return GiftDynamicData.builder()
                .description(dto.getDescription())
                .link(dto.getLink())
                .weight(dto.getWeight())
                .height(dto.getHeight())
                .width(dto.getWidth())
                .length(dto.getLength())
                .rewardAppGiftCode(dto.getRewardAppGiftCode())
                .smsGiftId(dto.getSmsGiftId())
                .activeSmsNotification(dto.getActiveSmsNotification())
                .sourceGift(dto.getSourceGift())
                .selectGift(dto.getSelectGift())
                .eProductId(dto.getEProductId())
                .eProductName(dto.getEProductName())
                .priceId(dto.getPriceId())
                .bkidsCourseClass(CollectionUtils.isNotEmpty(dto.getBkidsCourseClass()) ? String.join("," ,dto.getBkidsCourseClass()) : null)
                .bkidsCourseId(dto.getBkidsCourseId())
                .bkidsCourseName(dto.getBkidsCourseName())
                .bkidsCoursePrice(dto.getBkidsCoursePrice())
                .bkidsCourseType(dto.getBkidsCourseType())
                .giftCategoryId(dto.getGiftCategoryId())
                .code(dto.getCode())
                .build();
    }
}
