package com.vitadairy.gift.features.gifts.dto;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.main.dto.BaseDto;
import lombok.*;

import java.util.List;

@Data
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserGiftPreOrderSendNotifyEnoughtPointDto extends BaseDto {
    private GiftInUserGiftPreOrderSendNotifyEnoughtPointDto gift;

    public static UserGiftPreOrderSendNotifyEnoughtPointDto fromEntity(List<UserGift> userGifts) {
        List<Gift> gifts = userGifts.stream().map(UserGift::getGift).toList();
        return UserGiftPreOrderSendNotifyEnoughtPointDto.builder()
                .gift(GiftInUserGiftPreOrderSendNotifyEnoughtPointDto.fromEntity(gifts))
                .build();
    }
}
