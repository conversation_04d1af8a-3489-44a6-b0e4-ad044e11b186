package com.vitadairy.gift.features.users.services;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.repositories.UserRepository;

@Service("gsUser")
public class UserService {
    private UserRepository _userRepository;

    public UserService(UserRepository userRepository) {
        this._userRepository = userRepository;
    }

    public Optional<User> detail(Long userId) {
        return this._userRepository.findById(userId);
    }
}
