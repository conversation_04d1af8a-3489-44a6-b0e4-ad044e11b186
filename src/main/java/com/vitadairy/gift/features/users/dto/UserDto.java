package com.vitadairy.gift.features.users.dto;

import com.vitadairy.gift.entities.User;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserDto {
    private Long id = null; // user_id
    private Integer[] giftIds;
    private Float coin;

    public static UserDto fromEntity(User entity) {
        return UserDto.builder()
                .id(entity.getId())
                .giftIds(entity.getGiftIds())
                .coin(entity.getCoin()).build();
    }
}
