package com.vitadairy.gift.features.users.dto;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

import com.vitadairy.main.response.EntityResponse;

public class UserResponseDto extends EntityResponse<UserDto> {
    public UserResponseDto(UserDto data, HttpStatus httpStatus, String message) {
        super(data, HttpStatusCode.valueOf(httpStatus.value()), message);
    }
}
