package com.vitadairy.gift.features.users.controllers;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.features.users.dto.UserDto;
import com.vitadairy.gift.features.users.dto.UserResponseDto;
import com.vitadairy.gift.features.users.services.UserService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.iresponse.BaseResponse;

@RestController
@RequestMapping("v4/gs/users")
public class UserController {
    private final ResponseFactory _responseFactory;
    private UserService _userService;

    public UserController(
            @Qualifier("gsUser") UserService userService,
            ResponseFactory responseFactory) {
        this._userService = userService;
        this._responseFactory = responseFactory;
    }

    @PreAuthorize("hasAuthority('USER')")
    @GetMapping()
    public ResponseEntity<BaseResponse> getDetail() {
        var principal = AuthenticationHelper.getCurrentUser();
        Long userId = principal.getUserId();
        Optional<User> user = this._userService.detail(userId);
        if (user.isPresent()) {
            UserResponseDto userResponseDto = new UserResponseDto(UserDto.fromEntity(user.get()), HttpStatus.OK, "OK");
            return this._responseFactory.successDto(userResponseDto);
        }
        User userN = new User();
        userN.setId(userId);
        userN.setGiftIds(new Integer[0]);
        userN.setCoin(0F);
        UserResponseDto userResponseDto = new UserResponseDto(UserDto.fromEntity(userN), HttpStatus.OK, "OK");
        return this._responseFactory.successDto(userResponseDto);
    }
}
