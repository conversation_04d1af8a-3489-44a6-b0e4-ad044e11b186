package com.vitadairy.gift.services.impl;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.GiftDynamicFilter;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.constants.Constants;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.libraries.importexport.dto.CellData;
import com.vitadairy.libraries.importexport.dto.RowData;
import com.vitadairy.libraries.importexport.exception.ParseDataException;
import com.vitadairy.libraries.importexport.service.ParseDataService;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ParseGsGiftService implements ParseDataService<Gift> {
    @Override
    public Gift parseEntity(RowData rowData) throws ParseDataException {
        Gift gift = new Gift();
        GiftDynamicData dynamicData = new GiftDynamicData();
        gift.setDynamicData(dynamicData);
        List<String> images = new ArrayList<>();
        gift.setImages(images);
        GiftReservation giftReservation = new GiftReservation();
        gift.setGiftReservation(giftReservation);
        GiftDynamicFilter dynamicFilter = new GiftDynamicFilter();
        dynamicData.setDynamicFilter(dynamicFilter);
        gift.setCreatedAt(Instant.now());
        giftReservation.setGift(gift);

        for (CellData cellData : rowData.getCells()) {
            switch (cellData.getName().toLowerCase()) {
                case "id":
                    gift.setId(parseInt(cellData));
                    break;
                case "gift_type":
                    gift.setType(GiftTypeEnum.valueOf(cellData.getCellValue() + ""));
                    break;
                case "category_code":
                    gift.setCategoryCode(cellData.getCellValue() + "");
                    break;
                case "reward_name":
                    gift.setName(cellData.getCellValue() + "");
                    break;
                case "active_gift":
                    String value = cellData.getCellValue() + "";
                    if (value.equals("Yes")) {
                        gift.setStatus(GiftStatusEnum.ENABLED);
                    } else {
                        gift.setStatus(GiftStatusEnum.DISABLED);
                    }
                    break;
                case "description":
                    dynamicData.setDescription((cellData.getCellValue() + "").replace("\n", "<br>"));
                    break;
                case "image", "coverimage":
                    List<String> splitImages = (cellData.getCellValue() + "").contains("|") ?
                            Arrays.asList((cellData.getCellValue() + "").split("\\|")) :
                            List.of(cellData.getCellValue() + "");
                    images.addAll(splitImages);
                    break;
                case "huyhieu":
                    gift.setBadgeCodes(List.of((cellData.getCellValue() + "").split("\\|")));
                    break;
                case "source_gift":
                    dynamicData.setSourceGift(cellData.getCellValue() + "");
                    break;
                case "transport_type":
                    gift.setTransportTypeCode(cellData.getCellValue() + "");
                    break;
                case "tier_code":
                    gift.setTierCodes(List.of((cellData.getCellValue() + "").split("\\|")));
                    break;
                case "reward_app_giftcode":
                    dynamicData.setRewardAppGiftCode(cellData.getCellValue() + "");
                    break;
                case "priority":
                    gift.setPriority(parseInt(cellData));
                    break;
                case "display_point":
                    gift.setPoint(parseInt(cellData));
                    break;
                case "money":
                    gift.setPrice(BigInteger.valueOf(parseLong(cellData)));
                    break;
                case "sms_gift_code":
                    dynamicData.setSmsGiftId(cellData.getCellValue() + "");
                    break;
                case "send_sms":
                    dynamicData.setActiveSmsNotification((cellData.getCellValue() + "").equals("Yes"));
                    break;
                case "startdate":
                    gift.setStartDate(parseDate(cellData).orElse(null));
                    break;
                case "enddate":
                    gift.setEndDate(parseDate(cellData).orElse(null));
                    break;
                case "expired_date(date)":
                    gift.setExpireDate(parseDate(cellData).orElse(null));
                    break;
                case "expired_time(h)":
                    gift.setExpireHour(parseInt(cellData));
                    break;
                case "so_sct":
                    gift.setSctNumber(cellData.getCellValue() + "");
                    break;
                case "so_tpm":
                    gift.setTpmNumber(cellData.getCellValue() + "");
                    break;
                case "so_luong_total":
                    gift.setQuantity(parseLong(cellData));
                    break;
                case "so_luong_kho":
                    gift.setInventory(parseInt(cellData));
                    break;
                case "so_luong_gioi_han_doi_qua":
                    gift.setQuantityLimitForBooking(parseInt(cellData));
                    break;
                case "weight(cm)":
                    dynamicData.setWeight(parseBigDecimal(cellData));
                    break;
                case "length(cm)":
                    dynamicData.setLength(parseBigDecimal(cellData));
                    break;
                case "width(cm)":
                    dynamicData.setWidth(parseBigDecimal(cellData));
                    break;
                case "height(cm)":
                    dynamicData.setHeight(parseBigDecimal(cellData));
                    break;
                case "active_pre-order":
                    gift.setIsAllowReservation((cellData.getCellValue() + "").equals("Yes"));
                    break;
                case "sl_toi_da_co_the_dat_truoc":
                    giftReservation.setMaximumReservationQuantity(parseInt(cellData));
                    break;
                case "thoi_han_giu_qua":
                    giftReservation.setReservationExpiredDays(parseInt(cellData));
                    break;
                case "gioi_han_dat_truoc_huy_dat_truoc":
                    giftReservation.setLimitReservationTime(parseInt(cellData));
                    break;
                case "giam_gia_dat_truoc (%)":
                    giftReservation.setDiscountReservationPercent(parseInt(cellData));
                    break;
                case "so_xu_dat_truoc(%)":
                    giftReservation.setReservationPercent(parseInt(cellData));
                    break;
                case "so_xu_bi_tru(%)":
                    giftReservation.setCoinToMinusPercent(parseInt(cellData));
                    break;
                case "hidden_user_tag_ranking":
                    gift.setHiddenTags(List.of((cellData.getCellValue() + "").split("\\|")));
                    break;
                case "bo_loc_chinh":
                    dynamicFilter.setCode(cellData.getCellValue() + "");
                    break;
                case "gia_tri_loc_chinh":
                    dynamicFilter.setValueCode(cellData.getCellValue() + "");
                    break;
                case "bo_loc_phu":
                    dynamicFilter.setFirstLevelCode(cellData.getCellValue() + "");
                    break;
                case "gia_tri_loc_phu":
                    dynamicFilter.setFirstLevelValueCode(cellData.getCellValue() + "");
                    break;
                case "bo_loc_nho":
                    dynamicFilter.setSecondLevelCode(cellData.getCellValue() + "");
                    break;
                case "gia_tri_loc_nho":
                    dynamicFilter.setSecondLevelValueCode(cellData.getCellValue() + "");
                    break;
                default:
                    break;
            }
        }
        if (Objects.isNull(giftReservation.getMaximumReservationQuantity())
                && Objects.isNull(giftReservation.getReservationExpiredDays())
                && Objects.isNull(giftReservation.getLimitReservationTime())
                && Objects.isNull(giftReservation.getDiscountReservationPercent())
                && Objects.isNull(giftReservation.getReservationPercent())
                && Objects.isNull(giftReservation.getCoinToMinusPercent())) {
            gift.setGiftReservation(null);
        }

        // hard code expire hour for gift type GIFT
        if (gift.getType() == GiftTypeEnum.GIFT) {
            gift.setExpireHour(Constants.GIFT_EXPIRE_HOUR);
        }

        return gift;
    }

    Optional<Timestamp> parseDate(CellData cellData) {
        Object value = cellData.getCellValue();
        if (value instanceof Date date) {
            return Optional.of(new Timestamp(date.getTime()));
        }
        return Optional.empty();
    }

    Integer parseInt(CellData cellData) {
        Object value = cellData.getCellValue();
        if (Objects.isNull(value) || StringUtils.isEmpty(value.toString())) {
            return null;
        }
        return Integer.parseInt(value.toString());
    }

    Float parseFloat(CellData cellData) {
        Object value = cellData.getCellValue();
        if (Objects.isNull(value) || StringUtils.isEmpty(value.toString())) {
            return null;
        }
        return Float.parseFloat(value.toString());
    }

    Long parseLong(CellData cellData) {
        Object value = cellData.getCellValue();
        if (Objects.isNull(value) || StringUtils.isEmpty(value.toString())) {
            return null;
        }
        return Long.parseLong(value.toString());
    }

    BigDecimal parseBigDecimal(CellData cellData) {
        Object value = cellData.getCellValue();
        if (Objects.isNull(value) || StringUtils.isEmpty(value.toString())) {
            return null;
        }
        return new BigDecimal(value.toString());
    }
}
