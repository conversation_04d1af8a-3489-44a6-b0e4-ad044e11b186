package com.vitadairy.gift.services.impl;

import com.vitadairy.gift.entities.*;
import com.vitadairy.gift.features.dynamicFilter.dto.DynamicFilterDto;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.gift.repositories.ConstantRepository;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.repositories.GiftReservationRepository;
import com.vitadairy.libraries.importexport.common.BatchResponse;
import com.vitadairy.main.dto.ValidateRowResponse;
import com.vitadairy.main.services.ProcessDataAbstractService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.internal.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProcessDataGsGiftService extends ProcessDataAbstractService<Gift> {

    private final GiftRepository giftRepository;
    private final GiftReservationRepository giftReservationRepository;
    private final ConstantRepository constantRepository;

    private final static String GIFT_LINE_ERROR_MSG_FORMAT = "%s: %s";

    @Override
    protected Gift save(Gift entity) {
        return giftRepository.save(entity);
    }

    @Override
    public List<Gift> saveAll(List<Gift> list) throws Exception {
        return giftRepository.saveAll(list);
    }

    @Override
    protected List<Object[]> runFunction(Gift entity) {
        // not implemented
        return List.of();
    }

    @Override
    public BatchResponse processBatch(List<Gift> list) {
        Pair<List<Gift>, List<ValidateRowResponse>> validateResult = validateGiftList(list);
        List<Gift> toSaveList = validateResult.getLeft();
        log.info("Processing gift list, size {}", toSaveList.size());
        BatchResponse resp = super.processBatch(toSaveList);
        List<GiftReservation> giftReservations = toSaveList.stream()
                .map(Gift::getGiftReservation)
                .filter(Objects::nonNull)
                .toList();
        if (!CollectionUtils.isEmpty(giftReservations)) {
            giftReservationRepository.saveAll(giftReservations);
        }
        log.info("Processed gift reservation list, size {}", giftReservations.size());
        List<ValidateRowResponse> validateFailedList = validateResult.getRight();
        if (!CollectionUtils.isEmpty(validateFailedList)) {
            List<String> msgs = validateFailedList.stream()
                    .map(val -> "Line " + val.getRow() + ": " + val.getRd())
                    .toList();
            List<String> dbResp = Optional.ofNullable(resp.getDbResponses()).orElse(new ArrayList<>());
            dbResp.addAll(msgs);
            resp.setDbResponses(dbResp);
            resp.setFailedRecord(resp.getFailedRecord() + validateFailedList.size());
        }
        return resp;
    }

    protected Pair<List<Gift>, List<ValidateRowResponse>> validateGiftList(List<Gift> giftList) {
        log.info("Before validating list gift import, size {}", giftList.size());

        Map<String, Constant> constants = new HashMap<>();
        Map<String, DynamicFilterDto> filters = new HashMap<>();
        List<ValidateRowResponse> failedList = new ArrayList<>();
        Map<Integer, Gift> rowIdxProcessedGiftMap = new HashMap<>(giftList.size());
        
        // build row index updated gift map
        Map<Integer, Integer> updatedGiftIdToRowIdxMap = new HashMap<>(giftList.size());
        Map<Integer, Gift> updatedGiftIdToGiftMap = new HashMap<>(giftList.size());
        List<Integer> updatedGiftIds = new ArrayList<>(giftList.size());

        // validate new gift row (id is null) first time to remove invalid row
        for (int rowIdx = 2; rowIdx <= giftList.size(); rowIdx++) {
            var gift = giftList.get(rowIdx-2);
            if (gift.getId() != null) {
                updatedGiftIdToRowIdxMap.put(gift.getId(), rowIdx);
                updatedGiftIdToGiftMap.put(gift.getId(), gift);
                updatedGiftIds.add(gift.getId());
                continue;
            }

            var validateRes = this.validateGift(rowIdx, gift);
            if (validateRes.getRc() != 0) {
                failedList.add(validateRes);
                continue;
            }
            applySyncConstantData(gift, constants, filters);
            rowIdxProcessedGiftMap.put(rowIdx, gift);
        }

        List<Integer> notfoundUpdatedGiftIds = new ArrayList<>();

        while (!updatedGiftIds.isEmpty()) {
            List<Integer> subList = updatedGiftIds.subList(0, Math.min(updatedGiftIds.size(), 1000));
            List<Gift> gifts = giftRepository.findByIdIn(subList);
            List<Integer> existIds = gifts.stream().map(Gift::getId).toList();
            notfoundUpdatedGiftIds.addAll(subList.stream().filter(val -> !existIds.contains(val)).toList());

            if (!CollectionUtils.isEmpty(gifts)) {
                gifts.forEach(gift -> {
                    Gift fromImportFile = updatedGiftIdToGiftMap.get(gift.getId());
                    if (Objects.isNull(fromImportFile)) {
                        return;
                    }

                    var rowIdx = updatedGiftIdToRowIdxMap.get(gift.getId());
                    if (rowIdx == null) {
                        log.error("Not found imported row index for gift id {}", gift.getId());
                        return;
                    }

                    BeanUtils.copyProperties(fromImportFile, gift);
                    GiftReservation originalGiftReservation = giftReservationRepository.findByGiftId(gift.getId());
                    if (Objects.nonNull(originalGiftReservation)) {
                        GiftReservation updatingGiftReservation = fromImportFile.getGiftReservation();
                        updatingGiftReservation.setId(originalGiftReservation.getId());

                        BeanUtils.copyProperties(updatingGiftReservation, originalGiftReservation);
                        gift.setGiftReservation(originalGiftReservation);
                    }

                    // validate updated gift row
                    var validateRes = this.validateGift(rowIdx, gift);
                    if (validateRes.getRc() != 0) {
                        failedList.add(validateRes);
                        return;
                    }
                    applySyncConstantData(gift, constants, filters);
                    rowIdxProcessedGiftMap.put(rowIdx, gift);
                });
            }
            updatedGiftIds.removeAll(subList);
        }

        if (!CollectionUtils.isEmpty(notfoundUpdatedGiftIds)) {
            notfoundUpdatedGiftIds.forEach(gift -> {
                ValidateRowResponse notFoundValidateRes = new ValidateRowResponse();
                var rowIdx = updatedGiftIdToRowIdxMap.get(gift);
                if (rowIdx == null) {
                    log.error("Not found imported row index for gift id {}", gift);
                    return;
                }
                notFoundValidateRes.setRow(rowIdx);
                notFoundValidateRes.setFailed("Gift not found to update");
                failedList.add(notFoundValidateRes);
            });
        }

        List<Gift> res = rowIdxProcessedGiftMap.values().stream().toList();
        log.info("After validating list gift import, size {}", res.size());

        return Pair.of(res, failedList);
    }

    protected ValidateRowResponse validateGift(int rowNum, Gift gift) {
        ValidateRowResponse resp = new ValidateRowResponse();
        resp.setRow(rowNum);
        resp.setFailed();
        
        // validate gift code is not null first
        var rewardAppGiftCode = gift.getDynamicData().getRewardAppGiftCode();
        if (Objects.isNull(rewardAppGiftCode)) {
            resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, "reward_app_gift_code"));
            return resp;
        }

        // validate gift fields existence
        Set<String> errorFields = checkGiftField(gift);
        if (!CollectionUtils.isEmpty(errorFields)) {
            resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, String.join(", ", errorFields)));
            return resp;
        }
        
        // validate gift values
        if (gift.getStartDate().after(gift.getEndDate())) {
            resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, "start_date"));
            return resp;
        }
        
        if (gift.getType() == GiftTypeEnum.GIFT) {
            if (gift.getDynamicData() == null || gift.getDynamicData().getSourceGift() == null) {
                resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, "source_gift"));
                return resp;
            }
        } else {
            if (Objects.nonNull(gift.getExpireHour()) && gift.getExpireHour() < 0) {
                resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, "expire_hour"));
                return resp;
            }

            if (Objects.nonNull(gift.getExpireHour()) && Objects.nonNull(gift.getExpireDate())) {
                resp.setRd(String.format(GIFT_LINE_ERROR_MSG_FORMAT, rewardAppGiftCode, "expire_date,expire_hour"));
                return resp;
            }
        }

        GiftReservation giftReservation = gift.getGiftReservation();
        ValidateRowResponse validateGiftReservationRes = validateGiftReservation(gift, giftReservation);
        log.debug("Validate row resp {}", validateGiftReservationRes);
        validateGiftReservationRes.setRow(rowNum);
        return validateGiftReservationRes;
    }

    private Set<String> checkGiftField(Gift gift) {
        Set<String> errorFields = new HashSet<>();
        Map<Function<Gift, String>, String> notEmptyList = Map.ofEntries(
                Map.entry(Gift::getCategoryCode, "category_code"),
                Map.entry(Gift::getName, "name"),
                Map.entry(Gift::getSctNumber, "sct_number"),
                Map.entry(Gift::getTpmNumber, "tpm_number")
        );
        Map<Function<Gift, Object>, String> notNullList = Map.ofEntries(
                Map.entry(Gift::getType, "type"),
                Map.entry(Gift::getImages, "images"),
                Map.entry(Gift::getTierCodes, "tier_codes"),
                Map.entry(Gift::getPoint, "point"),
                Map.entry(Gift::getPrice, "price"),
                Map.entry(Gift::getStartDate, "start_date"),
                Map.entry(Gift::getEndDate, "end_date")
        );
        
        for (var entry : notEmptyList.entrySet()) {
            var func = entry.getKey();
            var val = func.apply(gift);
            var fieldName = entry.getValue();
            if (Objects.isNull(val) || !StringUtils.hasText(val.toString())) {
                errorFields.add(fieldName);
            }
        }
        for (var entry : notNullList.entrySet()) {  
            var func = entry.getKey();
            var val = func.apply(gift);
            var fieldName = entry.getValue();
            if (Objects.isNull(val)) {
                errorFields.add(fieldName);
            }
        }
        return errorFields;
    }

    private ValidateRowResponse validateGiftReservation(Gift gift, GiftReservation giftReservation) {
        ValidateRowResponse resp = new ValidateRowResponse();

        switch (gift.getType()) {
            case GIFT:
                if (Objects.isNull(gift.getTransportTypeCode()) || !StringUtils.hasText(gift.getTransportTypeCode())) {
                    resp.setFailed("Transport type is required for gift type GIFT");
                    return resp;
                }
                List<Function<GiftDynamicData, Object>> mustNotNullList = List.of(
                        GiftDynamicData::getWeight, GiftDynamicData::getLength, GiftDynamicData::getWidth, GiftDynamicData::getHeight
                );
                mustNotNullList.stream()
                        .map(func -> func.apply(gift.getDynamicData()))
                        .filter(Objects::isNull)
                        .map(val -> Optional.of(true))
                        .findFirst()
                        .ifPresent(val -> resp.setFailed("Weight, length, width, height are required for gift type GIFT"));
                if (resp.getRc() != 0) {
                    return resp;
                }
                if (Objects.isNull(gift.getGiftReservation())
                        || Boolean.FALSE.equals(gift.getIsAllowReservation())
                        || Objects.isNull(gift.getIsAllowReservation())) {
                    resp.setSuccess();
                    return resp;
                }
                if (Objects.isNull(giftReservation)) {
                    resp.setFailed("Gift reservation is required for gift type GIFT");
                    return resp;
                }
                break;
            default:
                if (Boolean.TRUE.equals(gift.getIsAllowReservation()) || Objects.nonNull(giftReservation)) {
                    resp.setFailed("Gift reservation is not required for gift type E-VOUCHER");
                    return resp;
                }
                List<Function<GiftDynamicData, Object>> mustBeNullList = List.of(
                        GiftDynamicData::getWeight, GiftDynamicData::getLength, GiftDynamicData::getWidth, GiftDynamicData::getHeight
                );
                mustBeNullList.stream()
                        .map(func -> func.apply(gift.getDynamicData()))
                        .filter(Objects::nonNull)
                        .map(Optional::of)
                        .findFirst()
                        .ifPresent(val -> resp.setFailed("Weight, length, width, height are not required for gift type E-VOUCHER"));
                if (Objects.nonNull(gift.getTransportTypeCode()) && StringUtils.hasText(gift.getTransportTypeCode())) {
                    resp.setFailed("Transport type is not required for gift type E-VOUCHER");
                    return resp;
                }
                if (resp.getRc() != 0) {
                    return resp;
                }
                resp.setSuccess();
                return resp;
//            default:
//                resp.setSuccess();
//                return resp;
        }
        if (Objects.isNull(giftReservation.getMaximumReservationQuantity())) {
            resp.setFailed("Maximum reservation quantity is required");
            return resp;
        }
        if (Objects.isNull(giftReservation.getReservationPercent())) {
            resp.setFailed("Reservation percent is required");
            return resp;
        }
        if (Objects.isNull(giftReservation.getCoinToMinusPercent())) {
            resp.setFailed("Coin to minus percent is required");
            return resp;
        }
        resp.setSuccess();
        return resp;
    }

    private void applySyncConstantData(Gift gift, Map<String, Constant> constants, Map<String, DynamicFilterDto> filters) {
        String keyAlpha = "GIFT";
        String keyBadge = "BADGE";
        String keyHiddenTag = "HIDDEN_TAG";

        String badge = CollectionUtils.isEmpty(gift.getBadgeCodes()) ? null : gift.getBadgeCodes().getFirst();
        if (StringUtils.hasText(badge)
                && constants.containsKey(badge) && Objects.nonNull(constants.get(badge))) {
            gift.setBadgeCodes(List.of(constants.get(badge).getValue()));
        } else if (StringUtils.hasText(badge)) {
            Constant badgeConstant = constantRepository.findFirstActiveByKeyAlphaAndKeyBetaAndName(
                    keyAlpha, keyBadge, badge);
            constants.put(badge, badgeConstant);
            if (Objects.nonNull(badgeConstant)) {
                gift.setBadgeCodes(List.of(badgeConstant.getValue()));
            }
        }

        List<String> hiddenTags = gift.getHiddenTags();
        if (!CollectionUtils.isEmpty(hiddenTags)) {
            List<String> hiddenTagCodes = hiddenTags.stream()
                    .map(tag -> {
                        if (constants.containsKey(tag) && Objects.nonNull(constants.get(tag))) {
                            return constants.get(tag).getValue();
                        }
                        Constant hiddenTagConstant = constantRepository.findFirstActiveByKeyAlphaAndKeyBetaAndName(
                                keyAlpha, keyHiddenTag, tag);
                        constants.put(tag, hiddenTagConstant);
                        return Objects.nonNull(hiddenTagConstant) ? hiddenTagConstant.getValue() : null;
                    })
                    .filter(Objects::nonNull)
                    .toList();
            gift.setHiddenTags(hiddenTagCodes);
        }

        GiftDynamicFilter dynamicFilter = gift.getDynamicData().getDynamicFilter();
        if (Objects.isNull(dynamicFilter)) {
            return;
        }
        String dynamicFilterValue = dynamicFilter.getCode();
        if (StringUtils.hasText(dynamicFilterValue)
                && filters.containsKey(dynamicFilterValue) && Objects.nonNull(filters.get(dynamicFilterValue))) {
            DynamicFilterDto filterDto = filters.get(dynamicFilterValue);
            setDynamicFilter(dynamicFilter, filterDto);

        } else if (StringUtils.hasText(dynamicFilterValue)) {
            Constant constant = constantRepository.findGiftFilterByName(dynamicFilterValue);
            if (Objects.isNull(constant)) {
                return;
            }
            try {
                DynamicFilterDto dynamicFilterDto = DynamicFilterDto.fromEntity(constant);
                filters.put(dynamicFilterValue, dynamicFilterDto);
                if (Objects.isNull(dynamicFilterDto)) {
                    return;
                }
                setDynamicFilter(dynamicFilter, dynamicFilterDto);
            } catch (Exception ex) {
                log.error("Error parsing dynamic filter", ex);
            }
        }

    }

    protected void setDynamicFilter(GiftDynamicFilter giftDynamicFilter, DynamicFilterDto filterDto) {
        giftDynamicFilter.setCode(filterDto.getCode());
        giftDynamicFilter.setName(filterDto.getName());
        if (CollectionUtils.isEmpty(filterDto.getValue())) {
            return;
        }
        String valueCode = giftDynamicFilter.getValueCode();
        DynamicFilterDto.DynamicFilterValueDto valueDto = filterDto.getValue()
                .stream().filter(val -> Objects.nonNull(val) && val.getCode().equals(valueCode) || val.getName().equals(valueCode))
                .findFirst().orElse(null);
        if (Objects.isNull(valueDto)) {
            return;
        }
        giftDynamicFilter.setValueCode(valueDto.getCode());
        if (CollectionUtils.isEmpty(valueDto.getFirstLevel())) {
            return;
        }
        String firstLevelCode = giftDynamicFilter.getFirstLevelCode();
        DynamicFilterDto.DynamicFilterFirstLevelDto firstLevelValueDto = valueDto.getFirstLevel()
                .stream().filter(val -> Objects.nonNull(val) && val.getCode().equals(firstLevelCode) || val.getName().equals(firstLevelCode))
                .findFirst().orElse(null);
        if (Objects.isNull(firstLevelValueDto)) {
            return;
        }
        giftDynamicFilter.setFirstLevelCode(firstLevelValueDto.getCode());
        if (CollectionUtils.isEmpty(firstLevelValueDto.getValue())) {
            return;
        }
        DynamicFilterDto.DynamicFirstLevelValueDto firstLevelValue = firstLevelValueDto.getValue().getFirst();
        if (Objects.isNull(firstLevelValue)) {
            return;
        }
        giftDynamicFilter.setFirstLevelValueCode(firstLevelValue.getCode());
        if (CollectionUtils.isEmpty(firstLevelValue.getSecondLevel())) {
            return;
        }
        String secondLevelCode = giftDynamicFilter.getSecondLevelCode();
        DynamicFilterDto.DynamicFilterSecondLevelDto secondLevelValue = firstLevelValue.getSecondLevel()
                .stream().filter(val -> Objects.nonNull(val) && val.getCode().equals(secondLevelCode) || val.getName().equals(secondLevelCode))
                .findFirst().orElse(null);
        if (Objects.isNull(secondLevelValue)) {
            return;
        }
        giftDynamicFilter.setSecondLevelCode(secondLevelValue.getCode());
        if (CollectionUtils.isEmpty(secondLevelValue.getValue())) {
            return;
        }
        DynamicFilterDto.DynamicFilterSecondLevelValueDto value = secondLevelValue.getValue().getFirst();
        if (Objects.isNull(value)) {
            return;
        }
        giftDynamicFilter.setSecondLevelValueCode(value.getCode());
    }
}
