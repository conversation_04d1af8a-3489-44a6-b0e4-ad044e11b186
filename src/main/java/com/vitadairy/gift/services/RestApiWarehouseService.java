package com.vitadairy.gift.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.main.dto.HistoryLuckyTransactionUpdateRequestDto;
import com.vitadairy.main.dto.SfUserInfoResponseDto;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.RestTemplateClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class RestApiWarehouseService {

    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;

    public RestApiWarehouseService(
            @Qualifier("restTemplateWarehouseClient") RestTemplateClient restTemplateClient,
            ObjectMapper objectMapper) {
        this.restTemplateClient = restTemplateClient;
        this.objectMapper = objectMapper;
    }

    public SfUserInfoResponseDto getUserInfo(Long userId) {
        try {
            return restTemplateClient.get("/in/ws/query-sf/user-info?userId=" + userId, new ParameterizedTypeReference<SfUserInfoResponseDto>() {
            });
        } catch (Exception ex) {
            log.error("Error when get user info ", ex);
            throw new ApplicationException("Error when get user info");
        }
    }

    public void updateUserGiftHistoryEvent(String transactionCode, UserGift.USER_GIFT_STATUS status) {
        try {
            HistoryLuckyTransactionUpdateRequestDto requestData = new HistoryLuckyTransactionUpdateRequestDto();
            requestData.setTransactionCode(transactionCode);
            requestData.setStatus(status.getValue());
            Map<String, Object> request = objectMapper.readValue(objectMapper.writeValueAsString(requestData), HashMap.class);
            restTemplateClient.put("/v4/int/ws/history-event/update", request, new ParameterizedTypeReference<>() {
            });
        } catch (Exception ex) {
            log.error("Error when update history event ", ex);
        }
    }
}
