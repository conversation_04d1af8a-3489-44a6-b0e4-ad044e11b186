package com.vitadairy.gift.services;

import com.vitadairy.gift.common.ExportTypeDefs;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.userGift.dto.GiftExportDto;
import com.vitadairy.gift.features.userGift.dto.UserGiftExportDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WriteExportGiftFileServiceStrategy {
    private final WriteExportFileService<UserGiftExportDto, UserGiftListRequest> writeExportFileUserGiftService;
    private final WriteExportFileService<GiftExportDto, ListGiftRequestDto> writeExportFileGiftService;

    public WriteExportFileService<?, ?> getProcessExportFileService(String type) {
        if (Objects.isNull(type)) {
            return null;
        }
        switch (type) {
            case ExportTypeDefs.USER_GIFTS:
                return writeExportFileUserGiftService;
            case ExportTypeDefs.GIFTS:
                return writeExportFileGiftService;
            default:
                return null;
        }
    }
}
