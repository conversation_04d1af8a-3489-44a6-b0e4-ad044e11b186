package com.vitadairy.gift.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.entities.GiftDynamicData;
import com.vitadairy.gift.entities.UserGiftDynamicData;
import com.vitadairy.gift.features.gifts.dto.ReadOnlyUserGiftDto;
import com.vitadairy.gift.features.userGift.dto.UserGiftExportDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.features.userGift.services.UserGiftAdminService;
import com.vitadairy.gift.repositories.UserGiftExportRepository;
import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.zoo.dto.ReadOnlyHistoryPointDto;
import com.vitadairy.zoo.repositories.HistoryPointRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FetchExportGsUserGiftService implements FetchDataService<UserGiftExportDto, UserGiftListRequest> {

    private UserGiftExportRepository userGiftExportRepository;
    private HistoryPointRepository zooHistoryPointRepository;
    private ObjectMapper objectMapper;

    @Override
    public List<UserGiftExportDto> fetch(FetchRequest<UserGiftListRequest> fetchRequest) {
        List<ReadOnlyUserGiftDto> userGifts = userGiftExportRepository.getAdminList(fetchRequest.getRequest());

        if (userGifts.size() == 0) {
            return new ArrayList<>();
        }

        Map<String, ReadOnlyHistoryPointDto> transactionExternalIdToHistoryPoint = new HashMap<>(userGifts.size());
        Set<String> transactionExternalIds = userGifts.stream()
                 .map(ReadOnlyUserGiftDto::getTransactionCode)
                 .collect(Collectors.toCollection(() -> new HashSet<>(userGifts.size())));
        
        if (transactionExternalIds.size() > 0) {
            List<ReadOnlyHistoryPointDto> points = zooHistoryPointRepository.getReadOnlyByTransactionExternalIdIn(transactionExternalIds);
            transactionExternalIdToHistoryPoint = points.stream().collect(Collectors.toMap(ReadOnlyHistoryPointDto::getTransactionExternalId, Function.identity()));
        }

        List<UserGiftExportDto> result = new ArrayList<>(userGifts.size());

        try {
            for (var userGift : userGifts) {
                var point = transactionExternalIdToHistoryPoint.get(userGift.getTransactionCode());
                var builder = UserGiftExportDto.builder();
                if (Objects.nonNull(point)) {
                    builder.customerName(point.getCustomerName())
                            .customerId(Objects.nonNull(point.getCustomerId()) ? point.getCustomerId().toString() : null)
                            .customerPhone(point.getCustomerPhone())
                            .transactionExternalId(point.getTransactionExternalId())
                            .transactionDate(Objects.nonNull(point.getTransactionDate())? Date.from(point.getTransactionDate()) : null)
                            .type(point.getType())
                            .actionType(point.getActionType())
                            .giftPoint(point.getGiftPoint())
                            .tierPoint(point.getTierPoint());
                }
    
                builder.userGiftId(userGift.getId())
                        .giftId(userGift.getGiftId())
                        .userGiftStatus(userGift.getStatus())
                        .quantity(userGift.getQuantity());
    
                if (Objects.nonNull(userGift.getDynamicData())) {
                    var dynamicData = objectMapper.readValue(userGift.getDynamicData(), UserGiftDynamicData.class);
                    builder.voucherRefId(dynamicData.getVoucherRefId())
                            .voucherCode(dynamicData.getVoucherCode())
                            .voucherLink(dynamicData.getVoucherLink())
                            .usedDate(Objects.nonNull(dynamicData.getUsedDate()) ? Date.from(dynamicData.getUsedDate()) : null)
                            .expiryDate(Objects.nonNull(dynamicData.getExpiryDate()) && !"null".equals(dynamicData.getExpiryDate()) ? parseDateFromExpiryDate(dynamicData.getExpiryDate()) : null)
                            .isReusing(dynamicData.getIsReusing())
                            .name(userGift.getGiftName())
                            .money(userGift.getGiftPrice().toBigInteger());
                }

                if (Objects.nonNull(userGift.getGiftDynamicData())) {
                    var dynamicData = objectMapper.readValue(userGift.getGiftDynamicData(), GiftDynamicData.class);
                    builder.eProductId(Objects.nonNull(dynamicData.getEProductId()) ? dynamicData.getEProductId().toString() : null)
                            .eProductName(dynamicData.getEProductName())
                            .priceId(Objects.nonNull(dynamicData.getPriceId()) ? dynamicData.getPriceId().toString() : null)
                            .giftCode(dynamicData.getRewardAppGiftCode());
                }
                
                result.add(builder.build());
            }
        } catch (Exception e) {
            log.error("Error when build user gift export dto: {}", ExceptionUtils.getStackTrace(e));
        }

        return result;
    }

    private Date parseDateFromExpiryDate(String expiryDate) {
        try {
            if(expiryDate == null || expiryDate.equals("null")) {
                return null;
            }
            Date result = parseDateWithPattern("yyyy-MM-dd", expiryDate);
            if(result == null){
                result = parseDateWithPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", expiryDate);
            }
            return result;
        } catch (Exception e) {
            log.error("Cannot parse date from expiry date: {}", expiryDate);
            return null;
        }
    }

    private Date parseDateWithPattern(String pattern, String Date){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            return simpleDateFormat.parse(Date);
        } catch (Exception e) {
            return null;
        }
    }
}
