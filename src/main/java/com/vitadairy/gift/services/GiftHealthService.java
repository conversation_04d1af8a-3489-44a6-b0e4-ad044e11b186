package com.vitadairy.gift.services;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.Connection;

@Service("GiftHealthService")
@Log4j2
public class GiftHealthService {
    private final HikariDataSource giftHikariDataSource;

    private final Integer giftDbConnectionTimeout;

    public GiftHealthService(
            @Qualifier("giftDataSource") HikariDataSource giftHikariDataSource,
            @Value("${spring.datasource.gift.hikari.connection-timeout:30000}") Integer giftDbConnectionTimeout
    ) {
        this.giftHikariDataSource = giftHikariDataSource;
        this.giftDbConnectionTimeout = giftDbConnectionTimeout;
    }

    public ResponseEntity<String> startupProbe() {
        //log.debug("Begin startupProbe");
        boolean giftHikariConnectionDbStatus = this.validateGiftHikariConnectionDbStatus();
        if (!giftHikariConnectionDbStatus) return ResponseEntity.status(500).body("Db Gift die");

        return ResponseEntity.ok().body("ok");
    }

    public ResponseEntity<String> livenessProbe() {
        //log.debug("Begin livenessProbe");
        boolean giftHikariConnectionDbStatus = this.validateGiftHikariConnectionDbStatus();
        if (!giftHikariConnectionDbStatus) return ResponseEntity.status(500).body("Db Gift die");

        return ResponseEntity.ok().body("ok");
    }

    private boolean validateGiftHikariConnectionDbStatus() {
        try (Connection connection = this.giftHikariDataSource.getConnection()) {
            if (connection.isValid(this.giftDbConnectionTimeout)) { // Check if connection is valid within 30 second
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to connect to the gift database: ", e);
            return false;
        }
    }
}
