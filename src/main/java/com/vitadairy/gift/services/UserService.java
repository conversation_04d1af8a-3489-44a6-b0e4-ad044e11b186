package com.vitadairy.gift.services;

import com.vitadairy.zoo.common.ProvinceType;
import com.vitadairy.zoo.dto.UpdateUserAddressRequestDto;
import com.vitadairy.zoo.entities.ProvinceNew;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.entities.UserProvince;
import com.vitadairy.zoo.entities.WardNew;
import com.vitadairy.zoo.repositories.*;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service("zooUserService")
@Slf4j
@AllArgsConstructor
public class UserService {
    private final UserRepository userRepository;
    private final ProvinceRepository provinceRepository;
    private final ProvinceNewRepository provinceNewRepo;
    private final WardNewRepository wardNewRepo;
    private final UserProvinceRepository userProvinceRepo;

    public void updateUserAddress(UpdateUserAddressRequestDto dto){
        User user = userRepository.findById(dto.getUserId()).orElseThrow(() -> new RuntimeException(String.format("User with id %s not found", dto.getUserId())));
        UserProvince userProvince = this.userProvinceRepo.findByUserId(user.getId()).orElse(null);
        if (Objects.isNull(userProvince)) {
            userProvince = new UserProvince();
            userProvince.setUser(user);
        }

//        if(Objects.nonNull(dto.getProvinceId())){
//            provinceRepository.findByIdAndType(dto.getProvinceId().longValue(), ProvinceType.PROVINCE).orElseThrow(() -> new RuntimeException("Province not found"));
//            user.setProvinceId(dto.getProvinceId());
//        }
//        if(Objects.nonNull(dto.getDistrictId())){
//            provinceRepository.findByIdAndType(dto.getDistrictId().longValue(), ProvinceType.DISTRICT).orElseThrow(() -> new RuntimeException("District not found"));
//            user.setDistrictId(dto.getDistrictId());
//        }
//        if(Objects.nonNull(dto.getWardId())){
//            provinceRepository.findByIdAndType(dto.getWardId().longValue(), ProvinceType.WARD).orElseThrow(() -> new RuntimeException("Ward not found"));
//            user.setWardId(dto.getWardId());
//        }
        Integer newProvinceId = dto.getNewProvinceId();
        Long newWardId = dto.getNewWardId();
        if (Objects.nonNull(newProvinceId)) {
            ProvinceNew provinceNew = this.provinceNewRepo.findById(dto.getNewProvinceId()).orElseThrow(() -> new RuntimeException("Province not found"));
            userProvince.setNewProvince(provinceNew);
            if (Objects.nonNull(newWardId)) {
                WardNew wardNew = this.wardNewRepo.findByIdAndNewProvince(dto.getNewWardId(), provinceNew).orElseThrow(() -> new RuntimeException("Ward not found"));
                userProvince.setNewWard(wardNew);
            }
            userProvince.setEnterByUser(Boolean.TRUE);
            this.userProvinceRepo.save(userProvince);
        }

        if(StringUtils.isNotEmpty(dto.getAddress())){
            user.setAddress(dto.getAddress());
        }

        userRepository.save(user);
    }

}
