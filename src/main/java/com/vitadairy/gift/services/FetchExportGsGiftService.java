package com.vitadairy.gift.services;

import com.vitadairy.gift.common.ConstantTypeDefs;
import com.vitadairy.gift.entities.Constant;
import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.gifts.services.GiftService;
import com.vitadairy.gift.features.userGift.dto.GiftExportDto;
import com.vitadairy.gift.repositories.ConstantRepository;
import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FetchExportGsGiftService implements FetchDataService<GiftExportDto, ListGiftRequestDto> {

    private GiftService giftService;
    private ConstantRepository constantRepository;

    @Override
    public List<GiftExportDto> fetch(FetchRequest<ListGiftRequestDto> fetchRequest) {
        ListGiftRequestDto dto = fetchRequest.getRequest();
        dto.setSort("id", Sort.Direction.DESC);
        dto.setIsCustomer(false);
        Page<Gift> data = giftService.pageableSpecification(dto);
        List<Constant> cats = constantRepository.findAllByKeyAlphaAndKeyBetaAndValueIn(ConstantTypeDefs.KEY_ALPHA_GIFT, ConstantTypeDefs.KEY_BETA_CATEGORY
                , data.stream().map(Gift::getCategoryCode).filter(Objects::nonNull).collect(Collectors.toList()));
        return data.get()
                .map(gift -> {
                    Constant cat = cats.stream().filter(c -> c.getValue().equals(gift.getCategoryCode())).findFirst().orElse(null);
                    return GiftExportDto.builder()
                            .id(gift.getId().toString())
                            .name(gift.getName())
                            .category(Objects.nonNull(cat) && Objects.nonNull(cat.getDynamicData())? cat.getDynamicData().getName() : null)
                            .rewardAppGiftCode(Objects.nonNull(gift.getDynamicData()) ? gift.getDynamicData().getRewardAppGiftCode() : null)
                            .type(Objects.nonNull(gift.getType()) ? gift.getType().toString() : null)
                            .sctNumber(gift.getSctNumber())
                            .tpmNumber(gift.getTpmNumber())
                            .badges(Objects.nonNull(gift.getBadgeCodes()) ? gift.getBadgeCodes().stream().filter(Objects::nonNull).collect(Collectors.joining(",")) : null)
                            .displayPoint(gift.getPoint())
                            .money(gift.getPrice())
                            .total(gift.getQuantity())
                            .status(gift.getStatus().toString())
                            .qtyReward(gift.getQuantityReward())
                            .qtyReservation(gift.getQuantityReservation())
                            .build();
                })
                .collect(Collectors.toList());
    }
}
