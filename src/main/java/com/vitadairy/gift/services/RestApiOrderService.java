package com.vitadairy.gift.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.gift.features.gifts.dto.request.CreateOrderRequestDto;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.helper.RestTemplateClient;
import com.vitadairy.main.response.RestApiBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class RestApiOrderService {

    private final RestTemplateClient restTemplateClient;
    private final ObjectMapper objectMapper;

    public RestApiOrderService(
            @Qualifier("restTemplateOrderClient") RestTemplateClient restTemplateClient,
            ObjectMapper objectMapper) {
        this.restTemplateClient = restTemplateClient;
        this.objectMapper = objectMapper;
    }

    public RestApiBaseResponse createOrder(String token, CreateOrderRequestDto requestData) {
        restTemplateClient.buildAuth(token);
        try {
            Map<String, Object> request = objectMapper.readValue(objectMapper.writeValueAsString(requestData), HashMap.class);
            return restTemplateClient.post("/v4/os/orders", request, new ParameterizedTypeReference<>() {});
        } catch (JsonProcessingException ex) {
            log.error("Error when parse request to api", ex);
            throw new ApplicationException("Error on create order", HttpStatus.BAD_REQUEST);
        }
    }
}
