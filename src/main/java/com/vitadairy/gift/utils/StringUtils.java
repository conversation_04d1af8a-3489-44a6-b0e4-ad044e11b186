package com.vitadairy.gift.utils;

import java.util.Arrays;

public class StringUtils {

    public static String generateUniqCode(String input, String delimiter) {
        return StringUtils.toSlug(input) + delimiter + System.currentTimeMillis();
    }

    public static String toSlug(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        // Convert to lower case
        String slug = input.toLowerCase();

        // Replace accented characters
        slug = slug.replaceAll("[áàảạãăắằẳẵặâấầẩẫậ]", "a");
        slug = slug.replaceAll("[éèẻẽẹêếềểễệ]", "e");
        slug = slug.replaceAll("[iíìỉĩị]", "i");
        slug = slug.replaceAll("[óòỏõọôốồổỗộơớờởỡợ]", "o");
        slug = slug.replaceAll("[úùủũụưứừửữự]", "u");
        slug = slug.replaceAll("[ýỳỷỹỵ]", "y");
        slug = slug.replaceAll("[đ]", "d");

        // Remove special characters
        slug = slug.replaceAll("[`~!@#$|%\\^&*()+=,.?/>'\";:_]", "");

        // Replace spaces with hyphens
        slug = slug.replaceAll(" ", "-");

        // Replace multiple hyphens with a single hyphen
        slug = slug.replaceAll("-{2,}", "-");

        // Remove hyphens at the start and end
        slug = slug.replaceAll("^-|-$", "");

        return slug;
    }

    public static String[] toStrings(String input) {
        if (input != null && !input.isEmpty()) {
            return input.split(",");
        }
        return null;
    }

    public static Integer[] toNumbers(String input) {
        if (input != null && !input.isEmpty()) {
            return Arrays.stream(input.split(","))
                    .map(Integer::parseInt)
                    .toArray(Integer[]::new);
        }
        return new Integer[] {};
    }

    public static String toArraySqlString(String input) {
        // Convert array to PostgreSQL array format: '{elem1,elem2,elem3}'
        return "{" + input + "}";
    }

    public static String toArraySqlString(String[] input) {
        // Convert array to PostgreSQL array format: '{elem1,elem2,elem3}'
        return "{" + String.join(",", input) + "}";
    }
}
