package com.vitadairy.gift.schedulers;

import com.vitadairy.main.schedules.ScheduledJobRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service("killLockRowSchedule")
@Slf4j
public class KillLockRowSchedule implements ScheduledJobRunner {
    private final JdbcTemplate jdbcTemplate;

    public KillLockRowSchedule(@Qualifier("zooJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public void run() {
        String checkCountLock = "SELECT COUNT(*) FROM pg_locks l     JOIN pg_stat_activity a ON l.pid = a.pid     WHERE l.locktype = 'tuple'";
        Integer lockCount = jdbcTemplate.queryForObject(checkCountLock, Integer.class);

        log.warn("Current lock count: {}", lockCount);

        if(lockCount > 20){
            log.warn("Starting to Kill row locked ...");
            String killRowLocked = "SELECT kill_tuple_locks_if_exceed_20()";
            jdbcTemplate.execute(killRowLocked);
        }
    }
}

