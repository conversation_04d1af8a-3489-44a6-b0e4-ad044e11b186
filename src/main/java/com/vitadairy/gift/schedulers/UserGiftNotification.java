package com.vitadairy.gift.schedulers;

import com.vitadairy.gift.features.gifts.dto.UserGiftUserActiveSmsDto;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.main.dto.CustomNotificationRequestDto;
import com.vitadairy.main.dto.NotificationData;
import com.vitadairy.main.dto.SmsRequestDto;
import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.main.services.NotificationService;
import com.vitadairy.zoo.enums.FeatureNoti;
import com.vitadairy.zoo.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Service("userGiftNotification")
@Slf4j
@RequiredArgsConstructor
public class UserGiftNotification implements ScheduledJobRunner {

    private final static String NOTI_REMIND_EXCHANGE_GIFT_TITLE = "XÁC NHẬN ĐỊA CHỈ NHẬN QUÀ!";
    private final static String NOTI_REMIND_EXCHANGE_GIFT_BUTTON_NAME = "QUÀ CỦA TÔI";
    private final static String NOTI_REMIND_EXCHANGE_GIFT_LINK = "/my-reward?tab=gift";
    private final static String NOTI_1DAY_REMIND_EXCHANGE_GIFT_CONTENT = "Vào mục \"Quà của tôi\" để xác nhận địa chỉ nhận quà. Hoàn tất ngay để không bị hủy quà nhé!";
    private final static String NOTI_1DAY_REMIND_EXCHANGE_GIFT_DESC = 
    "Hiện tại đơn quà đổi của Quý Khách chưa hoàn tất bước xác nhận địa chỉ trong vòng 07 ngày kể từ ngày đổi quà thành công.<br/>"+
    "<b>Còn 06 ngày (144 giờ) để xác nhận địa chỉ</b>, vui lòng hoàn tất trong thời gian quy định để VitaDairy có thể tiến hành giao quà đến Quý Khách.<br/>"+
    "Nếu quá thời hạn quy định, hệ thống sẽ tự động hủy đơn và không hoàn lại xu đổi quà.<br/>"+
    "Vui lòng kiểm tra lại mục \"Quà của tôi\" và xác nhận ngay để không bỏ lỡ món quà từ VitaDairy nhé!";

    private final static int STAGE_NOTIFIED_1D = 1;
    private final static int STAGE_NOTIFIED_5D = 5;

    private final UserGiftRepository userGiftRepository;
    private final UserRepository userRepository;
    private final NotificationService notificationService;

    @Override
    public void run() {
        log.warn("Start user gift notification");
        remind1DayExchangeGift();
        remind5DayExchangeGift();
        log.warn("End user gift notification ");
    }

    private void remind1DayExchangeGift() {
        try{
            var userGifts = userGiftRepository.getUsersOfPendingUserGifts(1, 2, STAGE_NOTIFIED_1D);
            log.warn("[1-day Remind] Number of user id have pending userGifts: {}", userGifts.size());
            if (CollectionUtils.isEmpty(userGifts)) {
                return;
            } 
            
            var userIds = userGifts.stream().map(UserGiftUserActiveSmsDto::getUserId).toList();

            CustomNotificationRequestDto customNotificationRequestDto = CustomNotificationRequestDto.builder()
                .userIds(userIds.toArray(new Long[0]))
                .title(NOTI_REMIND_EXCHANGE_GIFT_TITLE)
                .content(NOTI_1DAY_REMIND_EXCHANGE_GIFT_CONTENT)
                .description(NOTI_1DAY_REMIND_EXCHANGE_GIFT_DESC)
                .data(NotificationData.builder().deeplink("").description(NOTI_1DAY_REMIND_EXCHANGE_GIFT_DESC).build())
                .featureNoti(FeatureNoti.NOTI_EXCHANGE_GIFT)
                    .buttonName(NOTI_REMIND_EXCHANGE_GIFT_BUTTON_NAME)
                    .link(NOTI_REMIND_EXCHANGE_GIFT_LINK)
                .build();
            notificationService.push(customNotificationRequestDto);

            var userGiftIds = userGifts.stream().map(UserGiftUserActiveSmsDto::getUserGiftId).toList();
            userGiftRepository.updateNotifiedStageByIds(userGiftIds, STAGE_NOTIFIED_1D);
        }
        catch (Exception e){
            log.error("Error on 1-day remind exchange gift user notification: {}", e.getMessage(), e);
        }
    }

    private void remind5DayExchangeGift() {
        try{
            var userGifts = userGiftRepository.getUsersOfPendingUserGifts(5, 6, STAGE_NOTIFIED_5D);
            log.warn("[5-days Remind] Number of pending userGifts with active sms notification: {}", userGifts.size());
            if (CollectionUtils.isEmpty(userGifts)) {
                return;
            } 

            Map<Long, List<Long>> userUserGiftMap = new HashMap<>(userGifts.size());
            Set<Long> userIds = new HashSet<>(userGifts.size());
            userGifts.forEach(ug -> {
                userUserGiftMap.computeIfAbsent(ug.getUserId(), k -> new ArrayList<>())
                                .add(ug.getUserGiftId());
                userIds.add(ug.getUserId());
            });

            userRepository.findAllByIdIn(userIds).forEach(user -> {
                var ugIds = userUserGiftMap.get(user.getId());

                for (var ugId : ugIds) {
                    var smsPayload = new SmsRequestDto.UserGiftNotiPayload()
                        .setName(user.getFirstName() + " " + user.getLastName())
                        .setPhone(user.getPhoneNumber())
                        .setUserGiftId(ugId);
                    notificationService.sendSms(NotificationService.USER_GIFT_REMINDER_5_DAYS, smsPayload);
                }
            });

            var userGiftIds = userGifts.stream().map(UserGiftUserActiveSmsDto::getUserGiftId).toList();
            userGiftRepository.updateNotifiedStageByIds(userGiftIds, STAGE_NOTIFIED_5D);
        }
        catch (Exception e){
            log.error("Error on 5-days remind exchange gift zns/sms notification: {}", e.getMessage(), e);
        }
    }
}


