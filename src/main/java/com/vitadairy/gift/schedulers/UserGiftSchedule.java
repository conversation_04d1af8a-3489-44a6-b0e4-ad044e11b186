package com.vitadairy.gift.schedulers;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.entities.GiftReservation;
import com.vitadairy.gift.entities.User;
import com.vitadairy.gift.entities.UserGift;
import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;
import com.vitadairy.gift.features.userGift.services.UserGiftService;
import com.vitadairy.gift.repositories.GiftRepository;
import com.vitadairy.gift.repositories.GiftReservationRepository;
import com.vitadairy.gift.repositories.UserGiftRepository;
import com.vitadairy.gift.repositories.UserRepository;
import com.vitadairy.main.dto.CustomNotificationRequestDto;
import com.vitadairy.main.dto.NotificationData;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.schedules.ScheduledJobRunner;
import com.vitadairy.main.services.NotificationService;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.common.CrmTransactionTypeCode;
import com.vitadairy.zoo.dto.UserGiftNotificationRequestDto;
import com.vitadairy.zoo.enums.FeatureNoti;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service("gsUserGiftSchedule")
@Slf4j
public class UserGiftSchedule implements ScheduledJobRunner {
    private final UserGiftService userGiftService;
    private final UserGiftRepository userGiftRepository;
    private final UserRepository gsUserRepository;
    private final GiftRepository giftRepository;
    private final com.vitadairy.zoo.repositories.UserRepository userRepository;
    private final GiftReservationRepository giftReservationRepository;
    private final TransactionTemplate transactionTemplate;
    private final NotificationService notificationService;

    private final static String NOTI_EXPIRED_GIFT_TITLE = "QUÀ ĐÃ BỊ HỦY!";
    private final static String NOTI_EXPIRED_GIFT_CONTENT = "Rất tiếc quà của bạn đã bị hủy theo quy định đổi quà. Nhấn để xem chi tiết!";
    private final static String NOTI_EXPIRED_GIFT_DESCRIPTION = 
    "Kính gửi Quý Khách hàng,<br/>" +
    "Quà đổi %s đã bị hủy do Quý Khách chưa xác nhận địa chỉ trong thời gian quy định.<br/>" +
    "Vui lòng xem chi tiết chính sách tại: https://vitadairy.vn/thong-bao-chinh-sach-thanh-vien-ung-dung-vitadairy-doi-muong-nhan-qua-2-rara001.html<br/>" +
    "Cần hỗ trợ Quý Khách vui lòng liên hệ 1900 633 559 hoặc Zalo VitaDairy CSKH để được hỗ trợ.<br/>" +
    "Kính mong Quý Khách thông cảm vì sự bất tiện này!";

    
    public UserGiftSchedule(UserGiftService userGiftService, UserGiftRepository userGiftRepository,
            GiftRepository giftRepository,
            @Qualifier("zooUserRepository") com.vitadairy.zoo.repositories.UserRepository userRepository,
            @Qualifier("giftUserRepository") UserRepository gsUserRepository,
            GiftReservationRepository giftReservationRepository,
            @Qualifier("giftTransactionManager") PlatformTransactionManager transactionManager,
            NotificationService notificationService) {
        this.userGiftService = userGiftService;
        this.userGiftRepository = userGiftRepository;
        this.giftRepository = giftRepository;
        this.userRepository = userRepository;
        this.gsUserRepository = gsUserRepository;
        this.giftReservationRepository = giftReservationRepository;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.notificationService = notificationService;
    }

    @Override
    public void run() {
        log.info("User Gift Schedule Start");
        // expired quà bình thường
        this.doExpiredOnPendingGift();

        // mặc dù updateExpiredUserGifts có query cả user_gifts với status = 'PENDING',
        // nhưng những quà PENDING đã được xử lí trước đó bởi doExpiredOnPendingGift
        // doExpiredOnPendingGift là logic thêm vào sau, tách ra để tránh impact code cũ đang chạy
        this.transactionTemplate.executeWithoutResult(status -> {
            this.updateExpiredUserGifts();
            this.cancelOutOfHoldingUserGifts();
            this.remindLastHoldingDate();
        });

        log.info("User Gift Schedule End");
    }

    private void doExpiredOnPendingGift() {
        var expiredUserGifts = this.userGiftRepository.getExpiredUserGiftsWithPendingStatus();
        log.info("[Pending UserGifts] Number of processing user gifts: {}", expiredUserGifts.size());
        if (expiredUserGifts.isEmpty()) {
            return;
        }

        var revertQuantityGiftMap = new HashMap<Integer, Integer>(expiredUserGifts.size());
        var userGiftIds = new HashSet<Long>(expiredUserGifts.size());
        var userIds = new HashSet<Long>(expiredUserGifts.size());
        var userGiftNameMap = new HashMap<Long, List<String>>();

        // group sum of reverted quantity for each gift
        for (var userGift : expiredUserGifts) {
            var quantity = userGift.getQuantity();
            var giftId = userGift.getGiftId();
            var userId = userGift.getUserId();
            revertQuantityGiftMap.compute(giftId, (key, value) -> value == null ? quantity : value + quantity);
            userGiftIds.add(userGift.getUserGiftId());
            userIds.add(userId);
            userGiftNameMap.compute(userId, (key, value) -> value == null ? new ArrayList<>() : value).add(userGift.getGiftName());
        }

        int batchSize = 500;

        this.transactionTemplate.executeWithoutResult(status -> {
            // update status to expired in batches of 500
            Set<Long> userGiftIdBatch = new HashSet<>(batchSize);
            for (var userGiftId : userGiftIds) {
                userGiftIdBatch.add(userGiftId);
                if (userGiftIdBatch.size() == batchSize) {
                    this.userGiftRepository.updateStatusToExpired(userGiftIdBatch);
                    userGiftIdBatch.clear();
                }
            }
            if (!userGiftIdBatch.isEmpty()) { // handle remaining userGiftIds
                this.userGiftRepository.updateStatusToExpired(userGiftIdBatch);
            }

            // revert quantity to each gift
            for (var entry : revertQuantityGiftMap.entrySet()) {
                var giftId = entry.getKey();
                var quantity = entry.getValue();
                this.giftRepository.incrementQuantityAndReduceReward(giftId, quantity);
            }
        });

        // push notification
        var uniqueUserIds = userIds.toArray(new Long[0]);
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(uniqueUserIds, "SYSTEM_CANCEL", FeatureNoti.NOTI_CANCEL_GIFT);
        this.userGiftService.pushNotification(userGiftNotificationRequestDto);

        for (var entry : userGiftNameMap.entrySet()) {
            var userId = entry.getKey();
            var giftNames = entry.getValue();

            for (var giftName : giftNames) {
                CustomNotificationRequestDto customNotificationRequestDto = CustomNotificationRequestDto.builder()
                    .userIds(new Long[]{userId})
                    .title(NOTI_EXPIRED_GIFT_TITLE)
                    .content(NOTI_EXPIRED_GIFT_CONTENT)
                    .description(String.format(NOTI_EXPIRED_GIFT_DESCRIPTION, giftName))
                    .data(NotificationData.builder().deeplink("").description(String.format(NOTI_EXPIRED_GIFT_DESCRIPTION, giftName)).build())
                    .featureNoti(FeatureNoti.NOTI_CANCEL_GIFT)
                    .build();
                this.notificationService.push(customNotificationRequestDto);
            }
        }     
    }

    public void updateExpiredUserGifts() {
        log.info("Update Expired User Gifts Schedule Start");
        userGiftRepository.updateExpiredUserGifts();
        log.info("Update Expired User Gifts Schedule End");
    }

    public void cancelOutOfHoldingUserGifts() {
        long total = this.userGiftRepository.countByOutOfHoldingDate();
        // log.info("[START] CANCELED USER GIFTS THAT HAVE EXPIRED DATE - TOTAL: " + total);
        Set<Long> userIds = new HashSet<>();
        int batchSize = 500;
        int page = (int) Math.ceil((double) total / batchSize);
        for (int i = 0; i < page; i++) {
            List<UserGift> userGifts =
                    this.userGiftRepository.findAllByOutOfHoldingDate(batchSize, batchSize * i);
            // log.info("CANCELED USER GIFTS THAT HAVE EXPIRED DATE - BATCH: " + i);
            for (UserGift userGift : userGifts) {
                this.processingCancel(userGift);
                userIds.add(userGift.getUserId());
            }
        }

        Long[] uniqueUserIds = userIds.toArray(new Long[0]);
        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(uniqueUserIds, "SYSTEM_CANCEL", FeatureNoti.NOTI_CANCEL_GIFT);
        this.userGiftService.pushNotification(userGiftNotificationRequestDto);
        log.info("[SUCCESSFULLY] CANCELED USER GIFTS THAT HAVE EXPIRED DATE");
    }

    public void processingCancel(UserGift userGift) {
        // log.info("[START] CANCELED - ID: " + userGift.getId());
        long userId = userGift.getUserId();
        GiftReservation giftReservation = userGift.getGiftReservationSnapshot();
        var user = this.userRepository.getUserById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found " + userId));
        userGift.setStatus(USER_GIFT_STATUS.CANCEL);
        userGift.setPreOrderStatus(USER_GIFT_STATUS.CANCEL);
        this.giftReservationRepository.incrementMaximumReservationQuantity(giftReservation.getId());
        String type = Constant.HistoryPointType.ADD_POINT;
        String actionType = CrmTransactionTypeCode.RETURN_POINT;
        Float userPoint = user.getGiftPoint() + giftReservation.getCoinToMinusPoint();
        Float point = (float) giftReservation.getCoinToMinusPoint();

        // HistoryPoint historyPoint =
        this.userGiftService.handleTransactionPreOrder(userId, actionType, point, type, userGift,
                point, userPoint);

        Optional<User> gsUser = this.gsUserRepository.findById(userId);
        Float coin = gsUser.get().getCoin() - giftReservation.getCoinToMinusPoint();
        if (coin <= 0) {
            coin = 0F;
        }
        this.gsUserRepository.updateCointById(userId, coin);
        userGiftRepository.save(userGift);
        int rowAffected = this.giftRepository
                .increaseQuantityAndDecreaseQuantityReservation(userGift.getGift().getId());
        if (rowAffected == 0) {
            Optional<Gift> giftOptional = this.giftRepository.findById(userGift.getGift().getId());
            if (giftOptional.isPresent()) {
                Gift gift = giftOptional.get();
                if (gift.getQuantityReservation() < 0) {
                    gift.setQuantityReservation(0);
                }
                this.giftRepository.save(gift);
            } else {
                throw new ApplicationException(
                        "Cannot increase quantity and decrease quantity reservation");
            }
        }


    }

    private void remindLastHoldingDate() {
        Set<Long> userIds = new HashSet<>();
        // Remind Holding Date
        List<UserGift> userGiftList = this.userGiftRepository.findUserGiftsByHoldingDateMinusOne();
        for (UserGift userGift : userGiftList) {
            userIds.add(userGift.getUserId());
        }

        Long[] uniqueUserIds = userIds.toArray(new Long[0]);
        // Push notification
        UserGiftNotificationRequestDto userGiftNotificationRequestDto =
                new UserGiftNotificationRequestDto(uniqueUserIds, "LASTDAY_OUTDATE", FeatureNoti.NOTI_PRE_ORDER_GIFT);
        userGiftService.pushNotification(userGiftNotificationRequestDto);
    }

}

