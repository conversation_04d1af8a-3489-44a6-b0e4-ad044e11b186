package com.vitadairy.gift.validator;

import com.vitadairy.gift.entities.UserGift.USER_GIFT_STATUS;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class UserGiftStatusValidator implements ConstraintValidator<ValidUserGiftStatus, USER_GIFT_STATUS> {
    @Override
    public boolean isValid(USER_GIFT_STATUS value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // a required field set to false
        }
        return value == USER_GIFT_STATUS.EXCHANGED || value == USER_GIFT_STATUS.CANCEL;
    }
}
