package com.vitadairy.gift.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = UserGiftStatusValidator.class)
@Documented
public @interface ValidUserGiftStatus {
    String message() default "Invalid status. Must be EXCHANGED or CANCEL";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}