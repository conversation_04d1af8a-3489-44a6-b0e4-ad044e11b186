package com.vitadairy.gift.entities;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;

import com.vitadairy.gift.converter.JsonbToGiftEntityConverter;
import jakarta.persistence.Convert;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "gs_user_gifts", indexes = {
        @Index(name = "idx_user_gifts_user_id", columnList = "user_id")
})
@Entity
public class UserGift {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ManyToOne()
    @JoinColumn(name = "gift_id", nullable = false)
    // @JsonIgnoreProperties({ "userGifts", "hibernateLazyInitializer", "handler" })
    // // Exclude Hibernate properties
    private Gift gift;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private USER_GIFT_STATUS status = USER_GIFT_STATUS.PRE_ORDER;

    @Column(name = "holding_date") // Thời hạn giữ quà => filter để chạy schedule
    private Timestamp holdingDate;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "used_quantity", nullable = true) // Số point đã đặt trước
    private Integer usedQuantity;

    @Column(name = "reservation_point", nullable = false) // Số point đã đặt trước
    private Integer reservationPoint;

    @Column(name = "point", nullable = true) // Số point đã đặt trước
    private Float point = 0F;

    @Column(name = "stock_point") // Số point tồn
    private float stockPoint = 0F;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dynamic_data", columnDefinition = "JSONB", nullable = true)
    private UserGiftDynamicData dynamicData = null;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "TIMESTAMP")
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "TIMESTAMP")
    private Instant updatedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "gift_snapshot", columnDefinition = "JSONB")
    //@Convert(converter = JsonbToGiftEntityConverter.class)
    private Gift giftSnapshot;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "gift_reservation_snapshot", columnDefinition = "JSONB")
    private GiftReservation giftReservationSnapshot;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "recipient_snapshot", columnDefinition = "JSONB")
    private Map<String, String> recipientSnapshot;

    @Enumerated(EnumType.STRING)
    @Column(name = "pre_order_status")
    private USER_GIFT_STATUS preOrderStatus;

    @Column(name = "transaction_code")
    private String transactionCode;

    @Column(name = "extended_search")
    private String extendedSearch;

    @Column(name = "notified_at", columnDefinition = "TIMESTAMP")
    private LocalDateTime notifiedAt;

    @Column(name = "last_notified_stage")
    private Integer lastNotifiedStage;

    public enum USER_GIFT_STATUS {
        PRE_ORDER("PRE_ORDER"),
        PENDING("PENDING"),
        CANCEL("CANCEL"),
        EXCHANGED("EXCHANGED"),
        EXPIRED("EXPIRED"),
        TOPUP("TOPUP"),
        IN_PROCESS("IN_PROCESS"),
        USED("USED"),
        UNUSED("UNUSED"),
        FAILED("FAILED"),
        TOPUP_IS_PROCESSING("TOPUP_IS_PROCESSING"),
        LOCK("LOCK"),
        REUSED("REUSED"),
        FAIL("FAIL"),
        RECEIVED("RECEIVED"),
        CANCELED("CANCELED"),
        BLOCK("BLOCK"),
        NEED_REUSE("NEED_REUSE");

        private final String value;

        USER_GIFT_STATUS(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static USER_GIFT_STATUS fromValue(String value) {
            for (USER_GIFT_STATUS status : USER_GIFT_STATUS.values()) {
                if (status.getValue().equals(value)) {
                    return status;
                }
            }
            return null;
        }
    }
}
