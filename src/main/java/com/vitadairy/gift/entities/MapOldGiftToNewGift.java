package com.vitadairy.gift.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "gs_map_old_gift_to_new_gift", indexes = {
        @Index(name = "idx_map_old_gift_to_new_gift_old_gift_id", columnList = "old_gift_id")
})
@Entity
public class MapOldGiftToNewGift {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "old_gift_id", nullable = false)
    private Long oldGiftId;

    @ManyToOne()
    @JoinColumn(name = "new_gift_id", nullable = false)
    // @JsonIgnoreProperties({ "userGifts", "hibernateLazyInitializer", "handler" })
    // // Exclude Hibernate properties
    private Gift newGift;
}
