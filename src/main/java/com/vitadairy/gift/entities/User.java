package com.vitadairy.gift.entities;

import com.vitadairy.gift.common.UserFavoriteConstant;
import com.vitadairy.main.entities.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@Entity
@Table(name = UserFavoriteConstant.TABLE_NAME)
public class User extends BaseEntity {
    @Id
    private Long id; // user_id

    @Column(name = "fav_gift_ids", columnDefinition = "integer[]")
    private Integer[] giftIds;

    @Column(name = "coin")
    private Float coin;
}
