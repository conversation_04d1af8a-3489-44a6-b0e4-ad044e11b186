package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gs_gift_reservations")
public class GiftReservation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonBackReference
    @JoinColumn(name = "gift_id", nullable = false)
    private Gift gift;

    @Column(name = "reservation_point", nullable = false)
    private Integer reservationPoint;

    @Column(name = "reservation_percent")
    private Integer reservationPercent;

    @Column(name = "limit_reservation_time") // Giới hạn đặt trc và huỷ quà
    private Integer limitReservationTime;

    @Column(name = "discount_reservation_point") // Số lượng quà có thể đặt trc
    private Integer discountReservationPoint;

    @Column(name = "discount_reservation_percent")
    private Integer discountReservationPercent;

    @Column(name = "reservation_expired_days") // Thời hạn ngày giữ quà (Số ngày)
    private Integer reservationExpiredDays;

    @Column(name = "min_coins_for_reservation")
    private Integer minCoinsForReservation;

    @Column(name = "maximum_reservation_quantity")
    private Integer maximumReservationQuantity;

    @Column(name = "coin_to_minus_point")
    private Float coinToMinusPoint;

    @Column(name = "coin_to_minus_percent")
    private Integer coinToMinusPercent;

}
