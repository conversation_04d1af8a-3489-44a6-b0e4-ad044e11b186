package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.utils.JsonNodeUtils;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.IOException;
import java.time.Instant;

@NoArgsConstructor
@Data
@Builder
@Setter
@Getter
@JsonDeserialize(using = GUserGiftDynamicDataDeserializer.class)
public class UserGiftDynamicData {
    @JsonProperty("action_type")
    private String actionType;
    @JsonProperty("recipient_phone")
    private String recipientPhone;
    @JsonProperty("used_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant usedDate;
    @JsonProperty("is_used")
    private Boolean isUsed;
    @JsonProperty("voucher_ref_id")
    private String voucherRefId;
    @JsonProperty("voucher_code")
    private String voucherCode;
    @JsonProperty("voucher_link")
    private String voucherLink;
    @JsonProperty("expiry_date")
    private String expiryDate;
    @JsonProperty("telco")
    private String telco;
    @JsonProperty("topup_transaction_status_time_check")
    private String topupTransactionStatusTimeCheck;
    @JsonProperty("topup_transaction_status_number_check")
    private Integer topupTransactionStatusNumberCheck;
    @JsonProperty("is_reusing")
    private Boolean isReusing;
    @JsonProperty("exchange_date")
    private String exchangeDate;
    @JsonProperty("pushed_notify_enought_point_to_finish_pre_order")
    private Boolean pushedNotifyEnoughtPointToFinishPreOrder;
    @JsonProperty("event_add_can_loyalty_program_sf_id")
    private String eventAddCanLoyaltyProgramSfId;
    @JsonProperty("map_user_to_brand_point_id_send_sf")
    private String mapUserToBrandPointIdSendSf;
    @JsonProperty("map_user_to_brand_point_id")
    private Long mapUserToBrandPointId;

    @JsonCreator
    public UserGiftDynamicData(
            @JsonProperty("action_type") String actionType,
            @JsonProperty("recipient_phone") String recipientPhone,
            @JsonProperty("used_date") Instant usedDate,
            @JsonProperty("is_used") Boolean isUsed,
            @JsonProperty("voucher_ref_id") String voucherRefId,
            @JsonProperty("voucher_code") String voucherCode,
            @JsonProperty("voucher_link") String voucherLink,
            @JsonProperty("expiry_date") String expiryDate,
            @JsonProperty("telco") String telco,
            @JsonProperty("topup_transaction_status_time_check") String topupTransactionStatusTimeCheck,
            @JsonProperty("topup_transaction_status_number_check") Integer topupTransactionStatusNumberCheck,
            @JsonProperty("is_reusing") Boolean isReusing,
            @JsonProperty("exchange_date") String exchangeDate,
            @JsonProperty("pushed_notify_enought_point_to_finish_pre_order") Boolean pushedNotifyEnoughtPointToFinishPreOrder,
            @JsonProperty("event_add_can_loyalty_program_sf_id") String eventAddCanLoyaltyProgramSfId,
            @JsonProperty("map_user_to_brand_point_id_send_sf") String mapUserToBrandPointIdSendSf,
            @JsonProperty("map_user_to_brand_point_id") Long mapUserToBrandPointId) {
        this.actionType = actionType;
        this.recipientPhone = recipientPhone;
        this.usedDate = usedDate;
        this.isUsed = isUsed;
        this.voucherRefId = voucherRefId;
        this.voucherCode = voucherCode;
        this.voucherLink = voucherLink;
        this.expiryDate = expiryDate;
        this.telco = telco;
        this.topupTransactionStatusTimeCheck = topupTransactionStatusTimeCheck;
        this.topupTransactionStatusNumberCheck = topupTransactionStatusNumberCheck;
        this.isReusing = isReusing;
        this.exchangeDate = exchangeDate;
        this.pushedNotifyEnoughtPointToFinishPreOrder = pushedNotifyEnoughtPointToFinishPreOrder;
        this.eventAddCanLoyaltyProgramSfId = eventAddCanLoyaltyProgramSfId;
        this.mapUserToBrandPointIdSendSf = mapUserToBrandPointIdSendSf;
        this.mapUserToBrandPointId = mapUserToBrandPointId;
    }
}

class GUserGiftDynamicDataDeserializer extends JsonDeserializer<UserGiftDynamicData> {
    @Override
    public UserGiftDynamicData deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException, JacksonException {
        return JsonNodeUtils.deserialize(jp, UserGiftDynamicData.class);
    }
}