package com.vitadairy.gift.entities;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

import com.vitadairy.gift.constants.ExportStatus;
import com.vitadairy.gift.constants.ExportType;
import com.vitadairy.main.entities.BaseEntity;

import jakarta.persistence.Column;

@Entity
@Getter
@Setter
@Table(name = "gs_export_history")
public class ExportHistory extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "export_type")
    @Enumerated(EnumType.STRING)
    private ExportType exportType;

    @Column(name = "filter")
    private String filter;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ExportStatus status;

    @Column(name = "result_file_path")
    private String resultFilePath;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "started_at", columnDefinition = "TIMESTAMP")
    private Instant startedAt;
}
