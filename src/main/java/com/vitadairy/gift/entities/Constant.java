package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.entities.BaseEntity;
import com.vitadairy.main.utils.JsonNodeUtils;
import jakarta.persistence.*;
import lombok.*;
import lombok.Builder.Default;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.IOException;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = "gs_constants", uniqueConstraints = @UniqueConstraint(columnNames = {"key_alpha", "key_beta"}))
// @TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class Constant extends BaseEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "key_alpha", nullable = false, updatable = false)
    private String keyAlpha;

    @Column(name = "key_beta", nullable = false, updatable = false)
    private String keyBeta;

    @Column(name = "value", nullable = false, updatable = false)
    private String value;

    @Default
    @Column(name = "priority")
    private Integer priority = Integer.MAX_VALUE;

    @Default
    @Column(name = "is_active")
    private Boolean isActive = true;

    // @Convert(converter = JsonbConverter.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dynamic_data", columnDefinition = "JSONB")
    private ConstantDynamicData dynamicData;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonDeserialize(using = DynamicDataDeserializer.class)
    public static class ConstantDynamicData {
        private String description;
        private String name;
        private Integer updatedById;
        private String label;
        private String labelCode;
        private String color;
        private String icon;
        @JsonProperty("dynamic_filter")
        private DynamicFilter dynamicFilter;
    }
}


class DynamicDataDeserializer extends JsonDeserializer<Constant.ConstantDynamicData> {
    @Override
    public Constant.ConstantDynamicData deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException, JacksonException {
        return JsonNodeUtils.deserialize(jp, Constant.ConstantDynamicData.class);
    }
}