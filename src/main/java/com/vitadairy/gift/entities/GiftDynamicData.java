package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.utils.JsonNodeUtils;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.math.BigDecimal;

@NoArgsConstructor
@Data
@Builder
//@JsonDeserialize(using = GiftDataDeserializer.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GiftDynamicData {
    private String description;
    private String link;
    private BigDecimal weight;
    private BigDecimal height;
    private BigDecimal width;
    private BigDecimal length;
    private String rewardAppGiftCode;
    private String smsGiftId;
    private Boolean activeSmsNotification;
    private String sourceGift;
    private String selectGift;
    private GiftDynamicFilter dynamicFilter;
    private Long brandId;
    private Long eProductId;
    private String eProductName;
    private Long priceId;
    private String bkidsCourseType;
    private String bkidsCourseId;
    private String bkidsCourseName;
    private String bkidsCourseClass;
    private Long bkidsCoursePrice;
    private Long giftCategoryId;
    private String code;

    @JsonCreator
    public GiftDynamicData(@JsonProperty("description") String description,
                           @JsonProperty("link") String link,
                           @JsonProperty("weight") BigDecimal weight,
                           @JsonProperty("height") BigDecimal height,
                           @JsonProperty("width") BigDecimal width,
                           @JsonProperty("length") BigDecimal length,
                           @JsonProperty("rewardAppGiftCode") String rewardAppGiftCode,
                           @JsonProperty("smsGiftId") String smsGiftId,
                           @JsonProperty("activeSmsNotification") Boolean activeSmsNotification,
                           @JsonProperty("sourceGift") String sourceGift,
                           @JsonProperty("selectGift") String selectGift,
                           @JsonProperty("dynamicFilter") GiftDynamicFilter dynamicFilter,
                           @JsonProperty("brandId") Long brandId,
                           @JsonProperty("eProductId") Long eProductId,
                           @JsonProperty("eProductName") String eProductName,
                           @JsonProperty("priceId") Long priceId,
                           @JsonProperty("bkidsCourseType") String bkidsCourseType,
                           @JsonProperty("bkidsCourseId") String bkidsCourseId,
                           @JsonProperty("bkidsCourseName") String bkidsCourseName,
                           @JsonProperty("bkidsCourseClass") String bkidsCourseClass,
                           @JsonProperty("bkidsCoursePrice") Long bkidsCoursePrice,
                           @JsonProperty("giftCategoryId") Long giftCategoryId,
                           @JsonProperty("code") String code) {
        this.description = description;
        this.link = link;
        this.weight = weight;
        this.height = height;
        this.width = width;
        this.length = length;
        this.rewardAppGiftCode = rewardAppGiftCode;
        this.smsGiftId = smsGiftId;
        this.activeSmsNotification = activeSmsNotification;
        this.sourceGift = sourceGift;
        this.selectGift = selectGift;
        this.dynamicFilter = dynamicFilter;
        this.brandId = brandId;
        this.eProductId = eProductId;
        this.eProductName = eProductName;
        this.priceId = priceId;
        this.bkidsCourseType = bkidsCourseType;
        this.bkidsCourseId = bkidsCourseId;
        this.bkidsCourseName = bkidsCourseName;
        this.bkidsCourseClass = bkidsCourseClass;
        this.bkidsCoursePrice = bkidsCoursePrice;
        this.giftCategoryId = giftCategoryId;
        this.code = code;
    }
}

class GiftDataDeserializer extends JsonDeserializer<GiftDynamicData> {
    @Override
    public GiftDynamicData deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException, JacksonException {
        return JsonNodeUtils.deserialize(jp, GiftDynamicData.class);
    }
}