package com.vitadairy.gift.entities;

import java.time.OffsetDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.vitadairy.gift.enums.GiftTimeExchangeGiftPeriodTypeEnum;
import com.vitadairy.main.entities.BaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gs_gift_time_exchange_gifts")
public class GiftTimeExchangeGift extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "period_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private GiftTimeExchangeGiftPeriodTypeEnum periodType;

    @Column(name = "start_date", nullable = false)
    private OffsetDateTime startDate;

    @Column(name = "end_date", nullable = false)
    private OffsetDateTime endDate;

    @Column(name = "limit_exchange_gift", nullable = false)
    private Integer limitExchangeGift;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonBackReference
    @JoinColumn(name = "gift_id", nullable = false)
    private Gift gift;

    @OneToMany(mappedBy = "giftTimeExchangeGift", fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<GiftTimeExchangeGiftDetail> giftTimeExchangeGiftDetails;
}
