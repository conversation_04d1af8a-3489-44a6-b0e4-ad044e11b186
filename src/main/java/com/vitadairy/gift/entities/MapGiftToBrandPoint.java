package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Table(name = "map_gift_to_brand_points")
@Entity
public class MapGiftToBrandPoint {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne()
    @JsonBackReference
    @JoinColumn(name = "gift_id", nullable = false)
    private Gift gift;

    @Column(name = "brand_id", nullable = false)
    private Integer brandId;

    @Column(name = "brand_point", nullable = false)
    private Integer brandPoint;

    public Boolean checkIsExchangeBrandGift() {
        return this.brandPoint > 0;
    }
}
