package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Builder
@Data
public class GiftDynamicFilter {
    private String code;
    private String name;
    private String valueCode;
    // @JsonProperty("first_level_code")
    private String firstLevelCode;
    private String firstLevelValueCode;
    // @JsonProperty("second_level_code")
    private String secondLevelCode;
    private String secondLevelValueCode;

    @JsonCreator
    public GiftDynamicFilter(@JsonProperty("code") String code,
            @JsonProperty("name") String name,
            @JsonProperty("valueCode") String valueCode,
            @JsonProperty("firstLevelCode") String firstLevelCode,
            @JsonProperty("firstLevelValueCode") String firstLevelValueCode,
            @JsonProperty("secondLevelCode") String secondLevelCode,
            @JsonProperty("secondLevelValueCode") String secondLevelValueCode) {
        this.code = code;
        this.name = name;
        this.valueCode = valueCode;
        this.firstLevelCode = firstLevelCode;
        this.firstLevelValueCode = firstLevelValueCode;
        this.secondLevelCode = secondLevelCode;
        this.secondLevelValueCode = secondLevelValueCode;
    }
}
