package com.vitadairy.gift.entities;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.vitadairy.gift.enums.GiftStatusEnum;
import com.vitadairy.gift.features.gifts.constants.GiftTypeEnum;
import com.vitadairy.main.entities.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder.Default;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "gs_gifts")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Gift extends BaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(columnDefinition = "_text", nullable = false)
    private List<String> images;

    @Column(name = "transport_type_code", nullable = false)
    private String transportTypeCode;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    @Default
    private GiftStatusEnum status = GiftStatusEnum.ENABLED;

    @Column(name = "name", nullable = false)
    private String name;

    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "badge_codes", columnDefinition = "_text")
    private List<String> badgeCodes;

    @Column(name = "category_code", nullable = false)
    private String categoryCode;

    @JdbcTypeCode(SqlTypes.ARRAY)
    // @Convert(converter = StringArrayConverter.class)
    @Column(name = "tier_codes", nullable = false, columnDefinition = "_text")
    private List<String> tierCodes;

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private GiftTypeEnum type;

    @Column(name = "point", nullable = false)
    private Integer point;

    @Column(name = "price")
    private BigInteger price;

    @JdbcTypeCode(SqlTypes.ARRAY)
    // @Convert(converter = StringArrayConverter.class)
    @Column(name = "hidden_tags", columnDefinition = "_text")
    private List<String> hiddenTags;

    @Column(name = "sf_number")
    private Long sfNumber;

    @Column(name = "start_date")
    private Timestamp startDate;

    @Column(name = "end_date")
    private Timestamp endDate;

    @Column(name = "expire_date")
    private Timestamp expireDate;

    @Column(name = "expire_hour")
    private Integer expireHour;

    @Column(name = "sct_number")
    @JsonProperty("sct_number")
    private String sctNumber;

    @Column(name = "tpm_number")
    private String tpmNumber;

    @Transient
    @JsonProperty("sctNumber")
    private String sctNumberJson;

    @Default
    @Column(name = "quantity", updatable = false)
    private Long quantity = (long) 0;

    @Default
    @Column(name = "inventory", updatable = false) // quantity_in_stock
    private Integer inventory = 0;

    @Default
    @Column(name = "quantity_limit_for_booking", updatable = false)
    private Integer quantityLimitForBooking = 0;

    @Column(name = "purchase_option")
    private String purchaseOption;

    @Column(name = "event_id")
    private Long eventId;

    @Default
    @Column(name = "quantity_reward", updatable = false)
    private Integer quantityReward = 0;

    @Default
    @Column(name = "quantity_reservation", updatable = false)
    private Integer quantityReservation = 0;

    @Column(name = "is_allow_reservation", columnDefinition = "BOOLEAN default false")
    private Boolean isAllowReservation;

    @Column(name = "priority")
    private Integer priority;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "dynamic_data", columnDefinition = "JSONB")
    private GiftDynamicData dynamicData;

    @OneToOne(mappedBy = "gift",
            cascade = CascadeType.ALL,
            orphanRemoval = true)
    @JsonManagedReference // forward side for JSON
    private MapGiftToBrandPoint brandPoint;

    @Transient
    private GiftReservation giftReservation;

    @Transient
    private Boolean isEligible;

    @Transient
    private Boolean isDisabledPreOrderBt;

    public void setBrandPoint(MapGiftToBrandPoint map) {
        this.brandPoint = map;
        if (map != null && map.getGift() != this) {
            map.setGift(this);
        }
    }

    public Boolean checkIsGiftBrand() {
        return Objects.nonNull(this.brandPoint) ?
                Boolean.TRUE : Boolean.FALSE;
    }

}
