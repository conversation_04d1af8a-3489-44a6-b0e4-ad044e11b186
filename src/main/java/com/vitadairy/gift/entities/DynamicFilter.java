package com.vitadairy.gift.entities;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DynamicFilter {

    private String name;
    private String code;
    private int priority;
    @JsonProperty("is_active")
    private boolean isActive;
    List<DynamicFilterValue> value;

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterValue {
        private String name;
        private String code;
        @JsonProperty("first_level")
        List<DynamicFilterFirstLevel> firstLevel;
    }


    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterFirstLevel {
        private String name;
        @JsonProperty("is_active")
        private boolean isActive;
        private String code;
        private List<DynamicFirstLevelValue> value;
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFirstLevelValue {
        private String name;
        private String code;
        @JsonProperty("second_level")
        private List<DynamicFilterSecondLevel> secondLevel;
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterSecondLevel {
        private String name;
        @JsonProperty("is_active")
        private Boolean isActive;
        private String code;
        private List<DynamicFilterSecondLevelValue> value;
    }

    @Getter
    @Setter
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicFilterSecondLevelValue {
        private String code;
        private String name;
    }
}
