package com.vitadairy.gift.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.vitadairy.gift.entities.Gift;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.io.IOException;
import java.time.Instant;

@Converter(autoApply = true)
public class JsonbToGiftEntityConverter implements AttributeConverter<Gift, String> {
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .registerModule(new SimpleModule().addDeserializer(Instant.class, new InstantDeserializer()))
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    @Override
    public String convertToDatabaseColumn(Gift attribute) {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Error converting TargetEntity to JSON", e);
        }
    }

    @Override
    public Gift convertToEntityAttribute(String dbData) {
        try {
            return objectMapper.readValue(dbData, Gift.class);
        } catch (IOException e) {
            throw new IllegalArgumentException("Error converting JSON to TargetEntity", e);
        }
    }
}
