package com.vitadairy.gift.aspects;

import com.vitadairy.auth.utils.SecurityUtil;
import com.vitadairy.gift.features.dynamicFilter.dto.request.CreateOrUpdateDynamicFilterRequest;
import com.vitadairy.gift.features.gift_category.dto.request.GiftCategoryRequestDto;
import com.vitadairy.gift.features.gifts.dto.request.GiftRequestDto;
import com.vitadairy.gift.features.hidden_tags.dto.request.HiddenTagRequestDto;
import com.vitadairy.zoo.entities.AdminActionHistory;
import com.vitadairy.zoo.enums.EnumAdminActionHistoryActionName;
import com.vitadairy.zoo.enums.EnumNameAdminMenuModule;
import com.vitadairy.zoo.repositories.AdminActionHistoryRepository;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Aspect
@Component
@Slf4j
public class GiftAdminActionLoggerAspect {

    private final AdminActionHistoryRepository adminActionHistoryRepository;

    public GiftAdminActionLoggerAspect(
            @Qualifier("zooAdminActionHistoryRepository") AdminActionHistoryRepository adminActionHistoryRepository
    ) {
        this.adminActionHistoryRepository = adminActionHistoryRepository;
    }

    /**
     * Pointcut targeting controllers annotated with @GiftEnableLoggingAdminActionInterface.
     */
    @Pointcut("@within(com.vitadairy.gift.interfaces.GiftEnableLoggingAdminActionInterface)")
    public void enableLoggingAdminActionControllers() {}

    /**
     * Runs only after the controller method executes successfully.
     */
    @AfterReturning(pointcut = "enableLoggingAdminActionControllers()", returning = "result")
    public void logAdminAction(JoinPoint joinPoint, Object result) {
        // Proceed with method execution after logging
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes()).getRequest();

            if (request == null) {
                return;
            }

            String method = request.getMethod();
            String uri = request.getRequestURI();
            String adminEmail = SecurityUtil.getCurrentAdminEmail();
            String menuName = mapControllerBaseUrlToMenuName(uri);
            String action = mapHttpMethodToAction(uri, method);
            String recordIdentity = extractEntityName(joinPoint.getArgs());

            // Log the admin action only if all necessary information is present
            if (adminEmail != null && menuName != null) {
                if (recordIdentity != null && action != null) {
                    AdminActionHistory adminActionHistory = new AdminActionHistory();
                    adminActionHistory.setAdminName(adminEmail);
                    adminActionHistory.setAdminMenu(menuName);
                    adminActionHistory.setAdminAction(action);
                    adminActionHistory.setRecordIdentity(recordIdentity);

                    adminActionHistoryRepository.save(adminActionHistory);
                }
            } else {
                log.warn("Admin action log skipped due to missing information: [adminEmail={}, menuName={}, action={}, recordIdentity={}]",
                        adminEmail, menuName, action, recordIdentity);
            }
        } catch (Exception e) {
            log.error("Failed to log admin action: {}", e.getMessage(), e);
        }
    }

    /**
     * Maps the HTTP method and URI to an admin action label.
     */
    private String mapHttpMethodToAction(String uri, String method) {
        // Skip logging for specific endpoints
        if (uri.contains("v4/gs/admin/gifts/export")) {
            return null;
        }

        return switch (method) {
            case "POST" -> EnumAdminActionHistoryActionName.CREATE.getLabel();
            case "PUT", "PATCH" -> EnumAdminActionHistoryActionName.UPDATE.getLabel();
            case "DELETE" -> EnumAdminActionHistoryActionName.DELETE.getLabel();
            default -> null;
        };
    }

    /**
     * Maps URI to a friendly admin menu name (used in the audit log).
     */
    private String mapControllerBaseUrlToMenuName(String uri) {
        if (uri.contains("v4/gs/admin/gifts")) {
            return EnumNameAdminMenuModule.QUAN_LY_QUA.getLabel();
        }
        if (uri.contains("v4/gs/admin/gift-categories")) {
            return EnumNameAdminMenuModule.QUAN_LY_QUA.getLabel();
        }
        if (uri.contains("v4/os/admin/orders")) {
            return EnumNameAdminMenuModule.QUAN_LY_DON_HANG.getLabel();
        }
        if (uri.contains("v4/gs/admin/hidden-tags")) {
            return EnumNameAdminMenuModule.QUAN_LY_HIDDEN_USER_TAG.getLabel();
        }
        if (uri.contains("v4/gs/dynamic-filter")) {
            return EnumNameAdminMenuModule.QUAN_LY_BO_LOC.getLabel();
        }

        // Add more mappings for other modules here if needed
        return null;
    }

    /**
     * Attempts to extract a displayable name or identifier for the entity being affected.
     */
    private String extractEntityName(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof GiftRequestDto dto) {
                return dto.getName(); // Extract gift name for logging
            }
            if (arg instanceof GiftCategoryRequestDto dto) {
                return dto.getName(); // Extract gift name for logging
            }
            if (arg instanceof HiddenTagRequestDto dto) {
                return dto.getCode(); // Extract hidden tag code for logging
            }
            if (arg instanceof CreateOrUpdateDynamicFilterRequest dto) {
                return dto.getName(); // Extract dynamic filter name for logging
            }

            // Add more entity types here as needed
        }

        return null;
    }
}
