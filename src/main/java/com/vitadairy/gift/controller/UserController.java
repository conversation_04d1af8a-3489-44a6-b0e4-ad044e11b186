package com.vitadairy.gift.controller;

import com.vitadairy.gift.services.UserService;
import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.helper.AuthenticationHelper;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.zoo.dto.UpdateUserAddressRequestDto;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("zooUserController")
@RequestMapping("v4/gs/users")
@AllArgsConstructor
public class UserController {
    @Qualifier("zooUserService")
    private final UserService userService;
    private final ResponseFactory responseFactory;


    @PatchMapping("/update-address")
    public ResponseEntity<?> updateUserAddress(@RequestBody UpdateUserAddressRequestDto dto) {
        try{
            var principal = AuthenticationHelper.getCurrentUser();
            dto.setUserId(principal == null ? null : principal.getUserId());
            userService.updateUserAddress(dto);
        }
        catch (Exception ex){
            return responseFactory.errorDto(ex.getMessage());
        }
        return responseFactory.success(EntityResponse.<Boolean>entityBuilder()
                .statusCode(HttpStatus.OK)
                .msg("ok")
                .build());
    }
}
