package com.vitadairy.gift.controller;

import com.vitadairy.gift.services.GiftHealthService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("GiftHealthController")
@RequestMapping("v4/gs/no-auth-token/health")
@AllArgsConstructor
public class GiftHealthController {
    @Qualifier("GiftHealthService")
    private final GiftHealthService giftHealthService;

    @GetMapping("/startup-probe")
    public ResponseEntity<String> startupProbe() {
        return this.giftHealthService.startupProbe();
    }

    @GetMapping("/liveness-probe")
    public ResponseEntity<String> livenessProbe() {
        return this.giftHealthService.livenessProbe();
    }
}
