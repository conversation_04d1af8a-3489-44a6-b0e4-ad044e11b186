package com.vitadairy.gift.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * MultiTransactionHandler
 *
 * A utility to manage multiple transaction managers with rollback/commit handling.
 */
@Slf4j
@Service
public class MultiTransactionHandler {

    // Keep insertion order (LinkedHashMap)
    private final Map<String, TransactionManagerItem> transactions = new LinkedHashMap<>();

    private final Map<String, LazyTransactionItem> lazyTransactions = new LinkedHashMap<>();

    /**
     * Initialize a new transaction.
     *
     * @param manager The transaction manager.
     * @param name    Unique transaction name (used later for setting rollback actions).
     */
    public void initTransaction(PlatformTransactionManager manager, String name) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setName(name);
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = manager.getTransaction(def);
        transactions.put(name, new TransactionManagerItem(name, manager, status));
    }

    public void registerTransaction(PlatformTransactionManager manager, String name) {
        lazyTransactions.put(name, new LazyTransactionItem(name, manager, DefaultTransactionDefinition.PROPAGATION_REQUIRED));
    }

    /**
     * Attach a manual rollback action to a specific transaction by name.
     *
     * @param name    The transaction name.
     * @param action  The rollback action.
     */
    public void setManualRollbackAction(String name, Runnable action) {
        TransactionManagerItem item = transactions.get(name);
        if (item != null) {
            item.setManualRollbackAction(action);
        } else {
            throw new IllegalArgumentException("Transaction with name '" + name + "' not found");
        }
    }

    public void setLazyManualRollbackAction(String name, Runnable action) {
        LazyTransactionItem item = lazyTransactions.get(name);
        if (item != null) {
            item.setManualRollbackAction(action);
        } else {
            throw new IllegalArgumentException("Transaction with name '" + name + "' not found");
        }
    }

    /**
     * Commit all transactions in reverse order (last-in, first-out).
     */
    public void commitAll() {
        var items = transactions.values().toArray(new TransactionManagerItem[0]);
        for (int i = items.length - 1; i >= 0; i--) {
            items[i].manager().commit(items[i].status());
        }
    }

    public void lazyCommitAll() {
        for (LazyTransactionItem item : lazyTransactions.values()) {
            if (item.isStarted()) {
                item.commit();
            }
        }
    }

    /**
     * Rollback all transactions in reverse order.
     * - Normal rollback if still active.
     * - Manual rollback if already completed.
     */
    public void rollbackAll() {
        var items = transactions.values().toArray(new TransactionManagerItem[0]);
        for (int i = items.length - 1; i >= 0; i--) {
            TransactionManagerItem item = items[i];
            if (!item.status().isCompleted()) {
                item.manager().rollback(item.status());
                log.error("rollback {}", item.name());
            } else {
                if (item.manualRollbackAction() != null) {
                    item.manualRollbackAction().run();
                }
            }
        }
    }

    public void lazyRollbackAll() {
        for (LazyTransactionItem item : lazyTransactions.values()) {
            item.rollback();
        }
    }

    public void beginTransaction(String name) {
        LazyTransactionItem item = lazyTransactions.get(name);
        if (item != null) {
            item.begin();
        } else {
            throw new IllegalArgumentException("Transaction '" + name + "' is not registered.");
        }
    }

    public boolean isStarted(String name) {
        LazyTransactionItem item = lazyTransactions.get(name);
        return item != null && item.isStarted();
    }
}

