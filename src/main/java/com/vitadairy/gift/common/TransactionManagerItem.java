package com.vitadairy.gift.common;

import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;

/**
 * TransactionManagerItem
 *
 * Represents a single transaction managed by a PlatformTransactionManager.
 * Stores the transaction manager, transaction status, transaction name, and an optional manual rollback action.
 */
public class TransactionManagerItem {

    // The name identifying this transaction (used for logging or debugging)
    private final String name;

    // The transaction manager responsible for this transaction
    private final PlatformTransactionManager manager;

    // The current status of this transaction (e.g., active, committed, rolled back)
    private final TransactionStatus status;

    // Optional manual rollback action to execute if the transaction is already completed
    private Runnable manualRollbackAction;

    /**
     * Constructor to create a TransactionManagerItem with the given name, manager, and transaction status.
     *
     * @param name    The unique name of the transaction
     * @param manager The transaction manager handling this transaction
     * @param status  The current status of the transaction
     */
    public TransactionManagerItem(String name, PlatformTransactionManager manager, TransactionStatus status) {
        this.name = name;
        this.manager = manager;
        this.status = status;
    }

    /**
     * Get the name of the transaction.
     *
     * @return the transaction name
     */
    public String name() {
        return name;
    }

    /**
     * Get the PlatformTransactionManager associated with this transaction.
     *
     * @return the transaction manager
     */
    public PlatformTransactionManager manager() {
        return manager;
    }

    /**
     * Get the current TransactionStatus.
     *
     * @return the transaction status
     */
    public TransactionStatus status() {
        return status;
    }

    /**
     * Get the manual rollback action, if set.
     *
     * @return the manual rollback Runnable, or null if not set
     */
    public Runnable manualRollbackAction() {
        return manualRollbackAction;
    }

    /**
     * Set a manual rollback action.
     * This will be executed if the transaction has already been completed and cannot be rolled back normally.
     *
     * @param manualRollbackAction the Runnable containing custom rollback logic
     */
    public void setManualRollbackAction(Runnable manualRollbackAction) {
        this.manualRollbackAction = manualRollbackAction;
    }
}
