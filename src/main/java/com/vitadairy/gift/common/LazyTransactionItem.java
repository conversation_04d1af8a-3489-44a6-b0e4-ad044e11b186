package com.vitadairy.gift.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

@Slf4j
public class LazyTransactionItem {
    private String name;
    private PlatformTransactionManager manager;
    private DefaultTransactionDefinition definition;
    private Runnable manualRollbackAction = null;
    private TransactionStatus status = null;

    public LazyTransactionItem(String name, PlatformTransactionManager manager, int propagationBehavior) {
        this.name = name;
        this.manager = manager;
        this.definition = new DefaultTransactionDefinition();
        this.definition.setName(name);
        this.definition.setPropagationBehavior(propagationBehavior);
    }

    public void begin() {
        log.error("Call begin transaction for name {}", this.name);
        if (this.status == null) {
            log.error("Not begin, do it {}", this.name);
            this.status = manager.getTransaction(definition);
            log.error("Done begin transaction for name {}", this.name);
        } else {
            log.error("Already begin transaction for name {}", this.name);
        }
    }

    public void commit() {
        log.error("Call commit transaction for name {}", this.name);
        if (status != null && !status.isCompleted()) {
            log.error("Begin commit transaction for name {}", this.name);
            manager.commit(status);
            log.error("committed {}", this.name);
        } else {
            log.error("Not start, can not commit transaction for name {}", this.name);
        }
    }

    public void rollback() {
        log.error("Call rollback transaction for name {}", this.name);
        if (status != null) {
            log.error("Started transaction for name {}", this.name);
            if (!status.isCompleted()) {
                log.error("Begin rollback transaction for name {}", this.name);
                manager.rollback(status);
                log.error("rollbacked {}", this.name);
            } else if (manualRollbackAction != null) {
                log.error("Completed, can not rollback. Begin manual rollback {}", this.name);
                manualRollbackAction.run();
                log.error("Done manual rollback {}", this.name);
            }
        } else {
            log.error("Not start, can not rollback transaction for name {}", this.name);
        }
    }

    public boolean isStarted() {
        return status != null;
    }

    public String getName() {
        return name;
    }

    public void setManualRollbackAction(Runnable manualRollbackAction) {
        this.manualRollbackAction = manualRollbackAction;
    }
}

