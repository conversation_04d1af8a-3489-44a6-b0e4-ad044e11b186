package com.vitadairy.gift.configs;

import com.zaxxer.hikari.HikariDataSource;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = "com.vitadairy.gift.repositories", entityManagerFactoryRef = "giftEntityManagerFactory", transactionManagerRef = "giftTransactionManager")
public class GiftDatasourceConfiguration {

    @Value("${spring.flyway.gift.enabled}")
    private Boolean flywayEnabled;

    @Bean(name = "giftDataSource")
    @ConfigurationProperties("spring.datasource.gift")
    public DataSource dataSource(
            @Value("${spring.datasource.gift.hikari.pool-name:HikariGiftCP}") String poolName,
            @Value("${spring.datasource.gift.hikari.maximum-pool-size:10}") Integer maximumPoolSize,
            @Value("${spring.datasource.gift.hikari.minimum-idle:2}") Integer minimumIdle,
            @Value("${spring.datasource.gift.hikari.idle-timeout:120000}") Integer idleTimeout,
            @Value("${spring.datasource.gift.hikari.connection-timeout:30000}") Integer connectionTimeout,
            @Value("${spring.datasource.gift.hikari.max-lifetime:1800000}") Integer maxLifeTime
    ) {
        /*return DataSourceBuilder.create()
                .build();*/
        HikariDataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        dataSource.setConnectionTimeout(connectionTimeout);
        dataSource.setMaximumPoolSize(maximumPoolSize);
        dataSource.setMinimumIdle(minimumIdle);
        dataSource.setIdleTimeout(idleTimeout);
        dataSource.setMaxLifetime(maxLifeTime);
        dataSource.setPoolName(poolName);

        return dataSource;
    }

    @Bean(name = "giftTransactionManager")
    JpaTransactionManager dataSourceTransactionManager(
            @Qualifier("giftEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(
                entityManagerFactory.getObject());
        return transactionManager;
    }

    @Bean(name = "giftEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("giftDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.vitadairy.gift.entities")
                .persistenceUnit("gift")
                .build();
    }

    @Bean(name = "giftEntityManager")
    public EntityManager entityManager(
            @Qualifier("giftEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return entityManagerFactory.createEntityManager();
    }

    @Bean
    public FlywayMigrationInitializer dbGiftFlywayInitializer(@Qualifier("giftDataSource") DataSource dataSource) {
        if(flywayEnabled){
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:dbchange/gift")
                    .baselineOnMigrate(true)
                    .validateMigrationNaming(true)
                    .outOfOrder(true)
                    .load();
            return new FlywayMigrationInitializer(flyway);
        }
        else {
            return null;
        }
    }
}
