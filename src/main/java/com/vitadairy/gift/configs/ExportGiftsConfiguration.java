package com.vitadairy.gift.configs;

import com.vitadairy.gift.features.gifts.dto.request.ListGiftRequestDto;
import com.vitadairy.gift.features.userGift.dto.GiftExportDto;
import com.vitadairy.gift.features.userGift.dto.UserGiftExportDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.services.ImportLogger;
import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.WriteRowProcessor;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.libraries.importexport.service.WriteDataDateService;
import com.vitadairy.libraries.importexport.service.WriteDataDefaultService;
import com.vitadairy.libraries.importexport.service.WriteDataNumberService;
import com.vitadairy.libraries.importexport.service.WriteDataServiceStrategy;
import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.libraries.importexport.service.WriteExportFileServiceImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class ExportGiftsConfiguration {
    @Bean(name = "exportGiftsMetaData")
    public List<CellMetaData> exportGiftsMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("ID").dataType(DataType.STRING).fieldName("id").build());
        metaDataList.add(CellMetaData.builder().name("Name").dataType(DataType.STRING).fieldName("name").build());
        metaDataList.add(CellMetaData.builder().name("Category").dataType(DataType.STRING).fieldName("category").build());
        metaDataList.add(CellMetaData.builder().name("RewardAppGiftCode").dataType(DataType.STRING).fieldName("rewardAppGiftCode").build());
        metaDataList.add(CellMetaData.builder().name("Type").dataType(DataType.STRING).fieldName("type").build());
        metaDataList.add(CellMetaData.builder().name("SCT").dataType(DataType.STRING).fieldName("sctNumber").build());
        metaDataList.add(CellMetaData.builder().name("TPM").dataType(DataType.STRING).fieldName("tpmNumber").build());
        metaDataList.add(CellMetaData.builder().name("Huy hiệu").dataType(DataType.STRING).fieldName("badges").build());
        metaDataList.add(CellMetaData.builder().name("Display Point").dataType(DataType.NUMBER).fieldName("displayPoint").build());
        metaDataList.add(CellMetaData.builder().name("Money").dataType(DataType.NUMBER).fieldName("money").build());
        metaDataList.add(CellMetaData.builder().name("Total").dataType(DataType.NUMBER).fieldName("total").build());
        metaDataList.add(CellMetaData.builder().name("Status").dataType(DataType.STRING).fieldName("status").build());
        metaDataList.add(CellMetaData.builder().name("Quantity Reward").dataType(DataType.NUMBER).fieldName("qtyReward").build());
        metaDataList.add(CellMetaData.builder().name("Quantity Reservation").dataType(DataType.NUMBER).fieldName("qtyReservation").build());
        return metaDataList;
    }

    @Bean(name = "exportGiftsLogger")
    public ILogger exportGiftsLogger() {
        return new ImportLogger("ExportGifts");
    }

    @Bean(name = "exportGiftsRowProcessor")
    public WriteRowProcessor exportGiftsRowProcessor(
            @Qualifier("exportGiftsMetaData") List<CellMetaData> metaDataList,
            @Qualifier("exportGiftsLogger") ILogger logger) {
        return new WriteRowProcessor(
                new WriteDataServiceStrategy(
                        new WriteDataDefaultService(),
                        new WriteDataNumberService(),
                        new WriteDataDateService("dd/MM/yyyy", logger)),
                metaDataList,
                logger);
    }

    @Bean(name = "exportGiftsWriter")
    public WriteExportFileService<GiftExportDto, ListGiftRequestDto> exportGiftsWriter(
            FetchDataService<GiftExportDto, ListGiftRequestDto> fetchDataService,
            @Qualifier("exportGiftsRowProcessor") WriteRowProcessor rowProcessor,
            @Qualifier("exportGiftsLogger") ILogger logger) {
        return new WriteExportFileServiceImpl<>(fetchDataService, rowProcessor, logger);
    }
}
