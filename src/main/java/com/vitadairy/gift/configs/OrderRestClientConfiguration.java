package com.vitadairy.gift.configs;

import com.vitadairy.main.helper.RestTemplateClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration(value="orderRestClientConfiguration")
public class OrderRestClientConfiguration {

    @Value("${rest.url.order-service}")
    private String orderServiceUrl;

    @Bean
    public RestTemplateClient restTemplateOrderClient(RestTemplate restTemplate) {
        return new RestTemplateClient(orderServiceUrl, restTemplate);
    }
}
