package com.vitadairy.gift.configs;

import com.vitadairy.gift.features.userGift.dto.UserGiftExportDto;
import com.vitadairy.gift.features.userGift.dto.request.UserGiftListRequest;
import com.vitadairy.gift.services.ImportLogger;
import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.WriteRowProcessor;
import com.vitadairy.libraries.importexport.service.FetchDataService;
import com.vitadairy.libraries.importexport.service.WriteDataDateService;
import com.vitadairy.libraries.importexport.service.WriteDataDefaultService;
import com.vitadairy.libraries.importexport.service.WriteDataNumberService;
import com.vitadairy.libraries.importexport.service.WriteDataServiceStrategy;
import com.vitadairy.libraries.importexport.service.WriteExportFileService;
import com.vitadairy.libraries.importexport.service.WriteExportFileServiceImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class ExportUserGiftsConfiguration {
    @Bean(name = "exportUserGiftMetaData")
    public List<CellMetaData> exportUserGiftsMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("Customer Name").dataType(DataType.STRING).fieldName("customerName").build());
        metaDataList.add(CellMetaData.builder().name("Customer ID").dataType(DataType.STRING).fieldName("customerId").build());
        metaDataList.add(CellMetaData.builder().name("Customer Phone").dataType(DataType.STRING).fieldName("customerPhone").build());
        metaDataList.add(CellMetaData.builder().name("Transaction External ID").dataType(DataType.STRING).fieldName("transactionExternalId").build());
        metaDataList.add(CellMetaData.builder().name("Transaction Date").dataType(DataType.DATE).fieldName("transactionDate").build());
        metaDataList.add(CellMetaData.builder().name("Type").dataType(DataType.STRING).fieldName("type").build());
        metaDataList.add(CellMetaData.builder().name("Action Type").dataType(DataType.STRING).fieldName("actionType").build());
        metaDataList.add(CellMetaData.builder().name("Gift Points").dataType(DataType.NUMBER).fieldName("giftPoint").build());
        metaDataList.add(CellMetaData.builder().name("Tier Point").dataType(DataType.NUMBER).fieldName("tierPoint").build());
        metaDataList.add(CellMetaData.builder().name("User Gift ID").dataType(DataType.NUMBER).fieldName("userGiftId").build());
        metaDataList.add(CellMetaData.builder().name("Gift ID").dataType(DataType.NUMBER).fieldName("giftId").build());
        metaDataList.add(CellMetaData.builder().name("Voucher Ref ID").dataType(DataType.STRING).fieldName("voucherRefId").build());
        metaDataList.add(CellMetaData.builder().name("Voucher Code").dataType(DataType.STRING).fieldName("voucherCode").build());
        metaDataList.add(CellMetaData.builder().name("Voucher Link").dataType(DataType.STRING).fieldName("voucherLink").build());
        metaDataList.add(CellMetaData.builder().name("User Gift Status").dataType(DataType.STRING).fieldName("userGiftStatus").build());
        metaDataList.add(CellMetaData.builder().name("Quantity").dataType(DataType.NUMBER).fieldName("quantity").build());
        metaDataList.add(CellMetaData.builder().name("Used Date").dataType(DataType.DATE).fieldName("usedDate").build());
        metaDataList.add(CellMetaData.builder().name("Expiry Date").dataType(DataType.DATE).fieldName("expiryDate").build());
        metaDataList.add(CellMetaData.builder().name("Is Reusing").dataType(DataType.STRING).fieldName("isReusing").build());
        metaDataList.add(CellMetaData.builder().name("Name").dataType(DataType.STRING).fieldName("name").build());
        metaDataList.add(CellMetaData.builder().name("E Product ID").dataType(DataType.STRING).fieldName("eProductId").build());
        metaDataList.add(CellMetaData.builder().name("E Product Name").dataType(DataType.STRING).fieldName("eProductName").build());
        metaDataList.add(CellMetaData.builder().name("Price ID").dataType(DataType.STRING).fieldName("priceId").build());
        metaDataList.add(CellMetaData.builder().name("Money").dataType(DataType.NUMBER).fieldName("money").build());
        metaDataList.add(CellMetaData.builder().name("Gift Code").dataType(DataType.STRING).fieldName("giftCode").build());
        return metaDataList;
    }

    @Bean(name = "exportUserGiftLogger")
    public ILogger exportUserGiftLogger() {
        return new ImportLogger("ExportUserGift");
    }

    @Bean(name = "exportUserGiftRowProcessor")
    public WriteRowProcessor exportUserGiftRowProcessor(
            @Qualifier("exportUserGiftMetaData") List<CellMetaData> metaDataList,
            @Qualifier("exportUserGiftLogger") ILogger logger) {
        return new WriteRowProcessor(
                new WriteDataServiceStrategy(
                        new WriteDataDefaultService(),
                        new WriteDataNumberService(),
                        new WriteDataDateService("dd/MM/yyyy", logger)),
                metaDataList,
                logger);
    }

    @Bean(name = "exportUserGiftWriter")
    public WriteExportFileService<UserGiftExportDto, UserGiftListRequest> exportUserGiftWriter(
            FetchDataService<UserGiftExportDto, UserGiftListRequest> fetchDataService,
            @Qualifier("exportUserGiftRowProcessor") WriteRowProcessor rowProcessor,
            @Qualifier("exportUserGiftLogger") ILogger logger) {
        return new WriteExportFileServiceImpl<>(fetchDataService, rowProcessor, logger);
    }
}
