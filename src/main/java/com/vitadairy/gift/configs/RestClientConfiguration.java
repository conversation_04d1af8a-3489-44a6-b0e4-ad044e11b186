package com.vitadairy.gift.configs;

import com.vitadairy.main.helper.RestTemplateClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration(value="giftRestClientConfiguration")
public class RestClientConfiguration {

    @Value("${rest.url.warehouse-service}")
    private String whServiceUrl;

    @Bean
    public RestTemplateClient restTemplateWarehouseClient(RestTemplate restTemplate) {
        return new RestTemplateClient(whServiceUrl, restTemplate);
    }
}
