package com.vitadairy.gift.configs;

import com.vitadairy.gift.entities.Gift;
import com.vitadairy.gift.services.impl.ParseGsGiftService;
import com.vitadairy.gift.services.impl.ProcessDataGsGiftService;
import com.vitadairy.libraries.importexport.common.DataType;
import com.vitadairy.libraries.importexport.dto.CellMetaData;
import com.vitadairy.libraries.importexport.helper.ILogger;
import com.vitadairy.libraries.importexport.processor.ReadRowProcessor;
import com.vitadairy.libraries.importexport.service.*;
import com.vitadairy.main.services.ImportLogger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
public class ImportGiftConfiguration {

    @Bean(name = "importGiftMetaData")
    public List<CellMetaData> importOrGiftMetadata() {
        List<CellMetaData> metaDataList = new ArrayList<>();
        metaDataList.add(CellMetaData.builder().name("id").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("gift_type").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("category_code").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("reward_name").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("active_gift").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("description").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("image").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("coverImage").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("Huyhieu").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("source_gift").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("transport_Type").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("tier_code").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("reward_app_giftcode").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("priority").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("display_point").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("money").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("sms_gift_code").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("send_sms").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("startdate").dataType(DataType.DATE).build());
        metaDataList.add(CellMetaData.builder().name("endDate").dataType(DataType.DATE).build());
        metaDataList.add(CellMetaData.builder().name("expired_date(date)").dataType(DataType.DATE).build());
        metaDataList.add(CellMetaData.builder().name("expired_time(h)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_sct").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_tpm").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_luong_total").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_luong_kho").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_luong_gioi_han_doi_qua").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("weight(cm)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("length(cm)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("width(cm)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("height(cm)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("active_pre-order").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("sl_toi_da_co_the_dat_truoc").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("thoi_han_giu_qua").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("gioi_han_dat_truoc_huy_dat_truoc").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("giam_gia_dat_truoc (%)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_xu_dat_truoc(%)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("so_xu_bi_tru(%)").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("hidden_user_tag_ranking").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("bo_loc_chinh").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("gia_tri_loc_chinh").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("bo_loc_phu").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("gia_tri_loc_phu").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("bo_loc_nho").dataType(DataType.STRING).build());
        metaDataList.add(CellMetaData.builder().name("gia_tri_loc_nho").dataType(DataType.STRING).build());
        return metaDataList;
    }

    public Map<String, CellMetaData> importOrGiftMetadataMap() {
        return importOrGiftMetadata().stream().collect(Collectors.toMap(CellMetaData::getName, Function.identity()));
    }

    @Bean(name = "importGiftLogger")
    public ILogger importOrGiftLogger() {
        return new ImportLogger("ImportGift");
    }

    @Bean(name = "importGiftRowProcessor")
    public ReadRowProcessor importOrGiftRowProcessor(@Qualifier("importGiftLogger") ILogger logger) {
        return new ReadRowProcessor(
                new ParseCellDataStrategy(
                        new ParseCellDefaultService(),
                        new ParseCellDateService("MM-dd-yyyy HH:mm:ss", logger),
                        new ParseCellNumberService()
                ),
                importOrGiftMetadataMap()
        );
    }

    @Bean(name = "importGiftReader")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ReadImportFileService<Gift> importGiftReader(
            @Qualifier("importGiftRowProcessor") ReadRowProcessor rowProcessor,
            @Qualifier("importGiftLogger") ILogger logger
    ) {
        return new ReadImportFileServiceImpl<>(
                new ParseGsGiftService(), rowProcessor, logger
        );
    }

    @Bean(name = "giftProcessImportFileService")
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public ProcessImportFileService<Gift> importGiftService(
            @Qualifier("importGiftReader") ReadImportFileService<Gift> reader,
            ProcessDataGsGiftService processDataGsGiftService
    ) {
        return new ProcessImportFileServiceImpl<>(reader, processDataGsGiftService);
    }
}
