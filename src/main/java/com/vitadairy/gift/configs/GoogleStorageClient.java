package com.vitadairy.gift.configs;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.WriteChannel;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class GoogleStorageClient {
    private final Storage storage;
    private final String bucketName;

    public GoogleStorageClient(@Value("${google.storage.bucket.name}") String bucketName,
                                @Value("${google.storage.gcs.credential.path}") String gcsCredentialPath) throws IOException {
        
        this.storage = getStorage(gcsCredentialPath);
        this.bucketName = bucketName;
    }

    private Storage getStorage(String gcsCredentialPath) throws IOException {
        if (new File(gcsCredentialPath).exists()) {
            return StorageOptions.newBuilder().setCredentials(
                GoogleCredentials.fromStream(new FileInputStream(gcsCredentialPath))).build().getService();
        }
        return StorageOptions.getDefaultInstance().getService();
    }

    public String getPath(String blobName) {
        return "gs://" + bucketName + "/" + blobName;
    }

    public WriteChannel getWriteChannel(String blobName, String contentType) {
        BlobId blobId = BlobId.of(bucketName, blobName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(contentType).build();
        return storage.writer(blobInfo);
    }

    public URL generateGetSignedUrl(String blobName, long minutesDuration) {
        return generateGetSignedUrl(blobName, minutesDuration, null);
    }

    public URL generateGetSignedUrl(String objectName, long minutesDuration, String targetFileName) {
        BlobInfo blobinfo = BlobInfo.newBuilder(BlobId.of(bucketName, objectName)).build();
        Map<String, String> queryParams = new HashMap<>();
        if (targetFileName != null) {
            queryParams.put("response-content-disposition", String.format("attachment; filename=\"%s\"", targetFileName));
        }
        return storage.signUrl(blobinfo, minutesDuration, TimeUnit.MINUTES, Storage.SignUrlOption.withQueryParams(queryParams));
    }
}
