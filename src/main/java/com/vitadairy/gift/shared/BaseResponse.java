package com.vitadairy.gift.shared;

import java.util.List;

import com.vitadairy.main.entities.BaseEntity;

import lombok.Data;

@Data
public class BaseResponse<T extends BaseEntity> {
    private List<T> items;
    private Integer total;

    public BaseResponse(List<T> items, Integer total) {
        this.items = items;
        this.total = total;
    }

    public BaseResponse(List<T> items, long total) {
        this.items = items;
        this.total = (int) total;
    }
}