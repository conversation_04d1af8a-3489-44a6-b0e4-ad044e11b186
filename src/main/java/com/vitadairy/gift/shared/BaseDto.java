package com.vitadairy.gift.shared;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import com.vitadairy.gift.constants.CommonConstant;

import lombok.Data;

@Data
public class BaseDto {
    @Data
    public static class BaseFilter {
        Integer page = CommonConstant.DEFAULT_PAGE;
        Integer size;

        public Integer getPage() {
            return page;
        }

        public Integer getSize() {
            return size;
        }

        public void setPage(Integer page) {
            this.page = page;
        }

        public void setSize(Integer size) {
            this.size = size;
        }

        public Pageable getPageable(Sort sort) {
            if (this.getSize() == null) {
                return Pageable.unpaged(sort);
            } else {
                return PageRequest.of(this.getPage(), this.getSize(), sort);
            }
        }

        public Pageable getPageable() {
            if (this.getSize() == null) {
                return Pageable.unpaged();
            } else {
                return PageRequest.of(this.getPage(), this.getSize());
            }

        }
    }
}
