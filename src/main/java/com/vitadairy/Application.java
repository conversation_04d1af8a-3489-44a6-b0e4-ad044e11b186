package com.vitadairy;

import com.vitadairy.main.configs.RedisCacheConfigurationProperties;
import com.vitadairy.main.schedules.ScheduledJobRunner;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.internal.util.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeansException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;

import java.util.TimeZone;

@SpringBootApplication(exclude = TransactionAutoConfiguration.class)
@EnableConfigurationProperties({ConfigProperties.class, RedisCacheConfigurationProperties.class})
@EnableCaching
@Slf4j
public class Application implements CommandLineRunner, ApplicationContextAware {
    private ApplicationContext ctx;
    @Bean
    public ModelMapper modelMapper() {
        return new ModelMapper();
    }

    public static void main(String[] args) {
        var app = new SpringApplication(Application.class);
        for (var arg : args) {
            if (arg.startsWith("--cronJob")) {
                app.setWebApplicationType(WebApplicationType.NONE);
                break;
            }
        }
        app.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        for (String arg : args) {
            if ("--generate-schema".equals(arg)) {
                // Exit the application after generating the schema
                System.exit(0);
            } else if (arg.startsWith("--cronJob")) {
                var paths = arg.split("_");
                if (paths.length < 2 || !StringUtils.hasText(paths[1])) {
                    log.error("Cron Job name must not empty");
                    System.exit(1);
                }

                var runner = ctx.getBean(paths[1], ScheduledJobRunner.class);
                log.info("Starting to run scheduled job runner: {}", paths[1]);
                runner.run();
                System.exit(0);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }
    @PostConstruct
    public void init(){
        // Setting Spring Boot SetTimeZone
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+07:00"));
    }
}
