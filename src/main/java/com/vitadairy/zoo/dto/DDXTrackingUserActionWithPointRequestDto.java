package com.vitadairy.zoo.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DDXTrackingUserActionWithPointRequestDto {
    @NotNull
    Long customerId;

    @NotNull
    String customerPhone;

    @NotNull
    String customerName;

    @NotNull
    float totalPointBefore;

    @NotNull
    float totalPointAfter;

    @NotNull
    float numberPoint;

    @NotNull
    String type;

    @NotNull
    String actionType;

    @NotNull
    String transactionExternalId;

    @NotNull
    String transactionTime;
}
