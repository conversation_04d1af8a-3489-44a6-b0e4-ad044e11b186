package com.vitadairy.zoo.dto;

import java.time.Instant;

public interface ReadOnlyHistoryPointDto {
    Long getId();

    Long getCustomerId();

    String getCustomerName();

    String getCustomerPhone();

    String getType();

    String getStatus();

    String getActionType();

    Instant getTransactionDate();

    Float getGiftPoint();

    Float getStockPoint();

    Float getTierPoint();

    Long getMoney();

    Long getCustomerChildId();

    String getTransactionExternalId();

    String getBrand();

    Long getGiftId();

    Boolean getCdpSyncUp();

    Boolean getIsGiftReceived();
}
