package com.vitadairy.zoo.dto;

import com.vitadairy.zoo.entities.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

@Data
@Getter
@Setter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class UserDto{
    private Long userId;
    private String tierCode;
    private float giftPoint;

    public static UserDto fromEntity(User user) {
        return UserDto.builder()
                .userId(user.getId())
                .tierCode(user.getTierCode())
                .giftPoint(user.getGiftPoint())
                .build();
    }
}
