package com.vitadairy.zoo.dto;

import com.vitadairy.zoo.enums.FeatureNoti;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserGiftNotificationRequestDto {
    @NotNull
    Long[] userIds;

    @NotNull
    String status;

    FeatureNoti featureNoti;
}
