package com.vitadairy.zoo.requests;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
public class RequestCouponRequest {
    private String objectName;

    private String objectType;

    private String objectUnlock;

    public RequestCouponRequest(String objectName, String objectType, String objectUnlock) {
        this.objectName = objectName;
        this.objectType = objectType;
        this.objectUnlock = objectUnlock;
    }

    @JsonIgnore
    public boolean isInvalid() {
        return
                Objects.isNull(this.objectName) ||
                        this.objectName.isEmpty() ||
                        Objects.isNull(this.objectType) ||
                        this.objectType.isEmpty() ||
                        Objects.isNull(this.objectUnlock) ||
                        this.objectUnlock.isEmpty();
    }
}
