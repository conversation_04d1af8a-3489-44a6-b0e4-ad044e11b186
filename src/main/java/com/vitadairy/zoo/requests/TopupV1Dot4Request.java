package com.vitadairy.zoo.requests;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
public class TopupV1Dot4Request {
    private String phone;

    private Long amount;

    @JsonProperty("ref_id")
    private String refId;

    @JsonProperty("return_code_if_fail")
    private Integer returnCodeIfFail; // will return card code if failed

    public TopupV1Dot4Request(String phone, Long amount, Integer isGotItReturnCodeIfFail, String refId) {
        this.refId = refId;
        this.phone = phone;
        this.amount = amount;
        this.returnCodeIfFail = isGotItReturnCodeIfFail;

    }

    public boolean isInvalid() {
        return Objects.isNull(this.phone) || Objects.isNull(this.amount);
    }
}
