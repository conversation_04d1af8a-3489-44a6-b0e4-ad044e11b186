package com.vitadairy.zoo.requests;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.util.DateTimeUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Objects;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class GetVoucherRequest {
    private Long productId;

    private Long productPriceId;

    private Long quantity;

    private String campaignNm;

    private String expiryDate;

    private String voucherRefId;

    @JsonProperty("otp_type")
    private Long otpType;

    private String password;

    @JsonProperty("receiver_name")
    private String receiverName;

    private String phone;

    private String email;

    public GetVoucherRequest(Long eProductId, Long ePrice, Instant expireDate, String receiveName, String phoneNumber, String refId) {
        this.productId = eProductId;
        this.productPriceId = ePrice;
        this.quantity = 1L;
        this.campaignNm = "VitaDiary";
        this.expiryDate = DateTimeUtil.formatInstant(expireDate, Constant.DateTimeFormat.YYYY_MM_DD);
        this.receiverName = receiveName;
        this.voucherRefId = refId;
        this.phone = phoneNumber;
    }


    public boolean isInvalid() {
        return Objects.isNull(productId) || Objects.isNull(productPriceId) || Objects.isNull(quantity)
                || StringUtils.isEmpty(campaignNm) || StringUtils.isEmpty(expiryDate);
    }
}
