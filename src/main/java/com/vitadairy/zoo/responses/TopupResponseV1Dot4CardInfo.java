package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopupResponseV1Dot4CardInfo {
    @JsonProperty("card_number")
    private String cardNumber;

    @JsonProperty("card_serial")
    private String cardSerial;

    @JsonProperty("telco")
    private String telco;

    @JsonProperty("expiry")
    private String expiry;
}
