package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestDetailCouponResponse {

    private Long error;

    private RequestDetailCouponDataDetailCouponResponse data;

    @JsonIgnore
    public boolean isValid() {
        return this.error == 0;
    }
}
