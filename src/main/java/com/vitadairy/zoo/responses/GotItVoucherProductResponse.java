package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class GotItVoucherProductResponse {
    private Long productId;

    private String productNm;

    private String productImg;

    private Long brandId;

    private String brandNm;

    private SizeResponse size;

    private String productDesc;

    private String terms;

    private List<StoreResponse> storeList;

    private String link;

    private String productType;
}
