package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopupResponseV1Dot4 {

    private Integer status;

    private String message;

    @JsonProperty("ref_id")
    private String refId;

    private String code;

    private String link;

    @JsonProperty("card_info")
    private TopupResponseV1Dot4CardInfo cardInfo;

    public TopupResponse toTopupResponse() {
        var topupResponse = new TopupResponse();
        topupResponse.setRefId(this.refId);
        topupResponse.setLink(this.link);
        topupResponse.setCode(this.code);
        topupResponse.setMessage(this.message);
        topupResponse.setStatus(this.status);
        return topupResponse;
    }
}
