package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestCouponResponse {

    private Long error;

    private String msg;

    private List<RequestCouponDataCouponResponse> data;

    @JsonIgnore
    public boolean isValid() {
        return this.error == 0 && !this.data.isEmpty();
    }
}
