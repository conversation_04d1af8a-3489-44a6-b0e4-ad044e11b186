package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestCouponDataCouponResponse {

    private String _id;

    private String key;

    @JsonIgnore
    public boolean isValid() {
        return !this._id.isEmpty() && !this.key.isEmpty();
    }

}
