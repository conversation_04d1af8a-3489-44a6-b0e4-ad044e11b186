package com.vitadairy.zoo.responses;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.Instant;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestDetailCouponDataDetailCouponResponse {

    private String _id;

    private String status;

    private String key;

    private Instant createdAt;

    private Instant expireDate;

    @JsonIgnore
    public boolean isValid() {
        return !this._id.isEmpty() &&
                !this.status.isEmpty() &&
                !this.key.isEmpty();
    }

}
