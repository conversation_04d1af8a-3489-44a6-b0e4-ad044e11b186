package com.vitadairy.zoo.common;

import java.time.ZoneId;

public class Constant {
    public static class TIMEZONE {
        public static final String GMT_7 = "+7";
        public static final ZoneId HCM_ZONE = ZoneId.of("Asia/Ho_Chi_Minh");
        public static final ZoneId UTC_ZONE = ZoneId.of("UTC");
    }

    public static class WheelCode {
        public static final String WEIGHT = "W";
        public static final String CALOSURE = "CLS";
    }

    public static class ExtensionFile {
        public static final String FILE_EXT_CSV = ".csv";
        public static final String FILE_EXT_EXCEL = ".xlsx";
    }

    public static class Header {
        public static String TOKEN_TYPE = "Bearer";
        public static String AUTHORIZATION = "Authorization";
        public static String USER_TYPE = "UserType";
        public static String TIMEZONE = "timezone";
        public static String DEVICE = "Device";
        public static String DEVICE_OS = "deviceos";
        public static String MOBILE_VERSION = "appversionname";
        public static String MOBILE_VERSION_CODE = "appversioncode";
    }

    public static final class DateTimeFormat {
        public static final String YYYY_MM_DD = "yyyy-MM-dd";

        public static final String DD_MM_YYYY = "dd/MM/yyyy";

        public static final String DD_MM_YYYY_HH_MM = "dd/MM/yyyy HH:mm";
        public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
        public static final String YYYY_MM = "yyyy-MM";
        public static final String YYYY_MM_DD_HH_MM_SQL = "yyyy-MM-dd HH24:mi";
        public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
        public static final String YYYY_MM_DD_HH24_MM_SS = "yyyy-MM-dd HH24:mm:ss";
        public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
        public static final String YYYY_MM_DD_HH_MM_SS_SS_Z = "yyyy-MM-dd HH:mm:ss.SS'Z'";
        public static final String YYYY_MM_DD_HH_MM_SS_S = "yyyy-MM-dd HH:mm:ss.S";
        public static final String HH_MM_EEEE_DD_MM_YYYY = "HH:mm, EEEE dd/MM/yyyy";
        public static final String YYYY_MM_DD_T_HH_MM_SS_Z = "yyyy-MM-dd'T'HH:mm:ssZ";
        public static final String YYYY_MM_DD_T_HH_MM_SS_ZZ = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    }

    public static final class SystemConfigCode {
        public static final String GIFT_KEYWORDS = "GIFT_KEYWORDS";
        public static final String RESET_POINT_TIME_MINUTES = "RESET_POINT_TIME_MINUTES";
        public static final String RESET_TIER_TIME_MINUTES = "RESET_TIER_TIME_MINUTES";
        public static final String WELCOME_BACK_TIME_MINUTES = "WELCOME_BACK_TIME_MINUTES";
        public static final String SPECIAL_POINT = "SPECIAL_POINT";
        public static final String LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_MONTH = "LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_MONTH";
        public static final String LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_DAY = "LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_DAY";
        public static final String LIMIT_NUMBER_OF_SCAN_QR_FAILED = "LIMIT_NUMBER_OF_SCAN_QR_FAILED";
        public static final String BLOCKED_SCAN_SAME_QR_FAILED_TIME_MINUTES = "BLOCKED_SCAN_SAME_QR_FAILED_TIME_MINUTES";
        public static final String LIMIT_NUMBER_OF_SCAN_SAME_QR_FAILED = "LIMIT_NUMBER_OF_SCAN_SAME_QR_FAILED";
        public static final String BLOCKED_SCAN_QR_FAILED_TIME_MINUTES = "BLOCKED_SCAN_QR_FAILED_TIME_MINUTES";
        public static final String LIMIT_NUMBER_OF_BLOCKED_SCAN = "LIMIT_NUMBER_OF_BLOCKED_SCAN";
        public static final String UPDATE_GIFT_STATUS_TIME_MINUTES = "UPDATE_GIFT_STATUS_TIME_MINUTES";

        public static final String OGGI_WHEEL_LIMIT_NUMBER_OF_WIN = "OGGI_WHEEL_LIMIT_NUMBER_OF_WIN";
        public static final String OGGI_WHEEL_UP_RATE = "OGGI_WHEEL_UP_RATE";
        public static final String OGGI_WHEEL_DOWN_RATE = "OGGI_WHEEL_DOWN_RATE";
        public static final String OGGI_WHEEL_WIN_RATE_DEFAULT = "OGGI_WHEEL_WIN_RATE_DEFAULT";

        public static final String CBB_WHEEL_LIMIT_NUMBER_OF_WIN = "CBB_WHEEL_LIMIT_NUMBER_OF_WIN";
        public static final String CBB_WHEEL_UP_RATE = "CBB_WHEEL_UP_RATE";
        public static final String CBB_WHEEL_DOWN_RATE = "CBB_WHEEL_DOWN_RATE";
        public static final String CBB_WHEEL_WIN_RATE_DEFAULT = "CBB_WHEEL_WIN_RATE_DEFAULT";

        public static final String MONTHLY_ACTIVE_MEMBER_GIFT = "MONTHLY_ACTIVE_MEMBER_GIFT";
        public static final String PIECE_WIN_RATE_DEFAULT = "PIECE_WIN_RATE_DEFAULT";
    }

    public static final class CrmRequestParams {
        public static final String GRANT_TYPE = "grant_type";
        public static final String CLIENT_ID = "client_id";
        public static final String CLIENT_SECRET = "client_secret";
        public static final String USERNAME = "username";
        public static final String PASSWORD = "password";
        public static final String AUTH = "Authorization";
        public static final String REFRESH_TOKEN = "refresh_token";
    }

    public static final class SapRequestParams {
        public static final String GRANT_TYPE = "grant_type";
        public static final String CLIENT_ID = "client_id";
        public static final String CLIENT_SECRET = "client_secret";
        public static final String SCOPE = "scope";
    }

    public static final class ViettelRequestParams {
        public static final String AUTH = "Token";
        public static final String COOKIE = "Cookie";
    }

    public static final class HistoryPointAttributeCode {
        public static final String SF_ID = "SF_ID";
        public static final String QR_CODE = "QR_CODE";
        public static final String SPOON_CODE = "SPOON_CODE";
        public static final String PRODUCT_ID = "PRODUCT_ID";
        public static final String PRODUCT_CODE = "PRODUCT_CODE";
        public static final String PRODUCT_MONEY = "PRODUCT_MONEY";
        public static final String RECIPIENT_NAME = "RECIPIENT_NAME";
        public static final String RECIPIENT_ADDRESS = "RECIPIENT_ADDRESS";
        public static final String RECIPIENT_PHONE = "RECIPIENT_PHONE";
        public static final String GIFT_ID = "GIFT_ID";
        public static final String AMOUNT = "AMOUNT";
        public static final String GIFT_NAME = "GIFT_NAME";
        public static final String REF_ID = "REF_ID";
        public static final String VOUCHER_CODE = "VOUCHER_CODE";
        public static final String VOUCHER_LINK = "VOUCHER_LINK";
        public static final String VOUCHER_REF_ID = "VOUCHER_REF_ID";
        public static final String TOPUP_REF_ID = "TOPUP_REF_ID";
        public static final String TOPUP_STATUS = "TOPUP_STATUS";
        public static final String E_PRODUCT_ID = "E_PRODUCT_ID";
        public static final String E_PRODUCT_PRICE_ID = "E_PRODUCT_PRICE_ID";
        public static final String INVOICE_NO = "INVOICE_NO";
        public static final String EXPIRY_DATE = "EXPIRY_DATE";
        public static final String QUANTITY = "QUANTITY";
        public static final String SURVEY_ID = "SURVEY_ID";
        public static final String SURVEY_TITLE = "SURVEY_TITLE";
        public static final String USER_GIFT_ID = "USER_GIFT_ID";
        public static final String SUB_GIFT_ID = "SUB_GIFT_ID";
        public static final String SUB_GIFT_NAME = "SUB_GIFT_NAME";
        public static final String POINT = "POINT";
        public static final String QR_CODE_SMS = "QR_CODE_SMS";
        public static final String PRODUCT_NAME = "PRODUCT_NAME";
        public static final String LEVEL_POINT = "LEVEL_POINT";
        public static final String RE_VOUCHER_REF_ID = "RE_VOUCHER_REF_ID";
        public static final String SPEND_POINT_TX_ID = "SPEND_POINT_TX_ID";
    }

    public static final class HistoryPointType {
        public static final String ADD_POINT = "ADD_POINT";
        public static final String SPEND_POINT = "SPEND_POINT";
        public static final String GIFT = "GIFTING";
    }

    public static final class TransactionStatus {
        public static final String SUCCESS = "SUCCESS";
        public static final String RETURNED = "RETURNED";
        public static final String UNRETURNED = "UNRETURNED";

    }

    public static final class GiftType {
        public static final String GIFT = "GIFT";
        public static final String E_VOUCHER = "E_VOUCHER";
        public static final String E_VOUCHER_SHOP = "E_VOUCHER_SHOP";
        public static final String COUPON = "COUPON";
        public static final String E_VOUCHER_SHOP_BKIDS = "E_VOUCHER_SHOP_BKIDS";
        public static final String EV_VITA_CODE = "EV_VITA_CODE";
    }

    public static final class SubGiftType {
        public static final String PRODUCT = "PRODUCT";
        public static final String PHONE_CARD = "PHONE_CARD";
    }

    public static final class WheelLuckyGiftType {
        public static final String GIFT = "GIFT";
        public static final String ROTATION = "ROTATION";
    }

    public interface Column {
        String ID = "id";
        String CODE = "code";
        String CATEGORY_ID = "category_id";
        String IS_ACTIVE = "isActive";
        String BIRTHDAY = "birthday";
        String PHONE_NUMBER = "phoneNumber";
    }

    public static final class UserGiftStatus {
        public static final String PENDING = "PENDING";
        public static final String IN_PROCESS = "IN_PROCESS";
        public static final String RECEIVED = "RECEIVED";
        public static final String UNUSED = "UNUSED";
        public static final String USED = "USED";
        public static final String EXPIRED = "EXPIRED";
        public static final String CANCELED = "CANCELED";
        public static final String TOPUP = "TOPUP";
        public static final String GP_PENDING = "GP_PENDING";
        public static final String PIECE_PENDING = "PIECE_PENDING";
        public static final String LOCK = "LOCK";
        public static final String FAILED = "FAILED";
        public static final String NEED_REUSE = "NEED_REUSE";
    }

    public interface NotificationStatus {
        String UNREAD = "UNREAD";
    }

    public static final class ProvinceType {
        public static final String PROVINCE = "PROVINCE";
        public static final String DISTRICT = "DISTRICT";
        public static final String WARD = "WARD";
        public static final String REGION = "REGION";
    }

    public static final class CountryCode {
        public static final String VN = "VN";
    }

    public static final class BannerType {
        public static final String TOP = "TOP";
        public static final String BOTTOM = "BOTTOM";
        public static final String MIDDLE = "MIDDLE";
    }

    public static final class CampaignStatus {
        public static final String ACTIVE = "ACTIVE";
    }

    public static class DeepLinkFormat {
        public final static String MY_GIFT = "/my-reward?tab=gift";
        public final static String MY_VOUCHER = "/my-reward?tab=e-voucher";
        public final static String NOTIFICATION_BY_ID = "/notification?id=%d";
        public final static String HISTORY_ADD_POINT = "/history?tab=add-point";
        public final static String HISTORY_SPEND_POINT = "/history?tab=spend-point";
        public final static String MY_VITA_POINT = "/my-point";
    }

    public static class RuleTierType {
        public final static String POINT = "POINT";
        public final static String GIFT = "GIFT";
        public final static String FORMULA = "FORMULA";
        public final static String POINT_AND_GIFT = "POINT_AND_GIFT";
    }

    public static class PointRuleCode {
        public final static String SIGN_UP = "SIGN_UP";
        public final static String BIRTHDAY = "BIRTHDAY";
        public final static String CHILDREN_BIRTHDAY = "CHILDREN_BIRTHDAY";
        public final static String FREQUENCY_1 = "FREQUENCY_1";
        public final static String FREQUENCY_2 = "FREQUENCY_2";
        public final static String FIRST_POINT = "FIRST_POINT";
        public final static String INVITE_FRIEND = "INVITE_FRIEND";
        public final static String INVITED_USER = "INVITED_USER";
        public final static String WELCOME_BACK = "WELCOME_BACK";
        public final static String TIER_UP = "TIER_UP";
        public final static String SPECIAL_POINT = "SPECIAL_POINT";
        public final static String INVITE_FIVE_FRIENDS = "INVITE_FIVE_FRIENDS";
        public final static String ADD_SPIN = "ADD_SPIN";
    }

    public static class AccountStatus {
        public final static String ACTIVE = "ACTIVE";
        public final static String NON_ACTIVE = "NON_ACTIVE";
    }

    public static class CrmPointType {
        public final static String ADD = "Adding";
        public final static String SPEND = "Spending";
        public final static String GIFT = "Gifting";
    }

    public static class CrmPointStatus {
        public final static String TRANSFERRED = "Transferred";
    }

    public static class GotItVoucherStatus {
        public final static String USED = "Used";
        public final static String EXPIRED = "Expired";
        public final static String CANCELED = "Canceled";
    }

    public static class WheelLuckyUserGiftStatus {
        public final static String NEW = "NEW";
        public final static String SUCCESS = "SUCCESS";
    }

    public static class SurveyStatus {
        public final static String ACTIVE = "ACTIVE";
        public final static String NON_ACTIVE = "NON_ACTIVE";
    }

    public static class ScanHistoryStatus {
        public final static String FAILED = "FAILED";
        public final static String SUCCESS = "SUCCESS";
    }

    public static class BlockedHistoryType {
        public final static String BLOCKED_SCAN_SAME_QR = "BLOCKED_SCAN_SAME_QR";
        public final static String BLOCKED_SCAN_QR = "BLOCKED_SCAN_QR";
        public final static String BLOCKED_SCAN_BY_ADMIN = "BLOCKED_SCAN_BY_ADMIN";
        public final static String BLOCKED_ACCOUNT_BY_ADMIN = "BLOCKED_ACCOUNT_BY_ADMIN";
        public final static String BLOCKED_ACCOUNT_WHEN_SCAN_FAILED = "BLOCKED_ACCOUNT_WHEN_SCAN_FAILED";
        public final static String UNBLOCKED_ACCOUNT = "UNBLOCKED_ACCOUNT";
    }

    public static class BlockedScanType {
        public final static String BLOCKED_SCAN_QR = "BLOCKED_SCAN_QR";
        public final static String BLOCKED_SCAN_SAME_QR = "BLOCKED_SCAN_SAME_QR";
        public final static String BLOCKED_SCAN_BY_ADMIN = "BLOCKED_SCAN_BY_ADMIN";
        public final static String RUN_OUT_OF_SCAN_QR_IN_DAY = "RUN_OUT_OF_SCAN_QR_IN_DAY";
        public final static String RUN_OUT_OF_SCAN_QR_IN_MONTH = "RUN_OUT_OF_SCAN_QR_IN_MONTH";
        public final static String RUN_OUT_OF_SCAN_QR = "RUN_OUT_OF_SCAN_QR";
    }

    public static class BlockedAccountType {
        public final static String BLOCKED_ACCOUNT_BY_ADMIN = "BLOCKED_ACCOUNT_BY_ADMIN";
        public final static String BLOCKED_ACCOUNT_WHEN_SCAN_FAILED = "BLOCKED_ACCOUNT_WHEN_SCAN_FAILED";
    }

    public static class FeatureStatus {
        public final static String ACTIVE = "ACTIVE";
        public final static String INACTIVE = "INACTIVE";
        public final static String MAINTAIN = "MAINTAIN";
    }

    public static class FeatureCode {
        public final static String REDEEM_GIFT = "REDEEM_GIFT";
        public final static String SCAN_QR = "SCAN_QR";
        public final static String MY_GIFT = "MY_GIFT";
        public final static String MINI_GAME = "MINI_GAME";
        public final static String MINI_GAME_SNT = "MINI_GAME_SNT";
        public final static String SURVEY = "SURVEY";
        public final static String HISTORY = "HISTORY";
        public final static String NOTIFICATION = "NOTIFICATION";
    }

    public static class CrmCustomerStatus {
        public final static String ACTIVE = "Active";
        public final static String BLOCK = "Block";
        public final static String BLOCK_24H = "Block 24h";
    }

    public static class UserType {
        public final static String LEAD = "LEAD";
        public final static String ACCOUNT = "ACCOUNT";
        public final static String UNREGISTERED = "UNREGISTERED";
    }

    public static class NotificationUserType {
        public static int ALL = 0;
        public static int NOT_POINT = 1;
        public static int HAS_POINT_AND_HAS_OGGI = 2;
        public static int IMPORT_USERS = 3;
    }

    public static final class NumberOfHoursBeforeGiftExpiration {
        public static int PUSH_FIREBASE_NOTI_HOUR = 24;
        public static int PUSH_FIREBASE_NOTI_V2_FROM_HOUR = 24;
        public static int PUSH_FIREBASE_NOTI_V2_TO_HOUR = 48;
    }

    public static final class ActionType {
        public static final String CREATE = "CREATE";
        public static final String UPDATE = "UPDATE";
    }

    public static final class ScanQRApiType {
        public static final String SMS = "SMS";
        public static final String APP = "APP";
    }

    public static final class UserTier {
        public static final String MEMBER = "MEMBER";
        public static final String TITAN = "TITAN";
        public static final String SILVER = "SILVER";
        public static final String GOLD = "GOLD";
        public static final String PLATINUM = "PLATINUM";

    }

    public static final class QRCode {
        public static final int LENGTH_QR_DEFAULT = 16;
        public static final int LENGTH_QR_BRAVO = 17;
    }

    public static class UserGiftActionType {
        public static final String EVENT_PIECE = "EVENT_PIECE";
    };

    public static final class TypeOrder {
        public static final String FIRST_SPECIAL = "FIRST_SPECIAL";
        public static final String SECOND_SPECIAL = "SECOND_SPECIAL";
        public static final String SAMPLE = "SAMPLE";
        public static final String EXCHANGE = "EXCHANGE";
    }

    public static final class RegionCode {
        public static final String MIEN_DONG = "MIEN_DONG";
        public static final String MIEN_TAY = "MIEN_TAY";
        public static final String MIEN_TRUNG = "MIEN_TRUNG";
        public static final String HO_CHI_MINH = "HO_CHI_MINH";
        public static final String MIEN_BAC = "MIEN_BAC";
        public static final String BAC_TRUNG = "BAC_TRUNG";
    }

    public static final class StorageAddessHCM {
        public static final String PROVINCE = "79";
        public static final String PROVINCE_ID = "61";
        public static final String PROVINCE_NAME = "Hồ Chí Minh";
        public static final String DISTRICT = "761";
        public static final String DISTRICT_ID = "629";
        public static final String DISTRICT_NAME = "Quận 12";
        public static final String WARD = "26764";
        public static final String WARD_ID = "10237";
        public static final String WARD_NAME = "Phường Thạnh Xuân";
        public static final String STREET = "271 Tô Ngọc Vân";
        public static final String FULL_ADDRESS = "271 Tô Ngọc Vân, Phường Thạnh Xuân, Quận 12, Hồ Chí Minh, Việt Nam";
    }

    public static final class StorageAddessHN {
        public static final String PROVINCE = "01";
        public static final String PROVINCE_ID = "12";
        public static final String PROVINCE_NAME = "Hà Nội";
        public static final String DISTRICT = "279";
        public static final String DISTRICT_ID = "273";
        public static final String DISTRICT_NAME = "Thường Tín";
        public static final String WARD = "10234";
        public static final String WARD_ID = "4386";
        public static final String WARD_NAME = "Quất Động";
        public static final String STREET = "Khu công nghiệp Quất Động Đô Quan";
        public static final String FULL_ADDRESS = "Khu công nghiệp Quất Động Đô Quan, Quất Động, Thường Tín, Hà Nội, Việt Nam";
    }

    public static final class Storage {
        public static final String HCM = "HCM";
        public static final String HN = "HN";
    }

    public static final class ServiceProvider {
        public static final String KERRY = "KERRY";
        public static final String GHN = "GHN";
        public static final String VTP = "VTP";
    }

    public static final class StorageOption {
        public static final Integer HCM = 0;
        public static final Integer HN = 1;
        public static final Integer BOTH = 2;
        public static final Integer CANCEL = 3;
    }

    public static final class KerrySubOrderStatus {
        // Người dùng từ chối
        public static final String DLY03 = "DLY03";
        public static final String DLY06 = "DLY06";
        public static final String DLY17 = "DLY17";
        public static final String DLY80 = "DLY80";

        // Bất khả kháng
        public static final String DLY84 = "DLY84";
    }

    public static final class GhnSubOrderStatus {
        // Bất khả kháng
        public static final String GHN_DFC1A6 = "GHN-DFC1A6";
        public static final String GHN_DCD0A4 = "GHN-DCD0A4";
        public static final String GHN_DCD0A5 = "GHN-DCD0A5";
        public static final String GHN_DCD0A6 = "GHN-DCD0A6";
        public static final String GHN_DCD0A7 = "GHN-DCD0A7";
        public static final String GHN_DCD1A3 = "GHN-DCD1A3";

        // Người dùng từ chối
        public static final String GHN_DFC1A1 = "GHN-DFC1A1";
        public static final String GHN_DFC1A2 = "GHN-DFC1A2";
        public static final String GHN_DFC1A3 = "GHN-DFC1A3";
        public static final String GHN_DFC1A4 = "GHN-DFC1A4";
        public static final String GHN_DFC1A7 = "GHN-DFC1A7";
        public static final String GHN_DFC1A8 = "GHN-DFC1A8";
        public static final String GHN_DCD0A0 = "GHN-DCD0A0";
        public static final String GHN_DCD0A1 = "GHN-DCD0A1";
        public static final String GHN_DCD0A2 = "GHN-DCD0A2";
        public static final String GHN_DCD0A3 = "GHN-DCD0A3";
        public static final String GHN_DCD0A8 = "GHN-DCD0A8";
        public static final String GHN_DCD1A1 = "GHN-DCD1A1";
        public static final String GHN_DCD1A2 = "GHN-DCD1A2";
        public static final String GHN_DCD1A4 = "GHN-DCD1A4";
        public static final String GHN_DCD1A5 = "GHN-DCD1A5";
    }

    public static final class OrderStatus {
        public static final Integer CREATE_WAIT = 0;
        public static final Integer CREATE = 1;
        public static final Integer CREATE_FAIL = 2;
        public static final Integer ACCEPT = 3;
        public static final Integer FAIL_TRANSFER = 4;
        public static final Integer FAIL_VITA = 5;
        public static final Integer TRANSFERRING = 6;
        public static final Integer RETURN_WAIT = 7;
        public static final Integer CANCEL_WAIT = 8;
        public static final Integer SUCCESS = 9;
        public static final Integer COMPLETE = 10;
        public static final Integer COMPLETE_SYS = 11;
        public static final Integer RETURNED = 12;
        public static final Integer CANCELED = 13;
        public static final Integer CREATE_IMPORTED = 14;
        public static final Integer BLOCK = 15;
    }

    public static final class OrderStatusText {
        public static final String CREATE_WAIT = "CREATE_WAIT";
        public static final String CREATE = "CREATE";
        public static final String CREATE_FAIL = "CREATE_FAIL";
        public static final String ACCEPT = "ACCEPT";
        public static final String FAIL_TRANSFER = "FAIL_TRANSFER";
        public static final String FAIL_VITA = "FAIL_VITA";
        public static final String TRANSFERRING = "TRANSFERRING";
        public static final String RETURN_WAIT = "RETURN_WAIT";
        public static final String CANCEL_WAIT = "CANCEL_WAIT";
        public static final String SUCCESS = "SUCCESS";
        public static final String COMPLETE = "COMPLETE";
        public static final String COMPLETE_SYS = "COMPLETE_SYS";
        public static final String RETURNED = "RETURNED";
        public static final String CANCELED = "CANCELED";
        public static final String CREATE_IMPORTED = "CREATE_IMPORTED";
        public static final String BLOCK = "BLOCK";
    }

    public static final class StatusTabName {
        public static final String ALL = "ALL";
        public static final String ACCEPT = "ACCEPT";
        public static final String DELIVERY = "DELIVERY";
        public static final String SUCCESS = "SUCCESS";
        public static final String FAIL = "FAIL";
        public static final String RETURN = "RETURN";
        public static final String TRANSFERING = "TRANSFERING";
        public static final String RETURNED = "RETURNED";
    }

    public static final class KerryStatusOrder {
        // Accept
        public static final String DISP = "DISP";

        // Transfering
        public static final String PUP = "PUP";
        public static final String SIP = "SIP";
        public static final String SIP_L = "SIP-L";
        public static final String SOP_D = "SOP-D";

        public static final String PODEX = "PODEX";
        public static final String SOPR = "SOPR";

        // Success
        public static final String POD = "POD";

        // Return-Wait or Cancel-Wait
        public static final String PODR = "PODR";

        // Fail transfer
        public static final String PUX = "PUX";
    }

    public static final class GhnStatusOrder {
        // Accept
        public static final String READY_TO_PICK = "ready_to_pick";
        public static final String PICKING = "picking";
        public static final String MONEY_COLLECT_PICKING = "money_collect_picking";

        // Transfering
        public static final String PICKED = "picked";
        public static final String TRANSPORTING = "transporting";
        public static final String STORING = "storing";
        public static final String SORTING = "sorting";
        public static final String DELIVERING = "delivering";
        public static final String MONEY_COLLECT_DELIVERING = "money_collect_delivering";

        public static final String DELIVERY_FAIL = "delivery_fail";
        public static final String WAITING_TO_RETURN = "waiting_to_return";
        public static final String RETURN = "return";
        public static final String RETURN_TRANSPORTING = "return_transporting";
        public static final String RETURN_SORTING = "return_sorting";
        public static final String RETURNING = "returning";

        // Success
        public static final String DELIVERED = "delivered";

        // Return-Wait or Cancel-Wait
        public static final String RETURNED = "returned";
        public static final String RETURN_FAIL = "return_fail";
        public static final String DAMAGE = "damage";
        public static final String LOST = "lost";

        // Fail transfer
        public static final String CANCEL = "cancel";
    }

    public static final class NumberOfCancelNoFree {
        public static int NUMBER_OF_CANCEL_ORDER = 3;
    }

    public static final class KerrySubStatusDescription {
        // Người dùng từ chối
        public static final String DLY03 = "Kerrier Mobile | Sai địa chỉ/ Thiếu thông tin người nhận";
        public static final String DLY06 = "Kerrier Mobile | Không liên lạc được";
        public static final String DLY17 = "Kerrier Mobile | Khách hàng hẹn ngày khác";
        public static final String DLY80 = "Kerrier Mobile | Khách hàng đóng cửa nghỉ";

        // Bất khả kháng
        public static final String DLY84 = "Kerrier Mobile | Do dịch bệnh Covid-19";
    }

    public static final class CrmActionType {
        public static String CREATE = "CREATE";
        public static String UPDATE = "UPDATE";
    }

    public static class HrvCouponStatus {
        public final static String USED = "inactive";
        public final static String UNUSED = "active";
    }

    public static class OutboxMessageRetryNumber {
        public final static Integer CALL_FROM_JAVA_REWARD = 99;
    }

    public static class GiftBkidsCourseType {
        public final static String COURSE = "course";
        public final static String TRAINING_PATH = "training-path";
    }

    public static class BkidsRequestCouponObjectType {
        public final static String BKIDS_COURSE = "bkids_course";
        public final static String TRAINING_PATH = "training_path";
    }

    public static class GiftExchangeEVoucher {
        public final static String GotitTopupFailedMessageSendSMS = "VitaDairy gui ban ma nap tien %s. Chi tiet vui long lien he Hotline 1900633559";
    }

    public static class EventTypePrefix {
        public final static String EVENT_PRIZE_WINNING = "EVQ_";
        public final static String EVENT_246 = "EVL_";
    }

    public static final class EventAddCanType {
        public static final String EV_24_Q2_CLG = "24_EVQ2_CLG";
        public static final String EV_24_Q2_CLBB = "24_EVQ2_CLBB";
        public static final String EV_24_Q3_CLBB = "24_EVQ3_CLBB";
        public static final String EV_24_Q4_3BRAND = "24_EVQ4_3BRAND";
    }

    public static final class EvoucherService {
        public static final String GOT_IT = "got-it";
    }

    public static final class EvoucherShopService {
        public static final String GOT_IT = "got-it";
        public static final String BKIDS = "bkids";
        public static final String VITA_CODE = "vita-code";
    }

    public static final class Cache {
        public static final String CRM_TOKEN = "crm_token";
        public static final String SAP_TOKEN = "sap_token";
        public static final String TRIPLAYZ_TOKEN = "triplayz_token";
    }

    public static final class LoyaltyV3Service {
        public static final String GET_CODE_EVOUCHER_VACXIN = "/v1/user/event/evoucher-vacxin";
    }

    public static class PhoneNumberTelco {
        public static final String VIET_TEL = "VT";
        public static final String VINA_PHONE = "VP";
        public static final String MOBI_PHONE = "MB";
        public static final String VIETNAM_MOBILE = "VM";
        public static final String G_MOBILE = "GM";
    }
}
