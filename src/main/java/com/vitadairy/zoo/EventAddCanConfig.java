package com.vitadairy.zoo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@Data
@Builder
//@JsonDeserialize(using = GiftDataDeserializer.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventAddCanConfig {
    private Integer limitTimeCanExchangeGiftTotalTimes;
    private Integer limitTimeCanExchangeGiftNumberOfCanRate;
    private Integer limitTimeCanExchangeGiftNumberOfCanValue;

    @JsonCreator
    public EventAddCanConfig(
            @JsonProperty("limit_time_can_exchange_gift_total_times") Integer limitTimeCanExchangeGiftTotalTimes,
            @JsonProperty("limit_time_can_exchange_gift_number_of_can_rate") Integer limitTimeCanExchangeGiftNumberOfCanRate,
            @JsonProperty("limit_time_can_exchange_gift_number_of_can_value") Integer limitTimeCanExchangeGiftNumberOfCanValue
    ) {
        this.limitTimeCanExchangeGiftTotalTimes = limitTimeCanExchangeGiftTotalTimes;
        this.limitTimeCanExchangeGiftNumberOfCanRate = limitTimeCanExchangeGiftNumberOfCanRate;
        this.limitTimeCanExchangeGiftNumberOfCanValue = limitTimeCanExchangeGiftNumberOfCanValue;
    }
}

