package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.Objects;

@Table(name = "users")
@Entity
@Getter
@Setter
public class User {
    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "tier_code")
    private String tierCode;

    @Column(name = "gift_point")
    private float giftPoint = 0F;

    @Column(name = "stock_point")
    private float stockPoint = 0F;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Size(max = 32)
    @Column(name = "phone_number", length = 32)
    private String phoneNumber;

    @Column(name = "account_status")
    private String accountStatus;

    @Column(name = "province_id")
    private Integer provinceId;

    @Column(name = "district_id")
    private Integer districtId;

    @Column(name = "ward_id")
    private Integer wardId;

    @Column(name = "address")
    private String address;

    @Column(name = "start_freeze_point")
    private Instant startFreezePoint;

    @Column(name = "end_freeze_point")
    private Instant endFreezePoint;

    public boolean checkUserIsNotProcessingByDDX() {
        Instant startFreezePoint = this.getStartFreezePoint();
        Instant endFreezePoint = this.getEndFreezePoint();
        if (Objects.nonNull(startFreezePoint) && Objects.nonNull(endFreezePoint)) {
            Instant now = Instant.now();
            if (!now.isBefore(startFreezePoint) && !now.isAfter(endFreezePoint)) {
                return false;
            }
        }

        return true;
    }
}
