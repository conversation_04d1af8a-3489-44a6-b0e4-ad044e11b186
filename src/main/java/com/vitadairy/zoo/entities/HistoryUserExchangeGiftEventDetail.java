package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "history_user_exchange_gift_event_detail")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoryUserExchangeGiftEventDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "history_user_exchange_gift_event_id", nullable = false)
    private Integer historyUserExchangeGiftEventId;

    @Column(name = "gift_id", nullable = false)
    private Integer giftId;

    @Column(name = "total", nullable = false)
    private Integer total;
}
