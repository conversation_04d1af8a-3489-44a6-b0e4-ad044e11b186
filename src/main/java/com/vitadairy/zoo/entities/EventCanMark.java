package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "event_can_mark")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventCanMark {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "type_user", nullable = false)
    private String userType;

    @Column(name = "number_scan", nullable = false)
    private Integer numberScan;

    @Column(name = "preferential_point", nullable = false)
    private Integer preferentialPoint;

    @Column(name = "gift_id", nullable = true)
    private Long giftId;

    @Column(name = "gs_gift_id", nullable = true)
    private Long gsGiftId;
}
