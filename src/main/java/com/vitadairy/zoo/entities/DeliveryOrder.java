package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "delivery_order")
public class DeliveryOrder implements Serializable {
    @Id
    private Integer id;
    @Column(name = "ref_order_code")
    private String refOrderCode;
    @Column(name = "phone_number_user")
    private String phoneNumberUser;
    @Column(name = "phone_number_recipient")
    private String phoneNumberRecipient;
    @Column(name = "order_status")
    private String orderStatus;
    @Column(name = "accepted_time")
    private Timestamp acceptedTime;
    @Column(name = "transferring_time")
    private Timestamp transferringTime;
    @Column(name = "succeeded_time")
    private Timestamp succeededTime;
    @Column(name = "completed_time")
    private Timestamp completedTime;
    @Column(name = "canceled_time")
    private Timestamp canceledTime;
    @Column(name = "returned_time")
    private Timestamp returnedTime;
    @Column(name = "ship_fee")
    private Double shipFee;
    @Column(name = "order_type")
    private String orderType;
    @Column(name = "point")
    private Integer point;
    @Column(name = "transport_service")
    private String transportService;
    @Column(name = "is_freeship")
    private Boolean isFreeShip;
    @Column(name = "user_id")
    private Integer userId;
    @Column(name = "recipient_address")
    private String recipientAddress;
    @Column(name = "recipient_name")
    private String recipientName;
    @Column(name = "recipient_ward_code")
    private String recipientWardCode;
    @Column(name = "recipient_district_code")
    private String recipientDistrictCode;
    @Column(name = "recipient_province_code")
    private String recipientProvinceCode;
    @Column(name = "sub_order_status")
    private String subOrderStatus;
    @Column(name = "re_order_by")
    private Integer reOrderBy;
    @Column(name = "transport_time")
    private Timestamp transportTime;
    @Column(name = "transaction_order_id")
    private String transactionOrderId;
}
