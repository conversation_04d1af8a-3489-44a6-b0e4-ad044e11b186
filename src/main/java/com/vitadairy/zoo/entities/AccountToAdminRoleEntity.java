package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account_to_admin_roles")
@Getter
@Setter
public class AccountToAdminRoleEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(nullable = false, name = "account_id")
    private Integer accountId;

    @Column(nullable = false, name = "admin_role_id")
    private Integer adminRoleId;
}
