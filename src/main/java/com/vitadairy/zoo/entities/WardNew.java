package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "ward_new")
@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class WardNew {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Long id;
    private String code;
    private String name;

    @ManyToOne()
    @JoinColumn(name = "province_id")
    private ProvinceNew newProvince;
}
