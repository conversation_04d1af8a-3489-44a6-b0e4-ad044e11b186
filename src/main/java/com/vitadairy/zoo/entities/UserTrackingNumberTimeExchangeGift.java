package com.vitadairy.zoo.entities;

import com.vitadairy.zoo.EventAddCanConfig;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.OffsetDateTime;

@Entity
@Table(name = "user_tracking_number_time_exchange_gifts")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTrackingNumberTimeExchangeGift {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "number_of_times_earn", nullable = false)
    private Integer numberOfTimesEarn = 0;

    @Column(name = "number_of_times_used", nullable = false)
    private Integer numberOfTimesUsed = 0;

    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    @Column(name = "event_add_can_id", nullable = false)
    private Integer eventAddCanId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_add_can_config", columnDefinition = "JSONB")
    private EventAddCanConfig eventAddCanConfig;

    public Boolean checkUserExchangeGiftExceedNumberTimesEarned() {
        return
                this.numberOfTimesUsed >= this.numberOfTimesEarn ?
                        Boolean.TRUE :
                        Boolean.FALSE;
    }

    public Boolean checkUserExchangeGiftExceedTotalNumberTimes() {
        return
                this.numberOfTimesUsed >= this.eventAddCanConfig.getLimitTimeCanExchangeGiftTotalTimes() ?
                        Boolean.TRUE :
                        Boolean.FALSE;
    }
}
