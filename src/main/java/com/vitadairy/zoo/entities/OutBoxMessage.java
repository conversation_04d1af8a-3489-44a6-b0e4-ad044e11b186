package com.vitadairy.zoo.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.zoo.enums.*;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "outbox_message")
@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class OutBoxMessage {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @SequenceGenerator(name = "outbox_message_id_seq", sequenceName = "outbox_message_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "outbox_message_id_seq")
    private Long id;

    @Enumerated(EnumType.STRING)
    private SyncProvider provider;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_type")
    private CallType callType;

    @Enumerated(EnumType.STRING)
    @Column(name = "sync_type")
    private SyncType syncType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OutboxMessageStatus status;

    @Column(name = "retry_number")
    private Integer retryNumber;

    @Column
    private String request;

    @Column
    private String response;

    @Column
    @Enumerated(EnumType.STRING)
    private OutboxMessageType type;


    @Column(name = "created_date")
    @JsonIgnore
    private Instant createdDate;
}
