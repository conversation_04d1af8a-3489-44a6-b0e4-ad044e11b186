package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "user_number_scan")
public class UserNumberScan {
    @Id
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "number_scan_in_day")
    private Integer numberScanInDay;

    @Column(name = "number_scan_in_month")
    private Integer numberScanInMonth;

    @Column(name = "number_scan")
    private Integer numberScan;

    @Column(name = "number_point_used_in_day")
    private Float numberPointUsedInDay;

    public UserNumberScan(Long userId) {
        this.userId = userId;
        this.numberScanInDay = 1;
        this.numberScanInMonth = 1;
        this.numberScan = 1;
        this.numberPointUsedInDay = 0f;
    }
}
