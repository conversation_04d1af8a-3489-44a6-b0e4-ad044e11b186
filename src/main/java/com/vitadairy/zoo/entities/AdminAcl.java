package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "account_acls")
@Getter
@Setter
public class AdminAcl {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(nullable = false, name = "account_id")
    private Integer accountId;

    @Column(nullable = false, name = "action_id")
    private Integer actionId;

    @Column(nullable = false, name = "admin_menu_module_id")
    private Integer adminMenuModuleId;
}
