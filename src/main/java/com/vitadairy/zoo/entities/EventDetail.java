package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.util.LinkedHashSet;
import java.util.Set;

@Entity
@Table(name = "event_detail")
@Getter
@Setter
public class EventDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(nullable = false)
    private Integer probability;

    @Column(nullable = false)
    private Integer ordinal;

    @Column(nullable = false)
    private Long quantity;

    @Column(nullable = false, name = "notification_title")
    private String notificationTitle;

    @Column(nullable = false, name = "notification_content")
    private String notificationContent;

    @Column(nullable = false, name = "notification_description")
    private String notificationDescription;

    @Column(name = "gift_id", nullable = false)
    private Long giftId;

    @Column(name = "gs_gift_id", nullable = false)
    private Long gsGiftId;
}
