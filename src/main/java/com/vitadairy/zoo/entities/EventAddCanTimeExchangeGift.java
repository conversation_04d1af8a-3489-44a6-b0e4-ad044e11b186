package com.vitadairy.zoo.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "event_add_can_time_exchange_gift")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventAddCanTimeExchangeGift {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "start_date")
    private OffsetDateTime startDate;

    @Column(name = "end_date")
    private OffsetDateTime endDate;

    @Column(name = "event_add_can_id", nullable = false)
    private Integer eventAddCanId;
}
