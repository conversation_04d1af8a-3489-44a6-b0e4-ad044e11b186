package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "gift")
public class Gift implements Serializable {
    @Id
    private Integer id;
    @Column(name = "type")
    private String type;
    @Column(name = "name")
    private String name;
    @Column(name = "image")
    private String image;
    @Column(name = "e_product_id")
    private Integer eProductId;
    @Column(name = "e_product_name")
    private String eProductName;
    @Column(name = "price_id")
    private Integer priceId;
    @Column(name = "price_name")
    private String priceName;
    @Column(name = "price_value")
    private Integer priceValue;
    @Column(name = "point")
    private Double point;
    @Column(name = "total")
    private Integer total;
    @Column(name = "start_date")
    private Timestamp startDate;
    @Column(name = "end_date")
    private Timestamp endDate;
    @Column(name = "created_date")
    private Timestamp createdDate;
    @Column(name = "updated_date")
    private Timestamp updatedDate;
    @Column(name = "description")
    private String description;
    @Column(name = "gift_category_id")
    private Integer giftCategoryId;
    @Column(name = "link")
    private String link;
    @Column(name = "e_category_id")
    private Integer eCategoryId;
    @Column(name = "e_category_name")
    private String eCategoryName;
    @Column(name = "e_category_img")
    private String eCategoryImg;
    @Column(name = "cover_image")
    private String coverImage;
    @Column(name = "priority")
    private Integer priority;
    @Column(name = "money")
    private Integer money;
    @Column(name = "sub_category_id")
    private Integer subCategoryId;
    @Column(name = "active")
    private Boolean active;
    @Column(name = "sub_gift_type")
    private String subGiftType;
    @Column(name = "code")
    private String code;
    @Column(name = "total_by_location")
    private Boolean totalByLocation;
    @Column(name = "expire_time")
    private Integer expireTime;
    @Column(name = "active_sms_notification")
    private Boolean activeSmsNotification;
    @Column(name = "brand_id")
    private Integer brandId;
    @Column(name = "sms_gift_id")
    private String smsGiftId;
    @Column(name = "telco")
    private String telco;
    @Column(name = "campaign_id")
    private Integer campaignId;
    @Column(name = "campaign_code")
    private String campaignCode;
    @Column(name = "bkids_course_type")
    private String bkidsCourseType;
    @Column(name = "bkids_course_id")
    private String bkidsCourseId;
    @Column(name = "bkids_course_name")
    private String bkidsCourseName;
    @Column(name = "bkids_course_class")
    private String bkidsCourseClass;
    @Column(name = "bkids_course_price")
    private Integer bkidsCoursePrice;
    @Column(name = "highlight")
    private Boolean highlight;
}
