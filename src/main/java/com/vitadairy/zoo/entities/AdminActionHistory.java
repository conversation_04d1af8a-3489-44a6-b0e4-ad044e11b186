package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "admin_action_histories")
@Getter
@Setter
public class AdminActionHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(nullable = false, name = "admin_name")
    private String adminName;

    @Column(nullable = false, name = "admin_action")
    private String adminAction;

    @Column(nullable = false, name = "admin_menu")
    private String adminMenu;

    @Column(nullable = false, name = "record_identity")
    private String recordIdentity;
}
