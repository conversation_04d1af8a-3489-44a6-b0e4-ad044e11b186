package com.vitadairy.zoo.entities.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@Converter(autoApply = true)
public class InstantToGmt7JpaConverter implements AttributeConverter<Instant, Timestamp> {

    private static final ZoneId GMT_PLUS_7 = ZoneId.of("Asia/Bangkok");

    @Override
    public Timestamp convertToDatabaseColumn(Instant instant) {
        if (instant == null) {
            return null;
        }
        ZonedDateTime gmtPlus7Time = instant.atZone(GMT_PLUS_7);
        return Timestamp.from(gmtPlus7Time.toInstant());
    }

    @Override
    public Instant convertToEntityAttribute(Timestamp dbTimestamp) {
        if (dbTimestamp == null) {
            return null;
        }
        // Convert Timestamp (UTC) to ZonedDateTime in GMT+7, then get the Instant
        return dbTimestamp.toInstant();
    }
}
