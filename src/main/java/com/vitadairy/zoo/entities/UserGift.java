package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "user_gift")
public class UserGift implements Serializable {
    @Id
    private Integer id;
    @Column(name = "user_id")
    private Integer userId;
    @Column(name = "gift_id")
    private Integer giftId;
    @Column(name = "is_used")
    private Boolean isUsed;
    @Column(name = "used_date")
    private Timestamp usedDate;
    @Column(name = "expiry_date")
    private Timestamp expiryDate;
    @Column(name = "voucher_ref_id")
    private String voucherRefId;
    @Column(name = "recipient_name")
    private String recipientName;
    @Column(name = "recipient_phone")
    private String recipientPhone;
    @Column(name = "recipient_address")
    private String recipientAddress;
    @Column(name = "created_date")
    private Timestamp createdDate;
    @Column(name = "status")
    private String status;
    @Column(name = "voucher_link")
    private String voucherLink;
    @Column(name = "updated_date")
    private Timestamp updatedDate;
    @Column(name = "action_type")
    private String actionType;
    @Column(name = "point")
    private Double point;
    @Column(name = "product_id")
    private Integer productId;
    @Column(name = "sub_gift_id")
    private Integer subGiftId;
    @Column(name = "included_gift_id")
    private Integer includedGiftId;
    @Column(name = "voucher_code")
    private String voucherCode;
    @Column(name = "telco")
    private String telco;
    @Column(name = "deadline_reward")
    private Timestamp deadlineReward;
    @Column(name = "is_checked")
    private Boolean isChecked;
    @Column(name = "is_sent_expiration_notification")
    private Boolean isSendExpirationNotification;
    @Column(name = "rollbacked_at")
    private Timestamp rollbackedAt;
    @Column(name = "is_congratulate")
    private Boolean isCongratulate;
    @Column(name = "version")
    private Integer version;
    @Column(name = "cdp_sync_up")
    private Boolean cdpSyncUp;
    @Column(name = "quantity")
    private Integer quantity;
    @Column(name = "number_of_used")
    private Integer numberOfUsed;
    @Column(name = "response_from_3rd")
    private String responseFrom3rd;
    @Column(name = "is_reusing")
    private Boolean isReusing;
}
