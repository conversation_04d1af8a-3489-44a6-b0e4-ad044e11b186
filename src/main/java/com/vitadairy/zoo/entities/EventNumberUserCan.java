package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

import java.time.OffsetDateTime;

@Entity
@Table(name = "event_number_user_can")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventNumberUserCan {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "phone_number", nullable = false)
    private String phoneNumber;

    @Column(name = "number_of_can", nullable = false)
    private float numberOfCan = 0f;

    @Column(name = "number_of_can_used", nullable = false)
    private float numberOfCanUsed = 0f;

    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    @Column(name = "deleted_at", nullable = false)
    private OffsetDateTime deletedAt;

    @Column(name = "version", nullable = false)
    private Integer version = 1;

    @Column(name = "event_add_can_id", nullable = false)
    private Integer eventAddCanId;

    @Column(name = "user_id", nullable = false)
    private Long userId;
}
