package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@NoArgsConstructor
@Table(name = "crm_transaction_type")
public class CrmTransactionType {
    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CRMTRANSACTIONTYPE_ID_SEQ")
    @SequenceGenerator(name = "CRMTRANSACTIONTYPE_ID_SEQ", sequenceName = "CRMTRANSACTIONTYPE_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "code", unique = true, nullable = false)
    private String code;

    @Column(name = "name", unique = false, nullable = false)
    private String name;

    @Column(name = "description", unique = true, nullable = false)
    private String description;

    @Column(name = "main_code", unique = true, nullable = false)
    private String mainCode;

    @Column(name = "campaign_name")
    private String campaignName;

    @Column(name = "display_name")
    private String displayName;
    
    //@JsonIgnore
    //@OneToOne(mappedBy = "crmTransactionType", fetch = FetchType.LAZY)
    //private EventDetail eventDetail;
    
    public CrmTransactionType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public CrmTransactionType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
}