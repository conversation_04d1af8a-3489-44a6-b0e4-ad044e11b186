package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "user_tag_agg")
@IdClass(UserTagAggId.class)
public class UserTagAgg {

    @Id
    @Column(name = "user_id")
    private Long userId;

    @Id
    @Column(name = "brand")
    private String brand;

    @Column(name = "point")
    private Float point = 0F;
}
