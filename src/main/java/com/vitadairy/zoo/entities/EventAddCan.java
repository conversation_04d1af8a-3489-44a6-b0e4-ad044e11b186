package com.vitadairy.zoo.entities;

import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.enums.CompareDateTimeRsEnum;
import com.vitadairy.zoo.interfaces.CompareDateTimeRsInterface;
import com.vitadairy.zoo.util.DateTimeUtil;
import jakarta.persistence.*;
import lombok.*;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "event_add_can")
@Getter
@Setter
@ToString(exclude = { "giftCategories" })
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventAddCan {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "event_name", nullable = false)
    private String eventName;

    @Column(name = "start_date")
    private OffsetDateTime startDate;

    @Column(name = "end_date")
    private OffsetDateTime endDate;

    @Column(name = "status", nullable = false)
    private String status = "ACTIVE";

    @Column(name = "convert_point", nullable = false)
    private Integer convertPoint;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "deleted_at", nullable = false)
    private OffsetDateTime deletedAt;

    @OneToMany(mappedBy = "eventAddCan")
    private List<GiftCategory> giftCategories = new ArrayList<>();

    @Column(name = "gs_gift_category_codes", columnDefinition = "TEXT[]", nullable = true)
    private String[] gsGiftCategoryCodes;

    @Column(name = "enable_time_exchange_gift", nullable = false)
    private Boolean enableTimeExchangeGift;

    @Column(name = "enable_limit_time_can_exchange_gift", nullable = false)
    private Boolean enableLimitTimeCanExchangeGift;

    @Column(name = "loyalty_program_sf_id")
    private String loyaltyProgramSfId;

    public Boolean checkEventExpired() {
        OffsetDateTime now = OffsetDateTime.now(Constant.TIMEZONE.HCM_ZONE);
        CompareDateTimeRsInterface rsCompareStartDateWithNow = DateTimeUtil.compareTwoDate(this.startDate, now);
        CompareDateTimeRsInterface rsCompareEndDateWithNow = DateTimeUtil.compareTwoDate(this.endDate, now);

        if (
                Objects.isNull(rsCompareStartDateWithNow) ||
                        Objects.isNull(rsCompareEndDateWithNow)
        ) {
            return Boolean.TRUE;
        }

        if (
                rsCompareStartDateWithNow.equals(CompareDateTimeRsEnum.AFTER) ||
                        rsCompareEndDateWithNow.equals(CompareDateTimeRsEnum.BEFORE)
        ) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
