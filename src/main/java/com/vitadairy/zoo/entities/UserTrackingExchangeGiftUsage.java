package com.vitadairy.zoo.entities;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.vitadairy.main.entities.BaseEntity;
import com.vitadairy.zoo.GiftConfig;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Entity
@Getter
@Setter
@Table(name = "user_tracking_exchange_gift_usages")
@AllArgsConstructor
@NoArgsConstructor
public class UserTrackingExchangeGiftUsage extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "gift_id", nullable = false)
    private Integer giftId;

    @Column(name = "time_exchange_gift_id", nullable = false)
    private Long timeExchangeGiftId;

    @Column(name = "time_exchange_gift_detail_id", nullable = false)
    private Long timeExchangeGiftDetailId;

    @Column(name = "number_of_times_used", nullable = false)
    private Integer numberOfTimesUsed;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "gift_config", columnDefinition = "jsonb")
    private GiftConfig giftConfig;
}