package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString(exclude = { "eventAddCan" })
@Entity
@Table(name = "gift_category")
public class GiftCategory {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GIFT_CATEGORY_ID_SEQ")
    @SequenceGenerator(name = "GIFT_CATEGORY_ID_SEQ", sequenceName = "GIFT_CATEGORY_ID_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "parent_id")
    private Long parentId;

    @ManyToOne(optional = true)
    @JoinColumn(name = "event_add_can_id", referencedColumnName = "id")
    private EventAddCan eventAddCan;

    @Column(name = "event_add_can_id", insertable = false, updatable = false)
    private Integer eventAddCanId;
}
