package com.vitadairy.zoo.entities;

import com.vitadairy.zoo.enums.GotItTopupStatus;
import com.vitadairy.zoo.enums.GotItTransactionType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "got_it_transaction_log")
@NoArgsConstructor
public class GotItTransactionLog {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @SequenceGenerator(name = "got_it_transaction_log_id_seq", sequenceName = "got_it_transaction_log_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "got_it_transaction_log_id_seq")
    private Long id;

    @Column(name = "ref_id")
    private String refId;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "request", nullable = false)
    private String request = "{}";

    @Column(name = "response")
    private String response;

    @Column(name = "status")
    @Enumerated(EnumType.ORDINAL)
    private GotItTopupStatus status;

    @Column(name = "transaction_type")
    @Enumerated(EnumType.STRING)
    private GotItTransactionType transactionType;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;

    @Column(name = "created_by", updatable = false)
    private Long createdBy;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Column(name = "transaction_external_id")
    private String transactionExternalId;

    public GotItTransactionLog(User user, String request, Long createdBy, String refId, GotItTransactionType type){
        this.refId = refId;
        this.user = user;
        this.request = request;
        this.status = GotItTopupStatus.STATUS_WAITING;
        this.createdBy = createdBy;
        this.createdAt = Instant.now();
        this.transactionType = type;
    }

    public GotItTransactionLog(User user, Long userId, GotItTransactionType type) {
        this.user = user;
        this.status = GotItTopupStatus.STATUS_WAITING;
        this.createdBy = userId;
        this.createdAt = Instant.now();
        this.transactionType = type;
    }
}
