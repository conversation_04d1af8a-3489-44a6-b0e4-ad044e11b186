package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Table(name = "province_new")
@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ProvinceNew {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Integer id;
    private String code;
    private String name;
}
