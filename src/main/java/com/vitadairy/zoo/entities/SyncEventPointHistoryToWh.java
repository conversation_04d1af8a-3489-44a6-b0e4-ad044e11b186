package com.vitadairy.zoo.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vitadairy.zoo.enums.OutboxMessageStatus;
import com.vitadairy.zoo.enums.SyncEventPointHistoryToWhMethod;
import com.vitadairy.zoo.enums.SyncEventPointHistoryToWhStatus;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.Instant;

@Entity
@Table(name = "sync_event_point_history_to_wh")
@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class SyncEventPointHistoryToWh {
    @Id
    @Column(name = "id", unique = true, nullable = false)
    @SequenceGenerator(name = "sync_event_point_history_to_wh_id_seq", sequenceName = "sync_event_point_history_to_wh_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sync_event_point_history_to_wh_id_seq")
    private Long id;

    @Column(name = "event_point_history_id", nullable = false)
    private Long eventPointHistoryId;

    @Column(name = "transaction_code", nullable = false)
    private String transactionCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SyncEventPointHistoryToWhStatus status = SyncEventPointHistoryToWhStatus.WAITING;

    @Enumerated(EnumType.STRING)
    @Column(name = "method", nullable = false)
    private SyncEventPointHistoryToWhMethod method = SyncEventPointHistoryToWhMethod.INSERT;

    @Column(name = "retry", nullable = false)
    private Integer retry = 0;

    @Column(name = "user_access_token", nullable = false)
    private String userAccessToken;

    @Column(name = "request", nullable = false)
    private String request;

    @Column(name = "response")
    private String response;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, columnDefinition = "TIMESTAMP")
    private Instant createdAt;
}
