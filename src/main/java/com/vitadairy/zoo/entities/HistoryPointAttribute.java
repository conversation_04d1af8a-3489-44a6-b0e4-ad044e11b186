package com.vitadairy.zoo.entities;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "history_point_attribute")
public class HistoryPointAttribute {
    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_HISTORY_POINT_ATTRIBUTE_ID")
    @SequenceGenerator(name = "SEQ_HISTORY_POINT_ATTRIBUTE_ID", sequenceName = "SEQ_HISTORY_POINT_ATTRIBUTE_ID", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "history_point_id", nullable = false)
    private HistoryPoint historyPoint;

    @Column(name = "attribute_code")
    private String attributeCode;

    @Column(name = "value")
    private String value;

    public HistoryPointAttribute(HistoryPoint historyPoint, String attributeCode, String value) {
        this.historyPoint = historyPoint;
        this.attributeCode = attributeCode;
        this.value = value;
    }

    public HistoryPointAttribute(String attributeCode, String value) {
        this.historyPoint = historyPoint;
        this.attributeCode = attributeCode;
        this.value = value;
    }
}
