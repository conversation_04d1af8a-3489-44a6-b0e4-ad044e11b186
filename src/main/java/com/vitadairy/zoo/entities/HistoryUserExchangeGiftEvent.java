package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

import java.time.OffsetDateTime;

@Entity
@Table(name = "history_user_exchange_gift_event")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoryUserExchangeGiftEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "event_add_can_time_exchange_gift_id", nullable = false)
    private Integer eventAddCanTimeExchangeGiftId;

    @Column(name = "event_add_can_time_exchange_gift_detail_id", nullable = false)
    private Integer eventAddCanTimeExchangeGiftDetailId;

    @Column(name = "total", nullable = false)
    private Integer total;
}
