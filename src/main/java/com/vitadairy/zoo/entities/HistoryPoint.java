package com.vitadairy.zoo.entities;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity
@ToString
@Table(name = "history_point")
public class HistoryPoint {
    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_HISTORY_POINT_ID")
    @SequenceGenerator(name = "SEQ_HISTORY_POINT_ID", sequenceName = "SEQ_HISTORY_POINT_ID", allocationSize = 1)
    private Long id;

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "customer_phone")
    private String customerPhone;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private String status;

    @Column(name = "action_type")
    private String actionType;

    @Column(name = "transaction_date")
    private Instant transactionDate;

    @Column(name = "gift_point")
    private Float giftPoint = 0F;

    @Column(name = "stock_point")
    private Float stockPoint = 0F;

    @Column(name = "tier_point")
    private Float tierPoint = 0F;

    @Column(name = "money")
    private long money = 0L;

    @Column(name = "customer_child_id")
    private Long customerChildId;

    @Column(name = "transaction_external_id")
    private String transactionExternalId;

    @Column(name = "brand")
    private String brand;

    @Column(name = "gift_id")
    private Long giftId;

    @Column(name = "cdp_sync_up")
    private Boolean cdpSyncUp;

    @Column(name = "is_gift_received")
    private Boolean isGiftReceived = false;

    @OneToMany(mappedBy = "historyPoint", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<HistoryPointAttribute> attributes = new ArrayList<>();

    // @OneToOne(mappedBy = "historyPoint", cascade = CascadeType.ALL, orphanRemoval
    // = true, fetch = FetchType.LAZY)
    // private EventPointHistory eventPointHistory;

    // @OneToOne(mappedBy = "historyPoint", fetch = FetchType.LAZY)
    // private StoreInvitation storeInvitation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", insertable = false, updatable = false, referencedColumnName = "id")
    @Fetch(FetchMode.JOIN)
    private User user;

    public float getGiftPoint() {
        return Objects.isNull(giftPoint) ? 0F : (float) Math.round(giftPoint * 100) / 100;
    }

    public float getTierPoint() {
        return Objects.isNull(tierPoint) ? 0F : (float) Math.round(tierPoint * 100) / 100;
    }
}
