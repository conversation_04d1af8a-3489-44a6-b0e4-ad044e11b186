package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;

import java.time.OffsetDateTime;

@Entity
@Table(name = "event_add_can_time_exchange_gift_detail")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventAddCanTimeExchangeGiftDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "total", nullable = false)
    private Integer total;

    @Column(name = "event_add_can_time_exchange_gift_id", nullable = false)
    private Integer eventAddCanTimeExchangeGiftId;
}
