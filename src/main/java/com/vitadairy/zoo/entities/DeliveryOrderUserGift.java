package com.vitadairy.zoo.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "delivery_order_user_gift")
public class DeliveryOrderUserGift implements Serializable {
    @Id
    private Integer id;
    @Column(name = "user_gift_id")
    private Integer userGiftId;
    @Column(name = "order_id")
    private Integer orderId;
    @Column(name = "detail_name")
    private String detailName;
}
