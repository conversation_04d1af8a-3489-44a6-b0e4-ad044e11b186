package com.vitadairy.zoo.entities;

import com.vitadairy.zoo.enums.BkidsTransactionHistoryStatus;
import com.vitadairy.zoo.enums.BkidsTransactionHistoryType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "bkids_transaction_history")
@NoArgsConstructor
public class BkidsTransactionHistory {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @SequenceGenerator(name = "bkids_transaction_history_id_seq", sequenceName = "bkids_transaction_history_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bkids_transaction_history_id_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private BkidsTransactionHistoryType type;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private BkidsTransactionHistoryStatus status;

    @Column(name = "request")
    private String request;

    @Column(name = "response")
    private String response;

    @Column(name = "created_date")
    private Instant createdDate;

    public BkidsTransactionHistory(User user, BkidsTransactionHistoryType type) {
        this.user = user;
        this.type = type;
        this.createdDate = Instant.now();
    }
}
