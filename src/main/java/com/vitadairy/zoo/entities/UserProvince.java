package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "user_provinces")
public class UserProvince {
    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_provinces_id_seq")
    @SequenceGenerator(name = "user_provinces_id_seq", sequenceName = "user_provinces_id_seq", allocationSize = 1)
    private Long id;

    @OneToOne(optional = false)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @ManyToOne()
    @JoinColumn(name = "new_province_id")
    private ProvinceNew newProvince;

    @ManyToOne()
    @JoinColumn(name = "new_ward_id")
    private WardNew newWard;

    @Column(name = "enter_by_user", nullable = false)
    private Boolean enterByUser;
}
