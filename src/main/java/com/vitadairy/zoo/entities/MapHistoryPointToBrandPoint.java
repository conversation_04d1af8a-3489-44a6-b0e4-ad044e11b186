package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.OffsetDateTime;

@Entity
@Table(name = "map_history_to_brand_points")
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MapHistoryPointToBrandPoint {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "history_point_id", nullable = false)
    private Integer historyPointId;

    @Column(name = "brand_id", nullable = false)
    private Integer brandId;

    @Column(name = "event_add_can_id", nullable = false)
    private Integer eventAddCanId;

    @Column(name = "brand_point", nullable = false)
    private Float brandPoint;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;
}
