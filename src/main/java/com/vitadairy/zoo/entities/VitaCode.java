package com.vitadairy.zoo.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "vita_code")
@Getter
@Setter
public class VitaCode {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(nullable = false)
    private String code;

    @Column(name = "expiry_date")
    private Instant expiryDate;

    @Column(name = "created_date")
    private Instant createdDate;

    @Column(name = "sms_status")
    private boolean smsStatus;

    @Column(name = "province_id")
    private Integer provinceId;

    @Column(name = "event_source")
    private String eventSource;

    @Column(name = "new_province_id")
    private Integer newProvinceId;
}
