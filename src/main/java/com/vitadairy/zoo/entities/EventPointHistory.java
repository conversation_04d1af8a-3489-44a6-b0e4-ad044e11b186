package com.vitadairy.zoo.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.Instant;

@Entity
@Table(name = "event_point_history")
@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class EventPointHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "gift_name")
    private String giftName;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "product_id")
    private Long productId;

    @Column(name = "spoon_code")
    private String spoonCode;

    @Column(name = "qr_code")
    private String qrCode;

    @Column(name = "history_point_id")
    private Long historyPointId;

    @Column(name = "event_detail_id")
    private Long eventDetailId;

    @Column(name = "created_date")
    @JsonIgnore
    private Instant createdDate;

    @Column(name = "updated_date")
    @JsonIgnore
    private Instant updatedDate;
}
