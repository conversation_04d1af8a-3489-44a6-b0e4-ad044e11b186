package com.vitadairy.zoo.util;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DateRangeSplitterUtil {
    public enum PeriodType {
        DAY, WEEK, MONTH
    }

    public static class DateInterval {
        @JsonProperty("start_date")
        private OffsetDateTime startDate;

        @JsonProperty("end_date")
        private OffsetDateTime endDate;

        @JsonProperty("period_type")
        private PeriodType periodType;

        public DateInterval(OffsetDateTime startDate, OffsetDateTime endDate, PeriodType periodType) {
            this.startDate = startDate;
            this.endDate = endDate;
            this.periodType = periodType;
        }

        public OffsetDateTime getStartDate() { return startDate; }
        public OffsetDateTime getEndDate() { return endDate; }
        public PeriodType getPeriodType() { return periodType; }
    }

    public static List<DateInterval> split(OffsetDateTime startDate, OffsetDateTime endDate, PeriodType periodType) {
        List<DateInterval> intervals = new ArrayList<>();
        OffsetDateTime cursor = startDate;

        if (!startDate.isBefore(endDate)) {
            return intervals;
        }

        switch (periodType) {
            case DAY:
                while (!cursor.isAfter(endDate)) {  
                    OffsetDateTime startOfDay = DateTimeUtil.getStartOfDay(cursor);
                    OffsetDateTime endOfDay = DateTimeUtil.getEndOfDay(cursor);

                    if (startOfDay.isBefore(startDate)) startOfDay = startDate;
                    if (endOfDay.isAfter(endDate)) endOfDay = endDate;

                    intervals.add(new DateInterval(
                            startOfDay,
                            endOfDay,
                            PeriodType.DAY
                    ));
                    cursor = endOfDay.plusSeconds(1);
                }
                break;

            case WEEK:
                while (!cursor.isAfter(endDate)) {
                    OffsetDateTime startOfWeek = DateTimeUtil.getStartOfWeek(cursor);
                    OffsetDateTime endOfWeek = DateTimeUtil.getEndOfWeek(cursor);

                    if (startOfWeek.isBefore(startDate)) startOfWeek = startDate;
                    if (endOfWeek.isAfter(endDate)) endOfWeek = endDate;

                    intervals.add(new DateInterval(
                            startOfWeek,
                            endOfWeek,
                            PeriodType.WEEK
                    ));
                    cursor = endOfWeek.plusSeconds(1);
                }
                break;

            case MONTH:
                while (!cursor.isAfter(endDate)) {
                    OffsetDateTime startOfMonth = DateTimeUtil.getStartOfMonth(cursor);
                    OffsetDateTime endOfMonth = DateTimeUtil.getEndOfMonth(cursor);

                    if (startOfMonth.isBefore(startDate)) startOfMonth = startDate;
                    if (endOfMonth.isAfter(endDate)) endOfMonth = endDate;

                    intervals.add(new DateInterval(
                            startOfMonth,
                            endOfMonth,
                            PeriodType.MONTH
                    ));
                    cursor = endOfMonth.plusSeconds(1);
                }
                break;
        }
        return intervals;
    }
}
