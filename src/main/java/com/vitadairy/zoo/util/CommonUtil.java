package com.vitadairy.zoo.util;

import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.entities.Feature;
import com.vitadairy.zoo.repositories.FeatureRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.time.Instant;
import java.util.Collection;
import java.util.Random;
import java.util.function.Function;

@RequiredArgsConstructor
@Component
public class CommonUtil {
    private final FeatureRepository featureRepository;

    public void checkFeatureByCode(String code) {
        Feature feature = featureRepository.findByCodeIgnoreCase(code).orElseThrow(() -> new ApplicationException("Feature not exists", HttpStatus.FORBIDDEN));
        if (Constant.FeatureStatus.INACTIVE.equalsIgnoreCase(feature.getStatus())) {
            throw new ApplicationException("Feature locked", HttpStatus.FORBIDDEN);
        }
        if (Constant.FeatureStatus.MAINTAIN.equalsIgnoreCase(feature.getStatus())) {
            throw new ApplicationException("System is maintaining", HttpStatus.FORBIDDEN);
        }
    }

    public String generateUniqueTransactionId() {
        return Long.toHexString(System.currentTimeMillis()) + "_" + new Random().nextInt(1000);
    }

    public String generateUniqueVoucherRefId() {
        return Long.toString(Instant.now().toEpochMilli()).concat(Long.toString(new Random().nextLong()));
    }

    public <T> int findObjectCollectionByField(Collection<T> collection, Function<T, Object> function, T findingObject) {
        int index = 0;
        for (T t : collection) {
            if (function.apply(t).equals(function.apply(findingObject))) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public String buildUrlFromDto(String baseUrl, String path, Object dto) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl).path(path);

        try {
            for (PropertyDescriptor pd : Introspector.getBeanInfo(dto.getClass(), Object.class).getPropertyDescriptors()) {
                Object value = pd.getReadMethod().invoke(dto);
                if (value != null) {
                    builder.queryParam(pd.getName(), value.toString());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to build URL from DTO", e);
        }

        return builder.toUriString();
    }
}
