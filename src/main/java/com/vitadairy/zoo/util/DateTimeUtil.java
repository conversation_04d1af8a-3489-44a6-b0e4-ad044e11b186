package com.vitadairy.zoo.util;


import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.enums.CompareDateTimeRsEnum;
import com.vitadairy.zoo.interfaces.CompareDateTimeRsInterface;
import org.springframework.security.core.parameters.P;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

public class DateTimeUtil {
    public static String formatInstant(Instant input, String format) {
        if (input == null) {
            return null;
        } else {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault());
            return dateTimeFormatter.format(input);
        }
    }

    public static String formatInstant(Instant instant, String pattern, ZoneId zoneId) {
        try {
            ZonedDateTime zonedDateTime = instant.atZone(zoneId);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern); // e.g., "yyyy-MM-dd HH:mm:ss"
            return zonedDateTime.format(formatter);
        } catch (Exception e) {
            return null;
        }
    }

    public static Instant convertStringToInstant(String dateStr, String format, ZoneId zoneId) {

        if (dateStr == null || format == null)
            return null;
        if (zoneId == null) {
            zoneId = ZoneId.of("UTC");
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            if (format.length() <= 11) {
                LocalDate localDate = LocalDate.parse(dateStr, formatter);
                return localDate.atStartOfDay(zoneId).toInstant();
            } else {
                LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
                return localDateTime.atZone(zoneId).toInstant();
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static CompareDateTimeRsInterface compareTwoDate(
            OffsetDateTime date1,
            OffsetDateTime date2
    ) {
        if (
                Objects.isNull(date1) ||
                        Objects.isNull(date2)
        ) {
            return null;
        }

        if (date1.isBefore(date2)) {
            return CompareDateTimeRsEnum.BEFORE;
        } else if (date1.isAfter(date2)) {
            return CompareDateTimeRsEnum.AFTER;
        } else {
            return CompareDateTimeRsEnum.EQUAL;
        }
    }

    public static OffsetDateTime getCurrentOffsetDateTime() {
        return OffsetDateTime.now(Constant.TIMEZONE.HCM_ZONE);
    }

    public static OffsetDateTime convertTimestampToOffsetDateTime(Timestamp timestamp) {
        return timestamp.toInstant().atOffset(Constant.TIMEZONE.GMT_7_OFFSET);
    }

    public static boolean checkDateInDateRange(
            OffsetDateTime checkDate,
            OffsetDateTime startDate,
            OffsetDateTime endDate
    ) {
        if (
                Objects.isNull(startDate) ||
                        Objects.isNull(endDate) ||
                        Objects.isNull(checkDate)
        ) {
            return false;
        }

        if (checkDate.isBefore(startDate) || checkDate.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    public static boolean checkDateRangeOverlap(
            OffsetDateTime startDate1,
            OffsetDateTime endDate1,
            OffsetDateTime startDate2,
            OffsetDateTime endDate2
    ) {
        if (
                Objects.isNull(startDate1) ||
                        Objects.isNull(endDate1) ||
                        Objects.isNull(startDate2) ||
                        Objects.isNull(endDate2)
        ) {
            return false;
        }

        return !startDate1.isAfter(endDate2) && !startDate2.isAfter(endDate1);
    }

    public static OffsetDateTime getStartOfDay(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(LocalTime.MIN);
    }

    public static OffsetDateTime getEndOfDay(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(LocalTime.MAX.truncatedTo(ChronoUnit.MILLIS));
    }

    public static OffsetDateTime getStartOfWeek(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(DayOfWeek.MONDAY).with(LocalTime.MIN);
    }

    public static OffsetDateTime getEndOfWeek(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(DayOfWeek.SUNDAY).with(LocalTime.MAX.truncatedTo(ChronoUnit.MILLIS));
    }

    public static OffsetDateTime getStartOfMonth(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    public static OffsetDateTime getEndOfMonth(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX.truncatedTo(ChronoUnit.MILLIS));
    }

    public static OffsetDateTime getStartOfNextDay(OffsetDateTime date) {
        return date.withOffsetSameInstant(Constant.TIMEZONE.GMT_7_OFFSET).plusDays(1).with(LocalTime.MIN);
    }
}
