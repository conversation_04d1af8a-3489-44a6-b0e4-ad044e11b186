package com.vitadairy.zoo.util;


import com.vitadairy.zoo.enums.CompareDateTimeRsEnum;
import com.vitadairy.zoo.interfaces.CompareDateTimeRsInterface;
import org.springframework.security.core.parameters.P;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class DateTimeUtil {
    public static String formatInstant(Instant input, String format) {
        if (input == null) {
            return null;
        } else {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault());
            return dateTimeFormatter.format(input);
        }
    }

    public static String formatInstant(Instant instant, String pattern, ZoneId zoneId) {
        try {
            ZonedDateTime zonedDateTime = instant.atZone(zoneId);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern); // e.g., "yyyy-MM-dd HH:mm:ss"
            return zonedDateTime.format(formatter);
        } catch (Exception e) {
            return null;
        }
    }

    public static Instant convertStringToInstant(String dateStr, String format, ZoneId zoneId) {

        if (dateStr == null || format == null)
            return null;
        if (zoneId == null) {
            zoneId = ZoneId.of("UTC");
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            if (format.length() <= 11) {
                LocalDate localDate = LocalDate.parse(dateStr, formatter);
                return localDate.atStartOfDay(zoneId).toInstant();
            } else {
                LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
                return localDateTime.atZone(zoneId).toInstant();
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static CompareDateTimeRsInterface compareTwoDate(
            OffsetDateTime date1,
            OffsetDateTime date2
    ) {
        if (
                Objects.isNull(date1) ||
                        Objects.isNull(date2)
        ) {
            return null;
        }

        if (date1.isBefore(date2)) {
            return CompareDateTimeRsEnum.BEFORE;
        } else if (date1.isAfter(date2)) {
            return CompareDateTimeRsEnum.AFTER;
        } else {
            return CompareDateTimeRsEnum.EQUAL;
        }
    }
}
