package com.vitadairy.zoo.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.zoo.common.Constant;
import com.vitadairy.zoo.dto.DDXTrackingUserActionWithPointRequestDto;
import com.vitadairy.zoo.dto.UserGiftNotificationRequestDto;
import com.vitadairy.zoo.entities.OutBoxMessage;
import com.vitadairy.zoo.entities.User;
import com.vitadairy.zoo.enums.*;
import com.vitadairy.zoo.repositories.OutboxMsgRepository;
import com.vitadairy.zoo.util.CommonUtil;
import com.vitadairy.zoo.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service("zooDDXService")
public class DDXService {
    @Value("${rest.url.ddx-v3}")
    private String baseUrl;

    private RestTemplate restTemplate;

    private CommonUtil commonUtil;

    private OutboxMsgRepository outboxMsgRepository;

    private ObjectMapper objectMapper;

    public DDXService(
            RestTemplate restTemplate,
            CommonUtil commonUtil,
            OutboxMsgRepository outboxMsgRepository,
            ObjectMapper objectMapper
    ) {
        this.restTemplate = restTemplate;
        this.commonUtil = commonUtil;
        this.outboxMsgRepository = outboxMsgRepository;
        this.objectMapper = objectMapper;
    }

    public void trackingUserActionWithPoint(DDXTrackingUserActionWithPointRequestDto dto) {
//        if (List.of(577930L, 137143L).contains(dto.getCustomerId())) {
//            throw new ApplicationException("Test sync ddx fail and lose data", HttpStatus.BAD_REQUEST);
//        }

        try {
            String fullUrl = this.commonUtil.buildUrlFromDto(
                    baseUrl,
                    "/async/identity-point/ra-to-table",
                    dto
            );
            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> request = new HttpEntity<>(null, headers);
//            String curl = String.format(
//                    "curl -X POST \"%s\"",
//                    fullUrl
//            );
//            log.debug("cURL trackingUserActionWithPoint Equivalent: {}", curl);
            ResponseEntity<Void> response = restTemplate.exchange(fullUrl, HttpMethod.POST, request, Void.class);
            OutBoxMessage outBoxMessage = new OutBoxMessage();
            outBoxMessage.setProvider(SyncProvider.JAVA_NEW_TO_DDX);
            outBoxMessage.setCallType(CallType.SYNC);
            outBoxMessage.setSyncType(SyncType.IMMEDIATE);
            outBoxMessage.setRetryNumber(99);
            try {
                outBoxMessage.setRequest(this.objectMapper.writeValueAsString(dto));
                outBoxMessage.setResponse(this.objectMapper.writeValueAsString(response));
            } catch (Exception ignored) {}
            outBoxMessage.setStatus(OutboxMessageStatus.SUCCESS);

            this.outboxMsgRepository.save(outBoxMessage);
        } catch (Exception e) {
            throw e;
        }
    }

    public DDXTrackingUserActionWithPointRequestDto generateDataTracking(
            User user,
            EnumHistoryPointType type,
            EnumHistoryPointActionType actionType,
            float totalPointBefore,
            float numberPoint,
            float totalPointAfter,
            String transactionExternalId,
            Instant transactionTime
    ) {
        DDXTrackingUserActionWithPointRequestDto dto = new DDXTrackingUserActionWithPointRequestDto();

        dto.setCustomerId(user.getId());
        String customerName = "";
        if (
                Objects.nonNull(user.getName()) &&
                        !user.getName().isEmpty()
        ) {
            customerName = user.getName();
        } else {
            List<String> names = new ArrayList<>();
            if (
                    Objects.nonNull(user.getFirstName()) &&
                            !user.getFirstName().isEmpty()
            ) {
                names.add(user.getFirstName());
            }
            if (
                    Objects.nonNull(user.getLastName()) &&
                            !user.getLastName().isEmpty()
            ) {
                names.add(user.getLastName());
            }
            if (names.size() > 0) {
                customerName = String.join(" ", names);
            }
        }
        if (!customerName.isEmpty()) {
            dto.setCustomerName(customerName);
        }
        dto.setCustomerPhone(user.getPhoneNumber());
        dto.setType(type.getLabel());
        dto.setActionType(actionType.toString());
        dto.setTotalPointBefore(totalPointBefore);
        dto.setNumberPoint(Math.abs(numberPoint));
        dto.setTotalPointAfter(totalPointAfter);
        dto.setTransactionExternalId(transactionExternalId);
        String transactionTimeStr = DateTimeUtil.formatInstant(
                transactionTime,
                Constant.DateTimeFormat.YYYY_MM_DD_HH_MM_SS,
                Constant.TIMEZONE.HCM_ZONE
        );
        dto.setTransactionTime(transactionTimeStr);

        return dto;
    }
}
