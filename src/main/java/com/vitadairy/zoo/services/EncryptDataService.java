package com.vitadairy.zoo.services;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.DirectDecrypter;
import com.nimbusds.jose.crypto.DirectEncrypter;
import com.nimbusds.jose.jwk.OctetSequenceKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

@Slf4j
@Service("zooEncryptDataService")
public class EncryptDataService {
    public String encryptData(String data, String accessToken, Integer userId) throws Exception {
        if (
                data.isEmpty() ||
                        accessToken.isEmpty() ||
                        userId <= 0
        ) {
            return "";
        }

        String keyExtract = this.extractKey(accessToken, 79);

        // 1) Derive a 32-byte key from the token (SHA-256)
        byte[] keyBytes = this.sha256(String.valueOf(userId) + keyExtract + String.valueOf(userId));

        String kid = "derived-from-token"; // any identifier you like

        return this.encryptA256GCM(keyBytes, kid, data, /*optional AAD*/ null);
    }

    private byte[] sha256(String s) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        return md.digest(s.getBytes(StandardCharsets.UTF_8));
    }

    private String encodeToString(byte[] keyBytes) {
        return Base64.getEncoder().encodeToString(keyBytes);
    }

    /** Encrypts a plaintext string using JWE alg=DIR, enc=A256GCM. */
    private String encryptA256GCM(byte[] key, String kid, String plaintext, String aadOrNull) throws Exception {
        // Build header
        JWEHeader header = new JWEHeader.Builder(JWEAlgorithm.DIR, EncryptionMethod.A256GCM)
                .contentType("text/plain") // optional
                .keyID(kid)                // optional
                .build();

        // Create JWE object with payload
        JWEObject jwe = new JWEObject(header, new Payload(plaintext));

        // Encrypt with the symmetric key
        OctetSequenceKey oct = new OctetSequenceKey.Builder(key).keyID(kid).build();
        jwe.encrypt(new DirectEncrypter(oct.toSecretKey()));

        // Return compact serialization
        return jwe.serialize();
    }

    /** Decrypts a compact JWE string with the same key and optional AAD. */
    private String decryptA256GCM(byte[] key, String compactJwe, String aadOrNull) throws Exception {
        JWEObject jwe = JWEObject.parse(compactJwe);

        OctetSequenceKey oct = new OctetSequenceKey.Builder(key).build();
        jwe.decrypt(new DirectDecrypter(oct.toSecretKey()));

        return jwe.getPayload().toString();
    }

    private String extractKey(String token, int startIndex) {
        if (token == null || startIndex < 0 || startIndex >= token.length()) {
            throw new IllegalArgumentException("Invalid input");
        }
        int underscore = token.indexOf('_', startIndex);
        return (underscore >= 0)
                ? token.substring(startIndex, underscore)
                : token.substring(startIndex);
    }
}
