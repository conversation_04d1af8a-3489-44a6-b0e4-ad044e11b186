package com.vitadairy.zoo.services;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.vitadairy.zoo.dto.UserGiftNotificationRequestDto;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("zooNotificationService")
public class UserGiftNotificationService {
    @Value("${rest.url.vita-noti-v3}")
    private String notiApiUrl;

    private RestTemplate restTemplate;

    public UserGiftNotificationService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void push(UserGiftNotificationRequestDto body) {
        try {
            String url = notiApiUrl + "/user/gs-usergift";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> request = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            log.info("[Response] {} ", response.getBody());
        } catch (Exception e) {
            // TODO: handle exception
        }
    }
}
