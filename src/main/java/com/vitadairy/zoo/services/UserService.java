package com.vitadairy.zoo.services;

import com.vitadairy.main.dto.DeductPointResponseDto;
import com.vitadairy.main.dto.UserPointDto;
import com.vitadairy.zoo.repositories.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service("userService")
@Slf4j
public class UserService {
    private final UserRepository userRepository;
    private final JdbcTemplate jdbcTemplate;

    public UserService(@Qualifier("zooUserRepository") UserRepository userRepository, @Qualifier("zooJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.userRepository = userRepository;
        this.jdbcTemplate = jdbcTemplate;;
    }

    @Transactional(value = "zooTransactionManager", isolation = Isolation.SERIALIZABLE)
    public void lockUser(Long userId) {
        userRepository.lockUser(userId);
    }

//    @Transactional(value = "zooTransactionManager", isolation = Isolation.SERIALIZABLE, propagation = Propagation.REQUIRES_NEW)
    @Transactional(value = "zooTransactionManager", isolation = Isolation.SERIALIZABLE)
    public DeductPointResponseDto deductPoints(Long userId, double pointsToDeduct) {
        DeductPointResponseDto responseDto = DeductPointResponseDto.builder().success(false).build();

        String checkPointsSql = "SELECT coalesce(gift_point,0) as gift_point, coalesce(stock_point, 0) as stock_point FROM users WHERE id = ? FOR UPDATE NOWAIT";
        UserPointDto userPoints = jdbcTemplate.queryForObject(checkPointsSql, new Object[]{userId}, (rs, rowNum) ->
                new UserPointDto(rs.getDouble("gift_point"), rs.getDouble("stock_point"))
        );

        if (Objects.isNull(userPoints) || userPoints.getGiftPoint() < pointsToDeduct) {
            return responseDto;
        }
        // Deduct points
        if(userPoints.getStockPoint() >= pointsToDeduct){
            String deductStockPointsSql = "UPDATE users SET stock_point = stock_point - ?, gift_point = gift_point - ? WHERE id = ? AND gift_point - ? >= 0";
            int pointDeduct = jdbcTemplate.update(deductStockPointsSql, pointsToDeduct, pointsToDeduct, userId, pointsToDeduct);
            if (pointDeduct == 0){
                return responseDto;
            }
            else {
                responseDto.setSuccess(true);
                responseDto.setStockPoint(pointsToDeduct);
                responseDto.setGiftPoint(pointsToDeduct);
            }
        } else if(userPoints.getStockPoint() > 0){
            String deductStockPointsSql = "UPDATE users SET stock_point = 0, gift_point = gift_point - ? WHERE id = ? AND gift_point - ? >= 0";
            int pointDeduct = jdbcTemplate.update(deductStockPointsSql,pointsToDeduct, userId, pointsToDeduct);
            if (pointDeduct == 0){
                return responseDto;
            }
            else {
                responseDto.setSuccess(true);
                responseDto.setGiftPoint(pointsToDeduct);
                responseDto.setStockPoint(userPoints.getStockPoint());
            }
        }
        else{
            String deductPointsSql = "UPDATE users SET gift_point = gift_point - ? WHERE id = ? AND gift_point - ? >= 0";
            int pointDeduct = jdbcTemplate.update(deductPointsSql, pointsToDeduct, userId, pointsToDeduct);
            if (pointDeduct == 0){
                return responseDto;
            }
            else {
                responseDto.setSuccess(true);
                responseDto.setGiftPoint(pointsToDeduct);
            }
        }
        return responseDto;
    }

    @Transactional(value = "zooTransactionManager", isolation = Isolation.SERIALIZABLE)
    public DeductPointResponseDto returnPoint(Long userId, double stockPoint, double giftPoint, int retryTimes) {
        DeductPointResponseDto responseDto = DeductPointResponseDto.builder().success(false).build();
        try{
            String sql = "UPDATE users SET stock_point = coalesce(stock_point,0) + ?, gift_point = coalesce(gift_point,0) + ? WHERE id = ?";
            int result = jdbcTemplate.update(sql, stockPoint, giftPoint, userId);

            if (result == 0) {
                log.error("Revert deduct points failed for user id: {}", userId);
                if (retryTimes <= 5){
                    responseDto = returnPoint(userId, stockPoint, giftPoint, retryTimes + 1);
                }
            }
            else{
                responseDto.setSuccess(true);
                responseDto.setStockPoint(stockPoint);
                responseDto.setGiftPoint(giftPoint);
            }
        }
        catch (Exception e){
            log.error("Revert deduct points failed for user id: {}", userId, e);
            if (retryTimes <= 5){
                responseDto = returnPoint(userId, stockPoint, giftPoint, retryTimes + 1);
            }
        }
        return responseDto;
    }
}
