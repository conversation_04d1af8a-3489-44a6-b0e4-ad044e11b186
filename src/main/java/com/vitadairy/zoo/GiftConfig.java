package com.vitadairy.zoo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class GiftConfig {
    private Integer limitExchangeGift;

    @JsonCreator
    public GiftConfig(
        @JsonProperty("limit_exchange_gift") Integer limitExchangeGift
    ) {
        this.limitExchangeGift = limitExchangeGift;
    }
}

