package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.CrmTransactionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("zooCrmTransactionTypeRepository")
public interface CrmTransactionTypeRepository extends JpaRepository<CrmTransactionType, Long>,
	JpaSpecificationExecutor<CrmTransactionType> {
	Optional<CrmTransactionType> findFirstByCode(String code);
}