package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.AdminAcl;
import com.vitadairy.zoo.entities.AdminRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository("zooAdminAclRepository")
public interface AdminAclRepository extends JpaRepository<AdminAcl, Long> {
    @Query(value = "SELECT count(aa.id) FROM account_acls aa " +
            "INNER JOIN action_on_admin_menu_modules action " +
            "ON action.id = aa.action_id AND action.code = ANY(:actionCodes) " +
            "INNER JOIN admin_menu_modules menu " +
            "ON menu.id = aa.admin_menu_module_id AND menu.code = :adminMenuModuleCode " +
            "WHERE aa.account_id = :accountId ", nativeQuery = true)
    public Integer countSetupByAccountIdAndAdminMenuModuleCodeAndActionCodeIn(@Param("accountId") Integer accountId, @Param("adminMenuModuleCode") String adminMenuModuleCode, @Param("actionCodes") String[] actionCodes);
}
