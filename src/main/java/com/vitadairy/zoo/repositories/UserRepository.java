package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.User;
import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository("zooUserRepository")
public interface UserRepository extends JpaRepository<User, Long> {

    @Transactional("zooTransactionManager")
    Optional<User> getUserById(Long id);

    List<User> findAllByIdIn(Collection<Long> ids);

    @Transactional(value = "zooTransactionManager", isolation = Isolation.SERIALIZABLE)
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints(@QueryHint(name = "javax.persistence.lock.timeout",value = "0"))
    @Query(value = "SELECT c FROM User c WHERE c.id = :id")
    User lockUser(Long id);

    @Modifying
    @Transactional("zooTransactionManager")
    @Query("UPDATE User u SET u.giftPoint = :giftPoint WHERE u.id = :id")
    void updateGiftPointById(Long id, float giftPoint);
}
