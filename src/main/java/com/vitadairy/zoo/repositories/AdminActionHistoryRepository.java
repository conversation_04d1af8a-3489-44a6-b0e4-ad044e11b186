package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.AdminActionHistory;
import com.vitadairy.zoo.entities.AdminRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository("zooAdminActionHistoryRepository")
public interface AdminActionHistoryRepository extends JpaRepository<AdminActionHistory, Long> {
}
