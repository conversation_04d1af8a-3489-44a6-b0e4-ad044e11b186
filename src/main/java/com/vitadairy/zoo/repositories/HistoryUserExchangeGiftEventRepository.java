package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventAddCanTimeExchangeGift;
import com.vitadairy.zoo.entities.EventAddCanTimeExchangeGiftDetail;
import com.vitadairy.zoo.entities.HistoryUserExchangeGiftEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

public interface HistoryUserExchangeGiftEventRepository extends JpaRepository<HistoryUserExchangeGiftEvent, Long> {
    Optional<HistoryUserExchangeGiftEvent> findFirstByUserIdAndEventAddCanTimeExchangeGiftDetailId(Long userId, Integer eventAddCanTimeExchangeGiftDetailId);

    @Modifying
    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE history_user_exchange_gift_event AS history SET " +
            "total = history.total + :quantity " +
            "FROM event_add_can_time_exchange_gift_detail AS setup_detail " +
            "WHERE history.event_add_can_time_exchange_gift_detail_id = setup_detail.id AND history.user_id = :userId " +
            "AND history.event_add_can_time_exchange_gift_detail_id = :eventAddCanTimeExchangeGiftDetailId " +
            "AND setup_detail.total - history.total >= :quantity", nativeQuery = true)
    int increaseTotalExchangeEventGiftOfUser(@Param("userId") Long userId, @Param("eventAddCanTimeExchangeGiftDetailId") Integer eventAddCanTimeExchangeGiftDetailId, @Param("quantity") Integer quantity);

    @Modifying
//    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE history_user_exchange_gift_event AS history SET " +
            "total = coalesce(history.total - :quantity, 0) " +
            "WHERE history.user_id = :userId " +
            "AND history.event_add_can_time_exchange_gift_detail_id = :eventAddCanTimeExchangeGiftDetailId ", nativeQuery = true)
    int decreaseTotalExchangeEventGiftOfUser(@Param("userId") Long userId, @Param("eventAddCanTimeExchangeGiftDetailId") Integer eventAddCanTimeExchangeGiftDetailId, @Param("quantity") Integer quantity);
}
