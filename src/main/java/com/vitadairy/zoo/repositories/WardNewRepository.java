package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.ProvinceNew;
import com.vitadairy.zoo.entities.WardNew;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface WardNewRepository extends JpaRepository<WardNew, Long> {
    Optional<WardNew> findByIdAndNewProvince(Long id, ProvinceNew provinceNew);
}
