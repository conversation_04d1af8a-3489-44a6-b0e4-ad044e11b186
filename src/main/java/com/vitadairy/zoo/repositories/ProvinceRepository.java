package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.Province;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProvinceRepository extends JpaRepository<Province, Long> {

    Optional<Province> findAllByCodeAndType(String code, String type);

    Optional<Province> findByIdAndType(Long id, String type);
}
