package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventNumberUserCan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EventNumberUserCanRepository extends JpaRepository<EventNumberUserCan, Long> {
    @Query("select eventNumberUserCan from EventNumberUserCan eventNumberUserCan " +
            "where eventNumberUserCan.eventAddCanId = :eventAddCanId and eventNumberUserCan.userId = :userId")
    EventNumberUserCan findByEventAddCanAndUser(@Param("eventAddCanId") Integer eventAddCanId, @Param("userId") Long userId);
}
