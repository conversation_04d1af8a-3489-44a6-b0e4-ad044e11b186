package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.DeliveryOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface LegacyDeliveryOrderRepository extends JpaRepository<DeliveryOrder, Integer> {
    Page<DeliveryOrder> findAllByAcceptedTimeGreaterThanEqual(Timestamp acceptedTime, Pageable pageable);

    long countByAcceptedTimeGreaterThanEqual(Timestamp acceptedTime);

    Page<DeliveryOrder> findAllByIdIn(List<Integer> ids, Pageable pageable);

    Page<DeliveryOrder> findAllByAcceptedTimeGreaterThanEqualAndAcceptedTimeLessThanEqual(Timestamp from, Timestamp to, Pageable pageable);

    long countByAcceptedTimeGreaterThanEqualAndAcceptedTimeLessThanEqual(Timestamp from, Timestamp to);
}
