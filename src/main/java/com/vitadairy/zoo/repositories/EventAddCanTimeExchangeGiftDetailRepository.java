package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventAddCanTimeExchangeGift;
import com.vitadairy.zoo.entities.EventAddCanTimeExchangeGiftDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface EventAddCanTimeExchangeGiftDetailRepository extends JpaRepository<EventAddCanTimeExchangeGiftDetail, Long> {
    @Query(value = "SELECT eac_detail.* FROM event_add_can_time_exchange_gift eac " +
            "INNER JOIN event_add_can_time_exchange_gift_detail eac_detail ON " +
            "eac.id = eac_detail.event_add_can_time_exchange_gift_id " +
            "WHERE eac.event_add_can_id = :eventAddCanId AND " +
            "eac.start_date < NOW() AND NOW() < eac.end_date AND " +
            ":giftId = ANY(eac_detail.gift_ids) " +
            "LIMIT 1", nativeQuery = true)
    Optional<EventAddCanTimeExchangeGiftDetail> getDetailSetupGiftOfEventAddCanEffectivity(@Param("eventAddCanId") Integer eventAddCanId, @Param("giftId") Integer giftId);
}
