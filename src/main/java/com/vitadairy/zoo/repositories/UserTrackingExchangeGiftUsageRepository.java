package com.vitadairy.zoo.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.vitadairy.zoo.entities.UserTrackingExchangeGiftUsage;

@Repository("zooUserTrackingExchangeGiftUsageRepository")
public interface UserTrackingExchangeGiftUsageRepository extends JpaRepository<UserTrackingExchangeGiftUsage, Long> {
    @Query(value = "SELECT * FROM user_tracking_exchange_gift_usages " +
            "WHERE user_id = :userId " +
            "AND gift_id = :giftId " +
            "AND time_exchange_gift_detail_id IN (:timeExchangeGiftDetailIds)",
            nativeQuery = true)
    Optional<List<UserTrackingExchangeGiftUsage>> findByUserIdAndGiftIdAndTimeExchangeGiftDetailIds(@Param("userId") Long userId, 
                                                            @Param("giftId") Integer giftId, @Param("timeExchangeGiftDetailIds") List<Long> timeExchangeGiftDetailIds);

    @Modifying
    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE user_tracking_exchange_gift_usages " +
                "SET number_of_times_used = number_of_times_used + :numberOfTimesUsed, " +
                "updated_at = NOW() " +
                "WHERE id = :id " +
                "AND number_of_times_used = :oldNumberOfTimesUsed " +
                "AND number_of_times_used + :numberOfTimesUsed <= (gift_config->>'limit_exchange_gift')::int",
        nativeQuery = true)
    int increaseNumberOfTimesUsed(@Param("id") Long id, @Param("oldNumberOfTimesUsed") Integer oldNumberOfTimesUsed, @Param("numberOfTimesUsed") Integer numberOfTimesUsed);

    @Modifying
    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE user_tracking_exchange_gift_usages " +
                "SET number_of_times_used = number_of_times_used - :numberOfTimesUsed, " +
                "updated_at = NOW() " +
                "WHERE id = :id " + 
                "AND number_of_times_used = :oldNumberOfTimesUsed " +
                "AND number_of_times_used >= :numberOfTimesUsed"
                , nativeQuery = true)
    int decreaseNumberOfTimesUsed(@Param("id") Long id, @Param("oldNumberOfTimesUsed") Integer oldNumberOfTimesUsed, @Param("numberOfTimesUsed") Integer numberOfTimesUsed);
}
