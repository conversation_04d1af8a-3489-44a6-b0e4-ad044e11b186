package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.VitaCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VitaCodeRepository extends JpaRepository<VitaCode, Long> {
    VitaCode findByCode(String code);

    Optional<VitaCode> findFirstByProvinceIdAndSmsStatus(Integer provinceId, boolean smsStatus);

    Optional<VitaCode> findFirstByNewProvinceIdAndSmsStatus(Integer newProvinceId, boolean smsStatus);
    
    @Query(value = "SELECT * FROM vita_code vc WHERE vc.sms_status = :smsStatus AND event_source = :eventSource ORDER BY id LIMIT 1 FOR UPDATE SKIP LOCKED", nativeQuery = true)
    Optional<VitaCode> findFirstByEventSourceAndSmsStatusForUpdateSkipLocked(String eventSource, boolean smsStatus);
}
