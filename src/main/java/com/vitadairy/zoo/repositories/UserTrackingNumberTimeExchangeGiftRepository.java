package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventAddCan;
import com.vitadairy.zoo.entities.EventNumberUserCan;
import com.vitadairy.zoo.entities.UserTrackingNumberTimeExchangeGift;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository("zooUserTrackingNumberTimeExchangeGiftRepository")
public interface UserTrackingNumberTimeExchangeGiftRepository extends JpaRepository<UserTrackingNumberTimeExchangeGift, Long> {
    Optional<UserTrackingNumberTimeExchangeGift> findFistByEventAddCanIdAndUserId(Integer eventAddCanId, Long userId);

    @Modifying
    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE user_tracking_number_time_exchange_gifts SET " +
            "number_of_times_used = number_of_times_used + :numberOfTimesUsed " +
            "WHERE id = :id " +
            "AND number_of_times_earn >= number_of_times_used + :numberOfTimesUsed", nativeQuery = true)
    int increaseNumberOfTimesUsed(@Param("id") Integer id, @Param("numberOfTimesUsed") Integer numberOfTimesUsed);

    @Modifying
//    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE user_tracking_number_time_exchange_gifts SET " +
            "number_of_times_used = number_of_times_used - :numberOfTimesUsed " +
            "WHERE id = :id " +
            "AND number_of_times_used >= :numberOfTimesUsed", nativeQuery = true)
    int decreaseNumberOfTimesUsed(@Param("id") Integer id, @Param("numberOfTimesUsed") Integer numberOfTimesUsed);
}
