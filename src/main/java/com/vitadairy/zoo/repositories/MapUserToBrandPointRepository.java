package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.MapUserToBrandPoint;
import com.vitadairy.zoo.entities.UserTrackingNumberTimeExchangeGift;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository("zooMapUserToBrandPointRepository")
public interface MapUserToBrandPointRepository extends JpaRepository<MapUserToBrandPoint, Long> {
    Optional<MapUserToBrandPoint> findFistByUserIdAndBrandIdAndEventAddCanId(Long userId, Integer brandId, Integer eventAddCanId);

    @Modifying
    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE map_user_to_brand_points SET " +
            "brand_point = brand_point - :decreaseBrandPoint " +
            ",updated_at = NOW() " +
            "WHERE id = :id " +
            "AND brand_point = :oldBrandPoint " +
            "AND brand_point >= :decreaseBrandPoint", nativeQuery = true)
    int decreaseBrandPoint(@Param("id") Long id, @Param("oldBrandPoint") Float oldBrandPoint, @Param("decreaseBrandPoint") Integer decreaseBrandPoint);

    @Modifying
//    @Transactional(value="zooTransactionManager", propagation = Propagation.REQUIRED)
    @Query(value = "UPDATE map_user_to_brand_points SET " +
            "brand_point = brand_point + :increaseBrandPoint " +
            ",updated_at = NOW() " +
            "WHERE id = :id " +
            "AND brand_point = :oldBrandPoint", nativeQuery = true)
    int increaseBrandPoint(@Param("id") Long id, @Param("oldBrandPoint") Float oldBrandPoint, @Param("increaseBrandPoint") Integer increaseBrandPoint);
}
