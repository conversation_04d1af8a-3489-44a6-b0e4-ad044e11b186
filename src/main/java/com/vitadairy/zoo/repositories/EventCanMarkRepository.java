package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventAddCan;
import com.vitadairy.zoo.entities.EventCanMark;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("zooEventCanMarkRepository")
public interface EventCanMarkRepository extends JpaRepository<EventCanMark, Long> {
    Optional<EventCanMark> findFirstByGiftId(Long giftId);

    Optional<EventCanMark> findFirstByGsGiftId(Long gsGiftId);

    @Query(value = "select ecm.* from event_can_mark ecm " +
            "where ecm.event_add_can_id = :eventAddCanId and ecm.gift_id = :giftId", nativeQuery = true)
    Optional<EventCanMark> findByEventAddCanIdAndGiftId(long eventAddCanId, long giftId);

    @Query(value = "select ecm.* from event_can_mark ecm " +
            "where ecm.event_add_can_id = :eventAddCanId and ecm.gs_gift_id = :gsGiftId", nativeQuery = true)
    Optional<EventCanMark> findByEventAddCanIdAndGsGiftId(long eventAddCanId, long gsGiftId);
}
