package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.Feature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository("zooFeatureRepository")
public interface FeatureRepository extends JpaRepository<Feature, String> {
    Optional<Feature> findByCodeIgnoreCase(String code);

}
