package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.HistoryUserExchangeGiftEvent;
import com.vitadairy.zoo.entities.HistoryUserExchangeGiftEventDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

public interface HistoryUserExchangeGiftEventDetailRepository extends JpaRepository<HistoryUserExchangeGiftEventDetail, Long> {
}
