package com.vitadairy.zoo.repositories.impl;

import com.vitadairy.zoo.repositories.ZooCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ZooCustomRepositoryImpl implements ZooCustomRepository {

    private static final String SESSION_INPUT = "sessionInput";

    @PersistenceContext(unitName = "zoo")
    private final EntityManager entityManager;

    @Override
    public List<Object[]> runFunctionAsQuery(String sql, Map<String, Object> parameters) {
        try {
            Query query = entityManager.createNativeQuery(sql, Object.class);
            if (!parameters.isEmpty()) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    query.setParameter(entry.getKey(), entry.getValue());
                }
            }
            return query.getResultList();
        } catch (Exception ex) {
            log.error("Error when execute query: {}", sql, ex);
            return List.of();
        }
    }

    @Override
    public List<Object[]> callFunctionUpdateUserPoints(String input) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("input", input);
        return runFunctionAsQuery("SELECT * FROM update_user_points(:input)", parameters);
    }
}
