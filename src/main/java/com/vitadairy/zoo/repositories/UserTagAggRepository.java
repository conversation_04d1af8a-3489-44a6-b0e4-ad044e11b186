package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.UserTagAgg;
import com.vitadairy.zoo.entities.UserTagAggId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("zooUserTagRepository")
public interface UserTagAggRepository extends JpaRepository<UserTagAgg, UserTagAggId> {
    List<UserTagAgg> findAllByUserIdOrderByPointDescBrandAsc(Long userId);
}
