package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventAddCan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface EventAddCanRepository extends JpaRepository<EventAddCan, Long> {
    @Query("select eac from EventAddCan eac where eac.id = :eventAddCanId")
    Optional<EventAddCan> findFirstById(Integer eventAddCanId);

    @Query(value = "SELECT eac.* FROM event_add_can AS eac WHERE :categoryCode = ANY(eac.gs_gift_category_codes) AND eac.deleted_at IS NULL ORDER BY eac.id DESC LIMIT 1", nativeQuery = true)
    Optional<EventAddCan> findFistByCategoryCode(String categoryCode);

    @Query(value = "SELECT DISTINCT eac.gs_gift_category_codes FROM event_add_can AS eac WHERE eac.gs_gift_category_codes IS NOT NULL AND eac.deleted_at IS NULL", nativeQuery = true)
    Optional<List<List<String>>> findGiftCategoryCodesInListEventAddCan();
}
