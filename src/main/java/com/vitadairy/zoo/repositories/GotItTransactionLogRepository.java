package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.GotItTransactionLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Optional;

@Repository
public interface GotItTransactionLogRepository extends JpaRepository<GotItTransactionLog, Long> {
    @Query(value = "SELECT * FROM got_it_transaction_log WHERE user_id = :userId" +
            " and created_at >= :createdDate" +
            " order by created_at OFFSET 0 LIMIT 1", nativeQuery = true)
    Optional<GotItTransactionLog> getLogMatchWithUserGift(@Param("userId") Long userId, @Param("createdDate") Instant createdDate);

    @Query(value = "SELECT * FROM got_it_transaction_log WHERE ref_id = :refId" +
            " order by created_at OFFSET 0 LIMIT 1", nativeQuery = true)
    Optional<GotItTransactionLog> findByRefId(@Param("refId") String refId);
}
