package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.SystemConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository("zooSystemConfigRepository")
public interface SystemConfigRepository extends JpaRepository<SystemConfig, String> {

    List<SystemConfig> findByOrderByCode();

    Optional<SystemConfig> findByCode(String code);


}
