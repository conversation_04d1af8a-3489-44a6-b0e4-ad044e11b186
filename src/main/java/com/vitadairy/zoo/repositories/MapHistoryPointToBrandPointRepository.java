package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.MapHistoryPointToBrandPoint;
import com.vitadairy.zoo.entities.MapUserToBrandPoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository("zooMapHistoryPointToBrandPoint")
public interface MapHistoryPointToBrandPointRepository extends JpaRepository<MapHistoryPointToBrandPoint, Long> {}
