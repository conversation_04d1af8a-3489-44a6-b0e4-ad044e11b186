package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventCanMark;
import com.vitadairy.zoo.entities.EventDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("zooEventDetailRepository")
public interface EventDetailRepository extends JpaRepository<EventDetail, Long> {
    Optional<EventDetail> findFirstByGiftId(Long giftId);

    Optional<EventDetail> findFirstByGsGiftId(Long gsGiftId);
}
