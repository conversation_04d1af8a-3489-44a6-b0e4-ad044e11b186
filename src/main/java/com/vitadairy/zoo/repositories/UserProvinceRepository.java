package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.UserNumberScan;
import com.vitadairy.zoo.entities.UserProvince;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("zooUserProvince")
public interface UserProvinceRepository extends JpaRepository<UserProvince, Long> {
    Optional<UserProvince> findByUserId(Long userId);
}
