package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.AdminRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("zooAdminRoleRepository")
public interface AdminRoleRepository extends JpaRepository<AdminRole, Integer> {
    @Query(value = "SELECT count(ar.id) FROM admin_roles ar " +
            "INNER JOIN account_to_admin_roles atar " +
            "ON ar.id = atar.admin_role_id " +
            "WHERE atar.account_id = :accountId " +
            "AND ar.code = ANY(:roleCodes)", nativeQuery = true)
    public Integer countByAccountIdAndRoleCodeIn(@Param("accountId") Integer accountId, @Param("roleCodes") String[] roleCodes);
}
