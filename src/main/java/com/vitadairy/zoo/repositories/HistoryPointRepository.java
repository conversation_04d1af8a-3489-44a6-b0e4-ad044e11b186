package com.vitadairy.zoo.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.vitadairy.zoo.dto.ReadOnlyHistoryPointDto;
import com.vitadairy.zoo.entities.HistoryPoint;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository("zooHistoryPointRepository")
public interface HistoryPointRepository extends JpaRepository<HistoryPoint, Long> {

    List<HistoryPoint> findByGiftId(Long giftId);

    Optional<HistoryPoint> findFirstByGiftIdOrderByTransactionDateDesc(Long giftId);

    List<HistoryPoint> findByTransactionExternalIdIn(List<String> transactionExternalIds);

    @Query(value = "SELECT hp.* FROM history_point hp "
            + "WHERE hp.transaction_external_id IN (:transactionExternalIds) ", nativeQuery = true)
    List<ReadOnlyHistoryPointDto> getReadOnlyByTransactionExternalIdIn(Collection<String> transactionExternalIds);
}
