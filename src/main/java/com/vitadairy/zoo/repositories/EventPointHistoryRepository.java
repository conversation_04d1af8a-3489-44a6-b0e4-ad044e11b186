package com.vitadairy.zoo.repositories;

import com.vitadairy.zoo.entities.EventPointHistory;
import com.vitadairy.zoo.entities.SyncEventPointHistoryToWh;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository("zooEventPointHistoryRepository")
public interface EventPointHistoryRepository extends JpaRepository<EventPointHistory, Long> {
    Optional<EventPointHistory> findFirstByHistoryPointId(Long historyPointId);
}
