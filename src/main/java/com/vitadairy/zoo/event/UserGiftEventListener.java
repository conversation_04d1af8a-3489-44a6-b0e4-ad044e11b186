package com.vitadairy.zoo.event;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.vitadairy.zoo.dto.UserGiftNotificationRequestDto;
import com.vitadairy.zoo.services.UserGiftNotificationService;

@Component
public class UserGiftEventListener {
    private UserGiftNotificationService _userGiftNotificationService;

    public UserGiftEventListener(
            @Qualifier("zooNotificationService") UserGiftNotificationService userGiftNotificationService) {
        this._userGiftNotificationService = userGiftNotificationService;
    }

    @EventListener
    @Async
    public void handleUserGiftNotifyEvent(UserGiftNotificationRequestDto dto) {
        this._userGiftNotificationService.push(dto);
    }
}
