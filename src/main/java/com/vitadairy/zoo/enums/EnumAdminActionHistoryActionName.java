package com.vitadairy.zoo.enums;

/**
 * Enum representing the possible administrative actions.
 */
public enum EnumAdminActionHistoryActionName {

    /** Create. */
    CREATE("Tạo"),

    /** Update. */
    UPDATE("Cập nhật"),

    /** Delete. */
    DELETE("Xóa");

    private final String label;

    EnumAdminActionHistoryActionName(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
}
