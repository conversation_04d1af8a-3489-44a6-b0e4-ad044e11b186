package com.vitadairy.zoo.enums;

/**
 * Enum representing the different levels of action on admin menu module for admin setup.
 */
public enum EnumCodeActionOnAdminMenuModule {

    /** Full access */
    FULL_ACCESS("full-access"),

    /** Read */
    READ("read"),

    /** Create */
    CREATE("create"),

    /** Update */
    UPDATE("update"),

    /** Delete */
    DELETE("delete");

    private final String value;

    EnumCodeActionOnAdminMenuModule(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
