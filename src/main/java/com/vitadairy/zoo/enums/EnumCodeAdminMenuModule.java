package com.vitadairy.zoo.enums;

/**
 * Enum representing the code that is used to assign an admin access to all menus.
 */
public enum EnumCodeAdminMenuModule {

    /** Provides access to all available menus. */
    FULL_MENU("full-menu"),

    /** Manages gifts, including creation, editing, and deletion. */
    QUAN_LY_QUA("quan-ly-qua"),

    /** Oversees user gifts, tracking assignments and statuses. */
    QUAN_LY_USER_GIFT("quan-ly-user-gift"),

    /** Handles order management, including processing and tracking. */
    QUAN_LY_DON_HANG("quan-ly-don-hang"),

    /** Manages coin refunds and related transactions. */
    QUAN_LY_HOAN_XU("quan-ly-hoan-xu"),

    /** Tracks balance changes and transaction history. */
    LICH_SU_BIEN_DONG_SO_DU("lich-su-bien-dong-so-du"),

    /** Manages identification of stores and related verification processes. */
    CUA_HANG_DINH_DANH("cua-hang-dinh-danh"),

    /** Administers administrators, including roles and permissions. */
    QUAN_LY_QUAN_TRI_VIEN("quan-ly-quan-tri-vien"),

    /** Oversees event management, including scheduling and coordination. */
    QUAN_LY_SU_KIEN("quan-ly-su-kien"),

    /** Manages event groups, organizing related events together. */
    QUAN_LY_GROUP_EVENT("quan-ly-group-event"),

    /** Records history of prize winnings and distributions. */
    LICH_SU_TRUNG_GIAI("lich-su-trung-giai"),

    /** Records history of prize winnings with new tracking features. */
    LICH_SU_TRUNG_GIAI_NEW("lich-su-trung-giai-new"),

    /** Manages campaigns, including planning and execution. */
    QUAN_LY_CHIEN_DICH("quan-ly-chien-dich"),

    /** Oversees floating elements, such as pop-ups or notifications. */
    QUAN_LY_FLOATING("quan-ly-floating"),

    /** Manages banners displayed within the application. */
    QUAN_LY_BANNER("quan-ly-banner"),

    /** Handles user account management and settings. */
    QUAN_LY_TAI_KHOAN("quan-ly-tai-khoan"),

    /** Manages filter settings and configurations. */
    QUAN_LY_BO_LOC("quan-ly-bo-loc"),

    /** Oversees hidden user tags for internal tracking. */
    QUAN_LY_HIDDEN_USER_TAG("quan-ly-hidden-user-tag"),

    /** Manages web applications integrated into the system. */
    QUAN_LY_WEBAPP("quan-ly-webapp"),

    /** Oversees web views and related configurations. */
    QUAN_LY_WEBVIEW("quan-ly-webview"),

    /** Manages CRM transaction types and their definitions. */
    QUAN_LY_CRM_TRANSACTION_TYPE("quan-ly-crm-transaction-type"),

    /** Manages admin action histories. */
    QUAN_LY_LICH_SU_CHINH_SUA("quan-ly-lich-su-chinh-sua");

    private final String value;

    EnumCodeAdminMenuModule(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
