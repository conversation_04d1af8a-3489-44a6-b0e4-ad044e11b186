package com.vitadairy.zoo.enums;

/**
 * Enum representing types of point history actions
 * such as adding or spending loyalty points.
 */
public enum EnumHistoryPointType {

    /** Represents an action where points are added to the user's account */
    ADD_POINT("add_point"),

    /** Represents an action where points are deducted from the user's account */
    SPEND_POINT("spend_point");

    /** A string label associated with the enum constant, typically used for serialization or display */
    private final String label;

    /**
     * Constructor to initialize the enum constant with a custom label.
     *
     * @param label the string label representing the point action type
     */
    EnumHistoryPointType(String label) {
        this.label = label;
    }

    /**
     * Gets the label associated with the point history type.
     *
     * @return the string label of the enum constant
     */
    public String getLabel() {
        return label;
    }
}
