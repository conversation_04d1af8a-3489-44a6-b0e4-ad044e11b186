package com.vitadairy.zoo.enums;

/**
 * Enum representing the different levels of admin role options for admin setup.
 */
public enum EnumCodeAdminRole {

    /** Highest role, can access all features */
    SUPER_ADMIN("super-admin"),

    /** Admin role, setup ACL to access features */
    ADMIN("admin");

    private final String value;

    EnumCodeAdminRole(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}

