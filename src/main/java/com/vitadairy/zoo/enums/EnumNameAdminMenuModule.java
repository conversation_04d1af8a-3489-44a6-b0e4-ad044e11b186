package com.vitadairy.zoo.enums;

/**
 * Enum representing the administrative menu modules.
 */
public enum EnumNameAdminMenuModule {

    /** Gift management */
    QUAN_LY_QUA("Quản lý quà"),

    /** User gift management */
    QUAN_LY_USER_GIFT("Quản lý user gift"),

    /** Order management */
    QUAN_LY_DON_HANG("Quản lý đơn hàng"),

    /** Coin refund management */
    QUAN_LY_HOAN_XU("Quản lý hoàn xu"),

    /** Balance fluctuation history */
    LICH_SU_BIEN_DONG_SO_DU("Lịch sử biến động số dư"),

    /** Identified stores */
    CUA_HANG_DINH_DANH("Cửa hàng định danh"),

    /** Administrator management */
    QUAN_LY_QUAN_TRI_VIEN("Quản lý quản trị viên"),

    /** Event management */
    QUAN_LY_SU_KIEN("Quản lý sự kiện"),

    /** Group event management */
    QUAN_LY_GROUP_EVENT("Quản lý group event"),

    /** Winning history */
    LICH_SU_TRUNG_GIAI("Lịch sử trúng giải"),

    /** New winning history */
    LICH_SU_TRUNG_GIAI_NEW("Lịch sử trúng giải NEW"),

    /** Campaign management */
    QUAN_LY_CHIEN_DICH("Quản lý chiến dịch"),

    /** Floating content management */
    QUAN_LY_FLOATING("Quản lý floating"),

    /** Banner management */
    QUAN_LY_BANNER("Quản lý banner"),

    /** Account management */
    QUAN_LY_TAI_KHOAN("Quản lý tài khoản"),

    /** Filter management */
    QUAN_LY_BO_LOC("Quản lý bộ lọc"),

    /** Hidden user tag management */
    QUAN_LY_HIDDEN_USER_TAG("Quản lý hidden user tag"),

    /** Web application management */
    QUAN_LY_WEBAPP("Quản lý webapp"),

    /** Webview management */
    QUAN_LY_WEBVIEW("Quản lý webview"),

    /** CRM transaction type management */
    QUAN_LY_CRM_TRANSACTION_TYPE("Quản lý crm transaction type");

    private final String label;

    EnumNameAdminMenuModule(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
}

