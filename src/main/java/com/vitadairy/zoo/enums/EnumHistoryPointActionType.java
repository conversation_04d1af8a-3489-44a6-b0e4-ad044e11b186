package com.vitadairy.zoo.enums;

/**
 * Enum representing different types of point-related actions
 * such as rewarding gifts, pre-ordering, or returning points.
 */
public enum EnumHistoryPointActionType {

    /** Action where the user is rewarded with a gift */
    REWARD_GIFT("reward_gift"),

    /** Action for receiving a gift via pre-order */
    PRE_ORDER_GIFT("pre_order_gift"),

    /** Action for returning points from a pre-order */
    RETURN_POINT_PRE_ORD("return_point_pre_ord"),

    /** Action for returning points in general */
    RETURN_POINT("return_point");

    /** A string label associated with the action, typically used for storage or serialization */
    private final String label;

    /**
     * Constructor to assign a string label to the enum constant.
     *
     * @param label the lowercase string representation of the action
     */
    EnumHistoryPointActionType(String label) {
        this.label = label;
    }

    /**
     * Retrieves the label associated with the action type.
     *
     * @return the lowercase string label
     */
    public String getLabel() {
        return label;
    }
}
