package com.vitadairy.zoo.configs;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration("zooDatasourceConfiguration")
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.vitadairy.zoo.repositories",
        entityManagerFactoryRef = "zooEntityManagerFactory",
        transactionManagerRef = "zooTransactionManager"
)
public class ZooDatasourceConfiguration {
    @Bean(name = "zooDataSource")
    @ConfigurationProperties("spring.datasource.zoo")
    public DataSource dataSource(
            @Value("${spring.datasource.zoo.hikari.pool-name:HikariZooCP}") String poolName,
            @Value("${spring.datasource.zoo.hikari.maximum-pool-size:10}") Integer maximumPoolSize,
            @Value("${spring.datasource.zoo.hikari.minimum-idle:2}") Integer minimumIdle,
            @Value("${spring.datasource.zoo.hikari.idle-timeout:120000}") Integer idleTimeout,
            @Value("${spring.datasource.zoo.hikari.connection-timeout:30000}") Integer connectionTimeout,
            @Value("${spring.datasource.zoo.hikari.max-lifetime:1800000}") Integer maxLifeTime
    ) {
        /*return DataSourceBuilder.create()
                .build();*/
        HikariDataSource dataSource = DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
        dataSource.setConnectionTimeout(connectionTimeout);
        dataSource.setMaximumPoolSize(maximumPoolSize);
        dataSource.setMinimumIdle(minimumIdle);
        dataSource.setIdleTimeout(idleTimeout);
        dataSource.setMaxLifetime(maxLifeTime);
        dataSource.setPoolName(poolName);

        return dataSource;
    }

    @Bean(name = "zooTransactionManager")
    @Autowired
    JpaTransactionManager dataSourceTransactionManager(
            @Qualifier("zooEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(
                entityManagerFactory.getObject());
        return transactionManager;
    }

    @Bean(name = "zooEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("zooDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.vitadairy.zoo.entities")
                .persistenceUnit("zoo")
                .build();
    }

    @Bean(name = "zooJdbcTemplate")
    public JdbcTemplate jdbcTemplate(@Qualifier("zooDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
