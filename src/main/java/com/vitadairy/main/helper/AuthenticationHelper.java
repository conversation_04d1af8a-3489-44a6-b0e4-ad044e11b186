package com.vitadairy.main.helper;

import com.vitadairy.auth.dto.AuthorizationResponse;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Objects;

public class AuthenticationHelper {
    public static AuthorizationResponse getCurrentUser() {
        var authentication = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.nonNull(authentication) && authentication.getPrincipal() instanceof AuthorizationResponse resp) {
            return resp;
        }
        return null;
    }

    public static Long getCurrentUserId() {
        var authentication = getCurrentUser();
        return authentication == null ? null : authentication.getUserId();
    }
}
