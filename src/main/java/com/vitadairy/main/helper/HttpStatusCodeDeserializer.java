package com.vitadairy.main.helper;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpStatusCodeDeserializer extends StdDeserializer<HttpStatusCode> {
    protected HttpStatusCodeDeserializer() {
        super((Class<?>) null);
    }

    @Override
    public HttpStatusCode deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        JsonNode jsonNode = jsonParser.getCodec().readTree(jsonParser);
        String status = jsonNode.asText();
        for (HttpStatus httpStatus : HttpStatus.values()) {
            log.info(httpStatus.name());
            if (httpStatus.name().equals(status)) {
                return httpStatus;
            }
        }
        return null;
    }
}
