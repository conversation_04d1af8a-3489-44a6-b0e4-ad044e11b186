package com.vitadairy.main.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
public class RestTemplateClient {
    private final String url;
    private final RestTemplate restTemplate;
    private String authorization;


    public RestTemplateClient(String url, RestTemplate restTemplate) {
        this.url = url;
        this.restTemplate = restTemplate;
    }

    protected String getUrl() {
        return url;
    }

    public void buildAuth(String authorization) {
        this.authorization = authorization;
    }

    public HttpHeaders defaultHeader() {
        var headers = new HttpHeaders();
        if (this.authorization != null) {
            headers.set("Authorization", authorization);
        }
        return headers;
    }

    public <T> T data(HttpMethod method, HttpHeaders headers, Object request, String path, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> responseType) {
        String apiUrl = url + path;
        log.debug("[Internal API] [{}] {} | requestBody={}, pathQueries={}", method, apiUrl, request, queries);
        UriComponents uriComponents = buildUrl(queries, path);
        try {
            var respEntity = restTemplate
                .exchange(uriComponents.toUri(), method, new HttpEntity<>(request, headers), responseType);

            var statusCode = respEntity.getStatusCode();
            var response = respEntity.getBody();
            log.debug("[Internal API] [{}] {} | StatusCode={} | Response={}", method, apiUrl, statusCode, response);
            return response;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public <T> T data(HttpMethod method, Object request, String path, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> typeRef) {
        return this.data(method, defaultHeader(), request, path, queries, typeRef);
    }

    public <T> ResponseEntity<T> dataEntity(HttpMethod method, Object request, String path, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> typeRef) {
        return this.dataEntity(method, defaultHeader(), request, path, queries, typeRef);
    }

    public <T> ResponseEntity<T> dataEntity(HttpMethod method, HttpHeaders headers, Object request, String path, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> typeRef) {
        String apiUrl = url + path;
        log.info("[Internal API] [{}] {} | requestBody={}, pathQueries={}", method, apiUrl, request, queries);
        UriComponents uriComponents = buildUrl(queries, path);
        ResponseEntity<T> response = restTemplate
                .exchange(uriComponents.toUri(), method, new HttpEntity<>(request, headers), typeRef);
        log.info("[Internal API] [{}] {} | StatusCode={} | Response={}", method, apiUrl, response.getStatusCode(), response.getBody());
        return response;
    }

    public <T> T get(String path, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.GET, null, path, null, responseType);
    }

    public <T> T get(String path, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.GET, null, path, queries, responseType);
    }

    public <T> T post(String path, Object request, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.POST, request, path, queries, responseType);
    }

    public <T> ResponseEntity<T> postEntity(String path, HttpHeaders headers, Object request, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> responseType) {
        return dataEntity(HttpMethod.POST, headers, request, path, queries, responseType);
    }

    public <T> ResponseEntity<T> postEntity(String path, Object request, LinkedMultiValueMap<String, String> queries, ParameterizedTypeReference<T> responseType) {
        return dataEntity(HttpMethod.POST, request, path, queries, responseType);
    }

    public <T> T post(String path, HttpHeaders headers, Object request, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.POST, headers, request, path, null, responseType);
    }

    public <T> T post(String path, Object request, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.POST, request, path, null, responseType);
    }

    public <T> T put(String path, Object request, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.PUT, request, path, null, responseType);
    }

    public <T> T patch(String path, Object request, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.PATCH, request, path, null, responseType);
    }

    public <T> T delete(String path, Object request, ParameterizedTypeReference<T> responseType) {
        return data(HttpMethod.DELETE, request, path, null, responseType);
    }

    private UriComponents buildUrl(LinkedMultiValueMap<String, String> queries, String path) {
        UriComponentsBuilder builder;
        if (path != null)
            builder = UriComponentsBuilder.fromUriString(getUrl() + path);
        else
            builder = UriComponentsBuilder.fromUriString(getUrl());
        if (queries != null && !queries.isEmpty()) {
            queries.forEach((key, value) -> builder.queryParam(key, String.join(",", value)));
        }
        return builder.build().encode();
    }
}
