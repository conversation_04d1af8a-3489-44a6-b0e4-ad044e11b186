package com.vitadairy.main.request;

import com.vitadairy.libraries.importexport.dto.FetchRequest;
import com.vitadairy.libraries.importexport.dto.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class ExportRequest<R, ID> extends BaseRequest {
    protected R fetchRequest;
    protected List<ID> ids;
    protected Boolean isExportAll;
    protected Page page;

    protected FetchRequest<R> toFetchRequest() {
        return FetchRequest.<R>builder()
                .request(fetchRequest)
                .pageable(page)
                .fetchAll(isExportAll)
                .build();
    }

    public void doValidate() throws Exception {
        if (Objects.nonNull(isExportAll) && Boolean.TRUE.equals(isExportAll)) {
            // explicit set page in export service
            page = null;
        }
    }
}
