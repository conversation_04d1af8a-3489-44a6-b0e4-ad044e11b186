package com.vitadairy.main.request;

import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * Usage: template request for pageable request like search,
 * filter
 *
 */

@Data
public class PageableRequest {
    Integer page = 0;
    Integer size;

    /**
     * get pageable object with sort option
     * @param sort
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public Pageable getPageable(Sort sort) {
        if (this.getSize() == null) {
            return Pageable.unpaged(sort);
        } else {
            return PageRequest.of(this.getPage(), this.getSize(), sort);
        }
    }

    /**
     * get pageable object without sort option
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public Pageable getPageable() {
        if (this.getSize() == null) {
            return Pageable.unpaged();
        } else {
            return PageRequest.of(this.getPage(), this.getSize());
        }

    }

    public void doValidate() throws Exception {
        if (this.getPage() == null || this.getPage() < 1) {
            this.setPage(0);
        }
        if (this.getSize() == null || this.getSize() < 1) {
            this.setSize(10);
        }
    }
}
