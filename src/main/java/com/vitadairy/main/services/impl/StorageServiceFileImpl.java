package com.vitadairy.main.services.impl;

import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.services.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("storageFileService")
public class StorageServiceFileImpl implements StorageService {

    private final Path rootUploadFolder;

    public StorageServiceFileImpl(@Qualifier("rootUploadFolder") Path rootUploadFolder) {
        this.rootUploadFolder = rootUploadFolder;
    }

    @Override
    public String saveMultipartFile(MultipartFile file) {
        try {
            if (file.isEmpty()) {
                throw new ApplicationException("Failed to store empty file.");
            }
            Path destinationFile = rootUploadFolder.resolve(
                            Paths.get(Objects.requireNonNull(file.getOriginalFilename())))
                    .normalize().toAbsolutePath();
            if (!destinationFile.getParent().toString()
                    .equals(rootUploadFolder.toAbsolutePath().toString().replace("./", ""))) {
                // This is a security check
                throw new ApplicationException(
                        "Cannot store file outside current directory.");
            }
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, destinationFile,
                        StandardCopyOption.REPLACE_EXISTING);
            }
            return destinationFile.toAbsolutePath().toString();
        } catch (Exception ex) {
            throw new ApplicationException("Failed to store file." + ex.getMessage());
        }
    }
}
