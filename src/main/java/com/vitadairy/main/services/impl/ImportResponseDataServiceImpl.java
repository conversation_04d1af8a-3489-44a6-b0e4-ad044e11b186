package com.vitadairy.main.services.impl;

import com.vitadairy.libraries.importexport.dto.CellError;
import com.vitadairy.libraries.importexport.dto.RowError;
import com.vitadairy.main.dto.ImportResponseDto;
import com.vitadairy.main.response.ImportResponse;
import com.vitadairy.main.response.ResponseMetaData;
import com.vitadairy.main.services.ImportResponseDataService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ImportResponseDataServiceImpl implements ImportResponseDataService {
    @Override
    public ImportResponse dataSuccess() {
        ResponseMetaData metaData = ResponseMetaData.builder()
                .status(0)
                .msg("OK")
                .build();
        ImportResponseDto importResponseDto = ImportResponseDto.builder()
                .insertedCount(1)
                .updatedCount(0)
                .failedCount(0)
                .build();
        return ImportResponse.importResponseBuilder()
                .meta(metaData)
                .data(importResponseDto)
                .build();
    }

    @Override
    public ImportResponse dataFail() {
        ResponseMetaData metaData = ResponseMetaData.builder()
                .status(1000)
                .msg("OK")
                .build();
        ImportResponseDto importResponseDto = ImportResponseDto.builder()
                .insertedCount(1)
                .updatedCount(0)
                .failedCount(1)
                .build();
        return ImportResponse.importResponseBuilder()
                .meta(metaData)
                .data(importResponseDto)
                .build();
    }

    @Override
    public ImportResponse fromRawImportResp(com.vitadairy.libraries.importexport.common.ImportResponse importResponse) {
        ResponseMetaData metaData;
        if (importResponse.getRc() != 0) {
            metaData = ResponseMetaData.builder()
                    .status(1001)
                    .msg("Failed")
                    .build();
        } else if (importResponse.getReadFailed() != 0 || importResponse.getFailedRecord() != 0) {
            metaData = ResponseMetaData.builder()
                    .status(1000)
                    .msg("Some row failed")
                    .build();
            List<String> msgs = importResponse.getDbResponses();
            if (Objects.isNull(msgs) || msgs.isEmpty()) {
                msgs = new ArrayList<>();
                importResponse.setDbResponses(msgs);

                for (RowError r : importResponse.getRowErrors()) {
                    msgs.add("Line " + r.getRow() + ": " + Optional.ofNullable(r.getFields())
                            .orElse(new ArrayList<>())
                            .stream()
                            .map(CellError::getName)
                            .collect(Collectors.joining(", ")) + " data error");
                }
            }
        } else {
            metaData = ResponseMetaData.builder()
                    .status(0)
                    .msg("OK")
                    .build();
        }
        ImportResponseDto importResponseDto = ImportResponseDto.builder()
                .insertedCount(importResponse.getSuccessRecord())
                .updatedCount(0)
                .failedCount(importResponse.getFailedRecord() + importResponse.getReadFailed())
                .messages(importResponse.getDbResponses())
                .build();
        return ImportResponse.importResponseBuilder()
                .meta(metaData)
                .data(importResponseDto)
                .build();
    }
}
