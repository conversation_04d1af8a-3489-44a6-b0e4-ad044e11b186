package com.vitadairy.main.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.dto.ThirdPartyTransactionDto;
import com.vitadairy.main.dto.TransactionBatchDto;
import com.vitadairy.main.enums.DestinationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;

@Service("warehouseTransactionService")
@Slf4j
public class WarehouseTransactionService {
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    private final String whTransactionTopic;
    private final String wh3rdPartyTransactionTopic;
    private final List<String> allowClientCodes;


    public WarehouseTransactionService(KafkaTemplate<String, String> kafkaTemplate,
                                       ObjectMapper objectMapper,
                                       @Value("${spring.kafka.wh.transaction.topic}") String whTransactionTopic,
                                       @Value("${spring.kafka.wh.3rd.transaction.topic}") String wh3rdPartyTransactionTopic,
                                       @Value("${ws.allow.client.code}") List<String> allowClientCodes) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
        this.whTransactionTopic = whTransactionTopic;
        this.wh3rdPartyTransactionTopic = wh3rdPartyTransactionTopic;

        log.info("Warehouse allowed integration codes: {}", allowClientCodes);
        this.allowClientCodes = allowClientCodes;
    }

    public boolean send3rdPartyTransaction(ThirdPartyTransactionDto dto) {
        try {
            dto.setCreatedAt(Instant.now());
            var raw = this.objectMapper.writeValueAsString(dto);
            log.info("Produce message async to warehouse topic: {}", raw);
            this.kafkaTemplate.send(wh3rdPartyTransactionTopic, raw).handleAsync((result, e) -> {
                if (e != null) {
                    log.error("Error send transaction dto {}, got exception: {}", raw, ExceptionUtils.getStackTrace(e));
                }

                if (result != null) {
                    var metadata = result.getRecordMetadata();
                    log.info("Produce success to kafka transaction dto {} at timestamp {}, with metadata {}",
                            raw, metadata.timestamp(), metadata);
                }
                return true;
            });

            return true;
        } catch (JsonProcessingException e) {
            log.error("Error parsing batch: {}, detail: {}", dto, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    public boolean send(TransactionBatchDto batch) {
        try {
            for (var dto : batch.getTransactions()) {
                dto.setCreatedAt(Instant.now());
                var raw = this.objectMapper.writeValueAsString(dto);

                if (!this.allowClientCodes.contains(dto.getDestination().name()))  {
                    log.error("Not allow destination: {}", raw);
                    continue;
                }

                if (CollectionUtils.isEmpty(dto.getPayload()) && CollectionUtils.isEmpty(dto.getTListPayload())) {
                    log.error("Transaction has empty both payload and tlPayload: {}", raw);
                    continue;
                }

                if (DestinationEnum.SAP == dto.getDestination() && dto.getPayload() != null) {
                    log.warn("Destination SAP accepts only tListPayload");
                    dto.setTListPayload(List.of(dto.getPayload()));
                    dto.setPayload(null);
                }

                this.kafkaTemplate.send(whTransactionTopic, raw).handle((result, e) -> {
                    if (e != null) {
                        log.error("Error send transaction dto {}, got exception: {}", raw, ExceptionUtils.getStackTrace(e));
                    }

                    if (result != null) {
                        var metadata = result.getRecordMetadata();
                        log.info("Produce success to kafka transaction dto {} at timestamp {}, with metadata {}",
                                raw, metadata.timestamp(), metadata);
                    }
                    return true;
                });
            }

            return true;
        } catch (JsonProcessingException e) {
            log.error("Error parsing batch: {}, detail: {}", batch, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }
}
