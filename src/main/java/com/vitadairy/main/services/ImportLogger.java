package com.vitadairy.main.services;

import com.vitadairy.libraries.importexport.helper.ILogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class ImportLogger implements ILogger {

    private final Logger logger;

    public ImportLogger(String logName) {
        logger = LoggerFactory.getLogger(logName);
    }

    @Override
    public void info(String s) {
        logger.info(s);
    }

    @Override
    public void error(String s) {
        logger.error(s);
    }

    @Override
    public void error(String s, Throwable throwable) {
        logger.error(s, throwable);
    }

    @Override
    public void debug(String s) {
        logger.debug(s);
    }
}
