package com.vitadairy.main.services;

import com.vitadairy.libraries.importexport.common.BaseResponse;
import com.vitadairy.libraries.importexport.common.BatchResponse;
import com.vitadairy.libraries.importexport.service.ProcessDataService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public abstract class ProcessDataAbstractService<T> implements ProcessDataService<T> {

    @Override
    public BatchResponse processBatch(List<T> list) {
        BatchResponse response = new BatchResponse();
        response.setFailed();
        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        Iterator<T> iterator = list.iterator();
        List<T> batch = new ArrayList<>();
        int failedRecord = 0;
        int counter = 0;
        while (iterator.hasNext()) {
            T entity = iterator.next();
            batch.add(entity);
            if (counter != 0 && counter % 100 == 0) {
                try {
                    List<T> saveBatchRes = saveAll(batch);
                    if (saveBatchRes.size() != batch.size()) {
                        failedRecord += batch.size();
                    }
                } catch (Exception e) {
                    failedRecord += batch.size();
                }
                batch.clear();
            }
            ++counter;
        }
        if (!CollectionUtils.isEmpty(batch)) {
            try {
                List<T> saveBatchRes = saveAll(batch);
                if (saveBatchRes.size() != batch.size()) {
                    failedRecord += batch.size();
                }
            } catch (Exception e) {
                failedRecord += batch.size();
            }
        }
        response.setSuccess();
        response.setTotalRecord(list.size());
        response.setSuccessRecord(list.size() - failedRecord);
        response.setFailedRecord(failedRecord);
        List<Object[]> dbResponses = runFunction(list.get(0));
        List<String> lstString = new ArrayList<>();
        for (Object obj : dbResponses) {
            if (Objects.nonNull(obj)) {
                if (obj instanceof Object[]) {
                    Object[] objArr = (Object[]) obj;
                    Stream.of(objArr).forEach(o -> lstString.add((String) o));
                } else {
                    lstString.add((String) obj);
                }
            }
        }
        response.setDbResponses(lstString);
        return response;
    }

    @Override
    public BaseResponse process(T entity) {
        BaseResponse response = new BaseResponse();
        response.setFailed();
        if (entity == null) {
            return response;
        }
        save(entity);
        response.setSuccess();
        return response;
    }

    protected abstract T save(T entity);

    protected abstract List<T> saveAll(List<T> list) throws Exception;

    protected abstract List<Object[]> runFunction(T entity);
}
    
    
