package com.vitadairy.main.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitadairy.main.dto.CustomNotificationRequestDto;
import com.vitadairy.main.dto.SmsPayload;
import com.vitadairy.main.dto.SmsRequestDto;
import com.vitadairy.main.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class NotificationService {
    public static final String USER_GIFT_REMINDER_5_DAYS = "USER_GIFT_REMINDER_5_DAYS";
    
    @Value("${rest.url.vita-noti-v3}")
    private String notiApiUrl;

    @Value("${rest.url.vita-loyalty-v3}")
    private String loyaltyApiUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public NotificationService(ObjectMapper objectMapper, RestTemplate restTemplate) {
        this.objectMapper = objectMapper;
        this.restTemplate = restTemplate;
    }

    public void push(CustomNotificationRequestDto body) {
        try {
            String url = notiApiUrl + "/user/custom-notification";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> request = new HttpEntity<>(body, headers);
            var response = restTemplate.postForEntity(url, request, Object.class);
            log.warn("Push notification [Response] {} ", response.getBody());
        } catch (Exception e) {
            log.error("Error on push notification: {}", e.getMessage(), e);
        }
    }

    public void sendSms(String code, SmsPayload payload){
        if(payload.validate()){
            try{
                var request = new SmsRequestDto(code, payload);
                log.warn("Send sms [Request] {} ", objectMapper.writeValueAsString(request));
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                CloseableHttpResponse response = HttpUtils.sendPost(loyaltyApiUrl + "/send-custom-sms", objectMapper.writeValueAsString(request), headers);
                log.warn("Send sms [Response] {} ", HttpUtils.getDataFromResponse(response));
            }
            catch (Exception ex){
                log.error("Error on send sms: {}", ex.getMessage(), ex);
            }
        }
    }
}