package com.vitadairy.main.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

@ConditionalOnProperty(prefix = "spring", name = {"cache.type"}, havingValue = "redis")
@ConfigurationProperties(prefix = "spring.cache.redis")
@Getter
@Setter
public class RedisCacheConfigurationProperties {
    private Duration timeToLive;
    private Duration topExchangeTtl;
}
