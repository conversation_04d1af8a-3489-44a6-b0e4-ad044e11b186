package com.vitadairy.main.configs;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SwaggerConfig {
    @Value("${base.url:http://localhost:8090}")
    private String baseUrl;

    @Bean
    public OpenAPI springShopOpenAPI() {
        Server devServer = generateServer(baseUrl);
        return new OpenAPI()
                .info(new Info().title("Java main API")
                        .description("Java main sample application")
                        .version("v0.0.1")
                        .license(new License().name("Apache 2.0")))
                .servers(List.of(devServer));
    }

    private Server generateServer(String url) {
        Server server = new Server();
        server.setDescription("Server URL in Development environment");
        server.setUrl(url);
        return server;
    }
}