package com.vitadairy.main.configs;

import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.exception.ApplicationException;
import com.vitadairy.main.iresponse.BaseResponse;
import com.vitadairy.main.response.EntityResponse;
import com.vitadairy.main.response.PageableResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Usage: repsonse factory for all response
 */
@Component
public class ResponseFactory {

    /**
     * success with entity response, blind structure
     *
     * @param data
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 25/7/24
     */
    public <T> ResponseEntity<EntityResponse<T>> successEntity(T data) {
        return ResponseEntity.ok(new EntityResponse<T>(data));
    }

    public <T> ResponseEntity<EntityResponse<T>> successEntity(T data, VtdStatusEnum vtdStatusEnum) {
        var dto = new EntityResponse<>(data);
        dto.setVtdStatus(vtdStatusEnum);
        return ResponseEntity.ok(dto);
    }

    /**
     * success with pageable response, blind structure
     *
     * @param data
     * @param total
     * @param page
     * @param pageSize
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 25/7/24
     */
    public <T> ResponseEntity<PageableResponse<T>> pageableSuccess(List<T> data, Long total, Integer page,
                                                                   Integer pageSize) {
        PageableResponse<T> dto = PageableResponse.<T>pageableBuilder()
                .data(data)
                .total(total)
                .page(page)
                .pageSize(pageSize)
                .build();
        return ResponseEntity.ok(dto);
    }


    /**
     * success with pageable response, blind structure
     *
     * @param data
     * @param total
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 25/7/24
     */
    public <T> ResponseEntity<PageableResponse<T>> dataNoPageableSuccess(List<T> data, Long total) {
        PageableResponse<T> dto = new PageableResponse<>(data, total);
        return ResponseEntity.ok(dto);
    }

    /**
     * success with body of an object, blind structure
     *
     * @param data
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T extends BaseResponse> ResponseEntity<T> success(T data) {
        return this.success(data, null);
    }

    public <T extends BaseResponse> ResponseEntity<T> success(T data, VtdStatusEnum vtdStatusEnum) {
        data.setVtdStatus(vtdStatusEnum);
        return ResponseEntity.ok(data);
    }

    /**
     * success with body of an pojo
     *
     * @param data
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T extends BaseResponse> ResponseEntity<T> successDto(T data) {
        return this.successDto(data, null);
    }

    public <T extends BaseResponse> ResponseEntity<T> successDto(T data, VtdStatusEnum vtdStatusEnum) {
        data.setCode(HttpStatus.OK.value());
        data.setMsg("ok");
        data.setStatus(HttpStatus.OK);
        data.setVtdStatus(vtdStatusEnum);
        return ResponseEntity.ok(data);
    }

    /**
     * empty response body
     *
     * @param page
     * @param size
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public ResponseEntity<Object> pageableEmpty(int page, int size) {
        Map<String, Object> map = Map.of(
                "data", List.of(),
                "total", 0,
                "page", page,
                "pageSize", size,
                "code", HttpStatus.OK.value(),
                "statusCode", HttpStatus.OK,
                "msg", "ok");
        return ResponseEntity.ok(map);
    }

    /**
     * return pageable with empty dto, has generic
     *
     * @param page
     * @param size
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T extends PageableResponse<?>> ResponseEntity<T> pageableDtoEmpty(int page, int size) {
        PageableResponse<?> dto = PageableResponse.pageableBuilder()
                .data(List.of())
                .total(0L)
                .page(page)
                .pageSize(size)
                .msg("ok")
                .statusCode(HttpStatus.OK)
                .build();
        return ResponseEntity.ok((T) dto);
    }

    /**
     * return error response with message
     *
     * @param message
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public ResponseEntity<Object> error(String message) {
        return ResponseEntity.badRequest().body(Map.of("message", message));
    }

    /**
     * return error response with no message
     *
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T extends BaseResponse> ResponseEntity<T> errorDto() {
        return errorDto("Bad request");
    }

    public <T extends BaseResponse> ResponseEntity<T> errorDto(ApplicationException aex) {
        BaseResponse response = new EntityResponse<T>(null, aex.getHttpStatus(), aex.getMessage());
        return build((T) response, aex.getHttpStatus());
    }

    /**
     * return error response with message and pojo
     *
     * @param msg
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T extends BaseResponse> ResponseEntity<T> errorDto(String msg) {
        BaseResponse response = new EntityResponse<T>(null, HttpStatus.BAD_REQUEST, msg);
        return ResponseEntity.badRequest().body((T) response);
    }

    /**
     * return response with body and http status
     *
     * @param body
     * @param httpStatus
     * @param <T>
     * @return
     * <AUTHOR>
     * @since 10/7/24
     */
    public <T> ResponseEntity<T> build(T body, HttpStatus httpStatus) {
        return ResponseEntity.status(httpStatus).body(body);
    }
}
