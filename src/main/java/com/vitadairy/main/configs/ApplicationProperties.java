package com.vitadairy.main.configs;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "application")
@Component
public class ApplicationProperties {
    private ImportConfig importConfig;
    private ExportConfig exportConfig;
}
