package com.vitadairy.main.configs;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class BeanConfiguration {

    @Bean(name = "dbDateFormat")
    public SimpleDateFormat dbDateFormat() {
        return new SimpleDateFormat("yyyy-MM-dd");
    }

    @Bean
    public RestTemplate restTemplate(@Qualifier("objectMapper") ObjectMapper objectMapper, HttpComponentsClientHttpRequestFactory clientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);

        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        MappingJackson2HttpMessageConverter jsonMessageConverter = new MappingJackson2HttpMessageConverter();
        jsonMessageConverter.setObjectMapper(objectMapper);
        messageConverters.add(jsonMessageConverter);
        messageConverters.add(new FormHttpMessageConverter());
        restTemplate.setMessageConverters(messageConverters);

        return restTemplate;
    }

    @Bean
    public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory(CloseableHttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClient);
        return clientHttpRequestFactory;
    }


    @Bean("rootUploadFolder")
    public Path rootUploadFolder(ApplicationProperties properties) {
        Path path = Path.of(properties.getImportConfig().getPath());
        if (!path.toFile().exists()) {
            log.info("Create upload folder: {}, res {}", path.toString(), path.toFile().mkdir());
        }
        return path;
    }

    @Bean("rootExportFolder")
    public Path rootExportFolder(ApplicationProperties properties) {
        Path path = Path.of(properties.getExportConfig().getPath());
        if (!path.toFile().exists()) {
            log.info("Create export folder: {}, res {}", path.toString(), path.toFile().mkdir());
        }
        return path;
    }
}
