package com.vitadairy.main.exception;

import com.vitadairy.main.enums.VtdStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

import java.util.Date;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
public class ErrorDetail {
    ErrorDetailDto response;

    public static ErrorDetail build(String message, HttpStatus statusCode) {
        ErrorDetailDto errorDetailDto = ErrorDetailDto.builder()
                .timestamp(new Date())
                .msg(message)
                .code(statusCode.value())
                .status(statusCode)
                .build();
        return ErrorDetail.builder().response(errorDetailDto).build();
    }

    public static ErrorDetail build(String message, HttpStatus statusCode, VtdStatusEnum vtdStatusEnum) {
        ErrorDetailDto errorDetailDto = ErrorDetailDto.builder()
                .timestamp(new Date())
                .msg(message)
                .code(statusCode.value())
                .status(statusCode)
                .vtdStatus(vtdStatusEnum)
                .build();
        return ErrorDetail.builder().response(errorDetailDto).build();
    }

    public static ErrorDetail badRequest(List<String> messages) {
        return build(String.join(", ", messages), HttpStatus.BAD_REQUEST);
    }

    public static ErrorDetail internalServerError(String message) {
        return build(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public static ErrorDetail forbidden(String message) {
        return build(message, HttpStatus.FORBIDDEN);
    }
}

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
class ErrorDetailDto {
    private Date timestamp;
    private String msg;
    private Integer code;
    private HttpStatus status;
    private VtdStatusEnum vtdStatus;
}