package com.vitadairy.main.exception;

import com.vitadairy.main.configs.ResponseFactory;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.ErrorResponse;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.ArrayList;
import java.util.List;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {
    private final ResponseFactory responseFactory;

    public GlobalExceptionHandler(ResponseFactory responseFactory) {
        this.responseFactory = responseFactory;
    }

    @ExceptionHandler(ApplicationException.class)
    public final ResponseEntity<Object> handleException(ApplicationException exception) {
        log.warn("Application Exception", exception);
        return this.responseFactory.build(
                ErrorDetail.build(exception.getMessage(), exception.getHttpStatus(), exception.getVtdStatusEnum()),
                exception.getHttpStatus());
    }

    @ExceptionHandler(org.springframework.security.access.AccessDeniedException.class)
    public ResponseEntity<ErrorDetail> handleAuthorizationDeniedException(AccessDeniedException exception) {
        log.warn("AccessDeniedException", exception);
        return this.responseFactory.build(
                ErrorDetail.forbidden(exception.getMessage()),
                HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorDetail> handleGlobalException(Exception exception) {
        log.warn("Exception", exception);
        return this.responseFactory.build(
                ErrorDetail.internalServerError(exception.getMessage()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(ResourceException.class)
    public ResponseEntity<ErrorDetail> handleResourceException(ResourceException exception) {
        log.warn("ResourceException", exception);
        var errorDetail = ErrorDetail.build(exception.getMessage(), exception.getStatus());
        return this.responseFactory.build(
                errorDetail,
                HttpStatus.NOT_FOUND);
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, @Nullable Object body, HttpHeaders headers,
            HttpStatusCode statusCode, WebRequest request) {
        if (ex instanceof MethodArgumentNotValidException subEx) {
            return this.handleValidationExceptions(subEx);
        }

        if (request instanceof ServletWebRequest servletWebRequest) {
            HttpServletResponse response = servletWebRequest.getResponse();
            if (response != null && response.isCommitted()) {
                if (this.logger.isWarnEnabled()) {
                    this.logger.warn("Response already committed. Ignoring: " + ex);
                }

                return null;
            }
        }
        if (body == null && ex instanceof ErrorResponse errorResponse) {
            body = errorResponse.updateAndGetBody(super.getMessageSource(), LocaleContextHolder.getLocale());
        }
        if (statusCode.equals(HttpStatus.INTERNAL_SERVER_ERROR) && body == null) {
            request.setAttribute("jakarta.servlet.error.exception", ex, 0);
        }
        return this.createResponseEntity(body, headers, statusCode, request);
    }

    public ResponseEntity<Object> handleValidationExceptions(MethodArgumentNotValidException ex) {
        log.warn("ex: {}", ExceptionUtils.getStackTrace(ex));
        List<String> errors = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.add(fieldName + ": " + errorMessage);
        });
        return this.responseFactory.build(
                ErrorDetail.badRequest(errors),
                HttpStatus.BAD_REQUEST);
    }
}
