package com.vitadairy.main.exception;

import com.vitadairy.main.enums.VtdStatusEnum;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@Getter
public class ApplicationException extends RuntimeException {
    private final HttpStatus httpStatus;
    private VtdStatusEnum vtdStatusEnum;

    public ApplicationException(String msg) {
        this(msg, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ApplicationException(String msg, HttpStatus httpStatus) {
        super(msg);
        this.httpStatus = httpStatus;
    }

    public ApplicationException(String msg, VtdStatusEnum vtdStatusEnum) {
        super(msg);
        this.httpStatus = vtdStatusEnum.getHttpStatus();
        this.vtdStatusEnum = vtdStatusEnum;
    }

    public String getMsg() {
        return this.getMessage();
    }
}
