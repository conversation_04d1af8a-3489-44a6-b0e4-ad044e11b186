package com.vitadairy.main.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
public class ResourceException extends RuntimeException {
    private String code;
    private HttpStatus status;
}
