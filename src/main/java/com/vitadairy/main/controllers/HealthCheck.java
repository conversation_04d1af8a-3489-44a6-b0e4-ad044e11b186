package com.vitadairy.main.controllers;

import com.vitadairy.main.configs.ResponseFactory;
import com.vitadairy.main.response.EntityResponse;
import lombok.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("${micro.service.context-path}/health")
@RequiredArgsConstructor
public class HealthCheck {
    private final ResponseFactory responseFactory;

    @GetMapping("check")
    public ResponseEntity<HealthCheckResponse> health() {
        HealthCheckResponseDto data = HealthCheckResponseDto.builder()
                .hello("world")
                .build();
        HealthCheckResponse healthCheckRes = HealthCheckResponse.healthcheckBuilder()
                .data(data)
                .build();
        return responseFactory.success(healthCheckRes);
    }
}

@Setter
@Getter
@Builder
class HealthCheckResponseDto {
    String hello;
}

@Data
@NoArgsConstructor
class HealthCheckResponse extends EntityResponse<HealthCheckResponseDto> {
    @Builder(builderMethodName = "healthcheckBuilder")
    public HealthCheckResponse(HealthCheckResponseDto data) {
        super(data);
    }
}
