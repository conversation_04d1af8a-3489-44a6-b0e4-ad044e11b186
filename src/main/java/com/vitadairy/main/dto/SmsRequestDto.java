package com.vitadairy.main.dto;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@AllArgsConstructor
public class SmsRequestDto {
    private String code;
    private SmsPayload payload;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UserGiftNotiPayload extends SmsPayload {
        private String name;
        private String phone;
        
        @JsonProperty("user_gift_id")
        private Long userGiftId;

        @Override
        public boolean validate() {
            return StringUtils.hasText(name) && StringUtils.hasText(phone) && userGiftId != null;
        }
    }
}

