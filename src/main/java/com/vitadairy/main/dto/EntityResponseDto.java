package com.vitadairy.main.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.helper.HttpStatusCodeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.http.HttpStatusCode;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class EntityResponseDto<T> {
    private T data;
    private int code;
    private String msg;
    private VtdStatusEnum vtdStatus;
    @JsonDeserialize(using = HttpStatusCodeDeserializer.class)
    private HttpStatusCode status;

    public EntityResponseDto(T data, int code, String msg, HttpStatusCode status) {
        this.data = data;
        this.code = code;
        this.msg = msg;
        this.status = status;
    }
}
