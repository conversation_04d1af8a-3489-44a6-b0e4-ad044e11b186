package com.vitadairy.main.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SfUserInfoDetailDto {
    @JsonProperty("Tier_Points__c")
    private String tierPoint;
    @JsonProperty("Tier__c")
    private String tier;
    @JsonProperty("Loyalty_Customer_Status__c")
    private String customerStatus;
    @JsonProperty("Lead_Origin_Detail__c")
    private String leadOriginDetail;
    @JsonProperty("Lead_Origin__c")
    private String leadOrigin;
    @JsonProperty("Billing_Street__c")
    private String billingStreet;
    @JsonProperty("Billing_Ward__c")
    private String billingWard;
    @JsonProperty("Billing_District__c")
    private String billingDistrict;
    @JsonProperty("Billing_Province__c")
    private String billingProvince;
    @JsonProperty("Shipping_Address__c")
    private String shippingAddress;
    @JsonProperty("Billing_Country__c")
    private String billingCountry;
    @JsonProperty("Billing_Address__c")
    private String billingAddress;
    @JsonProperty("Level_Points__c")
    private String levelPoint;
    @JsonProperty("Redeem_Points__c")
    private String redeemPoint;
    @JsonProperty("Birthday__c")
    private String birthday;
    @JsonProperty("Level_sf_id")
    private String levelSfId;
    @JsonProperty("Level__c")
    private String level;
    @JsonProperty("Phone")
    private String phone;
    @JsonProperty("email")
    private String email;
    @JsonProperty("name")
    private String name;
    @JsonProperty("id")
    private String id;
}
