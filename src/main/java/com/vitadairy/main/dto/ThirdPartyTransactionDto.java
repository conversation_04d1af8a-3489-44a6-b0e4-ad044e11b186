package com.vitadairy.main.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vitadairy.main.enums.TransactionStatus;
import com.vitadairy.main.serdes.SFPayloadSerializer;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.Map;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ThirdPartyTransactionDto {
    private String transactionCode;
    @NotNull
    @JsonSerialize(using = SFPayloadSerializer.class)
    private Map<String, Object> request;
    @NotNull
    @JsonSerialize(using = SFPayloadSerializer.class)
    private Map<String, Object> response;
    private TransactionStatus status;
    private String notes;
    private Instant createdAt;
}