package com.vitadairy.main.dto;

// import com.fasterxml.jackson.databind.PropertyNamingStrategies;
// import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Setter
@Getter
// @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BaseDto {
    private Instant createdAt;
    private Instant updatedAt;
}
