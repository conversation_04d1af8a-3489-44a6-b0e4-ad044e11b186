package com.vitadairy.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class PageableDataDto<T> {
    private List<T> data;
    private Long total;
    private Long totalPages;
    private Integer page;
    private Integer pageSize;
}
