package com.vitadairy.main.dto;

import com.vitadairy.main.enums.DestinationEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
public class TransactionBatchDto {
    private List<TransactionDto> transactions;

    public TransactionBatchDto() {
        this.transactions = new ArrayList<>();
    }

    public TransactionBatchDto(List<TransactionDto> transactions) {
        this.transactions = new ArrayList<>(transactions);
        this.transactions.addAll(this.buildSAPTransactions(transactions));
    }

    private List<TransactionDto> buildSAPTransactions(List<TransactionDto> transactions) {
        List<TransactionDto> sapTransactions = new ArrayList<>();

        for (var t : transactions) {
            if ("EXCHANGE_GIFT".equals(t.getCode())
                    && t.getDestination() == DestinationEnum.SF) {
                sapTransactions.add(new TransactionDto()
                        .setCode(t.getCode())
                        .setDestination(DestinationEnum.SAP)
                        .setTListPayload(List.of(t.getPayload()))
                );
            }
        }

        return sapTransactions;
    }
}
