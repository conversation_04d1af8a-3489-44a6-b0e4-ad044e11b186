package com.vitadairy.main.dto;

import com.vitadairy.main.response.EntityResponse;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

import java.util.List;

@NoArgsConstructor
public class ReturnPointResponseDto extends EntityResponse<List<GiftReturnPointResponseDto>> {
    public ReturnPointResponseDto(List<GiftReturnPointResponseDto> data, HttpStatusCode statusCode, String message) {
        super(data, statusCode, message);
    }
}
