package com.vitadairy.main.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vitadairy.main.enums.DestinationEnum;
import com.vitadairy.main.serdes.SFPayloadArraySerializer;
import com.vitadairy.main.serdes.SFPayloadSerializer;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class TransactionDto {
    @NotNull
    private String code;
    @JsonSerialize(using = SFPayloadSerializer.class)
    private Map<String, Object> payload;
    @JsonSerialize(using = SFPayloadArraySerializer.class)
    @JsonProperty("tListPayload")
    private List<Map<String, Object>> tListPayload;
    private DestinationEnum destination = DestinationEnum.SF;
    private Instant createdAt;

    public TransactionDto() {}

    public TransactionDto(TransactionDto origin) {
        this.code = origin.getCode();
        this.payload = origin.getPayload();
        this.tListPayload = origin.getTListPayload();
        this.destination = origin.getDestination();
        this.createdAt = origin.getCreatedAt();
    }
}