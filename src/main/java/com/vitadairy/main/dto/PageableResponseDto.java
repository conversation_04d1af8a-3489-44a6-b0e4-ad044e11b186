package com.vitadairy.main.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vitadairy.main.helper.HttpStatusCodeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class PageableResponseDto<T> {
    PageableDataDto<T> data;
    private int code;
    private String msg;
    @JsonDeserialize(using = HttpStatusCodeDeserializer.class)
    private HttpStatusCode status;
}

