package com.vitadairy.main.dto;

import com.vitadairy.zoo.enums.FeatureNoti;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomNotificationRequestDto {
    @NotNull
    Long[] userIds;

    @NotNull
    String title;

    @NotNull
    String content;

    String description;

    FeatureNoti featureNoti;

    Integer notiTopicId;

    NotificationData data;

    String buttonName;

    String link;
}

