package com.vitadairy.main.dto;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * Usage: internal use only, for validating but not want to throw right away
 */
@Data
@ToString
public class BaseResponse {
    private int rc;
    private String rd;

    public void setSuccess() {
        this.rc = 0;
        this.rd = "ok";
    }

    public void setSuccess(String msg) {
        this.rc = 0;
        this.rd = msg;
    }

    public void setFailed() {
        this.rc = 1;
        this.rd = "failed";
    }

    public void setFailed(String msg) {
        this.rc = 1;
        this.rd = msg;
    }
}
