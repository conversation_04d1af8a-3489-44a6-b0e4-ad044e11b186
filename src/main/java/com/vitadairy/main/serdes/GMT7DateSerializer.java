package com.vitadairy.main.serdes;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class GMT7DateSerializer extends JsonSerializer<Date> {
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");// GMT+7

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        SIMPLE_DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("GMT+7"));
        gen.writeString(SIMPLE_DATE_FORMAT.format(value));
    }
}
