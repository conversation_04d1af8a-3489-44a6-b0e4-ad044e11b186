package com.vitadairy.main.serdes;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class SFPayloadArraySerializer extends JsonSerializer<List<Map<String, Object>>> {
    private static final ZoneId GMT7 = ZoneId.of("Asia/Bangkok");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME.withZone(GMT7); // GMT+7
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");// GMT+7

    @Override
    public void serialize(List<Map<String, Object>> arrayMap, JsonGenerator gen, SerializerProvider provider) throws IOException {
        // Start writing the map
        gen.writeStartArray();

        for (var map : arrayMap){
            gen.writeStartObject();
            // Iterate through the map entries
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // Write the field name (the key)
                gen.writeFieldName(key);

                // Custom serialization logic for the value
                if (value instanceof TemporalAccessor) {
                    gen.writeString(DATE_TIME_FORMATTER.format((TemporalAccessor) value));
                } else if (value instanceof Date) {
                    SIMPLE_DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                    gen.writeString(SIMPLE_DATE_FORMAT.format((Date) value));
                } else {
                    // Fallback: use default serializer for other types
                    provider.defaultSerializeValue(value, gen);
                }
            }
            gen.writeEndObject();
        }

        // End the map
        gen.writeEndArray();
    }
}
