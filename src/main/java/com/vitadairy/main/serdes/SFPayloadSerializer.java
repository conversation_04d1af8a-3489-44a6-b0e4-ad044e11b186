package com.vitadairy.main.serdes;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

public class SFPayloadSerializer extends JsonSerializer<Map<String, Object>> {
    private static final ZoneId GMT7 = ZoneId.of("Asia/Bangkok");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME.withZone(GMT7); // GMT+7
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");// GMT+7

    @Override
    public void serialize(Map<String, Object> map, JsonGenerator gen, SerializerProvider provider) throws IOException {
        // Start writing the map
        gen.writeStartObject();

        // Iterate through the map entries
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // Write the field name (the key)
            gen.writeFieldName(key);

            // Custom serialization logic for the value
            if (value instanceof Instant) {
                value = ((Instant) value).truncatedTo(ChronoUnit.SECONDS);
                gen.writeString(DATE_TIME_FORMATTER.format((TemporalAccessor) value));
            } else if (value instanceof LocalDateTime) {
                value = ((LocalDateTime) value).truncatedTo(ChronoUnit.SECONDS);
                gen.writeString(DATE_TIME_FORMATTER.format((TemporalAccessor) value));
            }else if (value instanceof Date) {
                SIMPLE_DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("GMT+7"));
                gen.writeString(SIMPLE_DATE_FORMAT.format((Date) value));
            } else {
                // Fallback: use default serializer for other types
                provider.defaultSerializeValue(value, gen);
            }
        }

        // End the map
        gen.writeEndObject();
    }
}
