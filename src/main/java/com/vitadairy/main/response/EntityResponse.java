package com.vitadairy.main.response;

import com.vitadairy.main.dto.EntityResponseDto;
import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.iresponse.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

/**
 * <AUTHOR>
 * template response for request that return only one object inside
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityResponse<T> implements BaseResponse {
    EntityResponseDto<T> response;

    @Builder(builderMethodName = "entityBuilder")
    public EntityResponse(T data, HttpStatusCode statusCode, String msg) {
        this.response = new EntityResponseDto<>(data, statusCode.value(), msg, statusCode);
    }

    public EntityResponse(T data) {
        this.response = new EntityResponseDto<>(data, HttpStatus.OK.value(), "Ok", HttpStatus.OK);
    }

    @Override
    public void setCode(int code) {
        this.response.setCode(code);
    }

    @Override
    public void setMsg(String msg) {
        this.response.setMsg(msg);
    }

    @Override
    public void setStatus(HttpStatusCode status) {
        this.response.setStatus(status);
    }

    @Override
    public void setVtdStatus(VtdStatusEnum vtdStatusEnum) {
        this.response.setVtdStatus(vtdStatusEnum);
    }

    public void setData(T data) {
        this.response = new EntityResponseDto<>(data, HttpStatus.OK.value(), "Ok", HttpStatus.OK);
    }
}
