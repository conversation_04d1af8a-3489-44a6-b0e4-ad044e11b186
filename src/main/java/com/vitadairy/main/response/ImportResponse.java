package com.vitadairy.main.response;

import com.vitadairy.main.dto.ImportResponseDto;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ImportResponse extends EntityResponse<ImportResponseDto> {

    private ResponseMetaData meta;

    @Builder(builderMethodName = "importResponseBuilder")
    public ImportResponse(ResponseMetaData meta, ImportResponseDto data) {
        super(data);
        this.meta = meta;
    }

}
