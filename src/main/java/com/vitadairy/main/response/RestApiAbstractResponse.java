package com.vitadairy.main.response;

import com.vitadairy.main.iresponse.BaseRestApiEntityResponse;

/**
 * <AUTHOR>
 */
public abstract class RestApiAbstractResponse<T> extends EntityResponse<T> implements BaseRestApiEntityResponse<T> {


    @Override
    public T getData() {
        return response.getData();
    }

    @Override
    public int getCode() {
        return response.getCode();
    }

    @Override
    public String getMsg() {
        return response.getMsg();
    }

    @Override
    public String getStatus() {
        return response.getStatus().toString();
    }
}
