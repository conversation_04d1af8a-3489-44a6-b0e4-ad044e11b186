package com.vitadairy.main.response;

import com.vitadairy.main.enums.VtdStatusEnum;
import com.vitadairy.main.dto.PageableDataDto;
import com.vitadairy.main.dto.PageableResponseDto;
import com.vitadairy.main.iresponse.BaseResponse;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

import java.util.List;

@Data
@NoArgsConstructor
public class PageableResponse<T> implements BaseResponse {
    PageableResponseDto<T> response;

    @Builder(builderMethodName = "pageableBuilder")
    public PageableResponse(List<T> data, Long total, Long totalPage, Integer page, Integer pageSize,
                            HttpStatusCode statusCode, String msg) {
        PageableDataDto<T> pageableDataDto = new PageableDataDto<>(data, total, totalPage, page, pageSize);
        this.response = new PageableResponseDto<>(pageableDataDto, statusCode != null ? statusCode.value() : 400, msg, statusCode);
    }

    public PageableResponse(List<T> data, Long total) {
        PageableDataDto<T> pageableDataDto = new PageableDataDto<>(data, total, 0L, 0, 0);
        this.response = new PageableResponseDto<>(pageableDataDto, 200, "Success", HttpStatusCode.valueOf(200));

    }

    @Override
    public void setCode(int code) {
        this.response.setCode(code);
    }

    @Override
    public void setMsg(String msg) {
        this.response.setMsg(msg);
    }

    @Override
    public void setStatus(HttpStatusCode status) {
        this.response.setStatus(status);
    }

    @Override
    public void setVtdStatus(VtdStatusEnum vtdStatusEnum) {
        // not implemented yet
    }

    public void setTotal(Long total) {
        this.response.getData().setTotal(total);
    }

    public void setData(List<T> data) {
        this.response.getData().setData(data);
    }
}
