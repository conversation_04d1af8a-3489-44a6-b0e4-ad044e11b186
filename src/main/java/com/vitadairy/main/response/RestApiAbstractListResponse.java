package com.vitadairy.main.response;

import com.vitadairy.main.iresponse.BaseRestApiListResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class RestApiAbstractListResponse<T> extends PageableResponse<T> implements BaseRestApiListResponse<T> {
    @Override
    public List<T> getData() {
        return response.getData().getData();
    }

    @Override
    public Long getTotal() {
        return response.getData().getTotal();
    }

    @Override
    public Integer getPage() {
        return response.getData().getPage();
    }

    @Override
    public Integer getPageSize() {
        return response.getData().getPageSize();
    }

    @Override
    public int getCode() {
        return response.getCode();
    }

    @Override
    public String getMsg() {
        return response.getMsg();
    }

    @Override
    public String getStatus() {
        return response.getStatus().toString();
    }
}
