package com.vitadairy.main.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.Map;

public class JsonNodeUtils {
    public static <T> T deserialize(JsonParser jp, Class<T> clazz) throws IOException {
        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        JsonNode node = mapper.readTree(jp);
        T result = null;
        try {
            result = clazz.getDeclaredConstructor().newInstance();
        } catch (NoSuchMethodException | InvocationTargetException | InstantiationException |
                 IllegalAccessException e) {
            // Add @NoArgsConstructor @AllArgsConstructor in to T class
            throw new RuntimeException(e);
        }

        // Iterate over all fields in OrderGift class
        for (Field field : clazz.getDeclaredFields()) {
            String camelCaseFieldName = field.getName();
            String snakeCaseFieldName = toSnakeCase(camelCaseFieldName);

            // Check for both camelCase and snake_case
            if (node.has(camelCaseFieldName) || node.has(snakeCaseFieldName)) {
                String jsonFieldName = node.has(camelCaseFieldName) ? camelCaseFieldName : snakeCaseFieldName;
                setField(result, field, node, camelCaseFieldName, jsonFieldName, mapper, clazz);
            }
        }
        return result;
    }

    private static <T> void setField(T target, Field field, JsonNode node, String camelCaseFieldName, String jsonFieldName, ObjectMapper mapper, Class<T> clazz) {
        try {
            Class<?> fieldType = field.getType();
            Method setter = clazz.getMethod(getSetterName(camelCaseFieldName), fieldType);

            if (fieldType == Instant.class) {
                try {
                    long date = node.get(jsonFieldName).asLong(-1);
                    String data = node.get(jsonFieldName).asText();
                    if (data.contains(".") && !data.matches(".*[a-zA-Z].*")) {
                        date = Long.parseLong(data.replace(".", "")) * 1000000;
                        setter.invoke(target, Instant.ofEpochMilli(date));
                    } else if (date != -1) {
                        long now = Instant.now().getEpochSecond();
                        if (date > now && date / now >= 100) {
                            setter.invoke(target, Instant.ofEpochMilli(date));
                        } else {
                            setter.invoke(target, Instant.ofEpochSecond(date));
                        }
                    }
                    if (date == -1) {
                        String instant = node.get(jsonFieldName).asText();
                        setter.invoke(target, Instant.parse(instant));
                    }
                } catch (DateTimeParseException e) {
                    setter.invoke(target, (Instant) null); // Handle parsing error
                }
            } else if (fieldType == Map.class) {
                setter.invoke(target, mapper.convertValue(node.get(jsonFieldName), Map.class));
            } else if (!fieldType.isPrimitive()) {
                setter.invoke(target, mapper.treeToValue(node.get(jsonFieldName), fieldType));
            } else {
                invokePrimitiveSetter(target, setter, node.get(jsonFieldName), fieldType);
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException |
                 JsonProcessingException e) {
            e.printStackTrace();  // Handle reflection exceptions
        }
    }

    private static <T> void invokePrimitiveSetter(T target, Method setter, JsonNode node, Class<?> fieldType)
            throws InvocationTargetException, IllegalAccessException {
        if (fieldType == int.class) {
            setter.invoke(target, node.asInt());
        } else if (fieldType == long.class) {
            setter.invoke(target, node.asLong());
        } else if (fieldType == boolean.class) {
            setter.invoke(target, node.asBoolean());
        } else if (fieldType == double.class) {
            setter.invoke(target, node.asDouble());
        } else if (fieldType == float.class) {
            setter.invoke(target, (float) node.asDouble());
        } else if (fieldType == short.class) {
            setter.invoke(target, (short) node.asInt());
        } else if (fieldType == byte.class) {
            setter.invoke(target, (byte) node.asInt());
        } else if (fieldType == char.class) {
            setter.invoke(target, node.asText().charAt(0));
        }
    }

    private static String getSetterName(String fieldName) {
        return "set" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
    }

    private static String toSnakeCase(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append('_').append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
}
