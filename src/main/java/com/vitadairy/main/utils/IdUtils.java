package com.vitadairy.main.utils;

import com.vitadairy.main.common.ConstantFieldDefs;
import com.vitadairy.zoo.common.CrmTransactionTypeCode;
import org.apache.commons.lang3.RandomStringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class IdUtils {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyMMdd");

    public static String generateId(String prefix) {
        return generateId(prefix, true, true, 6);
    }

    public static String generateId(String prefix, Date date) {
        return generateId(prefix, true, true, 6, date);
    }

    public static String generateId(String prefix, boolean includeDate, boolean includeSuffix, int suffixLength) {
        return generateId(prefix, includeDate, includeSuffix, suffixLength, null);
    }

    public static String generateId(String prefix, boolean includeDate, boolean includeSuffix, int suffixLength, Date date) {
        StringBuilder strBuilder = new StringBuilder(prefix.toUpperCase(Locale.ROOT));
        if (includeDate) {
            if (date == null) {
                date = new Date();
            }
            strBuilder.append(SDF.format(date));
        }
        if (includeSuffix) {
            strBuilder.append(RandomStringUtils.randomAlphanumeric(suffixLength).toUpperCase(Locale.ROOT));
        }
        return strBuilder.toString();
    }

    public static String generateIdWithActionType(String actionType) {
        var prefix = ConstantFieldDefs.TRANSACTION_CODE_PREFIX;
        if (CrmTransactionTypeCode.REWARD_GIFT.equals(actionType)) {
            prefix = ConstantFieldDefs.EXCHANGE_GIFT;
        }
        return IdUtils.generateId(prefix);
    }
}
