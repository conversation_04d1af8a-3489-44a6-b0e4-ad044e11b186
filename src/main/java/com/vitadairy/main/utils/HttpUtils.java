package com.vitadairy.main.utils;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class HttpUtils {

    public static CloseableHttpResponse sendPost(String url, String data, Map<String, String> headers){
        try{
            CloseableHttpClient client = getHttpClient();
            HttpPost httpPost = new HttpPost(url);
            if(Objects.nonNull(headers) && headers.size() > 0){
                for(Map.Entry<String, String> entry : headers.entrySet()){
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            if(Objects.nonNull(data)){
                StringEntity entity = new StringEntity(data);
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse res = client.execute(httpPost);
            return res;
        }
        catch (Exception ex){
            System.out.println("Error when call Post");
            ex.printStackTrace();
            return null;
        }
    }

    public static String getDataFromResponse(CloseableHttpResponse response){
        try{
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity, "UTF-8");
        }
        catch (Exception ex){
            System.out.println("Cannot get data from response");
            return null;
        }
    }

    public static CloseableHttpResponse sendGet(String url, Map<String, String> headers){
        try{
            CloseableHttpClient client = getHttpClient();
            HttpGet httpGet = new HttpGet(url);
            if(Objects.nonNull(headers)){
                for(Map.Entry<String, String> entry : headers.entrySet()){
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }
            CloseableHttpResponse res = client.execute(httpGet);
            return res;
        }
        catch (Exception ex){
            System.out.println("Error when call Get");
            return null;
        }
    }

    public static CloseableHttpResponse sendDelete(String url, Map<String, String> headers){
        try{
            CloseableHttpClient client = getHttpClient();
            HttpDelete httpDelete = new HttpDelete(url);
            for(Map.Entry<String, String> entry : headers.entrySet()){
                httpDelete.setHeader(entry.getKey(), entry.getValue());
            }
            CloseableHttpResponse res = client.execute(httpDelete);
            return res;
        }
        catch (Exception ex){
            System.out.println("Error when call delete");
            ex.printStackTrace();
            return null;
        }
    }

    private static CloseableHttpClient getHttpClient(){
        try{
            CloseableHttpClient httpClient = HttpClients.custom().
                    setHostnameVerifier(new AllowAllHostnameVerifier()).
                    setSslcontext(new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy()
                    {
                        public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException
                        {
                            return true;
                        }
                    }).build()).build();
            return httpClient;
        }
        catch (Exception ex){
            System.out.println("Error on create HttpClient");
            ex.printStackTrace();
            return null;
        }
    }
}
