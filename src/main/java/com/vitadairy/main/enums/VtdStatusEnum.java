package com.vitadairy.main.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum VtdStatusEnum {
        GF_SUCCESS(HttpStatus.OK,
                        "200000",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-thanh-cong.png",
                        "Đổi quà thành công",
                        "Bạn đã đổi quà thành công. Vui lòng kiểm tra mục quà của tôi để nhận quà bạn nhé!",
                        "XEM QUÀ CỦA TÔI",
                        "/my-reward?tab=gift",
                        "BỎ QUA",
                        null),
        GF_NOT_FOUND(HttpStatus.NOT_FOUND,
                        "404000",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Đã có lỗi xảy ra",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_USER(HttpStatus.NOT_FOUND,
                        "404001",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Đã có lỗi xảy ra",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_TIME(HttpStatus.NOT_FOUND,
                        "404002",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Đã có lỗi xảy ra",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_TIER(HttpStatus.NOT_FOUND,
                        "404003",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Thứ hạng không tồn tại",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_QUANTITY(HttpStatus.NOT_FOUND,
                        "404004",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Số lượng quà tặng đã hết",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_POINT(HttpStatus.BAD_REQUEST,
                        "404005",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Bạn không đủ xu để đặt trước món quà này. Hãy tích lũy thêm xu và thử lại.",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GF_STORAGE(HttpStatus.BAD_REQUEST,
                        "404006",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đổi quà không thành công",
                        "Bạn không đủ điểm đổi thưởng.",
                        "THỬ LẠI",
                        null,
                        "LIÊN HỆ NGAY",
                        "/call?phone=1900633559"),
        GP_ACTIVE(HttpStatus.BAD_REQUEST,
                        "404007",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Không thể đặt trước",
                        "Rất tiếc, món quà này không hỗ trợ đặt trước. Vui lòng chọn món quà khác.",
                        null,
                        null,
                        null,
                        null),
        GP_USER_PRE(HttpStatus.BAD_REQUEST,
                        "404008",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Đang đặt trước",
                        "Bạn đang trong quá trình đặt trước món quà này. Vui lòng kiểm tra lại đơn quà của bạn.",
                        null,
                        null,
                        null,
                        null),
        GP_LIMIT(HttpStatus.BAD_REQUEST,
                        "404009",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Vượt quá giới hạn",
                        "Món quà này đã đạt giới hạn số lượng đặt trước tối đa. Hãy thử lại sau.",
                        null,
                        null,
                        null,
                        null),
        GP_PERIOD_DAY(HttpStatus.BAD_REQUEST,
                        "404010",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Hết thời gian",
                        "Thời gian đặt trước cho món quà này đã kết thúc. Vui lòng theo dõi để đặt trước vào lần sau.",
                        null,
                        null,
                        null,
                        null),
        GP_POINT(HttpStatus.BAD_REQUEST,
                        "404011",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Không đủ xu",
                        "Bạn không đủ xu để đặt trước món quà này. Hãy tích lũy thêm xu và thử lại.",
                        null,
                        null,
                        null,
                        null),
        GP_QUANTITY(HttpStatus.BAD_REQUEST,
                        "404012",
                        "https://storage.googleapis.com/vitadairy_public/popup/ev-moi-khong-thanh-cong.png",
                        "Hết quà",
                        "Số lượng quà đặt trước đã hết. Vui lòng chọn món quà khác hoặc quay lại sau.",
                        null,
                        null,
                        null,
                        null),
                        ;

        VtdStatusEnum(HttpStatus httpStatus, String code, String image, String title, String content, String cta1,
                        String ctaLink1, String cta2, String ctaUri2) {
                this.httpStatus = httpStatus;
                this.code = code;
                this.image = image;
                this.title = title;
                this.content = content;
                this.cta1 = cta1;
                this.ctaLink1 = ctaLink1;
                this.cta2 = cta2;
                this.ctaLink2 = ctaUri2;
        }

        @JsonIgnore
        private final HttpStatus httpStatus;
        private final String code;
        private final String image;
        private final String title;
        private final String content;
        private final String cta1;
        private final String ctaLink1;
        private final String cta2;
        private final String ctaLink2;
}
