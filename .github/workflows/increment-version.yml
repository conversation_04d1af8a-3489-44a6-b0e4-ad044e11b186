name: Increment version

on:
  workflow_dispatch:
    inputs:
      action_name:
        description: "Action"
        default: "Increment development environment"
        required: true
        type: choice
        options:
          - Increment patch version (1.0.xx)
          - Increment minor version (1.xx.0)
          # - Increment major version (xx.0.0)
          # - Increment development environment
          # - Increment staging environment
          # - Increment product environment

defaults:
  run:
    shell: bash -leo pipefail {0}

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  # TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
  # TELEGRAM_GROUP_ID: ${{ vars.TELEGRAM_GROUP_ID }}
  # TELEGRAM_TOPIC_ID: ${{ vars.TELEGRAM_TOPIC_ID}}
  # TELEGRAM_CHANNEL_ID: ${{ vars.TELEGRAM_CHANNEL_ID }}
  GITHUB_TRIGGERING_ACTOR: ${{ github.triggering_actor }}

jobs:
  increment-version:
    name: Increment version
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get stage key
        uses: dkershner6/switch-case-action@v1
        id: switch-case
        with:
          default: "none"
          conditionals-with-values: |
            ${{ inputs.action_name == 'Increment patch version (1.0.xx)' }} => increment_core_tag patch
            ${{ inputs.action_name == 'Increment minor version (1.xx.0)' }} => increment_core_tag minor
            ${{ inputs.action_name == 'Increment major version (xx.0.0)' }} => increment_core_tag major
            ${{ inputs.action_name == 'Increment development environment' }} => increment_tag dev
            ${{ inputs.action_name == 'Increment staging environment' }} => increment_tag stg
            ${{ inputs.action_name == 'Increment product environment' }} => increment_tag prd

      - name: Increment
        uses: tuanngocptn/semantic-versioning-action@main
        if: ${{ steps.switch-case.outputs.value != 'none' }}
        with:
          script: ${{ steps.switch-case.outputs.value }}

      - name: Trigger
        run: |
          BRANCH_NAME=${{ github.head_ref || github.ref_name }} 
          TAG_NAME=$(git describe --tags)
          TRIGGERING_ACTOR=${{ github.triggering_actor }}
          echo $BRANCH_NAME
          echo $TAG_NAME
          echo $TRIGGERING_ACTOR
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.VTD_TRIGGER_PPAP_PAT }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/VitaDairy/vtd-be-ppap/dispatches \
            -d "{\"event_type\": \"refs/tags/$TAG_NAME~$TRIGGERING_ACTOR\", \"client_payload\": {\"ref_name\": \"$BRANCH_NAME\", \"tag_name\": \"$TAG_NAME\", \"triggering_actor\": \"$TRIGGERING_ACTOR\"}}"

      # - name: Generate message
      #   run: |
      #     source .github/scripts/utils.sh
      #     echo "VALUE=$(get_message_information)" >> "$GITHUB_OUTPUT"
      #   id: message_information

      # - name: send telegram message
      #   uses: PacificPromise/macos-telegram-action@main
      #   with:
      #     type: channel
      #     message: 'Increment version: ${{ steps.message_information.outputs.VALUE }}'