name: Clean check source code

on:
  push:

env:
  TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
  VTD_PRIVATE_WRITE_PAT: ${{ secrets.VTD_PRIVATE_WRITE_PAT }}
  TELEGRAM_GROUP_ID: ${{ vars.TELEGRAM_GROUP_CLEAN_CHECK_ID}}
  TELEGRAM_TOPIC_ID: ${{ vars.TELEGRAM_TOPIC_CLEAN_CHECK_ID}}
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

jobs:
  build-and-check:
    name: ${{ matrix.name }}
    timeout-minutes: 60
    runs-on: seal
    strategy:
      matrix:
        include: ${{ fromJson(vars.STRATEGY_MATRIX_SERVICES) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Clone ci properties file
        uses: actions/checkout@v4
        with:
          repository: VitaDairy/vtd-be-env
          sparse-checkout: v4-java/application-ci.properties
          path: temp/vtd-be-env/
          token: ${{ env.VTD_PRIVATE_WRITE_PAT }}

      - name: Switch profile
        run: |
          sed -i "s|spring.profiles.active=local|spring.profiles.active=ci|g" src/main/resources/application.properties
          mv temp/vtd-be-env/v4-java/application-ci.properties src/main/resources/application-ci.properties

      - name: Setup java 21
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.6

      - name: Generate message
        id: get-message-information
        run: |
          source .github/scripts/utils.sh
          echo "value=$(get_message_information)" >> "$GITHUB_OUTPUT"
        timeout-minutes: 1

      - name: Check version
        run: |
          java --version 
          mvn --version

      - name: Remove the rest
        run: |
          rm -rf temp && mkdir temp
          mv -v src/main/java/com/vitadairy/* temp
          mv temp/auth src/main/java/com/vitadairy/
          mv temp/zoo src/main/java/com/vitadairy/
          mv temp/main src/main/java/com/vitadairy/
          mv temp/Application.java src/main/java/com/vitadairy/
          mv temp/ConfigProperties.java src/main/java/com/vitadairy/
          mv temp/${{ matrix.service }} src/main/java/com/vitadairy/

      - name: Build and package project
        run: mvn -f pom.xml clean install package -q

      - name: Send telegram message failure
        if: failure()
        uses: PacificPromise/macos-telegram-action@main
        with:
          type: topic
          message: "💥 - Failure ${{ matrix.name }} (có thằng push code lỗi): ${{ steps.get-message-information.outputs.value }}"
