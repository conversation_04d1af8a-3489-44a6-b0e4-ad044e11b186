name: Trigger sync

on:
  push:
    branches:
      - main
      - stg/**
      - release/**
      - hotfix/**
  workflow_dispatch: {}

defaults:
  run:
    shell: bash -leo pipefail {0}

jobs:
  running:
    name: Sync
    runs-on: ubuntu-latest
    steps:
      - name: Trigger
        run: |
          BRANCH_NAME=${{ github.head_ref || github.ref_name }} 
          TRIGGERING_ACTOR=${{ github.triggering_actor }}
          echo $BRANCH_NAME
          echo $TRIGGERING_ACTOR
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.VTD_TRIGGER_PPAP_PAT }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/VitaDairy/vtd-be-ppap/dispatches \
            -d "{\"event_type\": \"refs/remotes/$BRANCH_NAME~$TRIGGERING_ACTOR\", \"client_payload\": {\"ref_name\": \"$BRANCH_NAME\", \"triggering_actor\": \"$TRIGGERING_ACTOR\"}}"