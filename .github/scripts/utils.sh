get_message_information() {
  LINK_ACTION="https://github.com/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
  AUTHOR=$GITHUB_TRIGGERING_ACTOR
  # AUTHOR=$TRIGGERING_ACTOR
  if [ -z "$AUTHOR" ]; then
    AUTHOR=$(git show -s --format='%an')
  fi
  AUTHOR=$(get_author_name "$AUTHOR")
  MESSAGE_INFO="\n- App: VTD BE Java.\n"
  MESSAGE_INFO+="- Thanh niên: ${AUTHOR}.\n"
  MESSAGE_INFO+="- Nhánh: ${BRANCH_NAME}.\n"
  MESSAGE_INFO+="- Link action: $LINK_ACTION.\n"
  echo $MESSAGE_INFO
}

get_author_name() {
  AUTHOR_NAME=$1
  case $AUTHOR_NAME in
  phamtien1709)
    AUTHOR_NAME="Tiến - Trâu <PERSON>"
    ;;
  '<PERSON>am')
    AUTHOR_NAME="Tiến - Trâ<PERSON> Thủ Đ<PERSON>"
    ;;
  tuanngocptn)
    AUTHOR_NAME="Cọc"
    ;;
  'Nick - Ngoc Loc Coc')
    AUTHOR_NAME="Cọc"
    ;;
  datnt2996)
    AUTHOR_NAME="Đạt - Người tử tế"
    ;;
  vladimirteddy)
    AUTHOR_NAME="Người cộng sản liêm khiết"
    ;;
  esac
  echo $AUTHOR_NAME
}
