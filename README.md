# vtd-be-java-main

Source code is built on the clean architecture model.

## Migration

Create sql files for each environment.  
Example: `src/main/resources/db/migration/dev/V20240626__name_migration`  
Format file migration: `V${timestamp}__name_migration`

Run your migration:

1. Create your SQL file match each enviroment.
2. Run below script:

```bash
./mvnw flyway:migrate -Dflyway.url=jdbc:url_db/db_name -Dflyway.user=username -Dflyway.password=password -Dflyway.locations=filesystem:src/main/resources/db/migration/$ENVIROMENT
```

$ENVIROMENT: dev, uat, prod
Example:

```bash
./mvnw flyway:migrate -Dflyway.url=jdbc:url_db/db_name -Dflyway.user=username -Dflyway.password=password -Dflyway.locations=filesystem:src/main/resources/db/migration/dev
```

## Create secret install and build dependency

### Create file "xxx.properties" from the template properties file "application-template.properties"

You should be create file application-dev.properties in path `/resources` for running locally


### Set profile for your project

```bash
./mvnw clean package -D spring.profiles.action=dev
```

## Optional step

### Install

```bash
./mvnw install
```

### Build

```bash
./mvnw package
```

## Run source code

The current repo has LiveReload. You can import into IDEs (vsCode, Eclipse, Inteliij,...).

## Spring initializr requirement

[Project: Maven] (https://maven.apache.org/guides/getting-started/maven-in-five-minutes.html)  
[Language: Java V21] (https://bell-sw.com/pages/downloads/#jdk-21-lts)  
[Spring Boot: 3.3.0] (https://spring.io/blog/2024/05/23/spring-boot-3-3-0-available-now).

# Getting Started

### Reference Documentation

For further reference, please consider the following sections:

- [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
- [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/3.3.0/maven-plugin/reference/html/)
- [Create an OCI image](https://docs.spring.io/spring-boot/docs/3.3.0/maven-plugin/reference/html/#build-image)
- [Spring Boot DevTools](https://docs.spring.io/spring-boot/docs/3.3.0/reference/htmlsingle/index.html#using.devtools)
- [Spring Web](https://docs.spring.io/spring-boot/docs/3.3.0/reference/htmlsingle/index.html#web)
- [Validation](https://docs.spring.io/spring-boot/docs/3.3.0/reference/htmlsingle/index.html#io.validation)
- [Spring Data JPA](https://docs.spring.io/spring-boot/docs/3.3.0/reference/htmlsingle/index.html#data.sql.jpa-and-spring-data)
- [Flyway Migration](https://docs.spring.io/spring-boot/docs/3.3.0/reference/htmlsingle/index.html#howto.data-initialization.migration-tool.flyway)

### Guides

The following guides illustrate how to use some features concretely:

- [Building a RESTful Web Service](https://spring.io/guides/gs/rest-service/)
- [Serving Web Content with Spring MVC](https://spring.io/guides/gs/serving-web-content/)
- [Building REST services with Spring](https://spring.io/guides/tutorials/rest/)
- [Validation](https://spring.io/guides/gs/validating-form-input/)
- [Accessing Data with JPA](https://spring.io/guides/gs/accessing-data-jpa/)